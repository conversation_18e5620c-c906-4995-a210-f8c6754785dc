from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                           QTableWidgetItem, QPushButton, QHeaderView, QLabel, 
                           QLineEdit, QComboBox, QDialog, QFormLayout, QMessageBox,
                           QSpinBox, QTreeWidget, QTreeWidgetItem, QDialogButtonBox)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon, QColor

class PermissionManagementModule(QWidget):
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 搜索区域
        search_layout = QHBoxLayout()
        
        self.permission_name_search = QLineEdit()
        self.permission_name_search.setPlaceholderText("权限名称")
        self.permission_name_search.setMaximumWidth(150)
        
        self.permission_code_search = QLineEdit()
        self.permission_code_search.setPlaceholderText("权限代码")
        self.permission_code_search.setMaximumWidth(150)
        
        self.type_filter = QComboBox()
        self.type_filter.addItem("全部类型", -1)
        self.type_filter.addItem("菜单", 1)
        self.type_filter.addItem("按钮", 2)
        self.type_filter.addItem("数据", 3)
        self.type_filter.setMaximumWidth(100)
        
        self.status_filter = QComboBox()
        self.status_filter.addItem("全部状态", -1)
        self.status_filter.addItem("启用", 1)
        self.status_filter.addItem("禁用", 0)
        self.status_filter.setMaximumWidth(100)
        
        search_button = QPushButton("搜索")
        search_button.clicked.connect(self.search_permissions)
        
        reset_button = QPushButton("重置")
        reset_button.clicked.connect(self.reset_search)
        
        search_layout.addWidget(QLabel("权限名称:"))
        search_layout.addWidget(self.permission_name_search)
        search_layout.addWidget(QLabel("权限代码:"))
        search_layout.addWidget(self.permission_code_search)
        search_layout.addWidget(QLabel("类型:"))
        search_layout.addWidget(self.type_filter)
        search_layout.addWidget(QLabel("状态:"))
        search_layout.addWidget(self.status_filter)
        search_layout.addWidget(search_button)
        search_layout.addWidget(reset_button)
        search_layout.addStretch()
        
        # 操作按钮区域
        button_layout = QHBoxLayout()
        
        add_button = QPushButton("添加权限")
        add_button.setIcon(QIcon("resources/icons/add.png"))
        add_button.clicked.connect(self.add_permission)
        
        edit_button = QPushButton("编辑权限")
        edit_button.setIcon(QIcon("resources/icons/edit.png"))
        edit_button.clicked.connect(self.edit_permission)
        
        delete_button = QPushButton("删除权限")
        delete_button.setIcon(QIcon("resources/icons/delete.png"))
        delete_button.clicked.connect(self.delete_permission)
        
        refresh_button = QPushButton("刷新")
        refresh_button.setIcon(QIcon("resources/icons/refresh.png"))
        refresh_button.clicked.connect(self.search_permissions)
        
        button_layout.addWidget(add_button)
        button_layout.addWidget(edit_button)
        button_layout.addWidget(delete_button)
        button_layout.addStretch()
        button_layout.addWidget(refresh_button)
        
        # 权限表格
        self.permission_table = QTableWidget()
        self.permission_table.setColumnCount(7)
        self.permission_table.setHorizontalHeaderLabels([
            "ID", "权限名称", "权限代码", "类型", "父权限", "排序", "状态"
        ])
        
        # 设置表格样式
        self.permission_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.permission_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.permission_table.setAlternatingRowColors(True)
        
        # 禁用表格编辑，只允许双击弹出编辑窗口
        self.permission_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 连接双击事件
        self.permission_table.cellDoubleClicked.connect(self.on_cell_double_clicked)
        
        self.permission_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: #fff;
            }
            QTableWidget::item:selected {
                background-color: #e0f2fe;
                color: #000;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 5px;
                border: none;
                border-right: 1px solid #ddd;
                border-bottom: 1px solid #ddd;
                font-weight: bold;
            }
        """)
        
        # 自动调整表格
        header = self.permission_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        
        # 布局
        layout.addLayout(search_layout)
        layout.addLayout(button_layout)
        layout.addWidget(self.permission_table)
        
        self.setLayout(layout)
        
        # 初始加载数据
        self.search_permissions()
    
    def search_permissions(self):
        """搜索权限"""
        try:
            permission_name = self.permission_name_search.text().strip()
            permission_code = self.permission_code_search.text().strip()
            permission_type = self.type_filter.currentData()
            status = self.status_filter.currentData()
            
            # 构建查询条件
            query = """
            SELECT 
                p.permission_id, p.permission_name, p.permission_code, 
                p.permission_type, p.parent_id, p.menu_sort, p.status,
                parent.permission_name as parent_name
            FROM permission p
            LEFT JOIN permission parent ON p.parent_id = parent.permission_id
            WHERE 1=1
            """
            params = []
            
            if permission_name:
                query += " AND p.permission_name LIKE %s"
                params.append(f"%{permission_name}%")
            
            if permission_code:
                query += " AND p.permission_code LIKE %s"
                params.append(f"%{permission_code}%")
            
            if permission_type != -1:
                query += " AND p.permission_type = %s"
                params.append(permission_type)
            
            if status != -1:
                query += " AND p.status = %s"
                params.append(status)
            
            query += " ORDER BY p.parent_id, p.menu_sort, p.permission_id"
            
            # 执行查询
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            results = db_manager.execute_query(query, tuple(params) if params else None)
            
            # 清空表格
            self.permission_table.setRowCount(0)
            
            # 填充数据
            if results:
                self.permission_table.setRowCount(len(results))
                for row, perm in enumerate(results):
                    self.permission_table.setItem(row, 0, QTableWidgetItem(str(perm['permission_id'])))
                    self.permission_table.setItem(row, 1, QTableWidgetItem(perm['permission_name']))
                    self.permission_table.setItem(row, 2, QTableWidgetItem(perm['permission_code']))
                    
                    # 权限类型
                    type_map = {1: "菜单", 2: "按钮", 3: "数据"}
                    type_item = QTableWidgetItem(type_map.get(perm['permission_type'], "未知"))
                    self.permission_table.setItem(row, 3, type_item)
                    
                    # 父权限
                    self.permission_table.setItem(row, 4, QTableWidgetItem(perm['parent_name'] or "无"))
                    
                    # 排序
                    self.permission_table.setItem(row, 5, QTableWidgetItem(str(perm['menu_sort'] or 0)))
                    
                    # 状态
                    status_item = QTableWidgetItem("启用" if perm['status'] == 1 else "禁用")
                    status_item.setForeground(QColor("#2ecc71" if perm['status'] == 1 else "#e74c3c"))
                    self.permission_table.setItem(row, 6, status_item)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询权限失败：{str(e)}")
    
    def reset_search(self):
        """重置搜索条件"""
        self.permission_name_search.clear()
        self.permission_code_search.clear()
        self.type_filter.setCurrentIndex(0)
        self.status_filter.setCurrentIndex(0)
        self.search_permissions()
    
    def on_cell_double_clicked(self, row, column):
        """处理表格双击事件"""
        if row >= 0:
            self.edit_permission()
    
    def add_permission(self):
        """添加权限"""
        dialog = PermissionDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 添加权限
            try:
                perm_data = dialog.get_permission_data()
                
                # 检查权限代码是否已存在
                from modules.db_manager import DatabaseManager
                db_manager = DatabaseManager()
                check_query = "SELECT COUNT(*) as count FROM permission WHERE permission_code = %s"
                result = db_manager.execute_query(check_query, (perm_data['permission_code'],))
                
                if result and result[0]['count'] > 0:
                    QMessageBox.warning(self, "警告", "权限代码已存在，请使用其他权限代码")
                    return
                
                # 插入权限
                insert_query = """
                INSERT INTO permission (
                    permission_name, permission_code, permission_type, 
                    parent_id, menu_icon, menu_path, menu_component, 
                    menu_sort, status
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                params = (
                    perm_data['permission_name'],
                    perm_data['permission_code'],
                    perm_data['permission_type'],
                    perm_data['parent_id'],
                    perm_data.get('menu_icon', None),
                    perm_data.get('menu_path', None),
                    perm_data.get('menu_component', None),
                    perm_data.get('menu_sort', 0),
                    perm_data['status']
                )
                
                db_manager.execute_update(insert_query, params)
                
                QMessageBox.information(self, "成功", "权限添加成功")
                self.search_permissions()
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"添加权限失败：{str(e)}")
    
    def edit_permission(self):
        """编辑权限"""
        selected_rows = self.permission_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要编辑的权限")
            return
        
        row = selected_rows[0].row()
        permission_id = int(self.permission_table.item(row, 0).text())
        
        try:
            # 获取权限信息
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            query = "SELECT * FROM permission WHERE permission_id = %s"
            result = db_manager.execute_query(query, (permission_id,))
            
            if not result:
                QMessageBox.warning(self, "警告", "未找到权限信息")
                return
            
            permission = result[0]
            
            # 打开编辑对话框
            dialog = PermissionDialog(self, permission)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # 更新权限
                perm_data = dialog.get_permission_data()
                
                # 检查权限代码是否已存在
                check_query = """
                SELECT COUNT(*) as count 
                FROM permission 
                WHERE permission_code = %s AND permission_id != %s
                """
                check_result = db_manager.execute_query(check_query, (perm_data['permission_code'], permission_id))
                
                if check_result and check_result[0]['count'] > 0:
                    QMessageBox.warning(self, "警告", "权限代码已存在，请使用其他权限代码")
                    return
                
                # 更新权限基本信息
                update_query = """
                UPDATE permission
                SET permission_name = %s, permission_code = %s, permission_type = %s,
                    parent_id = %s, menu_icon = %s, menu_path = %s, menu_component = %s,
                    menu_sort = %s, status = %s
                WHERE permission_id = %s
                """
                params = (
                    perm_data['permission_name'],
                    perm_data['permission_code'],
                    perm_data['permission_type'],
                    perm_data['parent_id'],
                    perm_data.get('menu_icon', None),
                    perm_data.get('menu_path', None),
                    perm_data.get('menu_component', None),
                    perm_data.get('menu_sort', 0),
                    perm_data['status'],
                    permission_id
                )
                
                db_manager.execute_update(update_query, params)
                
                QMessageBox.information(self, "成功", "权限信息更新成功")
                self.search_permissions()
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑权限失败：{str(e)}")
    
    def delete_permission(self):
        """删除权限"""
        selected_rows = self.permission_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要删除的权限")
            return
        
        row = selected_rows[0].row()
        permission_id = int(self.permission_table.item(row, 0).text())
        permission_name = self.permission_table.item(row, 1).text()
        
        # 确认删除
        reply = QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要删除权限{permission_name}吗？\n此操作不可恢复！\n\n注意：删除权限将同时删除与该权限关联的所有角色分配。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                from modules.db_manager import DatabaseManager
                db_manager = DatabaseManager()
                
                # 检查是否有子权限
                check_query = "SELECT COUNT(*) as count FROM permission WHERE parent_id = %s"
                check_result = db_manager.execute_query(check_query, (permission_id,))
                
                if check_result and check_result[0]['count'] > 0:
                    QMessageBox.warning(
                        self, 
                        "警告", 
                        f"该权限下有{check_result[0]['count']}个子权限，请先删除子权限"
                    )
                    return
                
                # 删除角色权限关联
                db_manager.execute_update(
                    "DELETE FROM role_permission WHERE permission_id = %s", 
                    (permission_id,)
                )
                
                # 删除系统Tab关联
                db_manager.execute_update(
                    "UPDATE system_tab SET permission_id = NULL WHERE permission_id = %s", 
                    (permission_id,)
                )
                
                # 删除权限
                db_manager.execute_update(
                    "DELETE FROM permission WHERE permission_id = %s", 
                    (permission_id,)
                )
                
                QMessageBox.information(self, "成功", "权限删除成功")
                self.search_permissions()
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除权限失败：{str(e)}")

class PermissionDialog(QDialog):
    def __init__(self, parent=None, permission=None):
        super().__init__(parent)
        self.permission = permission
        self.init_ui()
        self.load_parent_permissions()
        
    def init_ui(self):
        """初始化UI"""
        if self.permission:
            self.setWindowTitle("编辑权限")
        else:
            self.setWindowTitle("添加权限")
            
        self.setMinimumWidth(500)
        layout = QVBoxLayout(self)
        form_layout = QFormLayout()
        
        # 权限名称
        self.permission_name_input = QLineEdit()
        if self.permission:
            self.permission_name_input.setText(self.permission['permission_name'])
        form_layout.addRow("权限名称:", self.permission_name_input)
        
        # 权限代码
        self.permission_code_input = QLineEdit()
        if self.permission:
            self.permission_code_input.setText(self.permission['permission_code'])
        form_layout.addRow("权限代码:", self.permission_code_input)
        
        # 权限类型
        self.permission_type_input = QComboBox()
        self.permission_type_input.addItem("菜单", 1)
        self.permission_type_input.addItem("按钮", 2)
        self.permission_type_input.addItem("数据", 3)
        if self.permission:
            index = self.permission['permission_type'] - 1
            self.permission_type_input.setCurrentIndex(index if index >= 0 else 0)
        self.permission_type_input.currentIndexChanged.connect(self.on_type_changed)
        form_layout.addRow("权限类型:", self.permission_type_input)
        
        # 父权限
        self.parent_permission_input = QComboBox()
        self.parent_permission_input.addItem("无", 0)
        form_layout.addRow("父权限:", self.parent_permission_input)
        
        # 菜单图标（仅菜单类型）
        self.menu_icon_input = QLineEdit()
        if self.permission and self.permission['menu_icon']:
            self.menu_icon_input.setText(self.permission['menu_icon'])
        form_layout.addRow("菜单图标:", self.menu_icon_input)
        
        # 菜单路径（仅菜单类型）
        self.menu_path_input = QLineEdit()
        if self.permission and self.permission['menu_path']:
            self.menu_path_input.setText(self.permission['menu_path'])
        form_layout.addRow("菜单路径:", self.menu_path_input)
        
        # 菜单组件（仅菜单类型）
        self.menu_component_input = QLineEdit()
        if self.permission and self.permission['menu_component']:
            self.menu_component_input.setText(self.permission['menu_component'])
        form_layout.addRow("菜单组件:", self.menu_component_input)
        
        # 菜单排序
        self.menu_sort_input = QSpinBox()
        self.menu_sort_input.setRange(0, 9999)
        if self.permission and self.permission['menu_sort'] is not None:
            self.menu_sort_input.setValue(self.permission['menu_sort'])
        form_layout.addRow("菜单排序:", self.menu_sort_input)
        
        # 状态
        self.status_input = QComboBox()
        self.status_input.addItem("启用", 1)
        self.status_input.addItem("禁用", 0)
        if self.permission:
            index = 0 if self.permission['status'] == 1 else 1
            self.status_input.setCurrentIndex(index)
        form_layout.addRow("状态:", self.status_input)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        # 布局
        layout.addLayout(form_layout)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
        
        # 初始显示/隐藏控件
        self.on_type_changed(self.permission_type_input.currentIndex())
    
    def load_parent_permissions(self):
        """加载父权限列表"""
        try:
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            
            # 获取所有菜单类型权限作为父权限选项
            query = """
            SELECT permission_id, permission_name
            FROM permission
            WHERE permission_type = 1 AND status = 1
            ORDER BY parent_id, menu_sort, permission_id
            """
            permissions = db_manager.execute_query(query)
            
            # 添加到下拉框
            for perm in permissions:
                # 如果是编辑模式，排除自己及其子权限
                if self.permission and perm['permission_id'] == self.permission['permission_id']:
                    continue
                
                self.parent_permission_input.addItem(perm['permission_name'], perm['permission_id'])
            
            # 如果是编辑模式，设置当前父权限
            if self.permission and self.permission['parent_id'] > 0:
                for i in range(self.parent_permission_input.count()):
                    if self.parent_permission_input.itemData(i) == self.permission['parent_id']:
                        self.parent_permission_input.setCurrentIndex(i)
                        break
            
        except Exception as e:
            print(f"加载父权限列表失败: {str(e)}")
    
    def on_type_changed(self, index):
        """权限类型变更时显示/隐藏相关字段"""
        is_menu = self.permission_type_input.currentData() == 1
        
        # 只有菜单类型才显示菜单相关字段
        self.menu_icon_input.setEnabled(is_menu)
        self.menu_path_input.setEnabled(is_menu)
        self.menu_component_input.setEnabled(is_menu)
        self.menu_sort_input.setEnabled(is_menu)
    
    def accept(self):
        """验证并接受对话框"""
        if not self.validate():
            return
        super().accept()
    
    def validate(self):
        """验证表单"""
        permission_name = self.permission_name_input.text().strip()
        if not permission_name:
            QMessageBox.warning(self, "警告", "权限名称不能为空")
            return False
        
        permission_code = self.permission_code_input.text().strip()
        if not permission_code:
            QMessageBox.warning(self, "警告", "权限代码不能为空")
            return False
        
        return True
    
    def get_permission_data(self):
        """获取权限数据"""
        permission_type = self.permission_type_input.currentData()
        is_menu = permission_type == 1
        
        data = {
            'permission_name': self.permission_name_input.text().strip(),
            'permission_code': self.permission_code_input.text().strip(),
            'permission_type': permission_type,
            'parent_id': self.parent_permission_input.currentData(),
            'status': self.status_input.currentData()
        }
        
        # 如果是菜单类型，添加菜单相关信息
        if is_menu:
            data['menu_icon'] = self.menu_icon_input.text().strip() or None
            data['menu_path'] = self.menu_path_input.text().strip() or None
            data['menu_component'] = self.menu_component_input.text().strip() or None
            data['menu_sort'] = self.menu_sort_input.value()
        
        return data