"""
设置测试用户信息
用于开发测试时临时设置用户权限
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.config import set_current_user


def setup_test_user():
    """设置测试用户"""
    print("🔧 设置测试用户信息")
    print("=" * 30)
    
    # 创建测试用户信息
    test_user = {
        'user_id': 1,
        'username': 'test_admin',
        'real_name': '测试管理员',
        'department': '通路一',
        'departments': ['通路一', '通路二', '生产部', '采购部'],
        'permissions': [
            # 库存管理权限
            'inventory:view',
            'inventory:manage', 
            'inventory:initial',
            'inventory:transfer',
            'inventory:count',
            'inventory:report',
            'inventory:export',
            
            # 系统权限
            'system:admin',
            
            # 其他权限
            'material:basic',
            'return:delivery',
            'return:handover',
            'return:order'
        ],
        'role': 'admin',
        'is_active': True
    }
    
    # 设置当前用户
    set_current_user(test_user)
    
    print("✅ 测试用户设置成功")
    print(f"用户名: {test_user['username']}")
    print(f"真实姓名: {test_user['real_name']}")
    print(f"部门: {test_user['department']}")
    print(f"权限数量: {len(test_user['permissions'])}")
    print()
    print("📋 用户权限列表:")
    for perm in test_user['permissions']:
        print(f"  ✓ {perm}")
    print()
    print("🎉 现在可以正常使用库存管理功能了！")


def setup_limited_user():
    """设置受限用户"""
    print("🔧 设置受限测试用户信息")
    print("=" * 35)
    
    # 创建受限用户信息
    limited_user = {
        'user_id': 2,
        'username': 'test_user',
        'real_name': '测试用户',
        'department': '通路一',
        'departments': ['通路一'],
        'permissions': [
            # 只有基本的库存查看权限
            'inventory:view',
            'inventory:manage'
        ],
        'role': 'user',
        'is_active': True
    }
    
    # 设置当前用户
    set_current_user(limited_user)
    
    print("✅ 受限测试用户设置成功")
    print(f"用户名: {limited_user['username']}")
    print(f"真实姓名: {limited_user['real_name']}")
    print(f"部门: {limited_user['department']}")
    print(f"权限数量: {len(limited_user['permissions'])}")
    print()
    print("📋 用户权限列表:")
    for perm in limited_user['permissions']:
        print(f"  ✓ {perm}")
    print()
    print("⚠️  此用户权限受限，部分功能按钮将被禁用")


def clear_user():
    """清除用户信息"""
    print("🧹 清除用户信息")
    print("=" * 20)
    
    set_current_user(None)
    print("✅ 用户信息已清除")
    print("⚠️  库存管理功能将显示权限警告")


def main():
    """主函数"""
    print("🚀 库存管理模块用户设置工具")
    print("=" * 40)
    print()
    print("请选择操作：")
    print("1. 设置管理员用户（拥有所有权限）")
    print("2. 设置普通用户（权限受限）")
    print("3. 清除用户信息")
    print("4. 退出")
    print()
    
    while True:
        try:
            choice = input("请输入选择 (1-4): ").strip()
            
            if choice == '1':
                setup_test_user()
                break
            elif choice == '2':
                setup_limited_user()
                break
            elif choice == '3':
                clear_user()
                break
            elif choice == '4':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 操作失败: {str(e)}")


if __name__ == "__main__":
    # 直接设置管理员用户，方便测试
    setup_test_user()
    # main()
