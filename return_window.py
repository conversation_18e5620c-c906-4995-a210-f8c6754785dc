import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning) 
import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QTreeWidget, QTreeWidgetItem, QTabWidget,
                           QSplitter, QTabBar, QPushButton, QMessageBox, QSplashScreen,
                           QProgressBar, QLabel, QDialog, QMenu)
from PyQt6.QtCore import Qt, QTimer
from modules.return_module import ReturnModule
from modules.return_db import ReturnDB
from report.report_module import ReportModule
from home_module import HomeModule
from PyQt6.QtGui import QPainter, QColor, QPen, QIcon, QPixmap
from PyQt6.QtCore import QSize
from PyQt6.QtWidgets import QStyle, QStyleOption
from core.async_manager import AsyncManager
import os
from core.logger import Logger
from login_window import LoginWindow

class LoadingScreen(QWidget):
    def __init__(self):
        super().__init__()
        self.setFixedSize(400, 200)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        
        layout = QVBoxLayout()
        
        # 添加标题
        self.title = QLabel("系统初始化")
        self.title.setStyleSheet("""
            QLabel {
                color: #333333;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        self.title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.title)
        
        # 添加状态文本
        self.status_label = QLabel("正在初始化...")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 12px;
                margin-bottom: 10px;
            }
        """)
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
        
        # 添加进度条
        self.progress = QProgressBar()
        self.progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #CCCCCC;
                border-radius: 5px;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.progress)
        
        # 设置窗口样式
        self.setStyleSheet("""
            LoadingScreen {
                background-color: white;
                border: 1px solid #CCCCCC;
                border-radius: 10px;
            }
        """)
        
        self.setLayout(layout)
        
        # 设置窗口居中
        self.center()
    
    def center(self):
        screen = QApplication.primaryScreen().geometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def set_status(self, status, progress):
        self.status_label.setText(status)
        self.progress.setValue(progress)

class ReturnManageWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("退货管理系统")
        self.resize(1400, 800)
        
        print("开始初始化主窗口...")
        
        # 设置窗口图标
        icon_path = os.path.join(os.path.dirname(__file__), 'resources', 'icons', 'app.ico')
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
            # 设置任务栏图标
            import ctypes
            myappid = 'mycompany.myproduct.subproduct.version'
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
        else:
            Logger.log_init_error("图标文件不存在", extra={'icon_path': icon_path})
        
        # 当前用户信息和权限
        self.current_user = None
        self.user_permissions = []
        
        try:
            # 加载样式表
            self.setup_style()
            
            # 创建加载窗口
            print("创建加载窗口...")
            self.loading_screen = LoadingScreen()
            self.loading_screen.show()
            
            # 初始化系统
            self.loading_screen.set_status("正在初始化系统...", 0)
            QApplication.processEvents()  # 确保UI更新
            
            # 初始化数据库连接
            self.loading_screen.set_status("正在连接数据库...", 25)
            QApplication.processEvents()
            self.return_db = ReturnDB()
            
            # 初始化异步管理器
            self.loading_screen.set_status("正在初始化异步管理器...", 50)
            QApplication.processEvents()
            self.async_manager = AsyncManager()
            
            # 初始化UI
            self.loading_screen.set_status("正在初始化界面...", 75)
            QApplication.processEvents()
            
            # 显示登录窗口
            self.loading_screen.set_status("准备登录...", 90)
            QApplication.processEvents()
            
            # 关闭加载窗口
            print("关闭加载窗口...")
            self.loading_screen.close()
            QApplication.processEvents()  # 确保UI更新
            
            # 显示登录窗口
            print("显示登录窗口...")
            login_result = self.show_login_dialog()
            if not login_result:
                # 登录取消，关闭应用
                print("登录取消，关闭应用...")
                self.close()
                QApplication.quit()
                sys.exit(0)
            
            # 初始化UI
            print("登录成功，初始化UI...")
            self.initialize_ui()
            
            # 完成初始化
            # 使用定时器延迟关闭加载窗口
            QTimer.singleShot(1000, self.finish_loading)
            
        except Exception as e:
            # 记录错误日志
            print(f"初始化失败: {str(e)}")
            Logger.log_init_error("系统初始化失败", error=e, extra={
                'error_location': 'ReturnManageWindow.__init__',
                'error_stage': self.loading_screen.status_label.text() if hasattr(self, 'loading_screen') else '未知阶段',
                'initialization_step': 'main_window_init'
            })
            
            # 关闭加载窗口（如果存在）
            if hasattr(self, 'loading_screen'):
                self.loading_screen.close()
            
            # 关闭主窗口
            self.close()
            
            # 终止应用程序
            QApplication.quit()
            sys.exit(1)
    
    def show_login_dialog(self):
        """显示登录对话框"""
        print("创建登录对话框...")
        
        # 循环显示登录对话框，直到登录成功或用户取消
        while True:
            login_dialog = LoginWindow(self)
            
            # 显示登录对话框
            print("执行登录对话框...")
            result = login_dialog.exec()
            
            if result == QDialog.DialogCode.Accepted:
                # 检查登录结果
                print("对话框被接受，检查登录结果...")
                if hasattr(login_dialog, 'user') and login_dialog.user:
                    self.current_user = login_dialog.user
                    self.user_permissions = login_dialog.permissions
                    
                    # 设置数据库当前用户
                    self.return_db.current_user = login_dialog.user['real_name']
                    
                    print(f"登录成功：{login_dialog.user['username']}, 权限：{login_dialog.permissions}")
                    
                    # 提示登录成功
                    QMessageBox.information(
                        self, 
                        "登录成功", 
                        f"欢迎回来，{login_dialog.user['real_name']}！"
                    )
                    return True
                else:
                    # 登录失败，但对话框被接受了，说明有其他问题
                    print("登录数据异常，重新显示登录窗口")
                    continue
            else:
                # 用户取消或关闭登录窗口
                print("用户取消登录")
                return False
    
    def finish_loading(self):
        """完成加载，显示主窗口"""
        # 显示主窗口
        self.show()
        # 延迟加载初始数据
        QTimer.singleShot(100, self.load_initial_data)
    
    def initialize_ui(self):
        """初始化UI"""
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建水平布局
        layout = QHBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 创建并设置树形导航
        self.setup_tree_widget()
        
        # 创建设置标签页
        self.setup_tab_widget()
        
        # 添加到分割器
        splitter.addWidget(self.tree)
        splitter.addWidget(self.tab_widget)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 0)
        splitter.setStretchFactor(1, 1)
        
        # 将分割器添加到布局中
        layout.addWidget(splitter)
        
        # 设置窗口标题添加当前用户
        if self.current_user:
            self.setWindowTitle(f"退货管理系统 - {self.current_user['real_name']}")
    
    def setup_tree_widget(self):
        """设置树形导航"""
        self.tree = QTreeWidget()
        self.tree.setHeaderHidden(True)
        self.tree.setMinimumWidth(200)
        self.tree.setMaximumWidth(300)
        
        # 创建树节点
        self.home_item = QTreeWidgetItem(self.tree, ["首页"])
        self.home_item.setIcon(0, QIcon("resources/icons/home.ico"))
        
        # 根据权限添加菜单
        if self.has_permission('return:manage'):
            self.return_item = QTreeWidgetItem(self.tree, ["退货管理"])
            self.return_item.setIcon(0, QIcon("resources/icons/return.png]"))
            
            # 子节点
            if self.has_permission('return:delivery'):
                self.delivery_item = QTreeWidgetItem(self.return_item, ["出库单列表"])
                # self.delivery_item.setIcon(0, QIcon("resources/icons/delivery.svg"))
            
            if self.has_permission('return:handover'):
                self.handover_item = QTreeWidgetItem(self.return_item, ["交接单列表"])
                # self.handover_item.setIcon(0, QIcon("resources/icons/handover.png"))
            
            if self.has_permission('return:order'):
                self.return_warehouse_item = QTreeWidgetItem(self.return_item, ["退货入库列表"])
                # self.return_warehouse_item.setIcon(0, QIcon("resources/icons/warehouse.png"))
            
            if self.has_permission('return:log'):
                self.log_item = QTreeWidgetItem(self.return_item, ["操作日志"])
                # self.log_item.setIcon(0, QIcon("resources/icons/log.png"))
        
        if self.has_permission('report:manage'):
            self.report_item = QTreeWidgetItem(self.tree, ["报表中心"])
            self.report_item.setIcon(0, QIcon("resources/icons/report.png"))
            
            # 子节点
            if self.has_permission('report:return'):
                self.kpi_report_item = QTreeWidgetItem(self.report_item, ["物流KPI统计"])
                # self.kpi_report_item.setIcon(0, QIcon("resources/icons/statistics.png"))
        
        # 物料管理模块
        if self.has_permission('material:manage'):
            self.material_item = QTreeWidgetItem(self.tree, ["物料管理"])
            self.material_item.setIcon(0, QIcon("resources/icons/material.png"))
            
            # 子节点
            if self.has_permission('material:basic'):
                self.material_basic_item = QTreeWidgetItem(self.material_item, ["物料基础"])
                self.material_basic_item.setIcon(0, QIcon("resources/icons/basic.png"))
                
            # 添加东方货品菜单
            if self.has_permission('material:cj_goods'):
                self.cj_goods_item = QTreeWidgetItem(self.material_item, ["东方货品"])
                self.cj_goods_item.setIcon(0, QIcon("resources/icons/goods.png"))
            
            # 添加BOM管理菜单
            if self.has_permission('material:bom'):
                self.bom_item = QTreeWidgetItem(self.material_item, ["BOM管理"])
                self.bom_item.setIcon(0, QIcon("resources/icons/bom.png"))
        
        # 系统管理（仅管理员可见）
        if self.has_permission('system:manage'):
            self.system_item = QTreeWidgetItem(self.tree, ["系统管理"])
            self.system_item.setIcon(0, QIcon("resources/icons/system.png"))
            
            # 子节点
            if self.has_permission('system:user'):
                self.user_item = QTreeWidgetItem(self.system_item, ["用户管理"])
                # self.user_item.setIcon(0, QIcon("resources/icons/user.png"))
            
            if self.has_permission('system:role'):
                self.role_item = QTreeWidgetItem(self.system_item, ["角色管理"])
                # self.role_item.setIcon(0, QIcon("resources/icons/role.png"))
            
            if self.has_permission('system:permission'):
                self.permission_item = QTreeWidgetItem(self.system_item, ["权限管理"])
                # self.permission_item.setIcon(0, QIcon("resources/icons/permission.png"))
        
            if self.has_permission('system:auto_number'):
                self.auto_number_item = QTreeWidgetItem(self.system_item, ["自动编号管理"])
                # self.auto_number_item.setIcon(0, QIcon("resources/icons/auto_number.png"))
        #东方退货管理
        if self.has_permission('dongfang:return'):
            self.cj_return_item = QTreeWidgetItem(self.tree, ["东方退货管理"])
            self.cj_return_item.setIcon(0, QIcon("resources/icons/cj_return.png"))
            
            # 子节点
            if self.has_permission('dongfang:return:apply'):
                self.dongfang_return_apply_item = QTreeWidgetItem(self.cj_return_item, ["东方退货申请"])
                self.dongfang_return_apply_item.setIcon(0, QIcon("resources/icons/return_apply.png"))
            #子节点入库收货单
            if self.has_permission('dongfang:warehouse:receipt'):
                self.warehouse_receipt_item = QTreeWidgetItem(self.cj_return_item, ["入库收货单"])
                self.warehouse_receipt_item.setIcon(0, QIcon("resources/icons/warehouse_receipt.png"))

        # 需求计划管理模块
        if self.has_permission('demand:manage'):
            self.demand_item = QTreeWidgetItem(self.tree, ["需求计划管理"])
            self.demand_item.setIcon(0, QIcon("resources/icons/demand.png"))
            
            # 子节点
            if self.has_permission('demand:plan'):
                self.demand_plan_item = QTreeWidgetItem(self.demand_item, ["需求计划"])
                self.demand_plan_item.setIcon(0, QIcon("resources/icons/plan.png"))

        # 展开所有节点
        self.tree.expandAll()
        
        # 连接点击事件
        self.tree.itemClicked.connect(self.on_tree_item_clicked)
    
    def has_permission(self, permission_code):
        """检查是否有权限"""
        # 超级管理员拥有所有权限
        if 'system:admin' in self.user_permissions:
            return True
        return permission_code in self.user_permissions
    
    def setup_tab_widget(self):
        """设置标签页"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabsClosable(True)
        self.tab_widget.tabCloseRequested.connect(self.close_tab)
        
        # 初始化home模块作为默认页
        self.home_module = HomeModule()
        self.tab_widget.addTab(self.home_module, "首页")
        self.tab_widget.tabBar().setTabButton(0, QTabBar.ButtonPosition.RightSide, None)
        
        # 标签页样式将使用全局样式表 styles.qss，这里不需要重复定义
        # 只设置首页标签页不可关闭
        
        # 记录当前打开的tab
        self.opened_tabs = {"首页": 0}
        
        # 添加标签页切换事件
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
        # 添加标签页右键菜单
        self.tab_widget.tabBar().setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.tab_widget.tabBar().customContextMenuRequested.connect(self.show_tab_context_menu)
    
    def show_tab_context_menu(self, pos):
        """显示标签页右键菜单"""
        # 获取点击位置的标签页索引
        tab_index = self.tab_widget.tabBar().tabAt(pos)
        if tab_index < 0:
            return
            
        # 创建右键菜单
        menu = QMenu(self)
        
        # 添加"关闭其他标签页"选项
        close_others_action = menu.addAction("关闭其他标签页")
        close_others_action.triggered.connect(lambda: self.close_other_tabs(tab_index))
        
        # 显示菜单
        menu.exec(self.tab_widget.tabBar().mapToGlobal(pos))
    
    def close_other_tabs(self, current_index):
        """关闭除当前标签页和首页外的其他标签页"""
        # 从后向前遍历，避免索引变化
        for i in range(self.tab_widget.count() - 1, -1, -1):
            if i != current_index and i != 0:  # 不关闭当前标签页和首页
                self.close_tab(i)
    
    def load_initial_data(self):
        """加载初始数据"""
        # 根据权限加载不同的初始数据，但不在这里加载具体标签页数据
        # 标签页数据应该在标签页被打开时再加载
        print("初始数据加载完成")

    def find_tab_by_title(self, title):
        """根据标题查找标签页"""
        for i in range(self.tab_widget.count()):
            if self.tab_widget.tabText(i) == title:
                return self.tab_widget.widget(i)
        return None

    def update_delivery_tab(self, data):
        """更新出库单列表数据"""
        if data:
            total, results = data
            # 查找出库单标签页
            delivery_tab = self.find_tab_by_title("出库单列表")
            if delivery_tab and hasattr(delivery_tab, 'update_table'):
                delivery_tab.update_table(total, results)
    
    def on_tab_changed(self, index):
        """处理标签页切换事件"""
        # 获取当前标签页的widget
        if index < 0 or index >= self.tab_widget.count():
            return
            
        current_widget = self.tab_widget.widget(index)
        current_tab_text = self.tab_widget.tabText(index)
        
        # 根据标签页类型加载数据
        if current_tab_text == "出库单管理" and self.has_permission('return:delivery'):
            # 直接调用加载数据，避免异步管理器的参数问题
            try:
                total, results = self.return_db.get_delivery_orders({'page': 1, 'page_size': 20})
                self.update_tab_data(current_widget, (total, results))
            except Exception as e:
                print(f"加载出库单数据失败: {str(e)}")
        elif current_tab_text == "交接单管理" and self.has_permission('return:handover'):
            # 加载交接单数据
            pass
        elif current_tab_text == "退货单管理" and self.has_permission('return:order'):
            # 加载退货单数据
            pass
        elif current_tab_text == "退货统计" and self.has_permission('report:return'):
            # 加载报表数据
            pass
        # 东方退货管理模块
        elif current_tab_text == "东方退货申请" and self.has_permission('dongfang:return:apply'):
            # 刷新东方退货申请数据
            if hasattr(current_widget, 'load_data'):
                current_widget.load_data()
        elif current_tab_text == "入库收货单" and self.has_permission('dongfang:warehouse:receipt'):
            # 刷新入库收货单数据
            if hasattr(current_widget, 'load_data'):
                current_widget.load_data()
    
    def update_tab_data(self, tab, data):
        """更新标签页数据的统一处理方法"""
        if data:
            if hasattr(tab, 'search_orders'):
                tab.search_orders()
            elif hasattr(tab, 'search_logs'):
                tab.search_logs()
            else:
                print("[DEBUG] 标签页没有search_orders或search_logs方法")
            
    def on_tree_item_clicked(self, item, column):
        """处理树形菜单点击事件"""
        # 获取点击的项目文本
        item_text = item.text(0)
        parent = item.parent()
        
        # 根据点击的菜单项和权限打开对应的标签页
        if item_text == "首页":
            self.tab_widget.setCurrentIndex(0)
            return
            
        # 退货管理模块
        if parent and parent.text(0) == "退货管理":
            if item_text == "出库单列表" and self.has_permission('return:delivery'):
                from modules.return_module import DeliveryOrderTab
                delivery_tab = DeliveryOrderTab(self.return_db)
                # 初始化数据
                delivery_tab.load_logistics_companies()
                delivery_tab.search_orders()
                self.open_tab(delivery_tab, "出库单列表")
            elif item_text == "交接单列表" and self.has_permission('return:handover'):
                from modules.return_module import HandoverOrderTab
                handover_tab = HandoverOrderTab(self.return_db)
                # 初始化数据
                handover_tab.load_logistics_companies()
                handover_tab.search_orders()
                self.open_tab(handover_tab, "交接单列表")
            elif item_text == "退货入库列表" and self.has_permission('return:order'):
                from modules.return_module import ReturnWarehouseTab
                return_warehouse_tab = ReturnWarehouseTab(self.return_db)
                # 初始化数据
                return_warehouse_tab.load_logistics_companies()
                return_warehouse_tab.search_orders()
                self.open_tab(return_warehouse_tab, "退货入库列表")
            elif item_text == "操作日志" and self.has_permission('return:log'):
                from modules.return_module import LogTab
                log_tab = LogTab(self.return_db)
                # 初始化数据
                log_tab.search_logs()
                self.open_tab(log_tab, "操作日志")
        
        # 报表中心模块
        elif parent and parent.text(0) == "报表中心":
            if item_text == "物流KPI统计" and self.has_permission('report:return'):
                self.open_tab(ReportModule(self.return_db), "物流KPI统计")
        
        # 物料管理模块
        elif parent and parent.text(0) == "物料管理":
            if item_text == "物料基础" and self.has_permission('material:basic'):
                try:
                    from material.material_module import MaterialBasicTab
                    material_tab = MaterialBasicTab(self.return_db)
                    self.open_tab(material_tab, "物料基础")
                except ImportError as e:
                    QMessageBox.warning(self, "提示", f"物料管理模块导入失败: {str(e)}")
            elif item_text == "东方货品" and self.has_permission('material:cj_goods'):
                from material.cj_goods_tab import CJGoodsTab
                cj_goods_tab = CJGoodsTab(self.return_db)
                self.open_tab(cj_goods_tab, "东方货品")
            elif item_text == "BOM管理" and self.has_permission('material:bom'):
                try:
                    from material.bom_module import BOMManagementTab
                    bom_tab = BOMManagementTab(self.return_db)
                    self.open_tab(bom_tab, "BOM管理")
                except ImportError as e:
                    QMessageBox.warning(self, "提示", f"BOM管理模块导入失败: {str(e)}")
        
        # 系统管理模块
        elif parent and parent.text(0) == "系统管理":
            if item_text == "用户管理" and self.has_permission('system:user'):
                self.open_tab(self.create_user_management_tab(), "用户管理")
            elif item_text == "角色管理" and self.has_permission('system:role'):
                self.open_tab(self.create_role_management_tab(), "角色管理")
            elif item_text == "权限管理" and self.has_permission('system:permission'):
                self.open_tab(self.create_permission_management_tab(), "权限管理")
            elif item_text == "自动编号管理" and self.has_permission('system:auto_number'):
                self.open_tab(self.create_auto_number_management_tab(), "自动编号管理")
        # 东方退货管理模块
        elif parent and parent.text(0) == "东方退货管理":
            if item_text == "东方退货申请" and self.has_permission('dongfang:return:apply'):
                from cj_return.dongfang_return_module import DongfangReturnTab
                dongfang_tab = DongfangReturnTab(self.return_db)
                self.open_tab(dongfang_tab, "东方退货申请")
            elif item_text == "入库收货单" and self.has_permission('dongfang:warehouse:receipt'):
                from cj_return.warehouse_receipt_module import WarehouseReceiptTab
                warehouse_receipt_tab = WarehouseReceiptTab(self.return_db)
                self.open_tab(warehouse_receipt_tab, "入库收货单")
        # 需求计划管理模块
        elif parent and parent.text(0) == "需求计划管理":
            if item_text == "需求计划" and self.has_permission('demand:plan'):
                try:
                    from demand.demand_plan_module import DemandPlanTab
                    demand_plan_tab = DemandPlanTab(self.return_db)
                    self.open_tab(demand_plan_tab, "需求计划")
                except ImportError as e:
                    QMessageBox.warning(self, "提示", f"需求计划模块导入失败: {str(e)}")
    
    def create_user_management_tab(self):
        """创建用户管理标签页"""
        # 这里需要实现用户管理界面
        from system.user_module import UserManagementModule
        return UserManagementModule(self.return_db)
    
    def create_role_management_tab(self):
        """创建角色管理标签页"""
        # 这里需要实现角色管理界面
        from system.role_module import RoleManagementModule
        return RoleManagementModule(self.return_db)
    
    def create_permission_management_tab(self):
        """创建权限管理标签页"""
        # 这里需要实现权限管理界面
        from system.permission_module import PermissionManagementModule
        return PermissionManagementModule(self.return_db)
    
    def create_auto_number_management_tab(self):
        """创建自动编号管理标签页"""
        from system.auto_number_module import AutoNumberModule
        return AutoNumberModule(self.return_db)
    
    def open_tab(self, tab_widget, tab_text):
        """打开标签页"""
        # 检查tab是否已经打开且索引有效
        if tab_text in self.opened_tabs:
            cached_index = self.opened_tabs[tab_text]
            # 验证索引是否有效且标签页文本匹配
            if (cached_index < self.tab_widget.count() and 
                self.tab_widget.tabText(cached_index) == tab_text):
                self.tab_widget.setCurrentIndex(cached_index)
                return
            else:
                # 缓存索引无效，清除该缓存
                del self.opened_tabs[tab_text]
            
        # 检查是否有权限打开该Tab
        required_permission = self.get_tab_permission(tab_text)
        if required_permission and not self.has_permission(required_permission):
            QMessageBox.warning(self, "权限不足", f"您没有权限访问{tab_text}功能")
            return
        
        # 添加新标签页
        index = self.tab_widget.addTab(tab_widget, tab_text)
        self.tab_widget.setCurrentIndex(index)
        self.opened_tabs[tab_text] = index
    
    def get_tab_permission(self, tab_text):
        """获取标签页对应的权限代码"""
        # 映射标签页名称到权限代码
        tab_permission_map = {
            "出库单列表": "return:delivery",
            "交接单列表": "return:handover", 
            "退货入库列表": "return:order",
            "操作日志": "return:log",
            "物流KPI统计": "report:return",
            "物料基础": "material:basic",
            "东方货品": "material:cj_goods",
            "用户管理": "system:user",
            "角色管理": "system:role",
            "权限管理": "system:permission",
            "自动编号管理": "system:auto_number",
            "东方退货申请": "dongfang:return:apply",
            "入库收货单": "dongfang:warehouse:receipt",
            "BOM管理": "material:bom",
            "需求计划": "demand:plan"   
        }
        return tab_permission_map.get(tab_text)

    def close_tab(self, index):
        """关闭标签页"""
        tab_text = self.tab_widget.tabText(index)
        if tab_text != "🏠 首页":  # 不允许关闭首页标签页
            # 从缓存中移除该标签页
            if tab_text in self.opened_tabs:
                del self.opened_tabs[tab_text]
            
            # 移除标签页
            self.tab_widget.removeTab(index)
            
            # 重新更新所有标签页的索引
            self.update_tab_indexes()
    
    def update_tab_indexes(self):
        """更新所有标签页的索引缓存"""
        # 清空当前缓存
        self.opened_tabs.clear()
        
        # 重新构建索引缓存
        for i in range(self.tab_widget.count()):
            tab_text = self.tab_widget.tabText(i)
            self.opened_tabs[tab_text] = i
    
    def setup_style(self):
        """设置样式表"""
        try:
            # 读取样式表文件
            style_file = os.path.join(os.path.dirname(__file__), 'styles.qss')
            if not os.path.exists(style_file):
                style_file = 'styles.qss'
                
            with open(style_file, 'r', encoding='utf-8') as f:
                style_sheet = f.read()
                
            # 应用样式到应用程序实例
            QApplication.instance().setStyleSheet(style_sheet)
            
        except Exception as e:
            print(f"加载样式表失败: {str(e)}")
            QMessageBox.warning(self, "警告", "样式表加载失败，将使用默认样式。")
    
    def get_db_function(self, tab_widget):
        """获取对应的数据库查询函数"""
        # 通过类名判断标签页类型
        class_name = type(tab_widget).__name__
        
        if class_name == 'DeliveryOrderTab':
            return self.return_db.get_delivery_orders
        elif class_name == 'HandoverOrderTab':
            return self.return_db.get_handover_orders
        elif class_name == 'ReturnWarehouseTab':
            return self.return_db.get_return_warehouse_orders
        elif class_name == 'LogTab':
            return self.return_db.get_operation_logs
        elif class_name == 'MaterialBasicTab':
            # 直接使用 material_basic_tab 实例中的 material_db
            return tab_widget.material_db.get_materials if hasattr(tab_widget, 'material_db') else None
        elif class_name == 'CJGoodsTab':
            return tab_widget.cj_goods_db.get_cj_goods
        else:
            print(f"警告：未知的标签页类型: {class_name}")
            return None


if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # 设置高DPI支持
    import os
    os.environ["QT_AUTO_SCREEN_SCALE_FACTOR"] = "1"
    os.environ["QT_SCALE_FACTOR"] = "1"
    
    # 启用 Qt 的缓存机制
    app.setAttribute(Qt.ApplicationAttribute.AA_DontCreateNativeWidgetSiblings)
    
    # 设置应用序样式
    app.setStyle('Fusion')
    
    # 创建主窗口（不立即显示）
    window = ReturnManageWindow()
    
    # 启动应用程序
    sys.exit(app.exec()) 














