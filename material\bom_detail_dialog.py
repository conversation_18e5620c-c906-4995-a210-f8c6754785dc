"""
BOM明细添加/编辑对话框
用于添加和编辑BOM明细信息
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QComboBox, QFormLayout, QDialogButtonBox, QDoubleSpinBox,
                           QSpinBox, QTextEdit, QMessageBox, QPushButton, QFrame,
                           QGroupBox)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon
from material.material_db import MaterialDB

class BOMDetailDialog(QDialog):
    """BOM明细添加/编辑对话框"""
    
    def __init__(self, parent=None, detail_data=None):
        super().__init__(parent)
        self.detail_data = detail_data
        self.material_db = MaterialDB()
        self.selected_material = None
        
        self.setWindowTitle("BOM明细信息" if not detail_data else "编辑BOM明细")
        self.setModal(True)
        self.resize(500, 400)
        self.setup_ui()
        
        # 如果是编辑模式，填充数据
        if detail_data:
            self.fill_form_data()
    
    def setup_ui(self):
        """设置UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 物料信息组
        material_group = QGroupBox("物料信息")
        material_form = QFormLayout(material_group)
        material_form.setSpacing(10)
        
        # 物料选择
        material_layout = QHBoxLayout()
        self.material_id_edit = QLineEdit()
        self.material_id_edit.setPlaceholderText("请选择物料")
        self.material_id_edit.setReadOnly(True)
        material_layout.addWidget(self.material_id_edit)
        
        self.select_material_btn = QPushButton("选择物料")
        self.select_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.select_material_btn.clicked.connect(self.select_material)
        material_layout.addWidget(self.select_material_btn)
        material_form.addRow("物料编码:", material_layout)
        
        # 物料名称（显示用）
        self.material_name_label = QLabel()
        self.material_name_label.setStyleSheet("color: #666; font-style: italic;")
        material_form.addRow("物料名称:", self.material_name_label)
        
        # 物料分类（显示用）
        self.material_category_label = QLabel()
        self.material_category_label.setStyleSheet("color: #666; font-style: italic;")
        material_form.addRow("物料分类:", self.material_category_label)
        
        main_layout.addWidget(material_group)
        
        # 用量信息组
        quantity_group = QGroupBox("用量信息")
        quantity_form = QFormLayout(quantity_group)
        quantity_form.setSpacing(10)
        
        # 序号
        self.sequence_edit = QSpinBox()
        self.sequence_edit.setRange(1, 9999)
        self.sequence_edit.setValue(1)
        quantity_form.addRow("序号:", self.sequence_edit)
        
        # 需求数量
        self.required_qty_edit = QDoubleSpinBox()
        self.required_qty_edit.setRange(0.0001, 999999.9999)
        self.required_qty_edit.setDecimals(4)
        self.required_qty_edit.setValue(1.0000)
        quantity_form.addRow("需求数量:", self.required_qty_edit)
        
        # 需求单位
        self.required_unit_combo = QComboBox()
        self.required_unit_combo.addItems(["个", "提", "箱", "包", "套", "瓶","PICs","组"])
        self.required_unit_combo.setEditable(True)
        quantity_form.addRow("需求单位:", self.required_unit_combo)
        
        # 原箱规格数量
        self.original_box_qty_edit = QDoubleSpinBox()
        self.original_box_qty_edit.setRange(0, 999999.9999)
        self.original_box_qty_edit.setDecimals(4)
        quantity_form.addRow("原箱规格数量:", self.original_box_qty_edit)
        
        # 原箱单位
        self.original_box_unit_combo = QComboBox()
        self.original_box_unit_combo.addItems(["个", "提", "箱", "包", "套", "瓶","PICs","组"])
        self.original_box_unit_combo.setEditable(True)
        quantity_form.addRow("原箱单位:", self.original_box_unit_combo)
        
        # 损耗率
        self.scrap_rate_edit = QDoubleSpinBox()
        self.scrap_rate_edit.setRange(0, 100)
        self.scrap_rate_edit.setDecimals(2)
        self.scrap_rate_edit.setSuffix("%")
        quantity_form.addRow("损耗率:", self.scrap_rate_edit)
        
        main_layout.addWidget(quantity_group)
        
        # 备注信息
        remark_group = QGroupBox("备注信息")
        remark_layout = QVBoxLayout(remark_group)
        self.remark_edit = QTextEdit()
        self.remark_edit.setMaximumHeight(60)
        self.remark_edit.setPlaceholderText("请输入备注信息...")
        remark_layout.addWidget(self.remark_edit)
        main_layout.addWidget(remark_group)
        
        # 按钮区域
        self.create_button_area(main_layout)
    
    def create_button_area(self, parent_layout):
        """创建按钮区域"""
        button_frame = QFrame()
        button_frame.setStyleSheet("QFrame { background-color: #f5f5f5; }")
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(20, 10, 20, 10)
        
        button_layout.addStretch()
        
        # 确定按钮
        ok_btn = QPushButton("✅ 确定")
        ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
        """)
        ok_btn.clicked.connect(self.accept_detail)
        button_layout.addWidget(ok_btn)
        
        # 取消按钮
        cancel_btn = QPushButton("❌ 取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #d9d9d9;
                color: #333;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #bfbfbf;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        parent_layout.addWidget(button_frame)
    
    def select_material(self):
        """选择物料"""
        try:
            from material.bom_material_search_dialog import BOMaterialSearchDialog
            
            # 获取父对话框的成品物料ID和已选择的明细物料
            parent_dialog = self.parent()
            exclude_material_id = None
            selected_materials = []
            
            if hasattr(parent_dialog, 'parent_material_edit'):
                exclude_material_id = parent_dialog.parent_material_edit.text().strip()
            
            if hasattr(parent_dialog, 'detail_data'):
                selected_materials = [detail.get('child_material_id') for detail in parent_dialog.detail_data]
            
            dialog = BOMaterialSearchDialog(
                self,
                exclude_material_id=exclude_material_id,
                selected_materials=selected_materials
            )
            
            dialog.material_selected.connect(self.on_material_selected)
            dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"选择物料失败: {str(e)}")
    
    def on_material_selected(self, material):
        """处理选中的物料"""
        if material:
            self.selected_material = material
            self.material_id_edit.setText(material.get('material_id', ''))
            self.material_name_label.setText(material.get('name', ''))
            
            # 获取物料分类信息
            category_name = material.get('category_name', '')
            self.material_category_label.setText(category_name)
            
            # 自动填充单位信息
            base_unit = material.get('base_unit', '')
            if base_unit:
                # 确保转换为字符串
                base_unit_str = str(base_unit)
                # 设置需求单位
                index = self.required_unit_combo.findText(base_unit_str)
                if index >= 0:
                    self.required_unit_combo.setCurrentIndex(index)
                else:
                    self.required_unit_combo.setCurrentText(base_unit_str)
                
                # 设置原箱单位
                index = self.original_box_unit_combo.findText(base_unit_str)
                if index >= 0:
                    self.original_box_unit_combo.setCurrentIndex(index)
                else:
                    self.original_box_unit_combo.setCurrentText(base_unit_str)
    
    def fill_form_data(self):
        """填充表单数据（编辑模式）"""
        if not self.detail_data:
            return
        
        try:
            # 物料信息
            material_id = self.detail_data.get('child_material_id', '')
            self.material_id_edit.setText(material_id)
            self.material_name_label.setText(self.detail_data.get('material_name', ''))
            
            # 获取物料详细信息
            if material_id:
                material_info = self.material_db.get_material_by_id(material_id)
                if material_info:
                    self.selected_material = material_info
                    category_name = material_info.get('category_name', '')
                    self.material_category_label.setText(category_name)
            
            # 用量信息
            self.sequence_edit.setValue(int(self.detail_data.get('sequence_no', 1)))
            self.required_qty_edit.setValue(float(self.detail_data.get('required_qty', 1.0)))
            
            # 需求单位
            required_unit = self.detail_data.get('required_unit', '')
            if required_unit:
                # 确保转换为字符串
                required_unit_str = str(required_unit)
                index = self.required_unit_combo.findText(required_unit_str)
                if index >= 0:
                    self.required_unit_combo.setCurrentIndex(index)
                else:
                    self.required_unit_combo.setCurrentText(required_unit_str)
            
            # 原箱规格
            original_box_qty = self.detail_data.get('original_box_qty', 0)
            if original_box_qty:
                self.original_box_qty_edit.setValue(float(original_box_qty))
            
            # 原箱单位
            original_box_unit = self.detail_data.get('original_box_unit', '')
            if original_box_unit:
                # 确保转换为字符串
                original_box_unit_str = str(original_box_unit)
                index = self.original_box_unit_combo.findText(original_box_unit_str)
                if index >= 0:
                    self.original_box_unit_combo.setCurrentIndex(index)
                else:
                    self.original_box_unit_combo.setCurrentText(original_box_unit_str)
            
            # 损耗率
            scrap_rate = self.detail_data.get('scrap_rate', 0)
            if scrap_rate:
                self.scrap_rate_edit.setValue(float(scrap_rate))
            
            # 备注
            remark = self.detail_data.get('remark', '')
            self.remark_edit.setPlainText(remark)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"填充表单数据失败: {str(e)}")
    
    def validate_data(self):
        """验证数据"""
        if not self.material_id_edit.text().strip():
            QMessageBox.warning(self, "警告", "请选择物料")
            self.select_material_btn.setFocus()
            return False
        
        if self.required_qty_edit.value() <= 0:
            QMessageBox.warning(self, "警告", "需求数量必须大于0")
            self.required_qty_edit.setFocus()
            return False
        
        if not self.required_unit_combo.currentText().strip():
            QMessageBox.warning(self, "警告", "请输入需求单位")
            self.required_unit_combo.setFocus()
            return False
        
        if self.sequence_edit.value() <= 0:
            QMessageBox.warning(self, "警告", "序号必须大于0")
            self.sequence_edit.setFocus()
            return False
        
        return True
    
    def get_detail_data(self):
        """获取明细数据"""
        return {
            'sequence_no': self.sequence_edit.value(),
            'child_material_id': self.material_id_edit.text().strip(),
            'material_name': self.material_name_label.text(),
            'material_category_id': self.selected_material.get('category_id') if self.selected_material else None,
            'required_qty': self.required_qty_edit.value(),
            'required_unit': self.required_unit_combo.currentText().strip(),
            'original_box_qty': self.original_box_qty_edit.value() if self.original_box_qty_edit.value() > 0 else None,
            'original_box_unit': self.original_box_unit_combo.currentText().strip() if self.original_box_qty_edit.value() > 0 else None,
            'scrap_rate': self.scrap_rate_edit.value(),
            'remark': self.remark_edit.toPlainText().strip()
        }
    
    def accept_detail(self):
        """确认明细"""
        if not self.validate_data():
            return
        
        self.accept()


