"""
日志管理模块 - 统一管理系统日志记录（仅记录错误信息到文件）
"""
import logging
from logging.handlers import RotatingFileHandler
import os
from datetime import datetime
from .config import Config
import re
import hashlib

class Logger:
    _loggers = {}
    _console_logger = None
    
    # 定义敏感信息的正则表达式模式
    SENSITIVE_PATTERNS = {
        'password': r'password["\s]*[:=]["\s]*([^,"}\s]+)',
        'ip': r'\b(?:\d{1,3}\.){3}\d{1,3}\b',
        'database': r'(host|database|user)["\s]*[:=]["\s]*([^,"}\s]+)',
        'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
        'phone': r'\b1[3-9]\d{9}\b',
        'id_card': r'\b[1-9]\d{5}(?:19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}(?:\d|X|x)\b'
    }

    @classmethod
    def _get_console_logger(cls):
        """获取控制台日志记录器"""
        if cls._console_logger is None:
            logger = logging.getLogger('console')
            logger.setLevel(logging.INFO)
            
            # 只添加控制台处理器
            console_handler = logging.StreamHandler()
            formatter = logging.Formatter('%(message)s')
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
            
            cls._console_logger = logger
        return cls._console_logger

    @classmethod
    def log_db_connection(cls, message, level='info', extra=None):
        """记录数据库连接日志（仅控制台输出）"""
        logger = cls._get_console_logger()
        log_func = getattr(logger, level.lower())
        
        # 如果是数据库连接成功的消息，只显示简单提示
        if "数据库连接初始化成功" in message:
            log_func("数据库连接初始化成功")
        else:
            log_func(message)

    @classmethod
    def log_db_operation(cls, message, level='info', extra=None):
        """记录数据库操作日志（仅控制台输出）"""
        logger = cls._get_console_logger()
        log_func = getattr(logger, level.lower())
        log_func(message)

    @classmethod
    def log_async_operation(cls, message, level='info', extra=None):
        """记录异步操作日志（仅控制台输出）"""
        logger = cls._get_console_logger()
        log_func = getattr(logger, level.lower())
        log_func(message)
    
    @classmethod
    def mask_sensitive_info(cls, message):
        """对敏感信息进行脱敏处理"""
        if not isinstance(message, str):
            return message
            
        # 对不同类型的敏感信息进行处理
        for info_type, pattern in cls.SENSITIVE_PATTERNS.items():
            if info_type == 'password':
                message = re.sub(pattern, r'password=****', message, flags=re.IGNORECASE)
            elif info_type == 'ip':
                message = re.sub(pattern, lambda m: '.'.join(m.group(0).split('.')[:2] + ['***', '***']), message)
            elif info_type == 'database':
                message = re.sub(pattern, lambda m: f"{m.group(1)}=***", message)
            elif info_type in ['email', 'phone', 'id_card']:
                def mask_middle(match):
                    text = match.group(0)
                    if info_type == 'email':
                        at_index = text.find('@')
                        return text[:2] + '*' * (at_index - 3) + text[at_index-1:]
                    elif info_type == 'phone':
                        return text[:3] + '*' * 4 + text[-4:]
                    else:  # id_card
                        return text[:6] + '*' * 8 + text[-4:]
                message = re.sub(pattern, mask_middle, message)
        
        return message

    @classmethod
    def _format_message_with_extra(cls, message, extra):
        """格式化带有额外信息的日志消息，并处理敏感信息"""
        masked_extra = {}
        for k, v in extra.items():
            if any(keyword in k.lower() for keyword in ['password', 'token', 'key', 'secret']):
                masked_extra[k] = '****'
            else:
                masked_extra[k] = cls.mask_sensitive_info(str(v))
        
        extra_str = ' | '.join(f"{k}={v}" for k, v in masked_extra.items())
        masked_message = cls.mask_sensitive_info(message)
        return f"{masked_message} | {extra_str}"

    @classmethod
    def get_logger(cls, name='error'):
        """获取日志记录器（仅用于错误日志）"""
        if name not in cls._loggers:
            logger = cls._setup_logger(name)
            cls._loggers[name] = logger
        return cls._loggers[name]
    
    @classmethod
    def _setup_logger(cls, name):
        """设置日志记录器（仅用于错误日志）"""
        logger = logging.getLogger(name)
        logger.setLevel(logging.ERROR)  # 只记录错误级别的日志
        
        if not logger.handlers:
            # 确保日志目录存在
            if not os.path.exists(Config.LOG_DIR):
                os.makedirs(Config.LOG_DIR)
            
            # 只使用错误日志文件
            log_file = Config.LOG_CONFIG['error_log']
            
            # 添加文件处理器
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=Config.LOG_CONFIG['max_bytes'],
                backupCount=Config.LOG_CONFIG['backup_count'],
                encoding='utf-8',
                mode='a'
            )
            
            formatter = logging.Formatter(
                Config.LOG_CONFIG['format'],
                datefmt=Config.LOG_CONFIG['date_format']
            )
            formatter.converter = lambda *args: datetime.now().timetuple()
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger

    @classmethod
    def log_error(cls, message, error=None, extra=None):
        """记录错误日志"""
        logger = cls.get_logger('error')
        
        # 对消息进行脱敏处理
        masked_message = cls.mask_sensitive_info(message)
        
        if error:
            masked_message = f"{masked_message} - Error: {cls.mask_sensitive_info(str(error))}"
            if extra is None:
                extra = {}
            extra.update({
                'error_type': type(error).__name__,
                'error_details': cls.mask_sensitive_info(str(error)),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        
        if extra:
            masked_message = cls._format_message_with_extra(masked_message, extra)
        
        logger.error(masked_message)
        
        # 同时在控制台显示错误信息
        cls._get_console_logger().error(masked_message)
    
    @classmethod
    def log_db_error(cls, message, error=None, extra=None):
        """专门用于记录数据库相关错误的日志"""
        if extra is None:
            extra = {}
        extra.update({
            'error_category': 'database',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
        cls.log_error(message, error, extra)
    
    @classmethod
    def log_init_error(cls, message, error=None, extra=None):
        """专门用于记录初始化相关错误的日志"""
        if extra is None:
            extra = {}
        extra.update({
            'error_category': 'initialization',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
        cls.log_error(message, error, extra)
    
    @classmethod
    def log_success(cls, message, extra=None):
        """记录成功日志（仅控制台输出）"""
        logger = cls._get_console_logger()
        
        if extra:
            message = cls._format_message_with_extra(message, extra)
        
        logger.info(message)
    
    @classmethod
    def log_info(cls, message, extra=None):
        """记录信息日志"""
        logger = cls.get_logger('info')
        
        # 对消息进行脱敏处理
        masked_message = cls.mask_sensitive_info(message)
        
        if extra:
            masked_message = cls._format_message_with_extra(masked_message, extra)
        
        logger.info(masked_message)
        
        # 同时在控制台显示信息
        cls._get_console_logger().info(masked_message)
    
    @classmethod
    def shutdown(cls):
        """关闭所有日志处理器"""
        for logger in cls._loggers.values():
            for handler in logger.handlers[:]:
                handler.close()
                logger.removeHandler(handler)
        cls._loggers.clear()
        
        # 关闭控制台日志记录器
        if cls._console_logger:
            for handler in cls._console_logger.handlers[:]:
                handler.close()
                cls._console_logger.removeHandler(handler)
            cls._console_logger = None
