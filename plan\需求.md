# 库存管理模块开发需求文档

## 1. 项目概述

### 1.1 业务背景
- 基于需求计划的库存管理系统
- 支持按部门（通路一、通路二等）进行库存归属管理
- 实现需求计划 → BOM展开 → 库存检查 → 生产/采购决策的完整业务流程

### 1.2 核心目标
- 实现精确的库存可用性计算
- 支持库存预留和消耗管理
- 自动生成采购建议
- 完整的库存流水追溯
- 与现有需求计划模块无缝集成

## 2. 功能需求

### 2.1 核心功能模块

#### 2.1.1 库存查询管理
- **库存总览**：按部门、物料分类查看库存状态
- **库存明细**：查看具体物料的库存分布
- **库存流水**：查看所有库存变动记录
- **库存预警**：低库存、过期库存提醒

#### 2.1.2 库存业务操作
- **期初库存录入**：支持批量导入期初库存数据
- **库存调拨**：部门间库存转移
- **库存盘点**：定期盘点和差异处理
- **库存冻结/解冻**：质量问题库存管理

#### 2.1.3 生产集成功能
- **库存可用性检查**：基于需求计划检查物料可用性
- **库存预留**：为生产计划预留物料
- **生产领料**：生产执行时扣减库存
- **生产入库**：成品入库更新库存

#### 2.1.4 采购集成功能
- **采购建议生成**：基于库存不足自动生成采购建议
- **采购入库**：采购到货后更新库存
- **供应商库存分析**：分析供应商交货情况

### 2.2 权限控制需求

#### 2.2.1 功能权限
```
inventory:view          - 库存查看权限
inventory:manage        - 库存管理权限
inventory:initial       - 期初库存录入权限
inventory:transfer      - 库存调拨权限
inventory:count         - 库存盘点权限
inventory:freeze        - 库存冻结权限
inventory:reserve       - 库存预留权限
inventory:consume       - 库存消耗权限
inventory:report        - 库存报表权限
inventory:export        - 库存数据导出权限
```

#### 2.2.2 数据权限
- **部门数据权限**：用户只能查看和操作所属部门的库存
- **仓库数据权限**：限制用户可操作的仓库范围
- **物料分类权限**：限制用户可操作的物料分类

## 3. 技术架构

### 3.1 数据库设计
- ✅ `inventory_initial` - 期初库存表
- ✅ `inventory_transactions` - 库存流水表
- ✅ `department_inventory` - 部门库存归属表
- ✅ `inventory_reservations` - 库存预留记录表
- ✅ `view_inventory_available` - 库存可用量视图

### 3.2 服务层设计
- ✅ `InventoryService` - 库存业务服务类
- ✅ `InventoryDB` - 库存数据访问类
- ✅ `InventoryPermissionService` - 库存权限服务类

### 3.3 界面层设计
- 📋 `InventoryModule` - 库存管理主界面
- 📋 `InventoryQueryDialog` - 库存查询对话框
- 📋 `InventoryTransferDialog` - 库存调拨对话框
- 📋 `InventoryCountDialog` - 库存盘点对话框
- 📋 `InventoryInitialDialog` - 期初库存录入对话框

## 4. 开发计划

### Phase 1: 基础架构搭建（第1周）

#### 4.1 数据库初始化
- [x] 创建库存相关数据表
- [x] 创建库存服务基础类
- [ ] 数据库表结构测试和优化
- [ ] 创建测试数据

#### 4.2 权限系统集成
- [ ] 在 `user_permissions` 表中添加库存相关权限
- [ ] 在 `return_window.py` 中添加库存管理菜单项
- [ ] 创建库存权限检查装饰器
- [ ] 实现部门数据权限过滤

#### 4.3 服务层完善
- [x] 完善 `InventoryService` 核心方法
- [x] 创建 `InventoryDB` 数据访问类
- [x] 创建 `InventoryPermissionService` 权限服务类
- [ ] 实现库存计算核心算法
- [ ] 添加单元测试

### Phase 2: 核心功能开发（第2-3周）

#### 4.4 库存查询功能
- [ ] 创建 `InventoryModule` 主界面
- [ ] 实现库存总览功能
- [ ] 实现库存明细查询
- [ ] 实现库存流水查询
- [ ] 添加多条件搜索和分页

#### 4.5 库存业务操作
- [ ] 创建期初库存录入对话框
- [ ] 实现库存调拨功能
- [ ] 实现库存盘点功能
- [ ] 实现库存冻结/解冻功能
- [ ] 添加批量操作支持

#### 4.6 数据导入导出
- [ ] 实现期初库存Excel导入
- [ ] 实现库存数据Excel导出
- [ ] 添加数据验证和错误处理
- [ ] 实现导入导出进度显示

### Phase 3: 生产集成开发（第4周）

#### 4.7 需求计划集成
- [ ] 在需求计划中集成库存检查
- [ ] 实现库存可用性分析界面
- [ ] 实现库存预留确认流程
- [ ] 添加库存不足预警

#### 4.8 生产计划集成
- [ ] 创建生产计划数据表
- [ ] 实现生产计划与库存关联
- [ ] 实现生产领料功能
- [ ] 实现生产入库功能

#### 4.9 采购集成准备
- [ ] 设计采购订单数据表
- [ ] 实现采购建议生成
- [ ] 创建采购建议界面
- [ ] 实现采购入库预处理

### Phase 4: 高级功能开发（第5周）

#### 4.10 报表和分析
- [ ] 创建库存分析报表
- [ ] 实现库存周转率分析
- [ ] 实现库存预警报表
- [ ] 添加图表展示功能

#### 4.11 系统优化
- [ ] 性能优化和测试
- [ ] 界面体验优化
- [ ] 错误处理完善
- [ ] 添加操作日志

#### 4.12 集成测试
- [ ] 端到端业务流程测试
- [ ] 权限控制测试
- [ ] 数据一致性测试
- [ ] 用户接受度测试

## 5. 详细开发任务

### 5.1 权限系统集成任务

#### 5.1.1 权限数据初始化
```sql
-- 添加库存管理权限
INSERT INTO permission (permission_name, permission_code, permission_type, parent_id, menu_icon, menu_path, menu_component, menu_sort, status) VALUES
-- 库存管理菜单权限
('库存管理', 'inventory:view', 1, 0, 'inventory', '/inventory', 'inventory/InventoryModule', 50, 1),
-- 库存管理子菜单权限
('库存总览', 'inventory:overview', 1, (SELECT permission_id FROM permission WHERE permission_code = 'inventory:view'), 'dashboard', '/inventory/overview', 'inventory/OverviewModule', 10, 1),
('期初库存', 'inventory:initial', 1, (SELECT permission_id FROM permission WHERE permission_code = 'inventory:view'), 'edit', '/inventory/initial', 'inventory/InitialModule', 20, 1),
('库存调拨', 'inventory:transfer', 1, (SELECT permission_id FROM permission WHERE permission_code = 'inventory:view'), 'swap', '/inventory/transfer', 'inventory/TransferModule', 30, 1),
('库存盘点', 'inventory:count', 1, (SELECT permission_id FROM permission WHERE permission_code = 'inventory:view'), 'calculator', '/inventory/count', 'inventory/CountModule', 40, 1),
('库存报表', 'inventory:report', 1, (SELECT permission_id FROM permission WHERE permission_code = 'inventory:view'), 'bar-chart', '/inventory/report', 'inventory/ReportModule', 50, 1),

-- 库存管理按钮权限
('库存管理', 'inventory:manage', 2, (SELECT permission_id FROM permission WHERE permission_code = 'inventory:view'), NULL, NULL, NULL, 0, 1),
('库存冻结', 'inventory:freeze', 2, (SELECT permission_id FROM permission WHERE permission_code = 'inventory:view'), NULL, NULL, NULL, 0, 1),
('库存预留', 'inventory:reserve', 2, (SELECT permission_id FROM permission WHERE permission_code = 'inventory:view'), NULL, NULL, NULL, 0, 1),
('库存消耗', 'inventory:consume', 2, (SELECT permission_id FROM permission WHERE permission_code = 'inventory:view'), NULL, NULL, NULL, 0, 1),
('库存导出', 'inventory:export', 2, (SELECT permission_id FROM permission WHERE permission_code = 'inventory:view'), NULL, NULL, NULL, 0, 1),

-- 库存管理数据权限
('部门库存数据', 'inventory:dept:data', 3, (SELECT permission_id FROM permission WHERE permission_code = 'inventory:view'), NULL, NULL, NULL, 0, 1),
('仓库数据权限', 'inventory:warehouse:data', 3, (SELECT permission_id FROM permission WHERE permission_code = 'inventory:view'), NULL, NULL, NULL, 0, 1);
```

#### 5.1.2 主界面菜单集成
```python
# 在 return_window.py 中添加
if self.has_permission('inventory:view'):
    self.inventory_item = QTreeWidgetItem(self.tree, ["库存管理"])
    self.inventory_item.setIcon(0, QIcon("resources/icons/inventory.png"))
    
    # 子节点
    if self.has_permission('inventory:manage'):
        self.inventory_query_item = QTreeWidgetItem(self.inventory_item, ["库存查询"])
        self.inventory_initial_item = QTreeWidgetItem(self.inventory_item, ["期初库存"])
        self.inventory_transfer_item = QTreeWidgetItem(self.inventory_item, ["库存调拨"])
        self.inventory_count_item = QTreeWidgetItem(self.inventory_item, ["库存盘点"])
    
    if self.has_permission('inventory:report'):
        self.inventory_report_item = QTreeWidgetItem(self.inventory_item, ["库存报表"])
```

### 5.2 核心开发任务

#### 5.2.1 InventoryDB 数据访问类 ✅
```python
class InventoryDB:
    """库存数据访问类"""
    
    def __init__(self, current_user=None):
        self.db_manager = DatabaseManager()
        self.current_user = current_user or {}
    
    # 基础CRUD操作 ✅
    def get_department_inventory_list(self, filters=None, page=1, page_size=50)
    def get_inventory_transactions(self, filters=None, page=1, page_size=50)
    def get_inventory_reservations(self, filters=None, page=1, page_size=50)
    
    # 业务操作 ✅
    def create_initial_inventory(self, inventory_data)
    def transfer_inventory(self, transfer_data)
    def count_inventory(self, count_data)
    def freeze_inventory(self, material_id, department, freeze_qty)
    
    # 权限过滤 ✅
    def apply_department_filter(self, department_field)
    def check_operation_permission(self, operation, department)
```

#### 5.2.2 InventoryPermissionService 权限服务类 ✅
```python
class InventoryPermissionService:
    """库存权限服务类"""
    
    def __init__(self, current_user=None):
        self.current_user = current_user or get_current_user() or {}
        self.permission_manager = PermissionManager()
    
    # 基础权限检查 ✅
    def has_permission(self, permission_code)
    def check_view_permission(self)
    def check_manage_permission(self)
    
    # 数据权限检查 ✅
    def check_department_permission(self, department)
    def check_warehouse_permission(self, warehouse_code)
    def check_material_category_permission(self, category_id)
    
    # 权限装饰器 ✅
    def require_inventory_permission(self, permission_code)
    def require_department_permission(self, department_param)
```

#### 5.2.3 InventoryModule 主界面类 📋 开始开发
```python
class InventoryModule(QWidget):
    """库存管理主界面"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.inventory_db = InventoryDB()
        self.permission_service = InventoryPermissionService()
        self.current_user = get_current_user()
        self.setup_ui()
        self.setup_permissions()
    
    def setup_permissions(self):
        """设置权限控制"""
        # 根据用户权限显示/隐藏功能按钮
        
    def create_toolbar(self):
        """创建工具栏"""
        # 查询、新增、编辑、删除、导入、导出等按钮
        
    def create_table(self):
        """创建数据表格"""
        # 库存数据展示表格
        
    def load_data(self):
        """加载数据"""
        # 根据权限加载用户可见的库存数据
```

### 5.3 集成测试任务

#### 5.3.1 业务流程测试
1. **需求计划 → 库存检查流程**
   - 创建需求计划
   - BOM展开
   - 库存可用性检查
   - 验证结果准确性

2. **库存不足 → 采购建议流程**
   - 模拟库存不足场景
   - 生成采购建议
   - 验证采购数量计算
   - 验证供应商推荐

3. **采购入库 → 库存更新流程**
   - 模拟采购入库
   - 验证库存数量更新
   - 验证库存流水记录
   - 验证成本计算

4. **生产计划 → 库存扣减流程**
   - 创建生产计划
   - 库存预留
   - 生产领料
   - 验证库存扣减

#### 5.3.2 权限控制测试
1. **功能权限测试**
   - 不同角色用户登录
   - 验证菜单显示
   - 验证按钮权限
   - 验证操作权限

2. **数据权限测试**
   - 部门数据隔离
   - 仓库数据权限
   - 物料分类权限
   - 跨部门操作限制

## 6. 风险控制

### 6.1 技术风险
- **数据一致性风险**：多表关联更新时的事务控制
- **性能风险**：大数据量查询的性能优化
- **并发风险**：多用户同时操作库存的并发控制

### 6.2 业务风险
- **库存计算错误**：复杂的库存可用量计算逻辑
- **权限漏洞**：部门间数据泄露风险
- **业务流程中断**：与现有模块集成的兼容性

### 6.3 风险应对措施
- 充分的单元测试和集成测试
- 数据库事务和锁机制保证一致性
- 分阶段开发，逐步集成验证
- 详细的错误日志和监控机制

## 7. 验收标准

### 7.1 功能验收
- [ ] 所有核心功能正常运行
- [ ] 权限控制完全生效
- [ ] 数据导入导出正常
- [ ] 报表生成准确

### 7.2 性能验收
- [ ] 库存查询响应时间 < 3秒
- [ ] 大批量数据导入成功率 > 95%
- [ ] 并发操作无数据冲突
- [ ] 系统稳定性良好

### 7.3 用户体验验收
- [ ] 界面操作直观易用
- [ ] 错误提示清晰准确
- [ ] 数据展示美观规范
- [ ] 帮助文档完整

## 8. 后续扩展

### 8.1 高级功能
- 库存预测和补货建议
- 库存成本分析和优化
- 供应链协同管理
- 移动端库存管理

### 8.2 系统集成
- ERP系统集成
- 条码/RFID集成
- 自动化仓储集成
- BI报表系统集成

## 9. 当前开发状态

### ✅ 已完成
- **数据库设计**：所有库存相关表结构已创建
- **服务层架构**：
  - ✅ `InventoryService` - 库存业务服务类
  - ✅ `InventoryDB` - 库存数据访问类  
  - ✅ `InventoryPermissionService` - 库存权限服务类

### 📋 正在进行
- **界面层设计**：准备开始开发主界面和对话框
- **单元测试**：准备对服务层进行测试验证

### 📅 下一步计划
1. **界面层开发**（1-2周）
   - 创建 `InventoryModule` 主界面
   - 创建各种业务对话框
   - 集成权限控制

2. **测试和调试**（1周）
   - 单元测试
   - 集成测试
   - 权限测试

3. **功能完善**（1-2周）
   - 数据导入导出
   - 报表功能
   - 性能优化

