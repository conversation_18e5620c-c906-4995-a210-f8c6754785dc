# 忽略所有文件
*

# 不忽略以下文件和文件夹
!cj_return/
!core/
!modules/
!return_window.py
!return_build.spec
!requirements.txt
!styles.qss
!report/
!home_module.py
!readme.md
!material/
!system/
!login_window.py
!resources/
!migosys.sql
!plan/
!demand/
!inventory/
# 不忽略.gitignore本身
!.gitignore

# 不忽略文件夹中的内容
!core/**
!modules/**
!report/**
!material/**
!system/**
!cj_return/**
!resources/**
!plan/** 
!demand/**
!inventory/**
# 忽略所有文件夹中的__pycache__
**/__pycache__/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# IDE
.idea/
.vscode/
*.swp
*.swo

# 环境
venv/
env/
ENV/
.env

# 日志
*.log

# 数据库
*.db
*.sqlite3

# 系统文件
.DS_Store
Thumbs.db 