"""
需求计划数据库操作类
处理需求计划相关的数据库操作
"""
from modules.db_manager import DatabaseManager
from core.logger import Logger
from core.config import get_current_user
from datetime import datetime, date
import traceback

class DemandPlanDB:
    """需求计划数据库操作类"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.current_user = get_current_user()
        # 确保自动编号规则存在
        try:
            self.ensure_auto_number_rule()
        except Exception as e:
            Logger.log_error(f"初始化需求计划自动编号规则失败: {str(e)}")
    
    def ensure_auto_number_rule(self):
        """确保需求计划自动编号规则存在"""
        try:
            # 检查XQJH规则是否存在
            check_query = "SELECT COUNT(*) as count FROM auto_number_rule WHERE rule_code = 'XQJH'"
            result = self.db_manager.execute_query(check_query)
            
            if result and result[0].get('count', 0) == 0:
                # 创建XQJH规则
                insert_query = """
                INSERT INTO auto_number_rule 
                (rule_code, rule_name, prefix, date_format, sequence_length, reset_type, status, description)
                VALUES ('XQJH', '需求计划编号', 'XQJH-', 'YYYYMMDD', 4, 1, 1, '需求计划管理模块编号规则')
                """
                self.db_manager.execute_update(insert_query)
                Logger.log_info("已创建XQJH自动编号规则")
                
        except Exception as e:
            Logger.log_error(f"检查需求计划自动编号规则失败: {str(e)}")
    
    def generate_auto_number(self):
        """使用自动编号服务生成需求计划编号"""
        try:
            from system.auto_number_module import AutoNumberService
            
            auto_number_service = AutoNumberService()
            
            # 使用XQJH规则生成编号
            plan_number = auto_number_service.generate_number(
                rule_code='XQJH',
                business_date=date.today().strftime('%Y-%m-%d'),
                business_type='demand_plan',
                business_id=None,
                user_id=self.current_user.get('user_id') if self.current_user else None
            )
            
            return plan_number
            
        except Exception as e:
            Logger.log_error(f"生成需求计划自动编号失败: {str(e)}")
            # 如果自动编号失败，回退到手动编号
            return self.generate_manual_plan_no()
    
    def generate_manual_plan_no(self):
        """手动生成需求计划编号（备用方案）"""
        try:
            today = datetime.now().strftime('%Y%m%d')
            
            # 查询今天已有的最大序号
            query = """
            SELECT plan_no FROM demand_plans 
            WHERE plan_no LIKE %s 
            ORDER BY plan_no DESC LIMIT 1
            """
            result = self.db_manager.execute_query(query, (f'XQJH-{today}-%',))
            
            if result:
                last_no = result[0]['plan_no']
                # 提取序号部分
                seq_part = last_no.split('-')[-1]
                next_seq = int(seq_part) + 1
            else:
                next_seq = 1
            
            return f'XQJH-{today}-{next_seq:04d}'
            
        except Exception as e:
            Logger.log_error(f"手动生成需求计划编号失败: {str(e)}")
            # 最后的备用方案
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            return f'XQJH-{timestamp}'
    
    def create_demand_plan(self, plan_data, details_data):
        """创建需求计划（包含主表和明细）"""
        try:
            self.db_manager.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 生成需求计划编号（如果没有提供）
            if not plan_data.get('plan_no'):
                plan_no = self.generate_auto_number()
                plan_data['plan_no'] = plan_no
            
            # 保存主表 - 移除 production_plan_no 字段
            insert_query = """
            INSERT INTO demand_plans 
            (plan_no, plan_date, plan_name, department, applicant,
             demand_type, priority_level, plan_status,
             total_amount, remark, create_time, create_user,
             update_time, update_user)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            params = (
                plan_data['plan_no'],
                plan_data['plan_date'],
                plan_data.get('plan_name', ''),
                plan_data.get('department', ''),
                plan_data.get('applicant', ''),
                plan_data.get('demand_type', 1),
                plan_data.get('priority_level', 2),
                plan_data.get('plan_status', 0),
                plan_data.get('total_amount', 0),
                plan_data.get('remark', ''),
                current_time,
                current_user,
                current_time,
                current_user
            )
            
            self.db_manager.execute_update(insert_query, params)
            
            # 获取插入的计划ID
            plan_id_result = self.db_manager.execute_query("SELECT LAST_INSERT_ID() as id")
            if plan_id_result and len(plan_id_result) > 0:
                if isinstance(plan_id_result[0], dict):
                    plan_id = plan_id_result[0]['id']
                else:
                    plan_id = plan_id_result[0][0]
            else:
                raise Exception("无法获取插入的需求计划记录ID")
            
            # 保存明细
            if details_data:
                self.save_demand_plan_details(plan_id, plan_data['plan_no'], details_data)
            
            self.db_manager.commit_transaction()
            return plan_id
            
        except Exception as e:
            self.db_manager.rollback_transaction()
            Logger.log_error(f"保存需求计划主表失败: {str(e)}")
            raise Exception(f"保存需求计划主表失败: {str(e)}")
    
    def save_demand_plan_details(self, plan_id, plan_no, details_data):
        """保存需求计划明细数据 - 修复版本"""
        try:
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 先删除原有明细（更新时使用）
            delete_query = "DELETE FROM demand_plan_details WHERE plan_id = %s"
            self.db_manager.execute_update(delete_query, (plan_id,))
            
            # 插入新明细 - 移除supplier_id字段，确保列数匹配
            if details_data:
                insert_query = """
                INSERT INTO demand_plan_details 
                (plan_id, plan_no, sequence_no, material_id, material_name,
                 material_category_id, required_qty, required_unit,
                 unit_price, total_amount, required_date, usage_purpose, 
                 remark, execution_status, executed_qty,
                 production_plan_no, production_plan_id, purchase_request_no, purchase_request_id,
                 bom_id, bom_code, is_bom_expanded, bom_expand_time,
                 create_time, create_user, update_time, update_user)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                for detail in details_data:
                    params = (
                        plan_id,                                    # 1. plan_id
                        plan_no,                                    # 2. plan_no
                        detail.get('sequence_no', 1),               # 3. sequence_no
                        detail.get('material_id', ''),              # 4. material_id
                        detail.get('material_name', ''),            # 5. material_name
                        detail.get('material_category_id'),         # 6. material_category_id
                        detail.get('required_qty', 0),              # 7. required_qty
                        detail.get('required_unit', ''),            # 8. required_unit
                        detail.get('unit_price', 0),                # 9. unit_price
                        detail.get('total_amount', 0),              # 10. total_amount
                        detail.get('required_date'),                # 11. required_date
                        detail.get('usage_purpose', ''),            # 12. usage_purpose
                        detail.get('remark', ''),                   # 13. remark
                        detail.get('execution_status', 0),          # 14. execution_status
                        detail.get('executed_qty', 0),              # 15. executed_qty
                        detail.get('production_plan_no'),           # 16. production_plan_no
                        detail.get('production_plan_id'),           # 17. production_plan_id
                        detail.get('purchase_request_no'),          # 18. purchase_request_no
                        detail.get('purchase_request_id'),          # 19. purchase_request_id
                        detail.get('bom_id'),                       # 20. bom_id
                        detail.get('bom_code'),                     # 21. bom_code
                        detail.get('is_bom_expanded', 0),           # 22. is_bom_expanded
                        detail.get('bom_expand_time'),              # 23. bom_expand_time
                        current_time,                               # 24. create_time
                        current_user,                               # 25. create_user
                        current_time,                               # 26. update_time
                        current_user                                # 27. update_user
                    )
                    
                    self.db_manager.execute_update(insert_query, params)
            
        except Exception as e:
            Logger.log_error(f"保存需求计划明细失败: {str(e)}")
            raise Exception(f"保存需求计划明细失败: {str(e)}")
    
    def get_demand_plans(self, filters=None, page=1, page_size=50):
        """获取需求计划列表（支持分页和筛选）"""
        try:
            # 构建查询条件
            where_conditions = []
            params = []
            
            if filters:
                if filters.get('plan_no'):
                    where_conditions.append("dp.plan_no LIKE %s")
                    params.append(f"%{filters['plan_no']}%")
                
                if filters.get('department'):
                    where_conditions.append("dp.department LIKE %s")
                    params.append(f"%{filters['department']}%")
                
                if filters.get('applicant'):
                    where_conditions.append("dp.applicant LIKE %s")
                    params.append(f"%{filters['applicant']}%")
                
                # 修改这里，使用 'demand_type' in filters 而不是 filters.get('demand_type')
                if 'demand_type' in filters:
                    where_conditions.append("dp.demand_type = %s")
                    params.append(filters['demand_type'])
                
                # 修改这里，使用 'plan_status' in filters 而不是 filters.get('plan_status')
                # 这样可以正确处理 plan_status 为 0 的情况
                if 'plan_status' in filters:
                    where_conditions.append("dp.plan_status = %s")
                    params.append(filters['plan_status'])
                
                # 修改这里，使用 'priority_level' in filters 而不是 filters.get('priority_level')
                if 'priority_level' in filters:
                    where_conditions.append("dp.priority_level = %s")
                    params.append(filters['priority_level'])
                
                if filters.get('plan_date_start'):
                    where_conditions.append("dp.plan_date >= %s")
                    params.append(filters['plan_date_start'])
                
                if filters.get('plan_date_end'):
                    where_conditions.append("dp.plan_date <= %s")
                    params.append(filters['plan_date_end'])
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            
            # 查询总数
            count_query = f"""
            SELECT COUNT(*) as total
            FROM demand_plans dp
            WHERE {where_clause}
            """
            count_result = self.db_manager.execute_query(count_query, params)
            total = count_result[0]['total'] if count_result else 0
            
            # 查询列表数据
            offset = (page - 1) * page_size
            list_query = f"""
            SELECT 
                dp.plan_id,
                dp.plan_no,
                dp.plan_date,
                dp.plan_name,
                dp.department,
                dp.applicant,
                dp.demand_type,
                CASE dp.demand_type
                    WHEN 1 THEN '生产需求'
                    WHEN 2 THEN '销售需求'
                    WHEN 3 THEN '库存补充'
                    WHEN 4 THEN '其他需求'
                    ELSE '未知'
                END as demand_type_name,
                dp.priority_level,
                CASE dp.priority_level
                    WHEN 1 THEN '普通'
                    WHEN 2 THEN '紧急'
                    WHEN 3 THEN '特急'
                    ELSE '未知'
                END as priority_level_name,
                dp.plan_status,
                CASE dp.plan_status
                    WHEN 0 THEN '草稿'
                    WHEN 1 THEN '已确认'
                    WHEN 2 THEN '执行中'
                    WHEN 3 THEN '已完成'
                    WHEN 9 THEN '已取消'
                    ELSE '未知'
                END as plan_status_name,
                dp.total_amount,
                dp.is_locked,
                dp.remark,
                dp.create_time,
                dp.create_user,
                dp.update_time,
                dp.update_user,
                -- 统计明细数量
                COALESCE(detail_count.detail_num, 0) as detail_count
            FROM demand_plans dp
            LEFT JOIN (
                SELECT plan_id, COUNT(*) as detail_num
                FROM demand_plan_details
                GROUP BY plan_id
            ) detail_count ON dp.plan_id = detail_count.plan_id
            WHERE {where_clause}
            ORDER BY dp.create_time DESC
            LIMIT %s OFFSET %s
            """
            
            params.extend([page_size, offset])
            results = self.db_manager.execute_query(list_query, params)
            
            return {
                'data': results or [],
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            Logger.log_error(f"查询需求计划列表失败: {str(e)}")
            raise Exception(f"查询需求计划列表失败: {str(e)}")
    
    def get_demand_plan_by_id(self, plan_id):
        """根据ID获取需求计划详情"""
        try:
            query = """
            SELECT 
                dp.plan_id,
                dp.plan_no,
                dp.plan_date,
                dp.plan_name,
                dp.department,
                dp.applicant,
                dp.demand_type,
                dp.priority_level,
                dp.plan_status,
                dp.total_amount,
                dp.is_locked,
                dp.remark,
                dp.create_time,
                dp.create_user,
                dp.update_time,
                dp.update_user
            FROM demand_plans dp
            WHERE dp.plan_id = %s
            """
            
            result = self.db_manager.execute_query(query, (plan_id,))
            return result[0] if result else None
            
        except Exception as e:
            Logger.log_error(f"查询需求计划详情失败: {str(e)}")
            raise Exception(f"查询需求计划详情失败: {str(e)}")
    
    def get_demand_plan_details(self, plan_id):
        """获取需求计划明细列表 - 移除已删除字段的查询"""
        try:
            query = """
            SELECT 
                dpd.detail_id,
                dpd.plan_id,
                dpd.plan_no,
                dpd.sequence_no,
                dpd.material_id,
                dpd.material_name,
                dpd.material_category_id,
                mc.category_name,
                dpd.required_qty,
                dpd.required_unit,
                dpd.unit_price,
                dpd.total_amount,
                dpd.required_date,
                dpd.usage_purpose,
                dpd.remark,
                dpd.execution_status,
                CASE dpd.execution_status
                    WHEN 0 THEN '未执行'
                    WHEN 1 THEN '部分执行'
                    WHEN 2 THEN '已完成'
                    ELSE '未知'
                END as execution_status_name,
                dpd.executed_qty,
                dpd.production_plan_no,
                dpd.production_plan_id,
                dpd.purchase_request_no,
                dpd.purchase_request_id,
                dpd.bom_id,
                dpd.bom_code,
                dpd.is_bom_expanded,
                dpd.bom_expand_time,
                dpd.create_time,
                dpd.create_user,
                dpd.update_time,
                dpd.update_user
            FROM demand_plan_details dpd
            LEFT JOIN material_categories mc ON dpd.material_category_id = mc.category_id
            WHERE dpd.plan_id = %s
            ORDER BY dpd.sequence_no
            """
            
            return self.db_manager.execute_query(query, (plan_id,))
            
        except Exception as e:
            Logger.log_error(f"查询需求计划明细失败: {str(e)}")
            raise Exception(f"查询需求计划明细失败: {str(e)}")
    
    def update_demand_plan(self, plan_id, plan_data, details_data):
        """更新需求计划数据 - 移除 required_date 字段"""
        try:
            self.db_manager.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 更新主表 - 移除 required_date 字段
            update_query = """
            UPDATE demand_plans 
            SET plan_date=%s, plan_name=%s, department=%s, applicant=%s,
                demand_type=%s, priority_level=%s, plan_status=%s,
                total_amount=%s, remark=%s, 
                update_time=%s, update_user=%s
            WHERE plan_id=%s
            """
            
            params = (
                plan_data['plan_date'],
                plan_data.get('plan_name', ''),
                plan_data.get('department', ''),
                plan_data.get('applicant', ''),
                plan_data.get('demand_type', 1),
                plan_data.get('priority_level', 2),
                plan_data.get('plan_status', 0),
                plan_data.get('total_amount', 0),
                plan_data.get('remark', ''),
                current_time,
                current_user,
                plan_id
            )
            
            affected_rows = self.db_manager.execute_update(update_query, params)
            
            if affected_rows == 0:
                raise Exception("需求计划记录不存在或未发生变更")
            
            # 更新明细（先删除再插入）
            if details_data is not None:  # 允许空列表清空明细
                plan_no = plan_data.get('plan_no') or self.get_plan_no_by_id(plan_id)
                self.save_demand_plan_details(plan_id, plan_no, details_data)
            
            self.db_manager.commit_transaction()
            
        except Exception as e:
            self.db_manager.rollback_transaction()
            Logger.log_error(f"更新需求计划失败: {str(e)}")
            raise Exception(f"更新需求计划失败: {str(e)}")
    
    def get_plan_no_by_id(self, plan_id):
        """根据ID获取计划编号"""
        try:
            query = "SELECT plan_no FROM demand_plans WHERE plan_id = %s"
            result = self.db_manager.execute_query(query, (plan_id,))
            return result[0]['plan_no'] if result else None
        except Exception as e:
            Logger.log_error(f"获取计划编号失败: {str(e)}")
            return None
    
    def update_plan_status(self, plan_id, new_status):
        """更新需求计划状态"""
        try:
            current_time = datetime.now()
            user_name = self.current_user.get('real_name') if self.current_user else 'system'
            
            # 当状态为"已完成"(3)时，同时设置 is_locked 为 1（锁定）
            if new_status == 3:
                query = """
                UPDATE demand_plans 
                SET plan_status = %s, is_locked = 1, update_time = %s, update_user = %s
                WHERE plan_id = %s
                """
            else:
                query = """
                UPDATE demand_plans 
                SET plan_status = %s, update_time = %s, update_user = %s
                WHERE plan_id = %s
                """
            
            result = self.db_manager.execute_update(query, (new_status, current_time, user_name, plan_id))
            
            if result:
                Logger.log_info(f"成功更新需求计划状态: {plan_id} -> {new_status}")
                return True
            else:
                raise Exception("更新失败，可能记录不存在")
                
        except Exception as e:
            Logger.log_error(f"更新需求计划状态失败: {str(e)}")
            raise Exception(f"更新需求计划状态失败: {str(e)}")
    
    def delete_demand_plan(self, plan_id):
        """删除需求计划（级联删除明细和执行记录）"""
        try:
            # 开始事务
            self.db_manager.begin_transaction()
            
            # 检查是否可以删除（已锁定的不能删除）
            check_query = "SELECT is_locked, plan_status FROM demand_plans WHERE plan_id = %s"
            check_result = self.db_manager.execute_query(check_query, (plan_id,))
            
            if not check_result:
                raise Exception("需求计划不存在")
            
            plan_info = check_result[0]
            if plan_info['is_locked'] == 1:
                raise Exception("需求计划已锁定，不能删除")
            
            if plan_info['plan_status'] in [2, 3]:  # 执行中、已完成
                raise Exception("需求计划已在执行或已完成，不能删除")
            
            # 删除执行记录
            delete_executions_query = "DELETE FROM demand_executions WHERE plan_id = %s"
            self.db_manager.execute_update(delete_executions_query, (plan_id,))
            
            # 删除明细（外键约束会自动级联删除）
            delete_details_query = "DELETE FROM demand_plan_details WHERE plan_id = %s"
            self.db_manager.execute_update(delete_details_query, (plan_id,))
            
            # 删除主表
            delete_main_query = "DELETE FROM demand_plans WHERE plan_id = %s"
            result = self.db_manager.execute_update(delete_main_query, (plan_id,))
            
            if result:
                # 提交事务
                self.db_manager.commit_transaction()
                Logger.log_info(f"成功删除需求计划: {plan_id}")
                return True
            else:
                raise Exception("删除失败")
                
        except Exception as e:
            # 回滚事务
            self.db_manager.rollback_transaction()
            Logger.log_error(f"删除需求计划失败: {str(e)}")
            raise Exception(f"删除需求计划失败: {str(e)}")
    
    def create_execution_record(self, execution_data):
        """创建需求执行跟踪记录"""
        try:
            # 设置创建信息
            current_time = datetime.now()
            user_name = self.current_user.get('real_name') if self.current_user else 'system'
            
            execution_data.update({
                'create_time': current_time,
                'create_user': user_name,
                'update_time': current_time,
                'update_user': user_name
            })
            
            query = """
            INSERT INTO demand_executions (
                plan_id, detail_id, plan_no, material_id, execution_type,
                execution_no, executed_qty, execution_date, executor,
                execution_status, remark, create_time, create_user,
                update_time, update_user
            ) VALUES (
                %(plan_id)s, %(detail_id)s, %(plan_no)s, %(material_id)s, %(execution_type)s,
                %(execution_no)s, %(executed_qty)s, %(execution_date)s, %(executor)s,
                %(execution_status)s, %(remark)s, %(create_time)s, %(create_user)s,
                %(update_time)s, %(update_user)s
            )
            """
            
            execution_id = self.db_manager.execute_insert(query, execution_data)
            
            # 更新明细表的执行状态和已执行数量
            self.update_detail_execution_status(execution_data['detail_id'])
            
            Logger.log_info(f"成功创建需求执行记录: {execution_id}")
            return execution_id
            
        except Exception as e:
            Logger.log_error(f"创建需求执行记录失败: {str(e)}")
            raise Exception(f"创建需求执行记录失败: {str(e)}")
    
    def update_detail_execution_status(self, detail_id):
        """更新明细的执行状态和已执行数量"""
        try:
            # 计算已执行总数量
            sum_query = """
            SELECT COALESCE(SUM(executed_qty), 0) as total_executed
            FROM demand_executions
            WHERE detail_id = %s AND execution_status IN (1, 2)
            """
            sum_result = self.db_manager.execute_query(sum_query, (detail_id,))
            total_executed = sum_result[0]['total_executed'] if sum_result else 0
            
            # 获取需求数量
            detail_query = "SELECT required_qty FROM demand_plan_details WHERE detail_id = %s"
            detail_result = self.db_manager.execute_query(detail_query, (detail_id,))
            
            if detail_result:
                required_qty = detail_result[0]['required_qty']
                
                # 确定执行状态
                if total_executed == 0:
                    execution_status = 0  # 未执行
                elif total_executed >= required_qty:
                    execution_status = 2  # 已完成
                else:
                    execution_status = 1  # 部分执行
                
                # 更新明细表
                update_query = """
                UPDATE demand_plan_details 
                SET executed_qty = %s, execution_status = %s,
                    update_time = %s, update_user = %s
                WHERE detail_id = %s
                """
                
                current_time = datetime.now()
                user_name = self.current_user.get('real_name') if self.current_user else 'system'
                
                self.db_manager.execute_update(update_query, (
                    total_executed, execution_status, current_time, user_name, detail_id
                ))
                
        except Exception as e:
            Logger.log_error(f"更新明细执行状态失败: {str(e)}")
            raise Exception(f"更新明细执行状态失败: {str(e)}")
    
    def get_execution_records(self, plan_id=None, detail_id=None):
        """获取需求执行跟踪记录"""
        try:
            where_conditions = []
            params = []
            
            if plan_id:
                where_conditions.append("de.plan_id = %s")
                params.append(plan_id)
            
            if detail_id:
                where_conditions.append("de.detail_id = %s")
                params.append(detail_id)
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            
            query = f"""
            SELECT 
                de.execution_id,
                de.plan_id,
                de.detail_id,
                de.plan_no,
                de.material_id,
                m.name as material_name,
                de.execution_type,
                CASE de.execution_type
                    WHEN 1 THEN '采购执行'
                    WHEN 2 THEN '生产执行'
                    WHEN 3 THEN '调拨执行'
                    ELSE '未知'
                END as execution_type_name,
                de.execution_no,
                de.executed_qty,
                de.execution_date,
                de.executor,
                de.execution_status,
                CASE de.execution_status
                    WHEN 1 THEN '已执行'
                    WHEN 2 THEN '已完成'
                    WHEN 3 THEN '已取消'
                    ELSE '未知'
                END as execution_status_name,
                de.remark,
                de.create_time,
                de.create_user,
                de.update_time,
                de.update_user
            FROM demand_executions de
            LEFT JOIN materials m ON de.material_id = m.material_id
            WHERE {where_clause}
            ORDER BY de.create_time DESC
            """
            
            return self.db_manager.execute_query(query, params)
            
        except Exception as e:
            Logger.log_error(f"查询需求执行记录失败: {str(e)}")
            raise Exception(f"查询需求执行记录失败: {str(e)}")
    
    def get_demand_statistics(self, filters=None):
        """获取需求计划统计信息"""
        try:
            # 构建查询条件
            where_conditions = []
            params = []
            
            if filters:
                if filters.get('plan_date_start'):
                    where_conditions.append("dp.plan_date >= %s")
                    params.append(filters['plan_date_start'])
                
                if filters.get('plan_date_end'):
                    where_conditions.append("dp.plan_date <= %s")
                    params.append(filters['plan_date_end'])
                
                if filters.get('department'):
                    where_conditions.append("dp.department = %s")
                    params.append(filters['department'])
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            
            query = f"""
            SELECT 
                COUNT(*) as total_plans,
                SUM(CASE WHEN dp.plan_status = 0 THEN 1 ELSE 0 END) as draft_count,
                SUM(CASE WHEN dp.plan_status = 1 THEN 1 ELSE 0 END) as confirmed_count,
                SUM(CASE WHEN dp.plan_status = 2 THEN 1 ELSE 0 END) as executing_count,
                SUM(CASE WHEN dp.plan_status = 3 THEN 1 ELSE 0 END) as completed_count,
                SUM(CASE WHEN dp.plan_status = 9 THEN 1 ELSE 0 END) as cancelled_count,
                COALESCE(SUM(dp.total_amount), 0) as total_amount,
                COUNT(DISTINCT dp.department) as department_count,
                COUNT(DISTINCT dp.applicant) as applicant_count
            FROM demand_plans dp
            WHERE {where_clause}
            """
            
            result = self.db_manager.execute_query(query, params)
            return result[0] if result else {}
            
        except Exception as e:
            Logger.log_error(f"查询需求计划统计失败: {str(e)}")
            raise Exception(f"查询需求计划统计失败: {str(e)}")

    def get_bom_products_for_demand(self, search_text="", page=1, page_size=50):
        """获取可用于需求计划的BOM成品物料列表"""
        try:
            # 构建查询条件
            where_conditions = ["bh.status = 1"]  # 只获取启用状态的BOM
            params = []
            
            # 搜索条件
            if search_text:
                where_conditions.append("(bh.parent_material_id LIKE %s OR m.name LIKE %s)")
                params.extend([f"%{search_text}%", f"%{search_text}%"])
            
            where_clause = " AND ".join(where_conditions)
            
            # 查询总数
            count_query = f"""
            SELECT COUNT(DISTINCT bh.parent_material_id)
            FROM bom_header bh
            LEFT JOIN materials m ON bh.parent_material_id = m.material_id
            WHERE {where_clause}
            """
            
            count_result = self.db_manager.execute_query(count_query, params)
            total = count_result[0]['COUNT(DISTINCT bh.parent_material_id)'] if count_result else 0
            
            # 查询数据
            offset = (page - 1) * page_size
            list_query = f"""
            SELECT DISTINCT
                bh.parent_material_id as material_id,
                m.name as material_name,
                m.spec_model as specification,
                m.base_unit as unit,
                mc.category_name,
                mc.category_id,
                COUNT(bh.bom_id) as bom_count,
                GROUP_CONCAT(DISTINCT bh.bom_name ORDER BY bh.create_time DESC SEPARATOR '; ') as bom_names
            FROM bom_header bh
            LEFT JOIN materials m ON bh.parent_material_id = m.material_id
            LEFT JOIN material_categories mc ON m.category_id = mc.category_id
            WHERE {where_clause}
            GROUP BY bh.parent_material_id, m.name, m.spec_model, m.base_unit, 
                     mc.category_name, mc.category_id
            ORDER BY m.name
            LIMIT %s OFFSET %s
            """
            
            params.extend([page_size, offset])
            results = self.db_manager.execute_query(list_query, params)
            
            return total, results if results else []
            
        except Exception as e:
            Logger.log_error(f"获取BOM成品物料列表失败: {str(e)}")
            return 0, []

    def get_bom_material_details_for_demand(self, material_id):
        """获取BOM物料明细（包含原料和包材，用于需求展开）"""
        try:
            # 获取BOM ID
            bom_query = """
            SELECT bom_id FROM bom_header 
            WHERE parent_material_id = %s 
                AND status = 1 
                AND effective_date <= CURDATE() 
                AND expiry_date > CURDATE()
            ORDER BY create_time DESC 
            LIMIT 1
            """
            
            bom_result = self.db_manager.execute_query(bom_query, (material_id,))
            if not bom_result:
                return []
            
            bom_id = bom_result[0]['bom_id']
            all_details = []
            
            # 1. 获取原料明细 - 通过关联materials表获取物料名称
            material_query = """
            SELECT 'material' as detail_type, bmd.sequence_no,
                   bmd.raw_material_id as material_id, 
                   m.name as material_name,
                   bmd.required_qty, bmd.required_unit, 
                   bmd.scrap_rate, bmd.unit_cost, 
                   m.category_id
            FROM bom_material_detail bmd
            LEFT JOIN materials m ON bmd.raw_material_id = m.material_id
            WHERE bmd.bom_id = %s
            ORDER BY bmd.sequence_no
            """
            
            material_details = self.db_manager.execute_query(material_query, (bom_id,))
            if material_details:
                all_details.extend(material_details)
            
            # 2. 获取包材明细 - 通过关联materials表获取物料名称
            package_query = """
            SELECT 'package' as detail_type, bpd.sequence_no,
                   bpd.package_material_id as material_id,
                   m.name as material_name,
                   bpd.required_qty, bpd.required_unit, 
                   bpd.scrap_rate, bpd.unit_cost, 
                   m.category_id
            FROM bom_package_detail bpd
            LEFT JOIN materials m ON bpd.package_material_id = m.material_id
            WHERE bpd.bom_id = %s
            ORDER BY bpd.sequence_no
            """
            
            package_details = self.db_manager.execute_query(package_query, (bom_id,))
            if package_details:
                all_details.extend(package_details)
            
            # 按序号排序
            all_details.sort(key=lambda x: (x.get('detail_type', ''), x.get('sequence_no', 0)))
            
            return all_details
            
        except Exception as e:
            Logger.log_error(f"获取BOM物料明细失败: {str(e)}")
            return []

    def get_material_specification(self, material_id):
        """获取物料规格型号
        
        Args:
            material_id: 物料编码
            
        Returns:
            str: 物料规格型号，如果未找到返回空字符串
        """
        try:
            if not material_id:
                return ''
                
            query = """
            SELECT spec_model 
            FROM materials 
            WHERE material_id = %s
            """
            
            result = self.db_manager.execute_query(query, (material_id,))
            
            if result and len(result) > 0:
                return result[0].get('spec_model', '') or ''
            else:
                return ''
                
        except Exception as e:
            Logger.log_error(f"获取物料规格型号失败: {str(e)}")
            return ''

    def get_material_cost(self, material_id):
        """获取物料成本价格
        
        Args:
            material_id: 物料编码
            
        Returns:
            float: 物料成本价格，如果未找到返回0
        """
        try:
            if not material_id:
                return 0.0
                
            query = """
            SELECT cost 
            FROM materials 
            WHERE material_id = %s
            """
            
            result = self.db_manager.execute_query(query, (material_id,))
            
            if result and len(result) > 0:
                cost = result[0].get('cost', 0)
                return float(cost) if cost else 0.0
            else:
                return 0.0
                
        except Exception as e:
            Logger.log_error(f"获取物料成本价格失败: {str(e)}")
            return 0.0

    def expand_bom_materials(self, plan_id):
        """BOM展开：将成品物料需求展开为原材料和包材需求
        
        Args:
            plan_id: 需求计划ID
            
        Returns:
            dict: 展开结果统计
        """
        try:
            self.db_manager.begin_transaction()
            
            # 获取需求计划的成品物料明细
            details = self.get_demand_plan_details(plan_id)
            if not details:
                raise Exception("未找到需求计划明细")
            
            expand_stats = {
                'total_products': 0,
                'expanded_products': 0,
                'total_materials': 0,
                'failed_products': []
            }
            
            current_time = datetime.now()
            current_user = self.current_user.get('real_name') if self.current_user else 'system'
            
            for detail in details:
                try:
                    # 跳过已展开的明细
                    if detail.get('is_bom_expanded') == 1:
                        continue
                    
                    expand_stats['total_products'] += 1
                    material_id = detail['material_id']
                    required_qty = float(detail['required_qty'])
                    
                    # 获取BOM信息
                    bom_info = self.get_effective_bom(material_id)
                    if not bom_info:
                        expand_stats['failed_products'].append({
                            'material_id': material_id,
                            'reason': '未找到有效BOM'
                        })
                        continue
                    
                    # 获取BOM明细（原材料和包材）
                    bom_details = self.get_bom_material_details_for_demand(material_id)
                    if not bom_details:
                        expand_stats['failed_products'].append({
                            'material_id': material_id,
                            'reason': '未找到BOM明细'
                        })
                        continue
                    
                    # 计算展开数量
                    standard_output = float(bom_info.get('standard_output_qty', 1))
                    expand_ratio = required_qty / standard_output
                    
                    # 保存展开的物料需求
                    for bom_detail in bom_details:
                        self.save_expanded_material(
                            plan_id=plan_id,
                            detail_id=detail['detail_id'],
                            plan_no=detail['plan_no'],
                            parent_material_id=material_id,
                            parent_required_qty=required_qty,
                            bom_info=bom_info,
                            bom_detail=bom_detail,
                            expand_ratio=expand_ratio,
                            required_date=detail['required_date'],
                            current_time=current_time,
                            current_user=current_user
                        )
                        expand_stats['total_materials'] += 1
                    
                    # 更新明细表的展开状态
                    self.update_detail_bom_expanded(detail['detail_id'], bom_info['bom_id'], 
                                                  bom_info['bom_code'], current_time, current_user)
                    
                    expand_stats['expanded_products'] += 1
                    
                except Exception as e:
                    Logger.log_error(f"展开物料 {material_id} 失败: {str(e)}")
                    expand_stats['failed_products'].append({
                        'material_id': material_id,
                        'reason': str(e)
                    })
            
            self.db_manager.commit_transaction()
            Logger.log_info(f"BOM展开完成，计划ID: {plan_id}, 统计: {expand_stats}")
            return expand_stats
            
        except Exception as e:
            self.db_manager.rollback_transaction()
            Logger.log_error(f"BOM展开失败: {str(e)}")
            raise Exception(f"BOM展开失败: {str(e)}")
    
    def get_effective_bom(self, material_id):
        """获取有效的BOM信息"""
        try:
            query = """
            SELECT bom_id, bom_code, bom_name, standard_output_qty, 
                   production_unit, package_qty_per_box
            FROM bom_header 
            WHERE parent_material_id = %s 
                AND status = 1 
                AND effective_date <= CURDATE() 
                AND expiry_date > CURDATE()
            ORDER BY create_time DESC 
            LIMIT 1
            """
            
            result = self.db_manager.execute_query(query, (material_id,))
            return result[0] if result else None
            
        except Exception as e:
            Logger.log_error(f"获取BOM信息失败: {str(e)}")
            return None
    
    def save_expanded_material(self, plan_id, detail_id, plan_no, parent_material_id, 
                             parent_required_qty, bom_info, bom_detail, expand_ratio,
                             required_date, current_time, current_user):
        """保存展开的物料需求"""
        try:
            # 记录输入参数，便于调试
            Logger.log_info(f"BOM展开调试 - 开始保存展开物料 - 物料ID: {bom_detail['material_id']}, 名称: {bom_detail['material_name']}")
            
            # 计算实际需求数量
            unit_required_qty = float(bom_detail['required_qty'])
            total_required_qty = unit_required_qty * expand_ratio
            
            # 考虑损耗率
            scrap_rate = float(bom_detail.get('scrap_rate', 0))
            actual_required_qty = total_required_qty * (1 + scrap_rate / 100)
            
            Logger.log_info(f"BOM展开调试 - 数量计算 - 单位需求: {unit_required_qty}, 展开比例: {expand_ratio}, " +
                          f"总需求: {total_required_qty}, 损耗率: {scrap_rate}%, 实际需求: {actual_required_qty}")
            
            # 判断物料类型（使用BOM明细类型）
            material_type = self.determine_material_type(
                bom_detail['material_id'], 
                bom_detail.get('detail_type')
            )
            
            # 计算成本
            unit_cost = float(bom_detail.get('unit_cost', 0)) if bom_detail.get('unit_cost') else 0
            total_cost = actual_required_qty * unit_cost
            
            Logger.log_info(f"BOM展开调试 - 成本计算 - 单位成本: {unit_cost}, 总成本(原始): {total_cost}, " +
                          f"总成本类型: {type(total_cost)}")
            
            # 限制 total_cost 的范围，防止超出数据库字段定义 decimal(18,4) 的范围
            # 最大值为 10^14 - 0.0001 (考虑到小数点后4位)
            max_value = 99999999999999.9999
            if total_cost > max_value:
                Logger.log_warning(f"物料 {bom_detail['material_id']} 的总成本 {total_cost} 超出范围，已限制为 {max_value}")
                total_cost = max_value
            
            # 检查小数位数
            str_total_cost = str(total_cost)
            if '.' in str_total_cost:
                integer_part, decimal_part = str_total_cost.split('.')
                if len(integer_part) > 14:
                    Logger.log_warning(f"物料 {bom_detail['material_id']} 的总成本整数部分 {integer_part} 超过14位")
                if len(decimal_part) > 4:
                    Logger.log_warning(f"物料 {bom_detail['material_id']} 的总成本小数部分 {decimal_part} 超过4位")
                    # 截断小数部分到4位
                    total_cost = float(f"{integer_part}.{decimal_part[:4]}")
                    Logger.log_info(f"BOM展开调试 - 截断后的总成本: {total_cost}")
            
            # 尝试格式化为字符串，检查是否符合 decimal(18,4) 格式
            try:
                formatted_cost = "{:.4f}".format(total_cost)
                Logger.log_info(f"BOM展开调试 - 格式化后的总成本: {formatted_cost}")
                # 检查整数部分长度
                integer_part = formatted_cost.split('.')[0]
                if len(integer_part) > 14:
                    Logger.log_warning(f"格式化后的总成本整数部分 {integer_part} 仍然超过14位，将被截断")
                    # 强制截断为最大值
                    total_cost = max_value
            except Exception as format_err:
                Logger.log_error(f"格式化总成本时出错: {str(format_err)}")
            
            insert_query = """
            INSERT INTO demand_bom_materials (
                plan_id, detail_id, plan_no, parent_material_id, parent_required_qty,
                bom_id, bom_code, material_id, material_name, material_category_id,
                material_type, unit_required_qty, required_unit, total_required_qty,
                scrap_rate, actual_required_qty, unit_cost, total_cost,
                required_date, execution_status, executed_qty,
                create_time, create_user, update_time, update_user
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s
            )
            """
            
            params = (
                plan_id, detail_id, plan_no, parent_material_id, parent_required_qty,
                bom_info['bom_id'], bom_info['bom_code'], 
                bom_detail['material_id'], bom_detail['material_name'], 
                bom_detail.get('category_id'),
                material_type, unit_required_qty, bom_detail['required_unit'], 
                total_required_qty, scrap_rate, actual_required_qty,
                unit_cost, total_cost, required_date, 0, 0,
                current_time, current_user, current_time, current_user
            )
            
            # 记录最终参数值
            Logger.log_info(f"BOM展开调试 - 最终参数 - 物料ID: {bom_detail['material_id']}, " +
                          f"实际需求: {actual_required_qty}, 单位成本: {unit_cost}, 总成本: {total_cost}")
            
            try:
                self.db_manager.execute_update(insert_query, params)
                Logger.log_info(f"BOM展开调试 - 保存成功 - 物料ID: {bom_detail['material_id']}")
            except Exception as sql_err:
                Logger.log_error(f"BOM展开调试 - SQL执行错误: {str(sql_err)}")
                # 尝试更详细的错误诊断
                error_msg = str(sql_err)
                if "Data truncated for column 'total_cost'" in error_msg:
                    Logger.log_error(f"BOM展开调试 - 总成本数据截断 - 值: {total_cost}, 类型: {type(total_cost)}")
                    # 尝试强制转换为 Decimal 类型
                    from decimal import Decimal, ROUND_DOWN
                    try:
                        decimal_cost = Decimal(str(total_cost)).quantize(Decimal('0.0001'), rounding=ROUND_DOWN)
                        Logger.log_info(f"BOM展开调试 - 尝试使用Decimal: {decimal_cost}")
                        
                        # 再次尝试插入，使用 Decimal 类型
                        params_list = list(params)
                        params_list[17] = decimal_cost  # 替换 total_cost
                        self.db_manager.execute_update(insert_query, tuple(params_list))
                        Logger.log_info(f"BOM展开调试 - 使用Decimal后保存成功")
                        return  # 成功保存，直接返回
                    except Exception as decimal_err:
                        Logger.log_error(f"BOM展开调试 - 使用Decimal尝试失败: {str(decimal_err)}")
                raise  # 重新抛出原始异常
            
        except Exception as e:
            Logger.log_error(f"保存展开物料失败: {str(e)}")
            raise
    
    def determine_material_type(self, material_id, detail_type=None):
        """判断物料类型：1-原材料，2-包材
        
        Args:
            material_id: 物料编码
            detail_type: BOM明细类型（'material' 或 'package'）
        """
        try:
            # 如果有明确的明细类型，直接使用
            if detail_type == 'package':
                return 2  # 包材
            elif detail_type == 'material':
                return 1  # 原材料
            
            # 否则根据物料分类判断
            query = """
            SELECT mc.category_name, mc.category_code
            FROM materials m
            LEFT JOIN material_categories mc ON m.category_id = mc.category_id
            WHERE m.material_id = %s
            """
            
            result = self.db_manager.execute_query(query, (material_id,))
            if result:
                category_name = result[0].get('category_name', '').lower()
                # 简单的分类规则，可根据实际情况调整
                if '包材' in category_name or 'package' in category_name:
                    return 2  # 包材
                else:
                    return 1  # 原材料
            
            return 1  # 默认为原材料
            
        except Exception as e:
            Logger.log_error(f"判断物料类型失败: {str(e)}")
            return 1
    
    def update_detail_bom_expanded(self, detail_id, bom_id, bom_code, current_time, current_user):
        """更新明细表的BOM展开状态"""
        try:
            update_query = """
            UPDATE demand_plan_details 
            SET bom_id = %s, bom_code = %s, is_bom_expanded = 1, 
                bom_expand_time = %s, update_time = %s, update_user = %s
            WHERE detail_id = %s
            """
            
            self.db_manager.execute_update(update_query, (
                bom_id, bom_code, current_time, current_time, current_user, detail_id
            ))
            
        except Exception as e:
            Logger.log_error(f"更新BOM展开状态失败: {str(e)}")
            raise

    def calculate_material_shortage(self, material_id, required_qty):
        """计算物料库存缺口
        
        Args:
            material_id: 物料编码
            required_qty: 需求数量
            
        Returns:
            dict: 库存缺口信息
        """
        try:
            # TODO: 实现库存查询逻辑
            # 这里需要根据实际的库存表结构来实现
            
            # 临时实现：假设库存为0，全部需要采购
            current_stock = 0  # TODO: 从库存表查询实际库存
            
            shortage_qty = max(0, required_qty - current_stock)
            available_qty = min(required_qty, current_stock)
            
            return {
                'material_id': material_id,
                'required_qty': required_qty,
                'current_stock': current_stock,
                'available_qty': available_qty,  # 库存可满足数量
                'shortage_qty': shortage_qty,    # 缺口数量
                'need_purchase': shortage_qty > 0,
                'can_produce': available_qty > 0
            }
            
        except Exception as e:
            Logger.log_error(f"计算物料缺口失败: {str(e)}")
            return {
                'material_id': material_id,
                'required_qty': required_qty,
                'current_stock': 0,
                'available_qty': 0,
                'shortage_qty': required_qty,
                'need_purchase': True,
                'can_produce': False
            }
    
    def calculate_plan_material_shortages(self, plan_id):
        """计算整个计划的物料缺口"""
        try:
            # 获取BOM展开后的物料需求
            query = """
            SELECT material_id, material_name, material_type, required_unit,
                   SUM(actual_required_qty) as total_required_qty
            FROM demand_bom_materials 
            WHERE plan_id = %s AND execution_status = 0
            GROUP BY material_id, material_name, material_type, required_unit
            """
            
            materials = self.db_manager.execute_query(query, (plan_id,))
            shortage_results = []
            
            for material in materials:
                shortage_info = self.calculate_material_shortage(
                    material['material_id'], 
                    material['total_required_qty']
                )
                shortage_info.update({
                    'material_name': material['material_name'],
                    'material_type': material['material_type'],
                    'required_unit': material['required_unit']
                })
                shortage_results.append(shortage_info)
            
            return shortage_results
            
        except Exception as e:
            Logger.log_error(f"计算计划物料缺口失败: {str(e)}")
            return []

    def generate_execution_plans(self, plan_id):
        """生成执行计划：生产计划和采购申请
        
        Args:
            plan_id: 需求计划ID
            
        Returns:
            dict: 执行计划生成结果
        """
        try:
            self.db_manager.begin_transaction()
            
            # 计算物料缺口
            shortage_results = self.calculate_plan_material_shortages(plan_id)
            
            execution_stats = {
                'production_plans': 0,
                'purchase_requests': 0,
                'failed_items': []
            }
            
            current_time = datetime.now()
            current_user = self.current_user.get('real_name') if self.current_user else 'system'
            
            for shortage in shortage_results:
                try:
                    material_id = shortage['material_id']
                    
                    # 生成生产计划（如果有库存可满足部分生产）
                    if shortage['can_produce']:
                        production_result = self.generate_production_plan(
                            plan_id, material_id, shortage, current_time, current_user
                        )
                        if production_result:
                            execution_stats['production_plans'] += 1
                    
                    # 生成采购申请（如果有缺口需要采购）
                    if shortage['need_purchase']:
                        purchase_result = self.generate_purchase_request(
                            plan_id, material_id, shortage, current_time, current_user
                        )
                        if purchase_result:
                            execution_stats['purchase_requests'] += 1
                    
                except Exception as e:
                    Logger.log_error(f"生成执行计划失败，物料: {material_id}, 错误: {str(e)}")
                    execution_stats['failed_items'].append({
                        'material_id': material_id,
                        'reason': str(e)
                    })
            
            self.db_manager.commit_transaction()
            Logger.log_info(f"执行计划生成完成，计划ID: {plan_id}, 统计: {execution_stats}")
            return execution_stats
            
        except Exception as e:
            self.db_manager.rollback_transaction()
            Logger.log_error(f"生成执行计划失败: {str(e)}")
            raise Exception(f"生成执行计划失败: {str(e)}")
    
    def generate_production_plan(self, plan_id, material_id, shortage_info, current_time, current_user):
        """生成生产计划（TODO：需要实现具体的生产计划表操作）"""
        try:
            # TODO: 实现生产计划生成逻辑
            # 1. 创建生产计划记录
            # 2. 更新 demand_plan_details 的 production_plan_id
            # 3. 在 demand_executions 中记录执行跟踪
            
            Logger.log_info(f"TODO: 生成生产计划 - 物料: {material_id}, 数量: {shortage_info['available_qty']}")
            return True
            
        except Exception as e:
            Logger.log_error(f"生成生产计划失败: {str(e)}")
            return False
    
    def generate_purchase_request(self, plan_id, material_id, shortage_info, current_time, current_user):
        """生成采购申请（TODO：需要实现具体的采购申请表操作）"""
        try:
            # TODO: 实现采购申请生成逻辑
            # 1. 创建采购申请记录
            # 2. 更新 demand_bom_materials 的 purchase_request_id
            # 3. 在 demand_executions 中记录执行跟踪
            
            Logger.log_info(f"TODO: 生成采购申请 - 物料: {material_id}, 数量: {shortage_info['shortage_qty']}")
            return True
            
        except Exception as e:
            Logger.log_error(f"生成采购申请失败: {str(e)}")
            return False

    def execute_demand_plan(self, plan_id):
        """执行需求计划：完整的BOM展开和执行计划生成流程
        
        Args:
            plan_id: 需求计划ID
            
        Returns:
            dict: 执行结果统计
        """
        try:
            Logger.log_info(f"开始执行需求计划，ID: {plan_id}")
            
            # 1. BOM展开
            Logger.log_info("步骤1: 开始BOM展开...")
            expand_result = self.expand_bom_materials(plan_id)
            
            # 2. 生成执行计划
            Logger.log_info("步骤2: 开始生成执行计划...")
            execution_result = self.generate_execution_plans(plan_id)
            
            # 3. 更新计划状态为执行中
            self.update_plan_status(plan_id, 2)  # 2-执行中
            
            result = {
                'success': True,
                'expand_result': expand_result,
                'execution_result': execution_result,
                'message': '需求计划执行成功'
            }
            
            Logger.log_info(f"需求计划执行完成，结果: {result}")
            return result
            
        except Exception as e:
            Logger.log_error(f"执行需求计划失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': f'需求计划执行失败: {str(e)}'
            }

    def check_inventory_and_generate_plans(self, plan_id):
        """检查库存并生成生产/采购计划"""
        try:
            from inventory.inventory_service import InventoryService
            
            # 获取需求计划信息
            plan_info = self.get_plan_by_id(plan_id)
            if not plan_info:
                raise Exception("需求计划不存在")
            
            department = plan_info['department']
            
            # 获取BOM展开后的物料需求
            material_requirements = self.get_bom_expanded_materials(plan_id)
            if not material_requirements:
                raise Exception("未找到BOM展开的物料需求")
            
            # 检查库存可用性
            inventory_service = InventoryService(self.current_user)
            availability_result = inventory_service.check_material_availability(
                material_requirements, department
            )
            
            execution_result = {
                'inventory_check': availability_result,
                'production_plans': [],
                'purchase_suggestions': [],
                'reservations': []
            }
            
            # 如果库存充足，直接生成生产计划
            if availability_result['can_produce_all']:
                production_result = self.generate_production_plan_with_inventory(
                    plan_id, availability_result
                )
                execution_result['production_plans'] = production_result
                
                # 预留库存
                reservation_result = self.reserve_materials_for_production(
                    plan_id, material_requirements, department
                )
                execution_result['reservations'] = reservation_result
            
            # 如果库存不足，生成采购建议
            if not availability_result['can_produce_all']:
                purchase_suggestions = inventory_service.generate_purchase_suggestions(
                    availability_result
                )
                execution_result['purchase_suggestions'] = purchase_suggestions
            
            return execution_result
            
        except Exception as e:
            Logger.log_error(f"检查库存并生成计划失败: {str(e)}")
            raise
    
    def get_bom_expanded_materials(self, plan_id):
        """获取BOM展开后的物料需求"""
        try:
            query = """
            SELECT material_id, material_name, material_type, 
                   SUM(actual_required_qty) as required_qty,
                   required_unit, AVG(unit_cost) as unit_cost
            FROM demand_bom_materials 
            WHERE plan_id = %s AND execution_status = 0
            GROUP BY material_id, material_name, material_type, required_unit
            ORDER BY material_type, material_id
            """
            
            return self.db_manager.execute_query(query, (plan_id,))
            
        except Exception as e:
            Logger.log_error(f"获取BOM展开物料失败: {str(e)}")
            return []






