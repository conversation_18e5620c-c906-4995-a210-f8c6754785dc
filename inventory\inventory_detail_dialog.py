"""
库存详情对话框
用于显示库存的详细信息
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QFormLayout, QDialogButtonBox, QFrame, QTextEdit,
                           QGroupBox, QGridLayout, QWidget)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from datetime import datetime

from core.logger import Logger


class InventoryDetailDialog(QDialog):
    """库存详情对话框"""
    
    def __init__(self, parent=None, inventory_data=None):
        super().__init__(parent)
        self.inventory_data = inventory_data or {}
        
        self.setWindowTitle("库存详情")
        self.setModal(True)
        self.resize(600, 500)
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建基本信息区域
        self.create_basic_info_section(main_layout)
        
        # 创建库存信息区域
        self.create_inventory_info_section(main_layout)
        
        # 创建按钮区域
        self.create_button_section(main_layout)
    
    def create_basic_info_section(self, parent_layout):
        """创建基本信息区域"""
        # 基本信息卡片
        basic_card = QFrame()
        basic_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        basic_layout = QVBoxLayout(basic_card)
        basic_layout.setContentsMargins(20, 15, 20, 20)
        basic_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📦 基本信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        basic_layout.addWidget(title_label)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)
        
        # 第一行：物料编码、物料名称
        form_layout.addWidget(QLabel("物料编码:"), 0, 0)
        material_id_edit = QLineEdit(str(self.inventory_data.get('material_id', '')))
        material_id_edit.setReadOnly(True)
        form_layout.addWidget(material_id_edit, 0, 1)
        
        form_layout.addWidget(QLabel("物料名称:"), 0, 2)
        material_name_edit = QLineEdit(str(self.inventory_data.get('material_name', '')))
        material_name_edit.setReadOnly(True)
        form_layout.addWidget(material_name_edit, 0, 3)
        
        # 第二行：物料分类、部门
        form_layout.addWidget(QLabel("物料分类:"), 1, 0)
        category_name_edit = QLineEdit(str(self.inventory_data.get('category_name', '')))
        category_name_edit.setReadOnly(True)
        form_layout.addWidget(category_name_edit, 1, 1)
        
        form_layout.addWidget(QLabel("归属部门:"), 1, 2)
        department_edit = QLineEdit(str(self.inventory_data.get('department', '')))
        department_edit.setReadOnly(True)
        form_layout.addWidget(department_edit, 1, 3)
        
        # 第三行：仓库、单位
        form_layout.addWidget(QLabel("仓库:"), 2, 0)
        warehouse_name = self.inventory_data.get('warehouse_name') or self.inventory_data.get('warehouse_code', '')
        warehouse_edit = QLineEdit(str(warehouse_name))
        warehouse_edit.setReadOnly(True)
        form_layout.addWidget(warehouse_edit, 2, 1)
        
        form_layout.addWidget(QLabel("单位:"), 2, 2)
        unit_edit = QLineEdit(str(self.inventory_data.get('unit', '')))
        unit_edit.setReadOnly(True)
        form_layout.addWidget(unit_edit, 2, 3)
        
        basic_layout.addWidget(form_widget)
        parent_layout.addWidget(basic_card)
    
    def create_inventory_info_section(self, parent_layout):
        """创建库存信息区域"""
        # 库存信息卡片
        inventory_card = QFrame()
        inventory_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        inventory_layout = QVBoxLayout(inventory_card)
        inventory_layout.setContentsMargins(20, 15, 20, 20)
        inventory_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📊 库存信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        inventory_layout.addWidget(title_label)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)
        
        # 第一行：总数量、可用数量
        form_layout.addWidget(QLabel("总数量:"), 0, 0)
        total_qty_edit = QLineEdit(f"{self.inventory_data.get('total_qty', 0):.4f}")
        total_qty_edit.setReadOnly(True)
        form_layout.addWidget(total_qty_edit, 0, 1)
        
        form_layout.addWidget(QLabel("可用数量:"), 0, 2)
        available_qty_edit = QLineEdit(f"{self.inventory_data.get('available_qty', 0):.4f}")
        available_qty_edit.setReadOnly(True)
        form_layout.addWidget(available_qty_edit, 0, 3)
        
        # 第二行：预留数量、冻结数量
        form_layout.addWidget(QLabel("预留数量:"), 1, 0)
        reserved_qty_edit = QLineEdit(f"{self.inventory_data.get('reserved_qty', 0):.4f}")
        reserved_qty_edit.setReadOnly(True)
        form_layout.addWidget(reserved_qty_edit, 1, 1)
        
        form_layout.addWidget(QLabel("冻结数量:"), 1, 2)
        frozen_qty_edit = QLineEdit(f"{self.inventory_data.get('frozen_qty', 0):.4f}")
        frozen_qty_edit.setReadOnly(True)
        form_layout.addWidget(frozen_qty_edit, 1, 3)
        
        # 第三行：平均成本、库存金额
        form_layout.addWidget(QLabel("平均成本:"), 2, 0)
        avg_cost_edit = QLineEdit(f"{self.inventory_data.get('avg_unit_cost', 0):.4f}")
        avg_cost_edit.setReadOnly(True)
        form_layout.addWidget(avg_cost_edit, 2, 1)
        
        form_layout.addWidget(QLabel("库存金额:"), 2, 2)
        total_cost_edit = QLineEdit(f"{self.inventory_data.get('total_cost', 0):.2f}")
        total_cost_edit.setReadOnly(True)
        form_layout.addWidget(total_cost_edit, 2, 3)
        
        # 第四行：最小库存、最大库存
        form_layout.addWidget(QLabel("最小库存:"), 3, 0)
        min_stock_edit = QLineEdit(f"{self.inventory_data.get('min_stock_qty', 0):.4f}")
        min_stock_edit.setReadOnly(True)
        form_layout.addWidget(min_stock_edit, 3, 1)
        
        form_layout.addWidget(QLabel("最大库存:"), 3, 2)
        max_stock_edit = QLineEdit(f"{self.inventory_data.get('max_stock_qty', 0):.4f}")
        max_stock_edit.setReadOnly(True)
        form_layout.addWidget(max_stock_edit, 3, 3)
        
        # 第五行：库存状态
        form_layout.addWidget(QLabel("库存状态:"), 4, 0)
        status_edit = QLineEdit(str(self.inventory_data.get('stock_status', '')))
        status_edit.setReadOnly(True)
        # 根据状态设置颜色
        status = self.inventory_data.get('stock_status', '')
        if status == '缺货':
            status_edit.setStyleSheet("color: #f44336; font-weight: bold;")
        elif status == '库存不足':
            status_edit.setStyleSheet("color: #ff9800; font-weight: bold;")
        elif status == '库存过量':
            status_edit.setStyleSheet("color: #4caf50; font-weight: bold;")
        else:
            status_edit.setStyleSheet("color: #666666;")
        form_layout.addWidget(status_edit, 4, 1)
        
        # 第六行：最后入库日期、最后出库日期
        form_layout.addWidget(QLabel("最后入库:"), 5, 0)
        last_in_date = self.inventory_data.get('last_in_date')
        last_in_edit = QLineEdit(last_in_date.strftime('%Y-%m-%d') if last_in_date else '')
        last_in_edit.setReadOnly(True)
        form_layout.addWidget(last_in_edit, 5, 1)
        
        form_layout.addWidget(QLabel("最后出库:"), 5, 2)
        last_out_date = self.inventory_data.get('last_out_date')
        last_out_edit = QLineEdit(last_out_date.strftime('%Y-%m-%d') if last_out_date else '')
        last_out_edit.setReadOnly(True)
        form_layout.addWidget(last_out_edit, 5, 3)
        
        # 第七行：更新时间
        form_layout.addWidget(QLabel("更新时间:"), 6, 0)
        update_time = self.inventory_data.get('update_time')
        update_time_edit = QLineEdit(update_time.strftime('%Y-%m-%d %H:%M:%S') if update_time else '')
        update_time_edit.setReadOnly(True)
        form_layout.addWidget(update_time_edit, 6, 1, 1, 3)
        
        inventory_layout.addWidget(form_widget)
        parent_layout.addWidget(inventory_card)
    
    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 关闭按钮
        close_btn = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        parent_layout.addLayout(button_layout)
