"""
库存权限服务类
基于现有权限系统，提供库存模块特有的权限控制
"""
from typing import List, Dict, Optional, Any
from functools import wraps
from core.logger import Logger
from core.config import get_current_user
from system.permission_manager import PermissionManager, PermissionDecorator


class InventoryPermissionService:
    """库存权限服务类"""
    
    def __init__(self, current_user=None):
        """
        初始化库存权限服务
        
        Args:
            current_user: 当前用户信息
        """
        self.current_user = current_user or get_current_user() or {}
        self.permission_manager = PermissionManager(
            user_permissions=self.current_user.get('permissions', []),
            legacy_mode=True  # 兼容现有系统
        )
    
    # ==================== 基础权限检查 ====================
    
    def has_permission(self, permission_code: str) -> bool:
        """
        检查是否有指定权限
        
        Args:
            permission_code: 权限代码
            
        Returns:
            bool: 是否有权限
        """
        return self.permission_manager.has_permission(permission_code)
    
    def check_view_permission(self) -> bool:
        """检查库存查看权限"""
        return self.has_permission('inventory:view')
    
    def check_manage_permission(self) -> bool:
        """检查库存管理权限"""
        return self.has_permission('inventory:manage')
    
    def check_initial_permission(self) -> bool:
        """检查期初库存权限"""
        return self.has_permission('inventory:initial')
    
    def check_transfer_permission(self) -> bool:
        """检查库存调拨权限"""
        return self.has_permission('inventory:transfer')
    
    def check_count_permission(self) -> bool:
        """检查库存盘点权限"""
        return self.has_permission('inventory:count')
    
    def check_freeze_permission(self) -> bool:
        """检查库存冻结权限"""
        return self.has_permission('inventory:freeze')
    
    def check_reserve_permission(self) -> bool:
        """检查库存预留权限"""
        return self.has_permission('inventory:reserve')
    
    def check_consume_permission(self) -> bool:
        """检查库存消耗权限"""
        return self.has_permission('inventory:consume')
    
    def check_report_permission(self) -> bool:
        """检查库存报表权限"""
        return self.has_permission('inventory:report')
    
    def check_export_permission(self) -> bool:
        """检查库存导出权限"""
        return self.has_permission('inventory:export')
    
    # ==================== 数据权限检查 ====================
    
    def check_department_permission(self, department: str) -> bool:
        """
        检查部门数据权限
        
        Args:
            department: 部门名称
            
        Returns:
            bool: 是否有权限访问该部门数据
        """
        try:
            # 超级管理员有所有权限
            if self.has_permission('system:admin'):
                return True
            
            # 检查用户部门权限配置
            user_departments = self.current_user.get('departments', [])
            if not user_departments:
                # 如果没有配置部门权限，检查是否是该部门的用户
                user_department = self.current_user.get('department', '')
                return user_department == department
            
            return department in user_departments
            
        except Exception as e:
            Logger.log_error(f"检查部门权限失败: {str(e)}")
            return False
    
    def check_warehouse_permission(self, warehouse_code: str) -> bool:
        """
        检查仓库权限
        
        Args:
            warehouse_code: 仓库编码
            
        Returns:
            bool: 是否有权限访问该仓库
        """
        try:
            # 超级管理员有所有权限
            if self.has_permission('system:admin'):
                return True
            
            # 检查用户仓库权限配置
            user_warehouses = self.current_user.get('warehouses', [])
            if not user_warehouses:
                # 如果没有配置仓库权限，默认允许访问
                return True
            
            return warehouse_code in user_warehouses
            
        except Exception as e:
            Logger.log_error(f"检查仓库权限失败: {str(e)}")
            return False
    
    def check_material_category_permission(self, category_id: int) -> bool:
        """
        检查物料分类权限
        
        Args:
            category_id: 物料分类ID
            
        Returns:
            bool: 是否有权限访问该物料分类
        """
        try:
            # 超级管理员有所有权限
            if self.has_permission('system:admin'):
                return True
            
            # 检查用户物料分类权限配置
            user_categories = self.current_user.get('material_categories', [])
            if not user_categories:
                # 如果没有配置分类权限，默认允许访问
                return True
            
            return category_id in user_categories
            
        except Exception as e:
            Logger.log_error(f"检查物料分类权限失败: {str(e)}")
            return False
    
    def check_inventory_operation_permission(self, operation: str, department: str = None, 
                                           warehouse_code: str = None, category_id: int = None) -> bool:
        """
        检查库存操作权限（综合检查）
        
        Args:
            operation: 操作类型权限代码
            department: 部门名称
            warehouse_code: 仓库编码
            category_id: 物料分类ID
            
        Returns:
            bool: 是否有权限执行该操作
        """
        try:
            # 检查功能权限
            if not self.has_permission(operation):
                return False
            
            # 检查部门权限
            if department and not self.check_department_permission(department):
                return False
            
            # 检查仓库权限
            if warehouse_code and not self.check_warehouse_permission(warehouse_code):
                return False
            
            # 检查物料分类权限
            if category_id and not self.check_material_category_permission(category_id):
                return False
            
            return True
            
        except Exception as e:
            Logger.log_error(f"检查库存操作权限失败: {str(e)}")
            return False
    
    # ==================== 数据过滤方法 ====================
    
    def get_department_filter_condition(self, department_field: str = 'department') -> str:
        """
        获取部门数据过滤条件
        
        Args:
            department_field: 部门字段名
            
        Returns:
            str: SQL过滤条件
        """
        try:
            # 超级管理员查看所有数据
            if self.has_permission('system:admin'):
                return "1=1"
            
            # 获取用户可访问的部门列表
            user_departments = self.current_user.get('departments', [])
            if not user_departments:
                # 如果没有配置部门权限，只能查看自己部门的数据
                user_department = self.current_user.get('department', '')
                if user_department:
                    return f"{department_field} = '{user_department}'"
                else:
                    return "1=0"  # 无部门信息时不显示任何数据
            
            # 构建部门过滤条件
            if len(user_departments) == 1:
                return f"{department_field} = '{user_departments[0]}'"
            else:
                dept_list = "', '".join(user_departments)
                return f"{department_field} IN ('{dept_list}')"
                
        except Exception as e:
            Logger.log_error(f"获取部门过滤条件失败: {str(e)}")
            return "1=0"  # 出错时不显示任何数据
    
    def get_warehouse_filter_condition(self, warehouse_field: str = 'warehouse_code') -> str:
        """
        获取仓库数据过滤条件
        
        Args:
            warehouse_field: 仓库字段名
            
        Returns:
            str: SQL过滤条件
        """
        try:
            # 超级管理员查看所有数据
            if self.has_permission('system:admin'):
                return "1=1"
            
            # 获取用户可访问的仓库列表
            user_warehouses = self.current_user.get('warehouses', [])
            if not user_warehouses:
                # 如果没有配置仓库权限，默认不限制
                return "1=1"
            
            # 构建仓库过滤条件
            if len(user_warehouses) == 1:
                return f"{warehouse_field} = '{user_warehouses[0]}'"
            else:
                warehouse_list = "', '".join(user_warehouses)
                return f"{warehouse_field} IN ('{warehouse_list}')"
                
        except Exception as e:
            Logger.log_error(f"获取仓库过滤条件失败: {str(e)}")
            return "1=1"  # 出错时不限制
    
    def get_category_filter_condition(self, category_field: str = 'material_category_id') -> str:
        """
        获取物料分类数据过滤条件
        
        Args:
            category_field: 分类字段名
            
        Returns:
            str: SQL过滤条件
        """
        try:
            # 超级管理员查看所有数据
            if self.has_permission('system:admin'):
                return "1=1"
            
            # 获取用户可访问的物料分类列表
            user_categories = self.current_user.get('material_categories', [])
            if not user_categories:
                # 如果没有配置分类权限，默认不限制
                return "1=1"
            
            # 构建分类过滤条件
            if len(user_categories) == 1:
                return f"{category_field} = {user_categories[0]}"
            else:
                category_list = ", ".join(map(str, user_categories))
                return f"{category_field} IN ({category_list})"
                
        except Exception as e:
            Logger.log_error(f"获取分类过滤条件失败: {str(e)}")
            return "1=1"  # 出错时不限制
    
    # ==================== 权限装饰器 ====================
    
    def require_inventory_permission(self, permission_code: str, show_message: bool = True):
        """
        库存权限检查装饰器
        
        Args:
            permission_code: 需要的权限代码
            show_message: 是否显示权限不足消息
        """
        def decorator(func):
            @wraps(func)
            def wrapper(self_or_instance, *args, **kwargs):
                # 检查权限
                if not self.has_permission(permission_code):
                    if show_message:
                        try:
                            from PyQt6.QtWidgets import QMessageBox
                            QMessageBox.warning(
                                None,
                                "权限不足", 
                                f"您没有执行此操作的权限\n需要权限：{permission_code}"
                            )
                        except ImportError:
                            Logger.log_warning(f"权限不足，需要权限：{permission_code}")
                    return None
                
                return func(self_or_instance, *args, **kwargs)
            return wrapper
        return decorator
    
    def require_department_permission(self, department_param: str = 'department', show_message: bool = True):
        """
        部门权限检查装饰器
        
        Args:
            department_param: 部门参数名
            show_message: 是否显示权限不足消息
        """
        def decorator(func):
            @wraps(func)
            def wrapper(self_or_instance, *args, **kwargs):
                # 从参数中获取部门信息
                department = None
                if department_param in kwargs:
                    department = kwargs[department_param]
                elif hasattr(self_or_instance, department_param):
                    department = getattr(self_or_instance, department_param)
                
                # 检查部门权限
                if department and not self.check_department_permission(department):
                    if show_message:
                        try:
                            from PyQt6.QtWidgets import QMessageBox
                            QMessageBox.warning(
                                None,
                                "权限不足", 
                                f"您没有访问部门 '{department}' 数据的权限"
                            )
                        except ImportError:
                            Logger.log_warning(f"部门权限不足：{department}")
                    return None
                
                return func(self_or_instance, *args, **kwargs)
            return wrapper
        return decorator
    
    # ==================== 界面权限控制 ====================
    
    def setup_button_permissions(self, buttons: Dict[str, Any]):
        """
        设置按钮权限控制
        
        Args:
            buttons: 按钮字典 {权限代码: 按钮对象}
        """
        try:
            for permission_code, button in buttons.items():
                has_perm = self.has_permission(permission_code)
                
                # 设置按钮状态
                if hasattr(button, 'setEnabled'):
                    button.setEnabled(has_perm)
                
                # 可选：隐藏无权限的按钮
                # if hasattr(button, 'setVisible'):
                #     button.setVisible(has_perm)
                    
        except Exception as e:
            Logger.log_error(f"设置按钮权限失败: {str(e)}")
    
    def setup_menu_permissions(self, menu_items: Dict[str, Any]):
        """
        设置菜单权限控制
        
        Args:
            menu_items: 菜单项字典 {权限代码: 菜单项对象}
        """
        try:
            for permission_code, menu_item in menu_items.items():
                has_perm = self.has_permission(permission_code)
                
                # 设置菜单项状态
                if hasattr(menu_item, 'setEnabled'):
                    menu_item.setEnabled(has_perm)
                
                # 隐藏无权限的菜单项
                if hasattr(menu_item, 'setVisible'):
                    menu_item.setVisible(has_perm)
                    
        except Exception as e:
            Logger.log_error(f"设置菜单权限失败: {str(e)}")
    
    # ==================== 权限信息获取 ====================
    
    def get_user_permissions_info(self) -> Dict[str, Any]:
        """
        获取用户权限信息摘要
        
        Returns:
            Dict[str, Any]: 权限信息
        """
        try:
            inventory_permissions = [
                'inventory:view', 'inventory:manage', 'inventory:initial',
                'inventory:transfer', 'inventory:count', 'inventory:freeze',
                'inventory:reserve', 'inventory:consume', 'inventory:report',
                'inventory:export'
            ]
            
            user_inventory_permissions = []
            for perm in inventory_permissions:
                if self.has_permission(perm):
                    user_inventory_permissions.append(perm)
            
            return {
                'user_id': self.current_user.get('user_id'),
                'username': self.current_user.get('username'),
                'real_name': self.current_user.get('real_name'),
                'department': self.current_user.get('department'),
                'is_admin': self.has_permission('system:admin'),
                'inventory_permissions': user_inventory_permissions,
                'accessible_departments': self.current_user.get('departments', []),
                'accessible_warehouses': self.current_user.get('warehouses', []),
                'accessible_categories': self.current_user.get('material_categories', [])
            }
            
        except Exception as e:
            Logger.log_error(f"获取用户权限信息失败: {str(e)}")
            return {}


# ==================== 全局权限服务实例 ====================

_inventory_permission_service = None

def get_inventory_permission_service() -> InventoryPermissionService:
    """获取库存权限服务实例"""
    global _inventory_permission_service
    if _inventory_permission_service is None:
        _inventory_permission_service = InventoryPermissionService()
    return _inventory_permission_service

def init_inventory_permission_service(current_user=None):
    """初始化库存权限服务"""
    global _inventory_permission_service
    _inventory_permission_service = InventoryPermissionService(current_user)


# ==================== 便捷装饰器 ====================

def require_inventory_view(show_message: bool = True):
    """需要库存查看权限的装饰器"""
    return get_inventory_permission_service().require_inventory_permission('inventory:view', show_message)

def require_inventory_manage(show_message: bool = True):
    """需要库存管理权限的装饰器"""
    return get_inventory_permission_service().require_inventory_permission('inventory:manage', show_message)

def require_inventory_initial(show_message: bool = True):
    """需要期初库存权限的装饰器"""
    return get_inventory_permission_service().require_inventory_permission('inventory:initial', show_message)

def require_inventory_transfer(show_message: bool = True):
    """需要库存调拨权限的装饰器"""
    return get_inventory_permission_service().require_inventory_permission('inventory:transfer', show_message)

def require_department_access(department_param: str = 'department', show_message: bool = True):
    """需要部门访问权限的装饰器"""
    return get_inventory_permission_service().require_department_permission(department_param, show_message)