-- =====================================================
-- 入库收货单主表 (warehouse_receipt)
-- =====================================================
CREATE TABLE warehouse_receipt (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
    receipt_no VARCHAR(50) NOT NULL UNIQUE COMMENT '收货单号（自动生成，格式：CJRK-YYYYMMDD-序号）',
    return_application_id INT NOT NULL COMMENT '关联的退货申请ID',
    return_yewu_no VARCHAR(50) NOT NULL COMMENT '关联的退货申请业务编号',
    receipt_date DATE NOT NULL COMMENT '收货日期',
    receiver VARCHAR(50) COMMENT '收货人',
    warehouse VARCHAR(100) COMMENT '收货仓库',
    receipt_status TINYINT NOT NULL DEFAULT 0 COMMENT '收货状态：0=保存，1=审核，2=关闭',
    total_quantity INT NOT NULL DEFAULT 0 COMMENT '总收货数量',
    total_raw_quantity INT NOT NULL DEFAULT 0 COMMENT '总原料数量',
    total_deformed_quantity INT NOT NULL DEFAULT 0 COMMENT '总变形数量',
    total_to_scrap_quantity INT NOT NULL DEFAULT 0 COMMENT '总待报废数量',
    total_scrapped_quantity INT NOT NULL DEFAULT 0 COMMENT '总报废数量',
    remark TEXT COMMENT '备注',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user VARCHAR(50) COMMENT '创建人',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    update_user VARCHAR(50) COMMENT '最后更新人'
) COMMENT='入库收货单主表';

-- 添加外键约束
ALTER TABLE warehouse_receipt 
ADD CONSTRAINT fk_warehouse_receipt_return_app 
FOREIGN KEY (return_application_id) REFERENCES return_application(id) ON DELETE RESTRICT;

-- 添加索引
CREATE INDEX idx_receipt_no ON warehouse_receipt(receipt_no);
CREATE INDEX idx_return_yewu_no ON warehouse_receipt(return_yewu_no);
CREATE INDEX idx_receipt_date ON warehouse_receipt(receipt_date);
CREATE INDEX idx_receipt_status ON warehouse_receipt(receipt_status);
CREATE INDEX idx_create_time ON warehouse_receipt(create_time);

-- =====================================================
-- 入库收货单明细表 (warehouse_receipt_detail)
-- =====================================================
CREATE TABLE warehouse_receipt_detail (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
    receipt_id INT NOT NULL COMMENT '收货单ID（外键）',
    receipt_no VARCHAR(50) NOT NULL COMMENT '收货单号（冗余字段，便于查询）',
    return_detail_id INT NOT NULL COMMENT '关联的退货申请明细ID',
    waybill_no VARCHAR(50) NOT NULL COMMENT '运单号码',
    dongfang_code VARCHAR(50) NOT NULL COMMENT '东方货号',
    material_code VARCHAR(50) COMMENT '物料编码（从CJ_GOODS表关联获取）',
    material_name VARCHAR(200) COMMENT '物料名称（从materials表关联获取）',
    return_quantity INT NOT NULL COMMENT '退货数量（来自退货申请）',
    received_quantity INT NOT NULL DEFAULT 0 COMMENT '实际收货数量',
    box_spec VARCHAR(50) COMMENT '箱规（从CJ_GOODS表获取）',
    split_code VARCHAR(50) COMMENT '拆分编码（从CJ_GOODS表获取）',
    split_quantity INT DEFAULT 0 COMMENT '拆分数量（基于箱规计算）',
    raw_quantity INT NOT NULL DEFAULT 0 COMMENT '原料数量',
    deformed_quantity INT NOT NULL DEFAULT 0 COMMENT '变形数量',
    to_scrap_quantity INT NOT NULL DEFAULT 0 COMMENT '待报废数量',
    scrapped_quantity INT NOT NULL DEFAULT 0 COMMENT '报废数量',
    batch_no VARCHAR(50) COMMENT '批次号',
    storage_location VARCHAR(100) COMMENT '存储位置',
    remark TEXT COMMENT '明细备注',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user VARCHAR(50) COMMENT '创建人',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    update_user VARCHAR(50) COMMENT '最后更新人'
) COMMENT='入库收货单明细表';

-- 添加外键约束
ALTER TABLE warehouse_receipt_detail 
ADD CONSTRAINT fk_receipt_detail_receipt 
FOREIGN KEY (receipt_id) REFERENCES warehouse_receipt(id) ON DELETE CASCADE;

ALTER TABLE warehouse_receipt_detail 
ADD CONSTRAINT fk_receipt_detail_return_detail 
FOREIGN KEY (return_detail_id) REFERENCES return_application_detail(id) ON DELETE RESTRICT;

-- 添加数据完整性约束
ALTER TABLE warehouse_receipt_detail 
ADD CONSTRAINT chk_quantity_balance 
CHECK (raw_quantity + deformed_quantity + to_scrap_quantity + scrapped_quantity = received_quantity);

-- 添加索引
CREATE INDEX idx_receipt_id ON warehouse_receipt_detail(receipt_id);
CREATE INDEX idx_receipt_no ON warehouse_receipt_detail(receipt_no);
CREATE INDEX idx_return_detail_id ON warehouse_receipt_detail(return_detail_id);
CREATE INDEX idx_waybill_no ON warehouse_receipt_detail(waybill_no);
CREATE INDEX idx_dongfang_code ON warehouse_receipt_detail(dongfang_code);
CREATE INDEX idx_material_code ON warehouse_receipt_detail(material_code);
CREATE INDEX idx_split_code ON warehouse_receipt_detail(split_code);


-- =====================================================
-- 修改退货申请主表，添加入库收货单关联字段
-- =====================================================
ALTER TABLE return_application 
ADD COLUMN is_locked TINYINT NOT NULL DEFAULT 0 COMMENT '锁定状态：0=未锁定，1=已锁定（已生成入库收货单）',
ADD COLUMN warehouse_receipt_no VARCHAR(50) COMMENT '关联的入库收货单号',
ADD INDEX idx_is_locked (is_locked),
ADD INDEX idx_warehouse_receipt_no (warehouse_receipt_no);

-- 检查并修复外键约束
-- 如果外键约束有问题，可以先删除再重新创建

-- 删除现有外键约束（如果存在问题）
 ALTER TABLE warehouse_receipt_detail DROP FOREIGN KEY fk_receipt_detail_receipt;

-- 重新创建外键约束
ALTER TABLE warehouse_receipt_detail 
ADD CONSTRAINT fk_receipt_detail_receipt 
FOREIGN KEY (receipt_id) REFERENCES warehouse_receipt(id) ON DELETE CASCADE;

-- 检查表结构
DESCRIBE warehouse_receipt;
DESCRIBE warehouse_receipt_detail;

-- 检查是否有孤立的数据
SELECT COUNT(*) FROM warehouse_receipt_detail wrd 
LEFT JOIN warehouse_receipt wr ON wrd.receipt_id = wr.id 
WHERE wr.id IS NULL;
