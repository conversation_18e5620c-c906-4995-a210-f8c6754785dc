"""
BOM展开确认对话框
用于在执行需求计划前预览和确认BOM展开结果
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                           QTableWidget, QTableWidgetItem, QPushButton, 
                           QHeaderView, QMessageBox, QFrame, QGroupBox,
                           QProgressDialog, QTextEdit, QSplitter, QTabWidget,
                           QTreeWidget, QTreeWidgetItem, QCheckBox,QWidget)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QIcon, QColor
from demand.demand_plan_db import DemandPlanDB
from core.logger import Logger
from datetime import datetime

class BOMExpandWorker(QThread):
    """BOM展开预览工作线程"""
    progress_updated = pyqtSignal(int, str)  # 进度, 消息
    preview_completed = pyqtSignal(dict)     # 预览结果
    error_occurred = pyqtSignal(str)         # 错误信息
    
    def __init__(self, plan_ids):
        super().__init__()
        self.plan_ids = plan_ids
        self.demand_plan_db = DemandPlanDB()
    
    def run(self):
        """执行BOM展开预览"""
        try:
            total_plans = len(self.plan_ids)
            preview_results = {}
            
            for i, plan_id in enumerate(self.plan_ids):
                self.progress_updated.emit(
                    int((i / total_plans) * 100), 
                    f"正在预览计划 {plan_id} 的BOM展开..."
                )
                
                # 获取计划基本信息
                plan_info = self.demand_plan_db.get_demand_plan_by_id(plan_id)
                if not plan_info:
                    continue
                
                # 获取需求明细
                details = self.demand_plan_db.get_demand_plan_details(plan_id)
                
                # 预览BOM展开结果
                expand_preview = self.preview_bom_expand(plan_id, details)
                
                preview_results[plan_id] = {
                    'plan_info': plan_info,
                    'details': details,
                    'expand_preview': expand_preview
                }
            
            self.progress_updated.emit(100, "BOM展开预览完成")
            self.preview_completed.emit(preview_results)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def preview_bom_expand(self, plan_id, details):
        """预览BOM展开结果（不实际保存到数据库）"""
        try:
            expand_results = []
            
            for detail in details:
                # 跳过已展开的明细
                if detail.get('is_bom_expanded') == 1:
                    continue
                
                material_id = detail['material_id']
                required_qty = float(detail['required_qty'])
                
                # 获取BOM信息
                bom_info = self.demand_plan_db.get_effective_bom(material_id)
                if not bom_info:
                    expand_results.append({
                        'detail_id': detail['detail_id'],
                        'parent_material_id': material_id,
                        'parent_material_name': detail['material_name'],
                        'parent_required_qty': required_qty,
                        'status': 'no_bom',
                        'message': '未找到有效BOM',
                        'materials': [],
                        'packages': []
                    })
                    continue
                
                # 获取BOM明细（包含原料和包材）
                bom_details = self.demand_plan_db.get_bom_material_details_for_demand(material_id)
                if not bom_details:
                    expand_results.append({
                        'detail_id': detail['detail_id'],
                        'parent_material_id': material_id,
                        'parent_material_name': detail['material_name'],
                        'parent_required_qty': required_qty,
                        'status': 'no_details',
                        'message': '未找到BOM明细',
                        'materials': [],
                        'packages': []
                    })
                    continue
                
                # 计算展开结果
                standard_output = float(bom_info.get('standard_output_qty', 1))
                expand_ratio = required_qty / standard_output
                
                materials = []  # 原料
                packages = []   # 包材
                
                for bom_detail in bom_details:
                    unit_required_qty = float(bom_detail['required_qty'])
                    total_required_qty = unit_required_qty * expand_ratio
                    
                    # 考虑损耗率
                    scrap_rate = float(bom_detail.get('scrap_rate', 0))
                    actual_required_qty = total_required_qty * (1 + scrap_rate / 100)
                    
                    # 计算成本和总成本
                    unit_cost = float(bom_detail.get('unit_cost', 0))
                    total_cost = actual_required_qty * unit_cost
                    
                    # 限制 total_cost 的范围，防止超出数据库字段定义 decimal(18,4) 的范围
                    max_value = 99999999999999.9999
                    if total_cost > max_value:
                        total_cost = max_value
                    
                    detail_info = {
                        'material_id': bom_detail['material_id'],
                        'material_name': bom_detail['material_name'],
                        'unit_required_qty': unit_required_qty,
                        'total_required_qty': total_required_qty,
                        'scrap_rate': scrap_rate,
                        'actual_required_qty': actual_required_qty,
                        'required_unit': bom_detail['required_unit'],
                        'unit_cost': unit_cost,
                        'total_cost': total_cost
                    }
                    
                    # 根据明细类型分类
                    if bom_detail.get('detail_type') == 'package':
                        packages.append(detail_info)
                    else:
                        materials.append(detail_info)
                
                total_items = len(materials) + len(packages)
                expand_results.append({
                    'detail_id': detail['detail_id'],
                    'parent_material_id': material_id,
                    'parent_material_name': detail['material_name'],
                    'parent_required_qty': required_qty,
                    'bom_info': bom_info,
                    'status': 'success',
                    'message': f'可展开 {total_items} 个物料（原料:{len(materials)}, 包材:{len(packages)}）',
                    'materials': materials,
                    'packages': packages
                })
            
            return expand_results
            
        except Exception as e:
            Logger.log_error(f"预览BOM展开失败: {str(e)}")
            return []

class BOMExpandConfirmDialog(QDialog):
    """BOM展开确认对话框"""
    
    def __init__(self, parent=None, selected_plans=None):
        super().__init__(parent)
        self.selected_plans = selected_plans or []
        self.demand_plan_db = DemandPlanDB()
        self.preview_results = {}
        
        self.setWindowTitle("BOM展开确认")
        self.setModal(True)
        self.resize(1200, 800)
        
        self.setup_ui()
        self.start_preview()
    
    def setup_ui(self):
        """设置界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("需求计划BOM展开确认")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #262626;
                padding: 10px 0px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 计划概览选项卡
        self.create_overview_tab()
        
        # BOM展开预览选项卡
        self.create_preview_tab()
        
        # 创建按钮区域
        self.create_button_section(main_layout)
    
    def create_overview_tab(self):
        """创建计划概览选项卡"""
        overview_widget = QWidget()
        layout = QVBoxLayout(overview_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 选中计划信息
        info_label = QLabel(f"已选择 {len(self.selected_plans)} 个需求计划进行BOM展开：")
        info_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(info_label)
        
        # 计划列表表格
        self.plans_table = QTableWidget()
        self.plans_table.setColumnCount(6)
        self.plans_table.setHorizontalHeaderLabels([
            "计划编号", "申请部门", "申请人", "计划日期", "状态", "明细数量"
        ])
        
        # 设置表格样式
        self.plans_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.plans_table.setAlternatingRowColors(True)
        self.plans_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.plans_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)   
        
        # 填充计划数据
        self.fill_plans_table()
        
        layout.addWidget(self.plans_table)
        self.tab_widget.addTab(overview_widget, "📋 计划概览")
    
    def create_preview_tab(self):
        """创建BOM展开预览选项卡"""
        preview_widget = QWidget()
        layout = QVBoxLayout(preview_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 预览说明
        info_label = QLabel("BOM展开预览结果（预览模式，不会实际保存到数据库）：")
        info_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #1890ff;")
        layout.addWidget(info_label)
        
        # 创建树形控件显示展开结果
        self.preview_tree = QTreeWidget()
        self.preview_tree.setHeaderLabels([
            "物料信息", "需求数量", "单位", "损耗率", "实际需求", "单价", "总金额", "状态"
        ])
        
        # 设置列宽
        header = self.preview_tree.header()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.preview_tree)
        self.tab_widget.addTab(preview_widget, "🔍 BOM预览")
    
    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #d9d9d9;
                color: #595959;
                border: 1px solid #d9d9d9;
                padding: 10px 24px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #bfbfbf;
                border-color: #bfbfbf;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        # 确认执行按钮
        self.confirm_btn = QPushButton("确认执行BOM展开")
        self.confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 10px 24px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #bfbfbf;
            }
        """)
        self.confirm_btn.clicked.connect(self.confirm_execute)
        self.confirm_btn.setEnabled(False)  # 初始禁用，预览完成后启用
        button_layout.addWidget(self.confirm_btn)
        
        parent_layout.addLayout(button_layout)
    
    def fill_plans_table(self):
        """填充计划表格数据"""
        self.plans_table.setRowCount(len(self.selected_plans))
        
        for row, plan in enumerate(self.selected_plans):
            self.plans_table.setItem(row, 0, QTableWidgetItem(plan.get('plan_no', '')))
            self.plans_table.setItem(row, 1, QTableWidgetItem(plan.get('department', '')))
            self.plans_table.setItem(row, 2, QTableWidgetItem(plan.get('applicant', '')))
            self.plans_table.setItem(row, 3, QTableWidgetItem(str(plan.get('plan_date', ''))))
            self.plans_table.setItem(row, 4, QTableWidgetItem(plan.get('plan_status_name', '')))
            self.plans_table.setItem(row, 5, QTableWidgetItem("计算中..."))
    
    def start_preview(self):
        """开始BOM展开预览"""
        plan_ids = [plan['plan_id'] for plan in self.selected_plans]
        
        # 创建进度对话框
        self.progress_dialog = QProgressDialog("正在预览BOM展开...", "取消", 0, 100, self)
        self.progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
        self.progress_dialog.setAutoClose(True)
        self.progress_dialog.show()
        
        # 创建工作线程
        self.worker = BOMExpandWorker(plan_ids)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.preview_completed.connect(self.on_preview_completed)
        self.worker.error_occurred.connect(self.on_preview_error)
        self.worker.start()
    
    def update_progress(self, value, message):
        """更新进度"""
        self.progress_dialog.setValue(value)
        self.progress_dialog.setLabelText(message)
    
    def on_preview_completed(self, results):
        """预览完成处理"""
        self.progress_dialog.close()
        self.preview_results = results
        
        # 更新计划表格的明细数量
        self.update_plans_table_details()
        
        # 填充预览树
        self.fill_preview_tree()
        
        # 启用确认按钮
        self.confirm_btn.setEnabled(True)
        
        # 切换到预览选项卡
        self.tab_widget.setCurrentIndex(1)
    
    def on_preview_error(self, error_message):
        """预览错误处理"""
        self.progress_dialog.close()
        QMessageBox.critical(self, "预览失败", f"BOM展开预览失败：{error_message}")
        self.reject()
    
    def update_plans_table_details(self):
        """更新计划表格的明细数量"""
        for row, plan in enumerate(self.selected_plans):
            plan_id = plan['plan_id']
            if plan_id in self.preview_results:
                details_count = len(self.preview_results[plan_id]['details'])
                self.plans_table.setItem(row, 5, QTableWidgetItem(str(details_count)))
    
    def fill_preview_tree(self):
        """填充预览树"""
        self.preview_tree.clear()
        
        for plan_id, result in self.preview_results.items():
            plan_info = result['plan_info']
            expand_preview = result['expand_preview']
            
            # 创建计划根节点
            plan_item = QTreeWidgetItem(self.preview_tree)
            plan_item.setText(0, f"📋 {plan_info['plan_no']} - {plan_info['plan_name']}")
            plan_item.setText(7, f"共 {len(expand_preview)} 个成品")
            
            # 设置计划节点样式
            font = QFont()
            font.setBold(True)
            plan_item.setFont(0, font)
            plan_item.setBackground(0, QColor(240, 248, 255))
            
            for expand_result in expand_preview:
                # 创建成品节点
                product_item = QTreeWidgetItem(plan_item)
                product_item.setText(0, f"🏭 {expand_result['parent_material_name']}")
                product_item.setText(1, str(expand_result['parent_required_qty']))
                product_item.setText(7, expand_result['message'])
                
                # 根据状态设置颜色
                if expand_result['status'] == 'success':
                    product_item.setBackground(7, QColor(240, 255, 240))
                else:
                    product_item.setBackground(7, QColor(255, 240, 240))
                
                # 添加原料明细
                materials = expand_result.get('materials', [])
                if materials:
                    material_group = QTreeWidgetItem(product_item)
                    material_group.setText(0, f"🧪 原料明细 ({len(materials)}项)")
                    material_group.setBackground(0, QColor(248, 255, 248))
                    
                    for material in materials:
                        material_item = QTreeWidgetItem(material_group)
                        material_item.setText(0, f"  📦 {material['material_name']}")
                        material_item.setText(1, f"{material['total_required_qty']:.2f}")
                        material_item.setText(2, material['required_unit'])
                        material_item.setText(3, f"{material['scrap_rate']:.1f}%")
                        material_item.setText(4, f"{material['actual_required_qty']:.2f}")
                        material_item.setText(5, f"¥{material['unit_cost']:.2f}")
                        material_item.setText(6, f"¥{material['total_cost']:.2f}")
                        material_item.setText(7, "原料")
                
                # 添加包材明细
                packages = expand_result.get('packages', [])
                if packages:
                    package_group = QTreeWidgetItem(product_item)
                    package_group.setText(0, f"📦 包材明细 ({len(packages)}项)")
                    package_group.setBackground(0, QColor(255, 248, 248))
                    
                    for package in packages:
                        package_item = QTreeWidgetItem(package_group)
                        package_item.setText(0, f"  📦 {package['material_name']}")
                        package_item.setText(1, f"{package['total_required_qty']:.2f}")
                        package_item.setText(2, package['required_unit'])
                        package_item.setText(3, f"{package['scrap_rate']:.1f}%")
                        package_item.setText(4, f"{package['actual_required_qty']:.2f}")
                        package_item.setText(5, f"¥{package['unit_cost']:.2f}")
                        package_item.setText(6, f"¥{package['total_cost']:.2f}")
                        package_item.setText(7, "包材")
                
                # 如果没有明细，显示提示
                if not materials and not packages and expand_result['status'] == 'success':
                    no_detail_item = QTreeWidgetItem(product_item)
                    no_detail_item.setText(0, "  ⚠️ 未找到BOM明细")
                    no_detail_item.setBackground(0, QColor(255, 255, 240))
            
            # 展开计划节点
            plan_item.setExpanded(True)
            # 展开成品节点
            for i in range(plan_item.childCount()):
                plan_item.child(i).setExpanded(True)
    
    def confirm_execute(self):
        """确认执行BOM展开"""
        reply = QMessageBox.question(
            self, "确认执行",
            f"确定要执行 {len(self.selected_plans)} 个需求计划的BOM展开吗？\n"
            f"此操作将：\n"
            f"1. 将成品物料需求展开为原材料和包材需求\n"
            f"2. 更新计划状态为'执行中'\n"
            f"3. 生成相应的执行计划\n\n"
            f"执行后将无法撤销，是否继续？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.accept()

