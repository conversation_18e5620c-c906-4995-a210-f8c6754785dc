-- =====================================================
-- 需求计划表结构更新脚本
-- 执行日期: 2025-01-21
-- 说明: 移除冲突字段和不需要的字段
-- =====================================================

USE migosys;

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 1. 更新需求计划主表 (demand_plans)
-- =====================================================

-- 检查并删除 required_date 字段（如果存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'migosys' 
     AND TABLE_NAME = 'demand_plans' 
     AND COLUMN_NAME = 'required_date') > 0,
    'ALTER TABLE demand_plans DROP COLUMN required_date',
    'SELECT "字段 required_date 不存在于 demand_plans 表中" as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 2. 更新需求计划明细表 (demand_plan_details)
-- =====================================================

-- 检查并删除 current_stock_qty 字段（如果存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'migosys' 
     AND TABLE_NAME = 'demand_plan_details' 
     AND COLUMN_NAME = 'current_stock_qty') > 0,
    'ALTER TABLE demand_plan_details DROP COLUMN current_stock_qty',
    'SELECT "字段 current_stock_qty 不存在于 demand_plan_details 表中" as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并删除 shortage_qty 字段（如果存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'migosys' 
     AND TABLE_NAME = 'demand_plan_details' 
     AND COLUMN_NAME = 'shortage_qty') > 0,
    'ALTER TABLE demand_plan_details DROP COLUMN shortage_qty',
    'SELECT "字段 shortage_qty 不存在于 demand_plan_details 表中" as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 3. 验证表结构
-- =====================================================

-- 显示更新后的表结构
SELECT '=== 需求计划主表结构 ===' as info;
DESCRIBE demand_plans;

SELECT '=== 需求计划明细表结构 ===' as info;
DESCRIBE demand_plan_details;

-- =====================================================
-- 4. 检查外键约束是否正常
-- =====================================================

SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'migosys' 
AND TABLE_NAME IN ('demand_plans', 'demand_plan_details')
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 提交事务
COMMIT;

-- =====================================================
-- 执行完成提示
-- =====================================================
SELECT 
    '需求计划表结构更新完成！' as message,
    NOW() as update_time;

-- =====================================================
-- 备注说明
-- =====================================================
/*
更新内容说明：

1. 需求计划主表 (demand_plans)：
   - 移除 required_date 字段：需求日期应该在明细表中管理

2. 需求计划明细表 (demand_plan_details)：
   - 移除 current_stock_qty 字段：当前库存应该实时查询
   - 移除 shortage_qty 字段：缺口数量应该动态计算

3. 保留字段：
   - 明细表中的 required_date：每个物料的具体需求日期
   - execution_status 和 executed_qty：执行状态跟踪

执行方式：
1. 在MySQL客户端中执行此脚本
2. 或者使用命令行：mysql -u用户名 -p < update_demand_tables.sql

注意事项：
- 脚本使用了动态SQL来检查字段是否存在，避免重复删除错误
- 使用了事务确保操作的原子性
- 执行前建议备份相关表数据
*/