from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
from datetime import datetime, date
import traceback
from .dongfang_return_db import DongfangReturnDB
from material.material_search_dialog import MaterialSearchDialog
from material.material_db import MaterialDB

class WarehouseReceiptDialog(QDialog):
    """入库收货单详情对话框"""
    
    def __init__(self, parent=None, receipt_id=None, mode='view'):
        super().__init__(parent)
        self.receipt_id = receipt_id
        self.mode = mode  # 'view', 'edit'
        self.dongfang_db = DongfangReturnDB()
        self.material_db = MaterialDB()  # 添加物料数据库实例
        self.receipt_data = None
        
        self.setWindowTitle("入库收货单详情")
        self.setModal(True)
        self.resize(1200, 800)
        
        self.init_ui()
        if self.receipt_id:
            self.load_data()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建工具栏
        self.create_toolbar(layout)
        
        # 创建主表信息区域
        self.create_main_info_area(layout)
        
        # 创建明细表格
        self.create_detail_table(layout)
        
        # # 创建按钮区域
        # self.create_button_area(layout)
    
    def create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar_layout = QHBoxLayout()
        
        if self.mode == 'edit':
            # 提交按钮（原保存按钮）
            submit_btn = QPushButton("提交")
            submit_btn.setIcon(QIcon("icons/save.png"))
            submit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #1890ff;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #40a9ff;
                }
                QPushButton:pressed {
                    background-color: #096dd9;
                }
            """)
            submit_btn.clicked.connect(self.submit_receipt)
            toolbar_layout.addWidget(submit_btn)
            self.submit_action = submit_btn
            
            # 审核按钮
            audit_btn = QPushButton("审核")
            audit_btn.setIcon(QIcon("icons/audit.png"))
            audit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #52c41a;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #73d13d;
                }
                QPushButton:pressed {
                    background-color: #389e0d;
                }
            """)
            audit_btn.clicked.connect(self.audit_receipt)
            toolbar_layout.addWidget(audit_btn)
            self.audit_action = audit_btn
            
            # 删除按钮 - 只有保存状态可以删除
            delete_btn = QPushButton("删除")
            delete_btn.setIcon(QIcon("icons/delete.png"))
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ff4d4f;
                    color: white;
                    border: none;
                    padding: 5px 15px;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #ff7875;
                }
                QPushButton:pressed {
                    background-color: #d9363e;
                }
            """)
            delete_btn.clicked.connect(self.delete_receipt)
            toolbar_layout.addWidget(delete_btn)
            self.delete_action = delete_btn
        
        # 反审核按钮 - 所有模式都可能需要
        unaudit_btn = QPushButton("反审核")
        unaudit_btn.setIcon(QIcon("icons/unaudit.png"))
        unaudit_btn.setStyleSheet("""
            QPushButton {
                background-color: #faad14;
                color: white;
                border: none;
                padding: 5px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ffd666;
            }
            QPushButton:pressed {
                background-color: #d48806;
            }
        """)
        unaudit_btn.clicked.connect(self.unaudit_receipt)
        toolbar_layout.addWidget(unaudit_btn)
        self.unaudit_action = unaudit_btn
        
        # 打印按钮
        print_btn = QPushButton("打印")
        print_btn.setIcon(QIcon("icons/print.png"))
        print_btn.clicked.connect(self.print_receipt)
        toolbar_layout.addWidget(print_btn)
        self.print_action = print_btn
        
        # 关闭按钮 - 移动到打印按钮后面
        close_btn = QPushButton("关闭")
        close_btn.setIcon(QIcon("icons/close.png"))
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4d4f;
                color: white;
                border: none;
                padding: 5px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #a6a6a6;
            }
            QPushButton:pressed {
                background-color: #737373;
            }
        """)
        close_btn.clicked.connect(self.reject)
        toolbar_layout.addWidget(close_btn)
        
        toolbar_layout.addStretch()
        parent_layout.addLayout(toolbar_layout)
    
    def create_main_info_area(self, parent_layout):
        """创建主表信息区域"""
        main_frame = QFrame()
        main_frame.setFrameStyle(QFrame.Shape.Box)
        main_frame.setStyleSheet("QFrame { border: 1px solid #ddd; border-radius: 5px; padding: 10px; }")
        
        main_layout = QGridLayout(main_frame)
        main_layout.setSpacing(10)
        
        # 第一行
        main_layout.addWidget(QLabel("收货单号:"), 0, 0)
        self.receipt_no_label = QLabel()
        self.receipt_no_label.setStyleSheet("font-weight: bold; color: #0066cc;")
        main_layout.addWidget(self.receipt_no_label, 0, 1)
        #修改成可选中复制
        self.receipt_no_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        
        main_layout.addWidget(QLabel("退货申请号:"), 0, 2)
        self.return_yewu_no_label = QLabel()
        main_layout.addWidget(self.return_yewu_no_label, 0, 3)
        self.return_yewu_no_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        
        main_layout.addWidget(QLabel("收货状态:"), 0, 4)
        self.status_label = QLabel()
        main_layout.addWidget(self.status_label, 0, 5)
        
        # 第二行
        main_layout.addWidget(QLabel("收货日期:"), 1, 0)
        self.receipt_date_edit = QDateEdit()
        self.receipt_date_edit.setCalendarPopup(True)
        self.receipt_date_edit.setReadOnly(self.mode == 'view')
        main_layout.addWidget(self.receipt_date_edit, 1, 1)
        
        main_layout.addWidget(QLabel("收货人:"), 1, 2)
        self.receiver_input = QLineEdit()
        self.receiver_input.setReadOnly(self.mode == 'view')
        main_layout.addWidget(self.receiver_input, 1, 3)
        
        main_layout.addWidget(QLabel("收货仓库:"), 1, 4)
        self.warehouse_input = QLineEdit()
        self.warehouse_input.setReadOnly(self.mode == 'view')
        main_layout.addWidget(self.warehouse_input, 1, 5)
        
        # 第三行 - 数量统计
        main_layout.addWidget(QLabel("总数量:"), 2, 0)
        self.total_quantity_label = QLabel()
        self.total_quantity_label.setStyleSheet("font-weight: bold;")
        main_layout.addWidget(self.total_quantity_label, 2, 1)
        
        main_layout.addWidget(QLabel("原料数量:"), 2, 2)
        self.raw_quantity_label = QLabel()
        self.raw_quantity_label.setStyleSheet("color: green;")
        main_layout.addWidget(self.raw_quantity_label, 2, 3)
        
        main_layout.addWidget(QLabel("变形数量:"), 2, 4)
        self.deformed_quantity_label = QLabel()
        self.deformed_quantity_label.setStyleSheet("color: orange;")
        main_layout.addWidget(self.deformed_quantity_label, 2, 5)
        
        # 第四行
        main_layout.addWidget(QLabel("待报废数量:"), 3, 0)
        self.to_scrap_quantity_label = QLabel()
        self.to_scrap_quantity_label.setStyleSheet("color: red;")
        main_layout.addWidget(self.to_scrap_quantity_label, 3, 1)
        
        main_layout.addWidget(QLabel("报废数量:"), 3, 2)
        self.scrapped_quantity_label = QLabel()
        self.scrapped_quantity_label.setStyleSheet("color: gray;")
        main_layout.addWidget(self.scrapped_quantity_label, 3, 3)
        
        parent_layout.addWidget(main_frame)
    
    def create_detail_table(self, parent_layout):
        """创建明细表格"""
        # 表格标题
        detail_label = QLabel("收货明细:")
        detail_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        parent_layout.addWidget(detail_label)
        
        self.detail_table = QTableWidget()
        self.detail_table.setColumnCount(17)  # 保持17列
        
        headers = [
            "运单号码", "东方货号", "物料编码", "更新料号", "物料名称", "退货数量",
            "拆分编号", "箱规", "拆分数量", "实收数量", "原料数量", "变形数量",
            "待报废数量", "报废数量", "批次号", "存储位置", "备注"
        ]
        
        self.detail_table.setHorizontalHeaderLabels(headers)
        
        # 设置列宽
        column_widths = [100, 120, 100, 120, 200, 80, 100, 60, 80, 80, 80, 80, 80, 80, 100, 100, 150]
        for i, width in enumerate(column_widths):
            self.detail_table.setColumnWidth(i, width)
        
        # 设置表格属性
        self.detail_table.setAlternatingRowColors(True)
        # 修改选择行为：从整行选择改为单个单元格选择
        self.detail_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectItems)
        self.detail_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.detail_table.verticalHeader().setVisible(False)
        
        # 连接单元格点击事件
        self.detail_table.cellClicked.connect(self.on_cell_clicked)
        
        parent_layout.addWidget(self.detail_table)

    def on_cell_clicked(self, row, column):
        """处理单元格点击事件"""
        # 只有在编辑模式下，点击更新料号列(第3列)时才显示搜索按钮
        if self.mode == 'edit' and column == 3:
            self.show_material_search_for_row(row)

    def show_material_search_for_row(self, row):
        """为指定行显示物料搜索对话框"""
        try:
            dialog = MaterialSearchDialog(self)
            dialog.material_selected.connect(lambda material: self.on_material_selected_for_row(material, row))
            dialog.exec()
        except Exception as e:
            QMessageBox.warning(self, "警告", f"打开物料搜索失败：{str(e)}")

    def on_material_selected_for_row(self, material, row):
        """处理选中的物料，更新指定行的相关字段"""
        try:
            if not material:
                return
            
            print(f"更新第{row}行物料信息: {material}")
            
            # 更新料号（第3列）
            self._set_cell_value(row, 3, material.get("material_id", ""))
            
            # 更新物料名称（第4列）- 强制更新
            material_name = material.get("name", "")
            print(f"准备更新物料名称为: {material_name}")
            self._set_cell_value(row, 4, material_name)
            
            # 验证更新是否成功
            updated_name = self._get_cell_text_value(row, 4)
            print(f"更新后的物料名称: {updated_name}")
            
            # 获取物料的其他信息并更新
            material_id = material.get("material_id", "")
            if material_id:
                # 从CJ_GOODS表获取拆分信息
                cj_goods_info = self._get_cj_goods_info(material_id)
                if cj_goods_info:
                    print(f"获取到CJ_GOODS信息: {cj_goods_info}")
                    
                    # 更新拆分编号（第6列）
                    self._set_cell_value(row, 6, cj_goods_info.get("split_code", ""))
                    
                    # 更新箱规（第7列）
                    self._set_cell_value(row, 7, cj_goods_info.get("box_spec", ""))
                    
                    # 计算并更新拆分数量（第8列）
                    return_quantity = self._get_cell_int_value(row, 5)  # 退货数量
                    box_spec = cj_goods_info.get("box_spec", "")
                    split_quantity = self._calculate_split_quantity(return_quantity, box_spec)
                    self._set_cell_value(row, 8, split_quantity)
                    
                    print(f"更新后的数据 - 拆分编号: {cj_goods_info.get('split_code', '')}, 箱规: {box_spec}, 拆分数量: {split_quantity}")
            
            # 触发数量汇总更新
            self.update_quantity_summary()
            
            QMessageBox.information(self, "成功", f"已更新物料信息：{material_name}")
            
        except Exception as e:
            QMessageBox.warning(self, "警告", f"更新物料信息失败：{str(e)}")

    def _get_cj_goods_info(self, material_id):
        """根据物料编码获取CJ_GOODS信息"""
        try:
            from material.cj_goods_db import CJGoodsDB
            cj_goods_db = CJGoodsDB()
            
            # 通过物料编码查询CJ_GOODS
            params = {"material_id": material_id, "page": 1, "page_size": 1}
            _, goods_list = cj_goods_db.get_cj_goods(params)
            
            if goods_list:
                return goods_list[0]
            return None
        
        except Exception as e:
            print(f"获取CJ_GOODS信息失败：{str(e)}")
            return None

    def _calculate_split_quantity(self, return_quantity, box_spec):
        """计算拆分数量"""
        try:
            if not box_spec or return_quantity <= 0:
                return 0
            
            # 从箱规中提取数字（例如："50ml*10瓶" -> 10）
            import re
            numbers = re.findall(r'\d+', str(box_spec))
            if numbers:
                # 取最后一个数字作为箱规数量
                spec_quantity = int(numbers[-1])
                return return_quantity * spec_quantity
            
            return 0
        
        except Exception as e:
            print(f"计算拆分数量失败：{str(e)}")
            return 0

    # def create_button_area(self, parent_layout):
    #     """创建按钮区域"""
    #     button_layout = QHBoxLayout()
    #     button_layout.addStretch()
        
    #     # 关闭按钮
    #     close_btn = QPushButton("关闭")
    #     close_btn.clicked.connect(self.reject)
    #     button_layout.addWidget(close_btn)
        
    #     parent_layout.addLayout(button_layout)
    
    def load_data(self):
        """加载数据"""
        try:
            # 获取收货单详情
            receipt_data = self.dongfang_db.get_warehouse_receipt_details(self.receipt_id)
            
            if not receipt_data:
                QMessageBox.warning(self, "警告", "未找到收货单数据")
                return
            
            main_data = receipt_data['main']
            details = receipt_data['details']
            
            print(f"加载收货单数据 - 主表数据: {main_data}")
            print(f"加载收货单数据 - 明细数量: {len(details)}")
            
            # 填充主表数据
            self.receipt_no_label.setText(main_data.get('receipt_no', ''))
            self.return_yewu_no_label.setText(main_data.get('return_yewu_no', ''))
            
            # 设置状态显示
            status_text = ""
            status_value = main_data.get('receipt_status', 0)
            if status_value == 0:
                status_text = "保存"
                self.status_label.setStyleSheet("color: orange; font-weight: bold;")
            elif status_value == 1:
                status_text = "审核"
                self.status_label.setStyleSheet("color: green; font-weight: bold;")
            elif status_value == 2:
                status_text = "关闭"
                self.status_label.setStyleSheet("color: red; font-weight: bold;")
            self.status_label.setText(status_text)
            
            # 设置日期
            receipt_date = main_data.get('receipt_date')
            if receipt_date:
                if isinstance(receipt_date, str):
                    from datetime import datetime
                    try:
                        receipt_date = datetime.strptime(receipt_date, '%Y-%m-%d').date()
                    except ValueError:
                        receipt_date = datetime.strptime(receipt_date[:10], '%Y-%m-%d').date()
                self.receipt_date_edit.setDate(receipt_date)
            
            self.receiver_input.setText(main_data.get('receiver', ''))
            self.warehouse_input.setText(main_data.get('warehouse', ''))
            
            # 设置数量统计
            self.total_quantity_label.setText(str(main_data.get('total_quantity', 0)))
            self.raw_quantity_label.setText(str(main_data.get('total_raw_quantity', 0)))
            self.deformed_quantity_label.setText(str(main_data.get('total_deformed_quantity', 0)))
            self.to_scrap_quantity_label.setText(str(main_data.get('total_to_scrap_quantity', 0)))
            self.scrapped_quantity_label.setText(str(main_data.get('total_scrapped_quantity', 0)))
            
            # 填充明细数据
            self.populate_detail_table(details)
            
            # 立即更新按钮状态 - 这是关键
            self.update_button_states(status_value)
            
        except Exception as e:
            print(f"加载数据异常: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载数据失败：{str(e)}")

    def populate_detail_table(self, details):
        """填充明细表格数据"""
        print(f"开始填充明细表格，共{len(details)}条记录")
        
        self.detail_table.setRowCount(len(details))
        
        for row, detail in enumerate(details):
            print(f"处理第{row}行明细: {detail}")
            try:
                # 运单号码 - 存储明细ID，不可编辑
                waybill_item = QTableWidgetItem(str(detail.get('waybill_no', '')))
                waybill_item.setData(Qt.ItemDataRole.UserRole, detail.get('id'))
                waybill_item.setFlags(waybill_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                waybill_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 0, waybill_item)
                
                # 东方货号 - 不可编辑
                dongfang_item = QTableWidgetItem(str(detail.get('dongfang_code', '')))
                dongfang_item.setFlags(dongfang_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                dongfang_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 1, dongfang_item)
                
                # 物料编码 - 不可编辑
                material_item = QTableWidgetItem(str(detail.get('material_code', '')))
                material_item.setFlags(material_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                material_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 2, material_item)
                
                # 更新料号 - 可编辑，添加搜索提示
                update_material_code = detail.get('material_code', '')
                update_material_item = QTableWidgetItem(str(update_material_code))
                if self.mode == 'edit':
                    update_material_item.setFlags(update_material_item.flags() | Qt.ItemFlag.ItemIsEditable)
                    update_material_item.setBackground(QColor(255, 255, 200))  # 浅黄色背景
                    # 添加工具提示
                    update_material_item.setToolTip("点击此单元格可搜索物料")
                else:
                    update_material_item.setFlags(update_material_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                    update_material_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 3, update_material_item)
                
                # 物料名称 - 可更新但不可手动编辑
                material_name_item = QTableWidgetItem(str(detail.get('material_name', '')))
                # 设置为可选择但不可编辑，这样程序可以更新但用户不能手动修改
                material_name_item.setFlags(Qt.ItemFlag.ItemIsSelectable | Qt.ItemFlag.ItemIsEnabled)
                if self.mode == 'edit':
                    material_name_item.setBackground(QColor(250, 250, 250))  # 浅灰色背景表示可更新
                else:
                    material_name_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 4, material_name_item)
                
                # 退货数量 - 不可编辑
                return_qty_item = QTableWidgetItem(str(detail.get('return_quantity', 0)))
                return_qty_item.setFlags(return_qty_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                return_qty_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 5, return_qty_item)
                
                # 拆分编号 - 不可编辑（从CJ_GOODS获取）
                split_code = detail.get('split_code', '')
                split_code_item = QTableWidgetItem(str(split_code))
                split_code_item.setFlags(split_code_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                split_code_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 6, split_code_item)
                
                # 箱规 - 不可编辑（从CJ_GOODS获取）
                box_spec = detail.get('box_spec', '')
                box_spec_item = QTableWidgetItem(str(box_spec))
                box_spec_item.setFlags(box_spec_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                box_spec_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 7, box_spec_item)
                
                # 拆分数量 - 不可编辑（自动计算）
                split_quantity = detail.get('split_quantity', 0)
                split_qty_item = QTableWidgetItem(str(split_quantity))
                split_qty_item.setFlags(split_qty_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                split_qty_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 8, split_qty_item)
                
                # 其余列保持原有逻辑...
                # 实收数量 - 可编辑
                received_qty = detail.get('received_quantity', 0)
                received_item = QTableWidgetItem(str(received_qty))
                if self.mode == 'edit':
                    received_item.setFlags(received_item.flags() | Qt.ItemFlag.ItemIsEditable)
                    received_item.setBackground(QColor(255, 255, 255))
                else:
                    received_item.setFlags(received_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                    received_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 9, received_item)
                
                # 原料数量 - 可编辑
                raw_qty = detail.get('raw_quantity', 0)
                raw_item = QTableWidgetItem(str(raw_qty))
                if self.mode == 'edit':
                    raw_item.setFlags(raw_item.flags() | Qt.ItemFlag.ItemIsEditable)
                    raw_item.setBackground(QColor(240, 255, 240))
                else:
                    raw_item.setFlags(raw_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                    raw_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 10, raw_item)
                
                # 变形数量 - 可编辑
                deformed_qty = detail.get('deformed_quantity', 0)
                deformed_item = QTableWidgetItem(str(deformed_qty))
                if self.mode == 'edit':
                    deformed_item.setFlags(deformed_item.flags() | Qt.ItemFlag.ItemIsEditable)
                    deformed_item.setBackground(QColor(255, 248, 220))
                else:
                    deformed_item.setFlags(deformed_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                    deformed_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 11, deformed_item)
                
                # 待报废数量 - 可编辑
                to_scrap_qty = detail.get('to_scrap_quantity', 0)
                to_scrap_item = QTableWidgetItem(str(to_scrap_qty))
                if self.mode == 'edit':
                    to_scrap_item.setFlags(to_scrap_item.flags() | Qt.ItemFlag.ItemIsEditable)
                    to_scrap_item.setBackground(QColor(255, 240, 240))
                else:
                    to_scrap_item.setFlags(to_scrap_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                    to_scrap_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 12, to_scrap_item)
                
                # 报废数量 - 可编辑
                scrapped_qty = detail.get('scrapped_quantity', 0)
                scrapped_item = QTableWidgetItem(str(scrapped_qty))
                if self.mode == 'edit':
                    scrapped_item.setFlags(scrapped_item.flags() | Qt.ItemFlag.ItemIsEditable)
                    scrapped_item.setBackground(QColor(240, 240, 240))
                else:
                    scrapped_item.setFlags(scrapped_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                    scrapped_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 13, scrapped_item)
                
                # 批次号 - 可编辑
                batch_no = detail.get('batch_no') or ''
                batch_item = QTableWidgetItem(batch_no)
                if self.mode == 'edit':
                    batch_item.setFlags(batch_item.flags() | Qt.ItemFlag.ItemIsEditable)
                    batch_item.setBackground(QColor(255, 255, 255))
                else:
                    batch_item.setFlags(batch_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                    batch_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 14, batch_item)
                
                # 存储位置 - 可编辑
                storage_location = detail.get('storage_location') or ''
                storage_item = QTableWidgetItem(storage_location)
                if self.mode == 'edit':
                    storage_item.setFlags(storage_item.flags() | Qt.ItemFlag.ItemIsEditable)
                    storage_item.setBackground(QColor(255, 255, 255))
                else:
                    storage_item.setFlags(storage_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                    storage_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 15, storage_item)
                
                # 备注 - 可编辑
                remark = detail.get('remark') or ''
                remark_item = QTableWidgetItem(remark)
                if self.mode == 'edit':
                    remark_item.setFlags(remark_item.flags() | Qt.ItemFlag.ItemIsEditable)
                    remark_item.setBackground(QColor(255, 255, 255))
                else:
                    remark_item.setFlags(remark_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                    remark_item.setBackground(QColor(245, 245, 245))
                self.detail_table.setItem(row, 16, remark_item)
                
            except Exception as e:
                print(f"填充第{row}行数据时出错: {str(e)}")
                continue
        
        print(f"明细表格填充完成，共{self.detail_table.rowCount()}行")
        
        # 如果是编辑模式，连接数量变化事件
        if self.mode == 'edit':
            self.detail_table.itemChanged.connect(self.on_detail_item_changed)
    
    def on_detail_item_changed(self, item):
        """明细项目变化事件"""
        try:
            row = item.row()
            col = item.column()
            
            # 如果是状态数量列变化，需要验证数量平衡
            if col in [10, 11, 12, 13]:  # 原料、变形、待报废、报废
                self.validate_quantity_balance(row)
                self.update_quantity_summary()
                
        except Exception as e:
            QMessageBox.warning(self, "警告", f"数据验证失败：{str(e)}")
    
    def validate_quantity_balance(self, row):
        """验证数量平衡 - 修改为验证状态数量是否等于拆分数量"""
        try:
            # 获取拆分数量（等于箱规）
            split_item = self.detail_table.item(row, 8)
            split_qty = int(split_item.text()) if split_item and split_item.text().isdigit() else 0
            
            # 获取各状态数量
            raw_item = self.detail_table.item(row, 10)
            raw_qty = int(raw_item.text()) if raw_item and raw_item.text().isdigit() else 0
            
            deformed_item = self.detail_table.item(row, 11)
            deformed_qty = int(deformed_item.text()) if deformed_item and deformed_item.text().isdigit() else 0
            
            to_scrap_item = self.detail_table.item(row, 12)
            to_scrap_qty = int(to_scrap_item.text()) if to_scrap_item and to_scrap_item.text().isdigit() else 0
            
            scrapped_item = self.detail_table.item(row, 13)
            scrapped_qty = int(scrapped_item.text()) if scrapped_item and scrapped_item.text().isdigit() else 0
            
            # 检查状态数量总和是否等于拆分数量
            total_status_qty = raw_qty + deformed_qty + to_scrap_qty + scrapped_qty
            
            if total_status_qty != split_qty:
                # 高亮显示不平衡的行
                for col in range(8, 14):  # 拆分数量到报废数量
                    item = self.detail_table.item(row, col)
                    if item:
                        item.setBackground(QColor(255, 200, 200))  # 红色背景
            else:
                # 恢复正常背景色
                self.detail_table.item(row, 8).setBackground(QColor(245, 245, 245))  # 拆分数量不可编辑
                self.detail_table.item(row, 10).setBackground(QColor(240, 255, 240))
                self.detail_table.item(row, 11).setBackground(QColor(255, 248, 220))
                self.detail_table.item(row, 12).setBackground(QColor(255, 240, 240))
                self.detail_table.item(row, 13).setBackground(QColor(240, 240, 240))
                
        except Exception as e:
            print(f"验证数量平衡失败：{str(e)}")
    
    def update_quantity_summary(self):
        """更新数量汇总"""
        try:
            total_qty = 0
            raw_qty = 0
            deformed_qty = 0
            to_scrap_qty = 0
            scrapped_qty = 0
            
            for row in range(self.detail_table.rowCount()):
                # 实收数量 - 第9列
                received_item = self.detail_table.item(row, 9)
                if received_item and received_item.text().isdigit():
                    total_qty += int(received_item.text())
                
                # 原料数量 - 第10列
                raw_item = self.detail_table.item(row, 10)
                if raw_item and raw_item.text().isdigit():
                    raw_qty += int(raw_item.text())
                
                # 变形数量 - 第11列
                deformed_item = self.detail_table.item(row, 11)
                if deformed_item and deformed_item.text().isdigit():
                    deformed_qty += int(deformed_item.text())
                
                # 待报废数量 - 第12列
                to_scrap_item = self.detail_table.item(row, 12)
                if to_scrap_item and to_scrap_item.text().isdigit():
                    to_scrap_qty += int(to_scrap_item.text())
                
                # 报废数量 - 第13列
                scrapped_item = self.detail_table.item(row, 13)
                if scrapped_item and scrapped_item.text().isdigit():
                    scrapped_qty += int(scrapped_item.text())
            
            # 更新汇总显示
            self.total_quantity_label.setText(str(total_qty))
            self.raw_quantity_label.setText(str(raw_qty))
            self.deformed_quantity_label.setText(str(deformed_qty))
            self.to_scrap_quantity_label.setText(str(to_scrap_qty))
            self.scrapped_quantity_label.setText(str(scrapped_qty))
            
        except Exception as e:
            print(f"更新数量汇总失败：{str(e)}")
    
    def update_button_states(self, status_value):
        """更新按钮状态"""
        # 根据收货单状态设置按钮可用性
        is_editable = (self.mode == 'edit' and status_value == 0)  # 只有编辑模式且保存状态才可编辑
        
        # 设置主表输入框状态
        self.receipt_date_edit.setReadOnly(not is_editable)
        self.receiver_input.setReadOnly(not is_editable)
        self.warehouse_input.setReadOnly(not is_editable)
        if hasattr(self, 'remark_input'):
            self.remark_input.setReadOnly(not is_editable)
        
        # 设置明细表格可编辑状态
        self.update_detail_table_editable(is_editable)
        
        # 提交按钮：只有保存状态显示和启用
        if hasattr(self, 'submit_action'):
            self.submit_action.setEnabled(status_value == 0)
            self.submit_action.setVisible(status_value == 0)
        
        # 审核按钮：只有保存状态显示和启用
        if hasattr(self, 'audit_action'):
            self.audit_action.setEnabled(status_value == 0)
            self.audit_action.setVisible(status_value == 0)
        
        # 删除按钮：只有保存状态显示和启用
        if hasattr(self, 'delete_action'):
            self.delete_action.setEnabled(status_value == 0)
            self.delete_action.setVisible(status_value == 0)
        
        # 打印按钮：所有状态都显示，但只有保存状态启用
        if hasattr(self, 'print_action'):
            self.print_action.setEnabled(True)  # 所有状态都可以打印
            self.print_action.setVisible(True)  # 所有状态都显示
        
        # 反审核按钮：只有审核状态显示和启用
        if hasattr(self, 'unaudit_action'):
            self.unaudit_action.setEnabled(status_value == 1)
            self.unaudit_action.setVisible(status_value == 1)
        
        # 更新按钮样式
        self.update_button_styles(status_value)
        
        # 强制刷新工具栏布局
        self.refresh_toolbar_layout()

    def refresh_toolbar_layout(self):
        """强制刷新工具栏布局"""
        try:
            # 如果使用的是QHBoxLayout创建的工具栏
            if hasattr(self, 'toolbar_layout'):
                # 遍历布局中的所有按钮
                for i in range(self.toolbar_layout.count()):
                    item = self.toolbar_layout.itemAt(i)
                    if item and item.widget():
                        widget = item.widget()
                        widget.update()
                        widget.repaint()
            
                self.toolbar_layout.update()
            
            # 查找所有QPushButton并刷新
            for button in self.findChildren(QPushButton):
                button.update()
                button.repaint()
            
            # 强制重绘整个对话框
            self.update()
            self.repaint()
            
            # 使用QApplication处理所有待处理事件
            QApplication.processEvents()
            
        except Exception as e:
            print(f"刷新工具栏布局失败: {str(e)}")
    
    def update_button_styles(self, status_value):
        """更新按钮样式"""
        # 提交按钮样式
        if hasattr(self, 'submit_action'):
            if status_value == 0:
                self.submit_action.setStyleSheet("""
                    QPushButton {
                        background-color: #1890ff;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #40a9ff;
                    }
                """)
            else:
                self.submit_action.setStyleSheet("""
                    QPushButton {
                        background-color: #d9d9d9;
                        color: #666666;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                    }
                """)
        
        # 审核按钮样式
        if hasattr(self, 'audit_action'):
            if status_value == 0:
                self.audit_action.setStyleSheet("""
                    QPushButton {
                        background-color: #52c41a;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #73d13d;
                    }
                """)
            else:
                self.audit_action.setStyleSheet("""
                    QPushButton {
                        background-color: #d9d9d9;
                        color: #666666;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                    }
                """)
        
        # 删除按钮样式
        if hasattr(self, 'delete_action'):
            if status_value == 0:
                self.delete_action.setStyleSheet("""
                    QPushButton {
                        background-color: #ff4d4f;
                        color: white;
                        border: none;
                        padding: 5px 15px;
                        min-width: 80px;
                    }
                    QPushButton:hover {
                        background-color: #ff7875;
                    }
                """)
            else:
                self.delete_action.setStyleSheet("""
                    QPushButton {
                        background-color: #d9d9d9;
                        color: #666666;
                        border: none;
                        padding: 5px 15px;
                        min-width: 80px;
                    }
                """)
        
        # 打印按钮样式（所有状态都保持正常样式）
        if hasattr(self, 'print_action'):
            self.print_action.setStyleSheet("""
                QPushButton {
                    background-color: #722ed1;
                    color: white;
                    border: none;
                    padding: 5px 15px;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #9254de;
                }
            """)
        
        # 反审核按钮样式
        if hasattr(self, 'unaudit_action'):
            if status_value == 1:
                self.unaudit_action.setStyleSheet("""
                    QPushButton {
                        background-color: #faad14;
                        color: white;
                        border: none;
                        padding: 5px 15px;
                        min-width: 80px;
                    }
                    QPushButton:hover {
                        background-color: #ffd666;
                    }
                """)
            else:
                self.unaudit_action.setStyleSheet("""
                    QPushButton {
                        background-color: #d9d9d9;
                        color: #666666;
                        border: none;
                        padding: 5px 15px;
                        min-width: 80px;
                    }
                """)
    
    def submit_receipt(self):
        """提交收货单"""
        try:
            # 验证数据完整性
            if not self._validate_receipt_data():
                return
            
            # 收集主表数据
            main_data = {
                'receipt_date': self.receipt_date_edit.date().toPyDate(),
                'receiver': self.receiver_input.text().strip(),
                'warehouse': self.warehouse_input.text().strip(),
                'remark': getattr(self, 'remark_input', QTextEdit()).toPlainText().strip()
            }
            
            # 收集明细数据并计算汇总
            detail_data = self._collect_detail_data()
            totals = self._calculate_totals_from_details(detail_data)
            
            # 设置主表汇总数量
            main_data.update(totals)
            
            # 提交到数据库
            self.dongfang_db.update_warehouse_receipt(self.receipt_id, main_data, detail_data)
            
            QMessageBox.information(self, "成功", "提交成功！")
            self.load_data()  # 重新加载数据，这会更新按钮状态
            
            # 通知父窗口刷新
            if hasattr(self.parent(), 'load_data'):
                self.parent().load_data()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"提交失败：{str(e)}")

    def _validate_receipt_data(self):
        """验证收货单数据"""
        try:
            # 验证基本信息
            if not self.receiver_input.text().strip():
                QMessageBox.warning(self, "验证失败", "请输入收货人！")
                return False
            
            if not self.warehouse_input.text().strip():
                QMessageBox.warning(self, "验证失败", "请输入收货仓库！")
                return False
            
            # 验证明细数据
            for row in range(self.detail_table.rowCount()):
                # 验证拆分数量与状态数量的平衡
                split_qty = self._get_cell_int_value(row, 8)  # 拆分数量
                raw_qty = self._get_cell_int_value(row, 10)   # 原料数量
                deformed_qty = self._get_cell_int_value(row, 11)  # 变形数量
                to_scrap_qty = self._get_cell_int_value(row, 12)  # 待报废数量
                scrapped_qty = self._get_cell_int_value(row, 13)  # 报废数量
                
                total_status_qty = raw_qty + deformed_qty + to_scrap_qty + scrapped_qty
                
                if split_qty > 0 and total_status_qty != split_qty:
                    waybill_no = self._get_cell_text_value(row, 0)
                    QMessageBox.warning(
                        self, "数量不平衡", 
                        f"运单号 {waybill_no} 的状态数量总和({total_status_qty}) 不等于拆分数量({split_qty})！\n"
                        f"请检查原料、变形、待报废、报废数量的分配。"
                    )
                    return False
            
            return True
            
        except Exception as e:
            QMessageBox.critical(self, "验证错误", f"数据验证失败：{str(e)}")
            return False

    def _collect_detail_data(self):
        """收集明细数据"""
        detail_data = []
        
        for row in range(self.detail_table.rowCount()):
            # 获取明细ID
            detail_id = self.detail_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
            
            # 从表格中读取当前显示的数据（而不是原始数据）
            update_material_code = self._get_cell_text_value(row, 3)  # 更新料号
            original_material_code = self._get_cell_text_value(row, 2)  # 原物料编码
            material_name = self._get_cell_text_value(row, 4)  # 物料名称
            
            # 获取拆分信息（从表格读取最新值）
            split_code = self._get_cell_text_value(row, 6)  # 拆分编号
            box_spec = self._get_cell_text_value(row, 7)    # 箱规
            split_quantity = self._get_cell_int_value(row, 8)  # 拆分数量
            
            # 获取各种数量
            received_quantity = self._get_cell_int_value(row, 9)   # 收货数量
            raw_quantity = self._get_cell_int_value(row, 10)       # 原料数量
            deformed_quantity = self._get_cell_int_value(row, 11)  # 变形数量
            to_scrap_quantity = self._get_cell_int_value(row, 12)  # 待报废数量
            scrapped_quantity = self._get_cell_int_value(row, 13)  # 报废数量
            
            # 获取其他信息
            batch_no = self._get_cell_text_value(row, 14)          # 批号
            storage_location = self._get_cell_text_value(row, 15)  # 存储位置
            current_remark = self._get_cell_text_value(row, 16)    # 备注
            
            # 如果物料编码发生变化，在备注中添加信息
            if original_material_code != update_material_code and original_material_code:
                inconsistency_note = f"货品不一致：原料号{original_material_code}->更新料号{update_material_code}"
                if current_remark:
                    current_remark = f"{current_remark}；{inconsistency_note}"
                else:
                    current_remark = inconsistency_note
            
            detail = {
                'id': detail_id,
                'material_code': update_material_code,  # 使用更新料号作为物料编码
                'material_name': material_name,         # 添加物料名称
                'split_code': split_code,
                'box_spec': box_spec,
                'split_quantity': split_quantity,
                'received_quantity': received_quantity,
                'raw_quantity': raw_quantity,
                'deformed_quantity': deformed_quantity,
                'to_scrap_quantity': to_scrap_quantity,
                'scrapped_quantity': scrapped_quantity,
                'batch_no': batch_no,
                'storage_location': storage_location,
                'remark': current_remark
            }
            detail_data.append(detail)
        
        return detail_data

    def _calculate_totals_from_details(self, detail_data):
        """从明细数据计算汇总数量"""
        totals = {
            'total_quantity': 0,
            'total_raw_quantity': 0,
            'total_deformed_quantity': 0,
            'total_to_scrap_quantity': 0,
            'total_scrapped_quantity': 0
        }
        
        for detail in detail_data:
            totals['total_quantity'] += detail.get('received_quantity', 0)
            totals['total_raw_quantity'] += detail.get('raw_quantity', 0)
            totals['total_deformed_quantity'] += detail.get('deformed_quantity', 0)
            totals['total_to_scrap_quantity'] += detail.get('to_scrap_quantity', 0)
            totals['total_scrapped_quantity'] += detail.get('scrapped_quantity', 0)
        
        return totals

    def audit_receipt(self):
        """审核收货单"""
        try:
            # 先提交当前数据
            if not self._validate_receipt_data():
                return
            
            # 收集并提交数据
            main_data = {
                'receipt_date': self.receipt_date_edit.date().toPyDate(),
                'receiver': self.receiver_input.text().strip(),
                'warehouse': self.warehouse_input.text().strip(),
                'remark': getattr(self, 'remark_input', QTextEdit()).toPlainText().strip()
            }
            
            detail_data = self._collect_detail_data()
            totals = self._calculate_totals_from_details(detail_data)
            main_data.update(totals)
            
            # 更新数据
            self.dongfang_db.update_warehouse_receipt(self.receipt_id, main_data, detail_data)
            
            # 确认审核
            reply = QMessageBox.question(
                self, "确认审核",
                "确定要审核该收货单吗？\n审核后将不能修改数据。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 执行审核操作
                self.dongfang_db.audit_warehouse_receipt(self.receipt_id)
                
                # 显示成功消息
                QMessageBox.information(self, "成功", "审核成功！")
                
                # 重新加载数据 - 这会更新状态和按钮
                self.load_data()
                
                # 额外确保按钮状态正确更新
                self.update_button_states(1)  # 审核后状态变为审核(1)
                
                # 通知父窗口刷新数据
                if hasattr(self.parent(), 'load_data'):
                    self.parent().load_data()
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"审核失败：{str(e)}")

    def unaudit_receipt(self):
        """反审核收货单"""
        try:
            # 确认反审核
            reply = QMessageBox.question(
                self, "确认反审核",
                "确定要反审核该收货单吗？\n反审核后将恢复到保存状态，可以重新编辑。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 执行反审核操作
                self.dongfang_db.unaudit_warehouse_receipt(self.receipt_id)
                
                # 显示成功消息
                QMessageBox.information(self, "成功", "反审核成功！")
                
                # 反审核后切换到编辑模式
                self.mode = 'edit'
                
                # # 重新加载数据 - 这会更新状态和按钮
                # self.load_data()
                
                # 额外确保按钮状态正确更新
                self.update_button_states(0)  # 反审核后状态变为保存(0)
                 #重新加载数据 - 这会更新状态和按钮
                self.load_data()
                # 通知父窗口刷新数据
                if hasattr(self.parent(), 'load_data'):
                    self.parent().load_data()
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"反审核失败：{str(e)}")

    def print_receipt(self):
        """打印收货单"""
        try:
            # 获取当前收货单数据
            receipt_data = self._get_print_data()
            
            if not receipt_data:
                QMessageBox.warning(self, "警告", "无法获取收货单数据")
                return
            
            # 创建打印预览对话框
            from .warehouse_receipt_print_dialog import WarehouseReceiptPrintDialog
            preview_dialog = WarehouseReceiptPrintDialog(receipt_data, self)
            preview_dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打印失败：{str(e)}")

    def _get_print_data(self):
        """获取打印数据"""
        try:
            # 收集主表数据
            main_data = {
                'receipt_no': self.receipt_no_label.text(),
                'return_yewu_no': self.return_yewu_no_label.text(),
                'receipt_date': self.receipt_date_edit.date().toString('yyyy-MM-dd'),
                'receiver': self.receiver_input.text(),
                'warehouse': self.warehouse_input.text(),
                'status': self.status_label.text(),
                'total_quantity': self.total_quantity_label.text(),
                'total_raw_quantity': self.raw_quantity_label.text(),
                'total_deformed_quantity': self.deformed_quantity_label.text(),
                'total_to_scrap_quantity': self.to_scrap_quantity_label.text(),
                'total_scrapped_quantity': self.scrapped_quantity_label.text(),
                'remark': getattr(self, 'remark_input', QTextEdit()).toPlainText()
            }
            
            # 收集明细数据
            details = []
            for row in range(self.detail_table.rowCount()):
                detail = {
                    'waybill_no': self._get_cell_text_value(row, 0),
                    'return_yewu_no': self._get_cell_text_value(row, 1),
                    'material_code': self._get_cell_text_value(row, 2),
                    'update_material_code': self._get_cell_text_value(row, 3),
                    'material_name': self._get_cell_text_value(row, 4),
                    'return_quantity': self._get_cell_text_value(row, 5),  # 退货数量
                    'split_code': self._get_cell_text_value(row, 6),
                    'box_spec': self._get_cell_text_value(row, 7),
                    'split_quantity': self._get_cell_text_value(row, 8),  # 拆分数量
                    'received_quantity': self._get_cell_text_value(row, 9),  # 收货数量
                    'raw_quantity': self._get_cell_text_value(row, 10),
                    'deformed_quantity': self._get_cell_text_value(row, 11),
                    'to_scrap_quantity': self._get_cell_text_value(row, 12),
                    'scrapped_quantity': self._get_cell_text_value(row, 13),
                    'batch_no': self._get_cell_text_value(row, 14),  # 批次号
                    'storage_location': self._get_cell_text_value(row, 15),
                    'remark': self._get_cell_text_value(row, 16)
                }
                details.append(detail)
            
            return {
                'main': main_data,
                'details': details
            }
            
        except Exception as e:
            print(f"获取打印数据失败：{str(e)}")
            return None

    def delete_receipt(self):
        """删除收货单"""
        try:
            # 确认删除
            reply = QMessageBox.question(
                self, "确认删除",
                "确定要删除该收货单吗？\n删除后将恢复退货申请的锁定状态。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.dongfang_db.delete_warehouse_receipt(self.receipt_id)
                QMessageBox.information(self, "成功", "删除成功！")
                
                # 通知父窗口刷新数据
                if hasattr(self.parent(), 'load_data'):
                    self.parent().load_data()
                
                self.accept()  # 关闭对话框
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除失败：{str(e)}")

    def update_detail_table_editable(self, is_editable):
        """更新明细表格可编辑状态"""
        if not hasattr(self, 'detail_table') or not self.detail_table:
            return
        
        # 设置表格编辑触发器
        if is_editable:
            self.detail_table.setEditTriggers(
                QAbstractItemView.EditTrigger.DoubleClicked | 
                QAbstractItemView.EditTrigger.EditKeyPressed |
                QAbstractItemView.EditTrigger.AnyKeyPressed
            )
        else:
            self.detail_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        
        # 遍历所有行，设置可编辑列的状态
        for row in range(self.detail_table.rowCount()):
            # 永远不可编辑的列：运单号码(0)、东方货号(1)、物料编码(2)、物料名称(4)、退货数量(5)、拆分编号(6)、箱规(7)、拆分数量(8)
            readonly_columns = [0, 1, 2, 4, 5, 6, 7, 8]
            for col in readonly_columns:
                item = self.detail_table.item(row, col)
                if item:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                    item.setBackground(QColor(245, 245, 245))  # 灰色背景表示不可编辑
            
            # 可编辑的列：更新料号(3)、实收数量(9)、原料数量(10)、变形数量(11)、待报废数量(12)、报废数量(13)、批次号(14)、存储位置(15)、备注(16)
            editable_columns = [3, 9, 10, 11, 12, 13, 14, 15, 16]
            
            for col in editable_columns:
                item = self.detail_table.item(row, col)
                if item:
                    if is_editable:
                        # 设置为可编辑
                        item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)
                        
                        # 恢复背景色
                        if col == 3:  # 更新料号
                            item.setBackground(QColor(255, 255, 200))
                        elif col == 9:  # 实收数量
                            item.setBackground(QColor(255, 255, 255))
                        elif col == 10:  # 原料数量
                            item.setBackground(QColor(240, 255, 240))
                        elif col == 11:  # 变形数量
                            item.setBackground(QColor(255, 248, 220))
                        elif col == 12:  # 待报废数量
                            item.setBackground(QColor(255, 240, 240))
                        elif col == 13:  # 报废数量
                            item.setBackground(QColor(240, 240, 240))
                        else:  # 其他列
                            item.setBackground(QColor(255, 255, 255))
                    else:
                        # 设置为只读
                        item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                        # 只读状态使用灰色背景
                        item.setBackground(QColor(245, 245, 245))

    def _get_cell_int_value(self, row, col):
        """获取表格单元格的整数值"""
        try:
            item = self.detail_table.item(row, col)
            if item and item.text().strip():
                text = item.text().strip()
                if text.isdigit() or (text.startswith('-') and text[1:].isdigit()):
                    return int(text)
            return 0
        except (ValueError, AttributeError):
            return 0

    def _get_cell_text_value(self, row, col):
        """获取表格单元格的文本值"""
        try:
            item = self.detail_table.item(row, col)
            if item:
                return item.text().strip()
            return ''
        except AttributeError:
            return ''

    def _set_cell_value(self, row, col, value):
        """设置表格单元格的值"""
        try:
            item = self.detail_table.item(row, col)
            if item:
                # 特殊处理物料名称列（第4列）
                if col == 4:
                    # 临时设置为可编辑以便更新
                    original_flags = item.flags()
                    item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)
                    item.setText(str(value))
                    # 恢复原来的标志
                    item.setFlags(original_flags)
                else:
                    item.setText(str(value))
            else:
                new_item = QTableWidgetItem(str(value))
                # 如果是物料名称列，设置特殊标志
                if col == 4:
                    new_item.setFlags(Qt.ItemFlag.ItemIsSelectable | Qt.ItemFlag.ItemIsEnabled)
                    if self.mode == 'edit':
                        new_item.setBackground(QColor(250, 250, 250))
                self.detail_table.setItem(row, col, new_item)
                
            # 确保表格重新绘制
            self.detail_table.viewport().update()
            
            print(f"设置第{row}行第{col}列的值为: {value}")
            
        except Exception as e:
            print(f"设置单元格值失败: {str(e)}")





































