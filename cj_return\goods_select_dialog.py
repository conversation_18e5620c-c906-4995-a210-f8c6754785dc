from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
from .dongfang_return_db import DongfangReturnDB

class GoodsSelectDialog(QDialog):
    """货品选择对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.dongfang_db = DongfangReturnDB()
        self.selected_goods = None
        self.setWindowTitle("选择货品")
        self.setModal(True)
        self.resize(600, 400)
        self.init_ui()
        self.load_goods()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 搜索
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入货号或货品名称搜索")
        self.search_input.returnPressed.connect(self.search_goods)
        search_layout.addWidget(self.search_input)
        
        self.search_btn = QPushButton("搜索")
        self.search_btn.clicked.connect(self.search_goods)
        search_layout.addWidget(self.search_btn)
        
        # 添加货品按钮
        self.add_goods_btn = QPushButton("添加货品")
        self.add_goods_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 6px 15px;
                border-radius: 3px;
                font-weight: normal;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
            QPushButton:pressed {
                background-color: #389e0d;
            }
        """)
        self.add_goods_btn.clicked.connect(self.add_goods)
        search_layout.addWidget(self.add_goods_btn)
        
        layout.addLayout(search_layout)
        
        # 货品表格
        self.goods_table = QTableWidget()
        self.goods_table.setColumnCount(4)
        self.goods_table.setHorizontalHeaderLabels(["货品代码", "货品名称", "物料编码", "物料名称"])
        
        # 设置表格属性
        self.goods_table.setAlternatingRowColors(True)
        self.goods_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.goods_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.goods_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.goods_table.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        
        self.goods_table.doubleClicked.connect(self.on_goods_double_clicked)
        layout.addWidget(self.goods_table)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.on_ok_clicked)
        button_layout.addWidget(self.ok_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def load_goods(self):
        """加载货品数据"""
        try:
            goods_list = self.dongfang_db.get_dongfang_goods()
            self.update_goods_table(goods_list)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载货品失败：{str(e)}")
    
    def search_goods(self):
        """搜索货品"""
        try:
            search_text = self.search_input.text().strip()
            goods_list = self.dongfang_db.get_dongfang_goods(search_text)
            self.update_goods_table(goods_list)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"搜索货品失败：{str(e)}")
    
    def update_goods_table(self, goods_list):
        """更新货品表格"""
        self.goods_table.setRowCount(len(goods_list))
        
        for row, goods in enumerate(goods_list):
            # 处理字典格式数据
            if isinstance(goods, dict):
                self.goods_table.setItem(row, 0, QTableWidgetItem(str(goods.get('cj_code', ''))))
                self.goods_table.setItem(row, 1, QTableWidgetItem(str(goods.get('cj_name', ''))))
                self.goods_table.setItem(row, 2, QTableWidgetItem(str(goods.get('material_code', ''))))
                self.goods_table.setItem(row, 3, QTableWidgetItem(str(goods.get('material_name', ''))))
            else:
                # 处理元组格式数据
                self.goods_table.setItem(row, 0, QTableWidgetItem(str(goods[0])))  # cj_code
                self.goods_table.setItem(row, 1, QTableWidgetItem(str(goods[1])))  # cj_name
                self.goods_table.setItem(row, 2, QTableWidgetItem(str(goods[2] if len(goods) > 2 else '')))  # material_code
                self.goods_table.setItem(row, 3, QTableWidgetItem(str(goods[3] if len(goods) > 3 else '')))  # material_name
    
    def on_goods_double_clicked(self, index):
        """货品双击事件"""
        self.select_current_goods()
        self.accept()
    
    def on_ok_clicked(self):
        """确定按钮点击"""
        if self.select_current_goods():
            self.accept()
        else:
            QMessageBox.warning(self, "警告", "请选择一个货品！")
    
    def select_current_goods(self):
        """选择当前货品"""
        current_row = self.goods_table.currentRow()
        if current_row >= 0:
            cj_code = self.goods_table.item(current_row, 0).text()
            cj_name = self.goods_table.item(current_row, 1).text()
            material_code = self.goods_table.item(current_row, 2).text() if self.goods_table.item(current_row, 2) else ''
            material_name = self.goods_table.item(current_row, 3).text() if self.goods_table.item(current_row, 3) else ''
            
            self.selected_goods = {
                'cj_code': cj_code,
                'cj_name': cj_name,
                'material_code': material_code,
                'material_name': material_name
            }
            return True
        return False
    
    def get_selected_goods(self):
        """获取选中的货品"""
        return self.selected_goods

    def add_goods(self):
        """添加货品"""
        try:
            from material.cj_goods_dialog import CJGoodsDialog
            from material.cj_goods_db import CJGoodsDB
            
            # 创建添加货品对话框
            dialog = CJGoodsDialog(self)
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # 获取添加的货品数据
                goods_data = dialog.get_goods_data()
                
                # 详细调试信息 - 原始数据
                print("=" * 50)
                print("调试信息 - 原始数据分析")
                print("=" * 50)
                print(f"原始 goods_data: {goods_data}")
                print(f"goods_data 类型: {type(goods_data)}")
                print("各字段详细信息:")
                for key, value in goods_data.items():
                    print(f"  {key}: {value} (类型: {type(value)})")
                
                # 数据预处理 - 确保所有值都是基本数据类型
                processed_data = {}
                for key, value in goods_data.items():
                    if value is None:
                        processed_data[key] = ''  # 将None转换为空字符串
                    elif isinstance(value, (dict, list)):
                        processed_data[key] = str(value)  # 将复杂对象转换为字符串
                    else:
                        processed_data[key] = value
                
                # 调试信息 - 处理后数据
                print("\n处理后数据分析:")
                print(f"处理后 processed_data: {processed_data}")
                print(f"processed_data 类型: {type(processed_data)}")
                print("处理后各字段详细信息:")
                for key, value in processed_data.items():
                    print(f"  {key}: {value} (类型: {type(value)})")
                
                # 确保必填字段不为空
                if not processed_data.get('cj_code', '').strip():
                    QMessageBox.warning(self, "警告", "东方货号不能为空！")
                    return
                    
                if not processed_data.get('cj_name', '').strip():
                    QMessageBox.warning(self, "警告", "东方名称不能为空！")
                    return
                    
                if not processed_data.get('material_id', '').strip():
                    QMessageBox.warning(self, "警告", "物料编号不能为空！")
                    return
                
                # 保存到数据库
                cj_goods_db = CJGoodsDB()
                
                # 处理current_user - 提取real_name
                if isinstance(cj_goods_db.current_user, dict):
                    cj_goods_db.current_user = cj_goods_db.current_user.get('real_name', '系统')
                elif cj_goods_db.current_user is None:
                    cj_goods_db.current_user = '系统管理员'
                else:
                    cj_goods_db.current_user = str(cj_goods_db.current_user)
                
                # 调试信息 - 数据库对象
                print(f"\nCJGoodsDB 对象信息:")
                print(f"current_user(处理后): {cj_goods_db.current_user} (类型: {type(cj_goods_db.current_user)})")
                if hasattr(cj_goods_db, 'db'):
                    print(f"db 对象: {cj_goods_db.db} (类型: {type(cj_goods_db.db)})")
                
                # 调试信息 - 即将传递给数据库的参数
                print(f"\n即将传递给 add_cj_goods 的参数:")
                print(f"参数类型: {type(processed_data)}")
                print(f"参数内容: {processed_data}")
                print("=" * 50)
                
                success, message = cj_goods_db.add_cj_goods(processed_data)
                
                if success:
                    QMessageBox.information(self, "成功", "货品添加成功！")
                    # 刷新货品列表
                    self.load_goods()
                    
                    # 自动选中新添加的货品
                    self.select_goods_by_code(processed_data['cj_code'])
                else:
                    QMessageBox.warning(self, "警告", f"添加货品失败：{message}")
                    
        except ImportError as e:
            QMessageBox.critical(self, "错误", f"无法导入货品管理模块：{str(e)}")
            print(f"ImportError 详情: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加货品失败：{str(e)}")
            print(f"Exception 详情: {str(e)}")
            print("完整异常堆栈:")
            import traceback
            traceback.print_exc()
    
    def select_goods_by_code(self, cj_code):
        """根据货品代码选中货品"""
        try:
            for row in range(self.goods_table.rowCount()):
                if self.goods_table.item(row, 0) and self.goods_table.item(row, 0).text() == cj_code:
                    self.goods_table.selectRow(row)
                    self.goods_table.scrollToItem(self.goods_table.item(row, 0))
                    break
        except Exception as e:
            print(f"选中货品失败: {str(e)}")



