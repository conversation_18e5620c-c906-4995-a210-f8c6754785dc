"""
库存盘点对话框
用于库存盘点和差异处理
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QComboBox, QFormLayout, QDialogButtonBox, QDoubleSpinBox,
                           QDateEdit, QTextEdit, QMessageBox, QPushButton, QFrame,
                           QGroupBox, QGridLayout, QWidget, QTableWidget, QTableWidgetItem,
                           QHeaderView, QCheckBox, QSpinBox)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QIcon, QColor
from datetime import datetime, date

from core.logger import Logger
from core.config import get_current_user
from inventory.inventory_db import InventoryDB
from inventory.inventory_permission_service import InventoryPermissionService


class InventoryCountDialog(QDialog):
    """库存盘点对话框"""
    
    data_saved = pyqtSignal()  # 数据保存成功信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.inventory_db = InventoryDB()
        self.permission_service = InventoryPermissionService()
        self.current_user = get_current_user()
        self.inventory_data = []  # 库存数据列表
        self.count_items = []  # 盘点项目列表
        
        self.setWindowTitle("库存盘点")
        self.setModal(True)
        self.resize(1000, 700)
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建盘点条件区域
        self.create_condition_section(main_layout)
        
        # 创建盘点数据区域
        self.create_count_section(main_layout)
        
        # 创建按钮区域
        self.create_button_section(main_layout)
    
    def create_condition_section(self, parent_layout):
        """创建盘点条件区域"""
        # 条件卡片
        condition_card = QFrame()
        condition_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        condition_layout = QVBoxLayout(condition_card)
        condition_layout.setContentsMargins(20, 15, 20, 20)
        condition_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📋 盘点条件")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        condition_layout.addWidget(title_label)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)
        
        # 第一行：部门、仓库
        form_layout.addWidget(QLabel("盘点部门:"), 0, 0)
        self.department_combo = QComboBox()
        self.department_combo.currentTextChanged.connect(self.on_department_changed)
        self.load_department_options()
        form_layout.addWidget(self.department_combo, 0, 1)
        
        form_layout.addWidget(QLabel("仓库:"), 0, 2)
        self.warehouse_combo = QComboBox()
        self.warehouse_combo.addItem("全部", "")
        self.load_warehouse_options()
        form_layout.addWidget(self.warehouse_combo, 0, 3)
        
        # 第二行：物料分类、盘点日期
        form_layout.addWidget(QLabel("物料分类:"), 1, 0)
        self.category_combo = QComboBox()
        self.category_combo.addItem("全部", "")
        self.load_category_options()
        form_layout.addWidget(self.category_combo, 1, 1)
        
        form_layout.addWidget(QLabel("盘点日期:"), 1, 2)
        self.count_date_edit = QDateEdit()
        self.count_date_edit.setDate(QDate.currentDate())
        self.count_date_edit.setCalendarPopup(True)
        form_layout.addWidget(self.count_date_edit, 1, 3)
        
        # 第三行：加载数据按钮
        button_layout = QHBoxLayout()
        
        self.load_data_btn = QPushButton("🔍 加载库存数据")
        self.load_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.load_data_btn.clicked.connect(self.load_inventory_data)
        button_layout.addWidget(self.load_data_btn)
        
        button_layout.addStretch()
        form_layout.addLayout(button_layout, 2, 0, 1, 4)
        
        condition_layout.addWidget(form_widget)
        parent_layout.addWidget(condition_card)
    
    def create_count_section(self, parent_layout):
        """创建盘点数据区域"""
        # 盘点数据卡片
        count_card = QFrame()
        count_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        count_layout = QVBoxLayout(count_card)
        count_layout.setContentsMargins(20, 15, 20, 20)
        count_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📊 盘点数据")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        count_layout.addWidget(title_label)
        
        # 盘点表格
        self.count_table = QTableWidget()
        self.count_table.setColumnCount(8)
        self.count_table.setHorizontalHeaderLabels([
            "选择", "物料编码", "物料名称", "账面数量", "实盘数量", "差异数量", "单位", "备注"
        ])
        
        # 设置表格样式
        self.count_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #f0f0f0;
                background-color: white;
                alternate-background-color: #fafafa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QHeaderView::section {
                background-color: #fafafa;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #f0f0f0;
                font-weight: bold;
            }
        """)
        
        self.count_table.setAlternatingRowColors(True)
        self.count_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # 设置列宽
        header = self.count_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 选择
        header.resizeSection(0, 60)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)  # 物料编码
        header.resizeSection(1, 120)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # 物料名称
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # 账面数量
        header.resizeSection(3, 100)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)  # 实盘数量
        header.resizeSection(4, 100)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)  # 差异数量
        header.resizeSection(5, 100)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)  # 单位
        header.resizeSection(6, 60)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Stretch)  # 备注
        
        count_layout.addWidget(self.count_table)
        parent_layout.addWidget(count_card)
    
    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 左侧按钮组
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_items)
        button_layout.addWidget(self.select_all_btn)
        
        self.select_none_btn = QPushButton("全不选")
        self.select_none_btn.clicked.connect(self.select_none_items)
        button_layout.addWidget(self.select_none_btn)
        
        self.auto_fill_btn = QPushButton("自动填充实盘数量")
        self.auto_fill_btn.setToolTip("将账面数量复制到实盘数量")
        self.auto_fill_btn.clicked.connect(self.auto_fill_actual_qty)
        button_layout.addWidget(self.auto_fill_btn)
        
        button_layout.addStretch()
        
        # 右侧按钮组
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                color: #666;
                border: 1px solid #d9d9d9;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 14px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #e6f7ff;
                border-color: #1890ff;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.save_count_btn = QPushButton("保存盘点")
        self.save_count_btn.setStyleSheet("""
            QPushButton {
                background-color: #722ed1;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #9254de;
            }
        """)
        self.save_count_btn.clicked.connect(self.save_count_result)
        button_layout.addWidget(self.save_count_btn)
        
        parent_layout.addLayout(button_layout)

    # ==================== 业务逻辑方法 ====================

    def load_department_options(self):
        """加载部门选项"""
        try:
            # 根据用户权限加载可访问的部门
            user_departments = self.current_user.get('departments', [])
            if self.permission_service.has_permission('system:admin'):
                # 管理员可以看到所有部门
                departments = ["通路一", "通路二", "生产部", "采购部"]
            else:
                departments = user_departments or [self.current_user.get('department', '')]

            for dept in departments:
                if dept:  # 确保部门不为空
                    self.department_combo.addItem(dept)

        except Exception as e:
            Logger.log_error(f"加载部门选项失败: {str(e)}")

    def load_warehouse_options(self):
        """加载仓库选项"""
        try:
            # TODO: 从数据库加载仓库列表
            warehouses = [
                ("WH001", "主仓库"),
                ("WH002", "原料仓"),
                ("WH003", "成品仓")
            ]

            for code, name in warehouses:
                self.warehouse_combo.addItem(f"{name}({code})", code)

        except Exception as e:
            Logger.log_error(f"加载仓库选项失败: {str(e)}")

    def load_category_options(self):
        """加载物料分类选项"""
        try:
            # TODO: 从数据库加载物料分类
            categories = [
                (1, "原材料"),
                (2, "包装材料"),
                (3, "成品"),
                (4, "半成品")
            ]

            for category_id, name in categories:
                self.category_combo.addItem(name, category_id)

        except Exception as e:
            Logger.log_error(f"加载物料分类选项失败: {str(e)}")

    def on_department_changed(self):
        """部门变化时的处理"""
        # 清空现有数据
        self.count_table.setRowCount(0)
        self.inventory_data = []
        self.count_items = []

    def load_inventory_data(self):
        """加载库存数据"""
        try:
            department = self.department_combo.currentText()
            if not department:
                QMessageBox.warning(self, "提示", "请选择盘点部门")
                return

            # 检查部门权限
            if not self.permission_service.check_department_permission(department):
                QMessageBox.warning(self, "权限不足", f"您没有访问部门 '{department}' 库存的权限")
                return

            # 构建查询条件
            filters = {'department': department}

            if self.warehouse_combo.currentData():
                filters['warehouse_code'] = self.warehouse_combo.currentData()

            if self.category_combo.currentData():
                filters['category_id'] = self.category_combo.currentData()

            # 查询库存数据
            total, inventory_list = self.inventory_db.get_department_inventory_list(
                filters=filters, page=1, page_size=1000  # 盘点时加载所有数据
            )

            if not inventory_list:
                QMessageBox.information(self, "提示", "没有找到符合条件的库存数据")
                return

            self.inventory_data = inventory_list
            self.update_count_table()

            QMessageBox.information(self, "成功", f"成功加载 {len(inventory_list)} 条库存数据")

        except Exception as e:
            Logger.log_error(f"加载库存数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载数据失败：{str(e)}")

    def update_count_table(self):
        """更新盘点表格"""
        try:
            self.count_table.setRowCount(len(self.inventory_data))
            self.count_items = []

            for row, inventory in enumerate(self.inventory_data):
                # 选择复选框
                checkbox = QCheckBox()
                checkbox.setChecked(True)  # 默认选中
                self.count_table.setCellWidget(row, 0, checkbox)

                # 物料编码
                self.count_table.setItem(row, 1, QTableWidgetItem(str(inventory.get('material_id', ''))))

                # 物料名称
                self.count_table.setItem(row, 2, QTableWidgetItem(str(inventory.get('material_name', ''))))

                # 账面数量
                book_qty = inventory.get('total_qty', 0)
                self.count_table.setItem(row, 3, QTableWidgetItem(f"{book_qty:.4f}"))

                # 实盘数量（可编辑）
                actual_qty_spin = QDoubleSpinBox()
                actual_qty_spin.setRange(0, 999999.9999)
                actual_qty_spin.setDecimals(4)
                actual_qty_spin.setValue(0)  # 默认为0，需要手动输入
                actual_qty_spin.valueChanged.connect(lambda v, r=row: self.calculate_difference(r))
                self.count_table.setCellWidget(row, 4, actual_qty_spin)

                # 差异数量
                diff_item = QTableWidgetItem("0.0000")
                diff_item.setFlags(diff_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # 只读
                self.count_table.setItem(row, 5, diff_item)

                # 单位
                self.count_table.setItem(row, 6, QTableWidgetItem(str(inventory.get('unit', ''))))

                # 备注（可编辑）
                remark_edit = QLineEdit()
                remark_edit.setPlaceholderText("输入盘点备注...")
                self.count_table.setCellWidget(row, 7, remark_edit)

                # 存储盘点项目信息
                count_item = {
                    'inventory': inventory,
                    'checkbox': checkbox,
                    'actual_qty_spin': actual_qty_spin,
                    'remark_edit': remark_edit
                }
                self.count_items.append(count_item)

        except Exception as e:
            Logger.log_error(f"更新盘点表格失败: {str(e)}")

    def calculate_difference(self, row):
        """计算差异数量"""
        try:
            if row >= len(self.inventory_data):
                return

            book_qty = self.inventory_data[row].get('total_qty', 0)
            actual_qty_spin = self.count_table.cellWidget(row, 4)
            actual_qty = actual_qty_spin.value()

            difference = actual_qty - book_qty

            # 更新差异数量显示
            diff_item = self.count_table.item(row, 5)
            diff_item.setText(f"{difference:.4f}")

            # 根据差异设置颜色
            if difference > 0:
                diff_item.setBackground(QColor('#e8f5e8'))  # 绿色背景（盘盈）
                diff_item.setForeground(QColor('#4caf50'))
            elif difference < 0:
                diff_item.setBackground(QColor('#ffebee'))  # 红色背景（盘亏）
                diff_item.setForeground(QColor('#f44336'))
            else:
                diff_item.setBackground(QColor('#f5f5f5'))  # 灰色背景（无差异）
                diff_item.setForeground(QColor('#666666'))

        except Exception as e:
            Logger.log_error(f"计算差异数量失败: {str(e)}")

    def select_all_items(self):
        """全选"""
        for count_item in self.count_items:
            count_item['checkbox'].setChecked(True)

    def select_none_items(self):
        """全不选"""
        for count_item in self.count_items:
            count_item['checkbox'].setChecked(False)

    def auto_fill_actual_qty(self):
        """自动填充实盘数量"""
        try:
            reply = QMessageBox.question(
                self, "确认操作",
                "确认要将账面数量复制到实盘数量吗？\n这将覆盖已输入的实盘数量！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                for i, inventory in enumerate(self.inventory_data):
                    book_qty = inventory.get('total_qty', 0)
                    actual_qty_spin = self.count_table.cellWidget(i, 4)
                    actual_qty_spin.setValue(book_qty)

        except Exception as e:
            Logger.log_error(f"自动填充实盘数量失败: {str(e)}")

    def validate_count_data(self):
        """验证盘点数据"""
        selected_count = 0
        for count_item in self.count_items:
            if count_item['checkbox'].isChecked():
                selected_count += 1

        if selected_count == 0:
            QMessageBox.warning(self, "验证失败", "请至少选择一个盘点项目")
            return False

        return True

    def save_count_result(self):
        """保存盘点结果"""
        try:
            # 验证数据
            if not self.validate_count_data():
                return

            # 收集盘点数据
            count_results = []
            for i, count_item in enumerate(self.count_items):
                if not count_item['checkbox'].isChecked():
                    continue

                inventory = count_item['inventory']
                actual_qty = count_item['actual_qty_spin'].value()
                remark = count_item['remark_edit'].text().strip()
                book_qty = inventory.get('total_qty', 0)
                difference = actual_qty - book_qty

                count_results.append({
                    'material_id': inventory['material_id'],
                    'material_name': inventory['material_name'],
                    'department': inventory['department'],
                    'warehouse_code': inventory.get('warehouse_code'),
                    'book_qty': book_qty,
                    'actual_qty': actual_qty,
                    'difference_qty': difference,
                    'unit': inventory.get('unit', 'PCS'),
                    'remark': remark or None
                })

            if not count_results:
                QMessageBox.warning(self, "提示", "没有选中的盘点项目")
                return

            # 显示盘点汇总
            total_items = len(count_results)
            profit_items = len([r for r in count_results if r['difference_qty'] > 0])
            loss_items = len([r for r in count_results if r['difference_qty'] < 0])
            normal_items = len([r for r in count_results if r['difference_qty'] == 0])

            summary = (
                f"盘点汇总：\n"
                f"总盘点项目: {total_items}\n"
                f"盘盈项目: {profit_items}\n"
                f"盘亏项目: {loss_items}\n"
                f"无差异项目: {normal_items}\n\n"
                f"确认保存盘点结果吗？"
            )

            reply = QMessageBox.question(
                self, "确认保存",
                summary,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # TODO: 实现保存盘点结果到数据库的逻辑
            # 这里需要创建盘点单据，记录盘点结果，并根据差异调整库存

            QMessageBox.information(self, "成功", f"盘点结果保存成功！\n共处理 {total_items} 个项目")

            # 发送数据保存成功信号
            self.data_saved.emit()

            # 关闭对话框
            self.accept()

        except Exception as e:
            Logger.log_error(f"保存盘点结果失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"保存失败：{str(e)}")
