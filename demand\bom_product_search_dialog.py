"""
BOM成品物料选择对话框
专门为需求计划设计的成品物料选择功能
从BOM主表中选择启用状态的成品物料
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QTableWidget, QPushButton, QTableWidgetItem, QMessageBox,
                           QHeaderView, QComboBox, QFrame, QGroupBox, QFormLayout,
                           QTextEdit, QSplitter, QCheckBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon
from demand.demand_plan_db import DemandPlanDB

class BOMProductSearchDialog(QDialog):
    """BOM成品物料选择对话框"""
    
    # 定义信号
    material_selected = pyqtSignal(dict)  # 选中物料信号
    
    def __init__(self, parent=None, selected_materials=None):
        super().__init__(parent)
        self.demand_plan_db = DemandPlanDB()
        self.selected_materials = selected_materials or []  # 已选择的物料列表
        self.current_material = None  # 当前选中的物料
        
        self.setWindowTitle("BOM成品物料选择")
        self.setModal(True)
        self.resize(1200, 800)
        
        self.setup_ui()
        self.search_materials()
    
    def setup_ui(self):
        """设置UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建搜索区域
        self.create_search_area(main_layout)
        
        # 创建主体区域（分割器）
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：物料列表
        left_widget = self.create_left_panel()
        splitter.addWidget(left_widget)
        
        # 右侧：物料详情
        right_widget = self.create_right_panel()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([800, 400])
        main_layout.addWidget(splitter)
        
        # 创建按钮区域
        self.create_button_area(main_layout)
    
    def create_search_area(self, parent_layout):
        """创建搜索区域"""
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        
        search_layout = QVBoxLayout(search_frame)
        search_layout.setSpacing(10)
        
        # 搜索条件行
        search_row = QHBoxLayout()
        search_row.setSpacing(15)
        
        # 物料编码搜索
        search_row.addWidget(QLabel("物料编码:"))
        self.material_id_edit = QLineEdit()
        self.material_id_edit.setPlaceholderText("请输入物料编码")
        self.material_id_edit.setFixedWidth(150)
        self.material_id_edit.returnPressed.connect(self.search_materials)
        search_row.addWidget(self.material_id_edit)
        
        # 物料名称搜索
        search_row.addWidget(QLabel("物料名称:"))
        self.material_name_edit = QLineEdit()
        self.material_name_edit.setPlaceholderText("请输入物料名称")
        self.material_name_edit.setFixedWidth(200)
        self.material_name_edit.returnPressed.connect(self.search_materials)
        search_row.addWidget(self.material_name_edit)
        
        # 排除已选择的物料
        self.exclude_selected_check = QCheckBox("排除已选择的物料")
        self.exclude_selected_check.setChecked(True)
        self.exclude_selected_check.stateChanged.connect(self.search_materials)
        search_row.addWidget(self.exclude_selected_check)
        
        # 搜索按钮
        search_btn = QPushButton("🔍 搜索")
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        search_btn.clicked.connect(self.search_materials)
        search_row.addWidget(search_btn)
        
        # 重置按钮
        reset_btn = QPushButton("🔄 重置")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #d9d9d9;
                color: #333;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #bfbfbf;
            }
        """)
        reset_btn.clicked.connect(self.reset_search)
        search_row.addWidget(reset_btn)
        
        search_row.addStretch()
        search_layout.addLayout(search_row)
        
        parent_layout.addWidget(search_frame)
    
    def create_left_panel(self):
        """创建左侧面板"""
        left_widget = QFrame()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(10)
        
        # 物料列表
        self.create_material_table(left_layout)
        
        return left_widget
    
    def create_material_table(self, parent_layout):
        """创建物料表格"""
        # 表格标题
        table_label = QLabel("BOM成品物料列表")
        table_label.setFont(QFont("", 10, QFont.Weight.Bold))
        parent_layout.addWidget(table_label)
        
        # 创建表格
        self.material_table = QTableWidget()
        self.material_table.setColumnCount(6)
        
        headers = ["物料编码", "物料名称", "规格型号", "分类", "基本单位", "BOM数量"]
        self.material_table.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.material_table.setAlternatingRowColors(True)
        self.material_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.material_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.material_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.material_table.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

        # 设置列宽
        header = self.material_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 物料名称
        
        self.material_table.setColumnWidth(0, 120)  # 物料编码
        self.material_table.setColumnWidth(2, 120)  # 规格型号
        self.material_table.setColumnWidth(3, 100)  # 分类
        self.material_table.setColumnWidth(4, 80)   # 基本单位
        self.material_table.setColumnWidth(5, 80)   # BOM数量
        
        # 连接信号
        self.material_table.itemSelectionChanged.connect(self.on_material_selection_changed)
        self.material_table.itemDoubleClicked.connect(self.on_material_double_clicked)
        
        parent_layout.addWidget(self.material_table)
    
    def create_right_panel(self):
        """创建右侧面板"""
        right_widget = QFrame()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(10)
        
        # 物料详情标题
        detail_label = QLabel("物料详情")
        detail_label.setFont(QFont("", 10, QFont.Weight.Bold))
        right_layout.addWidget(detail_label)
        
        # 物料详情组
        detail_group = QGroupBox()
        detail_form = QFormLayout(detail_group)
        detail_form.setSpacing(8)
        
        # 基本信息
        self.detail_material_id = QLabel("-")
        detail_form.addRow("物料编码:", self.detail_material_id)
        
        self.detail_material_name = QLabel("-")
        self.detail_material_name.setWordWrap(True)
        detail_form.addRow("物料名称:", self.detail_material_name)
        
        self.detail_specification = QLabel("-")
        self.detail_specification.setWordWrap(True)
        detail_form.addRow("规格型号:", self.detail_specification)
        
        self.detail_category = QLabel("-")
        detail_form.addRow("物料分类:", self.detail_category)
        
        self.detail_unit = QLabel("-")
        detail_form.addRow("基本单位:", self.detail_unit)
        
        self.detail_bom_count = QLabel("-")
        detail_form.addRow("BOM数量:", self.detail_bom_count)
        
        right_layout.addWidget(detail_group)
        
        # BOM信息
        bom_label = QLabel("相关BOM")
        bom_label.setFont(QFont("", 9, QFont.Weight.Bold))
        right_layout.addWidget(bom_label)
        
        self.detail_bom_names = QTextEdit()
        self.detail_bom_names.setReadOnly(True)
        self.detail_bom_names.setMaximumHeight(100)
        self.detail_bom_names.setPlaceholderText("暂无BOM信息")
        right_layout.addWidget(self.detail_bom_names)
        
        right_layout.addStretch()
        
        return right_widget
    
    def create_button_area(self, parent_layout):
        """创建按钮区域"""
        button_frame = QFrame()
        button_frame.setStyleSheet("QFrame { background-color: #f5f5f5; }")
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(20, 10, 20, 10)
        
        # 选择信息标签
        self.selected_info_label = QLabel("请选择BOM成品物料")
        self.selected_info_label.setStyleSheet("color: #666; font-weight: bold;")
        button_layout.addWidget(self.selected_info_label)
        
        button_layout.addStretch()
        
        # 确定选择按钮
        self.select_btn = QPushButton("✅ 确定选择")
        self.select_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #999;
            }
        """)
        self.select_btn.setEnabled(False)
        self.select_btn.clicked.connect(self.confirm_selection)
        button_layout.addWidget(self.select_btn)
        
        # 取消按钮
        cancel_btn = QPushButton("❌ 取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #d9d9d9;
                color: #333;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #bfbfbf;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        parent_layout.addWidget(button_frame)
    
    def search_materials(self):
        """搜索物料"""
        try:
            # 构建搜索条件
            search_text = ""
            if self.material_id_edit.text().strip():
                search_text = self.material_id_edit.text().strip()
            elif self.material_name_edit.text().strip():
                search_text = self.material_name_edit.text().strip()
            
            # 获取数据
            total, results = self.demand_plan_db.get_bom_products_for_demand(
                search_text=search_text,
                page=1,
                page_size=1000
            )
            
            # 过滤已选择的物料
            if self.exclude_selected_check.isChecked() and self.selected_materials:
                results = [r for r in results if r.get('material_id') not in self.selected_materials]
            
            self.update_table(results)
            self.update_info_label(len(results))
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"搜索物料失败: {str(e)}")
    
    def update_table(self, materials):
        """更新表格数据"""
        self.material_table.setRowCount(len(materials))
        
        for row, material in enumerate(materials):
            # 物料编码
            item = QTableWidgetItem(str(material.get('material_id', '')))
            self.material_table.setItem(row, 0, item)
            
            # 物料名称
            item = QTableWidgetItem(str(material.get('material_name', '')))
            self.material_table.setItem(row, 1, item)
            
            # 规格型号
            item = QTableWidgetItem(str(material.get('specification', '')))
            self.material_table.setItem(row, 2, item)
            
            # 分类
            item = QTableWidgetItem(str(material.get('category_name', '')))
            self.material_table.setItem(row, 3, item)
            
            # 基本单位
            item = QTableWidgetItem(str(material.get('unit', '')))
            self.material_table.setItem(row, 4, item)
            
            # BOM数量
            bom_count = material.get('bom_count', 0)
            item = QTableWidgetItem(str(bom_count))
            self.material_table.setItem(row, 5, item)
            
            # 存储完整数据
            self.material_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, material)
    
    def update_info_label(self, count):
        """更新信息标签"""
        self.selected_info_label.setText(f"共找到 {count} 个BOM成品物料")
    
    def on_material_selection_changed(self):
        """物料选择变化"""
        current_row = self.material_table.currentRow()
        if current_row >= 0:
            item = self.material_table.item(current_row, 0)
            if item:
                material_data = item.data(Qt.ItemDataRole.UserRole)
                self.current_material = material_data
                self.update_detail_panel(material_data)
                self.select_btn.setEnabled(True)
                self.selected_info_label.setText(f"已选择: {material_data.get('material_name', '')}")
        else:
            self.current_material = None
            self.clear_detail_panel()
            self.select_btn.setEnabled(False)
            self.selected_info_label.setText("请选择BOM成品物料")
    
    def on_material_double_clicked(self, item):
        """双击选择物料"""
        self.confirm_selection()
    
    def update_detail_panel(self, material_data):
        """更新详情面板"""
        self.detail_material_id.setText(str(material_data.get('material_id', '-')))
        self.detail_material_name.setText(str(material_data.get('material_name', '-')))
        self.detail_specification.setText(str(material_data.get('specification', '-')))
        self.detail_category.setText(str(material_data.get('category_name', '-')))
        self.detail_unit.setText(str(material_data.get('unit', '-')))
        
        bom_count = material_data.get('bom_count', 0)
        self.detail_bom_count.setText(f"{bom_count} 个")
        
        bom_names = material_data.get('bom_names', '')
        self.detail_bom_names.setPlainText(bom_names if bom_names else "暂无BOM信息")
    
    def clear_detail_panel(self):
        """清空详情面板"""
        labels = [
            self.detail_material_id, self.detail_material_name,
            self.detail_specification, self.detail_category,
            self.detail_unit, self.detail_bom_count
        ]
        
        for label in labels:
            label.setText("-")
        
        self.detail_bom_names.clear()
    
    def reset_search(self):
        """重置搜索条件"""
        self.material_id_edit.clear()
        self.material_name_edit.clear()
        self.exclude_selected_check.setChecked(True)
        self.search_materials()
    
    def confirm_selection(self):
        """确认选择"""
        if self.current_material:
            self.material_selected.emit(self.current_material)
            self.accept()
        else:
            QMessageBox.warning(self, "警告", "请先选择一个物料")
    
    def get_selected_material(self):
        """获取选中的物料"""
        return self.current_material



