from datetime import datetime, date
from core.logger import Logger
from modules.db_manager import DatabaseManager

class InventoryService:
    """库存管理服务类"""
    
    def __init__(self, current_user=None):
        self.db_manager = DatabaseManager()
        self.current_user = current_user or {}
    
    def check_material_availability(self, material_requirements, department):
        """检查物料可用性
        
        Args:
            material_requirements: 物料需求列表 [{'material_id': '', 'required_qty': 0, ...}]
            department: 申请部门
            
        Returns:
            dict: 可用性检查结果
        """
        try:
            availability_results = []
            total_shortage_value = 0
            
            for requirement in material_requirements:
                material_id = requirement['material_id']
                required_qty = float(requirement['required_qty'])
                
                # 查询部门库存
                available_qty = self.get_available_quantity(material_id, department)
                
                # 计算缺口
                shortage_qty = max(0, required_qty - available_qty)
                available_for_use = min(required_qty, available_qty)
                
                # 获取物料成本
                unit_cost = self.get_material_cost(material_id)
                shortage_value = shortage_qty * unit_cost
                total_shortage_value += shortage_value
                
                availability_results.append({
                    'material_id': material_id,
                    'material_name': requirement.get('material_name', ''),
                    'required_qty': required_qty,
                    'available_qty': available_qty,
                    'shortage_qty': shortage_qty,
                    'available_for_use': available_for_use,
                    'unit_cost': unit_cost,
                    'shortage_value': shortage_value,
                    'can_produce': available_for_use > 0,
                    'need_purchase': shortage_qty > 0,
                    'availability_rate': (available_for_use / required_qty * 100) if required_qty > 0 else 0
                })
            
            return {
                'department': department,
                'total_materials': len(material_requirements),
                'available_materials': len([r for r in availability_results if r['shortage_qty'] == 0]),
                'shortage_materials': len([r for r in availability_results if r['shortage_qty'] > 0]),
                'total_shortage_value': total_shortage_value,
                'can_produce_all': all(r['shortage_qty'] == 0 for r in availability_results),
                'details': availability_results
            }
            
        except Exception as e:
            Logger.log_error(f"检查物料可用性失败: {str(e)}")
            raise
    
    def get_available_quantity(self, material_id, department, warehouse_code=None):
        """获取物料可用数量"""
        try:
            where_conditions = ["material_id = %s", "department = %s"]
            params = [material_id, department]
            
            if warehouse_code:
                where_conditions.append("warehouse_code = %s")
                params.append(warehouse_code)
            
            query = f"""
            SELECT COALESCE(SUM(available_qty), 0) as total_available
            FROM department_inventory 
            WHERE {' AND '.join(where_conditions)}
            """
            
            result = self.db_manager.execute_query(query, params)
            return float(result[0]['total_available']) if result else 0.0
            
        except Exception as e:
            Logger.log_error(f"获取可用数量失败: {str(e)}")
            return 0.0
    
    def reserve_inventory(self, material_id, department, reserved_qty, source_type, source_no, source_id, expected_use_date=None):
        """预留库存"""
        try:
            self.db_manager.begin_transaction()
            
            # 检查可用数量
            available_qty = self.get_available_quantity(material_id, department)
            if available_qty < reserved_qty:
                raise Exception(f"库存不足，可用数量: {available_qty}, 需要数量: {reserved_qty}")
            
            # 生成预留单号
            reservation_no = self.generate_reservation_number()
            
            # 创建预留记录
            insert_reservation = """
            INSERT INTO inventory_reservations (
                reservation_no, material_id, department, reserved_qty, unit,
                source_type, source_no, source_id, reservation_date, expected_use_date,
                status, create_time, create_user
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 1, %s, %s)
            """
            
            # 获取物料单位
            unit = self.get_material_unit(material_id)
            current_time = datetime.now()
            current_user = self.current_user.get('username', 'system')
            
            self.db_manager.execute_update(insert_reservation, (
                reservation_no, material_id, department, reserved_qty, unit,
                source_type, source_no, source_id, date.today(), expected_use_date,
                current_time, current_user
            ))
            
            # 更新部门库存的预留数量
            update_inventory = """
            UPDATE department_inventory 
            SET reserved_qty = reserved_qty + %s,
                available_qty = available_qty - %s,
                update_time = %s,
                update_user = %s
            WHERE material_id = %s AND department = %s
            """
            
            self.db_manager.execute_update(update_inventory, (
                reserved_qty, reserved_qty, current_time, current_user, material_id, department
            ))
            
            self.db_manager.commit_transaction()
            
            return {
                'success': True,
                'reservation_no': reservation_no,
                'message': f'成功预留库存 {reserved_qty} {unit}'
            }
            
        except Exception as e:
            self.db_manager.rollback_transaction()
            Logger.log_error(f"预留库存失败: {str(e)}")
            raise
    
    def consume_inventory(self, material_id, department, consume_qty, source_type, source_no, source_id, reservation_no=None):
        """消耗库存（生产领料）"""
        try:
            self.db_manager.begin_transaction()
            
            # 如果有预留单号，先处理预留
            if reservation_no:
                self.process_reservation_consumption(reservation_no, consume_qty)
            
            # 检查可用数量（包括预留数量）
            total_available = self.get_total_available_quantity(material_id, department)
            if total_available < consume_qty:
                raise Exception(f"库存不足，总可用数量: {total_available}, 需要数量: {consume_qty}")
            
            # 获取物料信息
            material_info = self.get_material_info(material_id)
            unit = material_info['base_unit']
            unit_cost = material_info['cost']
            
            # 生成流水单号
            transaction_no = self.generate_transaction_number()
            
            # 创建出库流水
            current_balance = self.get_current_balance(material_id, department)
            new_balance = current_balance - consume_qty
            
            insert_transaction = """
            INSERT INTO inventory_transactions (
                transaction_no, transaction_date, transaction_type, material_id, material_name,
                material_category_id, department, out_qty, balance_qty, unit, unit_cost, total_cost,
                source_type, source_no, source_id, create_time, create_user
            ) VALUES (%s, %s, 3, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            current_time = datetime.now()
            current_user = self.current_user.get('username', 'system')
            
            self.db_manager.execute_update(insert_transaction, (
                transaction_no, date.today(), material_id, material_info['name'],
                material_info['category_id'], department, consume_qty, new_balance,
                unit, unit_cost, consume_qty * unit_cost,
                source_type, source_no, source_id, current_time, current_user
            ))
            
            # 更新部门库存
            update_inventory = """
            UPDATE department_inventory 
            SET total_qty = total_qty - %s,
                available_qty = GREATEST(0, available_qty - %s),
                last_out_date = %s,
                update_time = %s,
                update_user = %s
            WHERE material_id = %s AND department = %s
            """
            
            self.db_manager.execute_update(update_inventory, (
                consume_qty, consume_qty, date.today(), current_time, current_user, material_id, department
            ))
            
            self.db_manager.commit_transaction()
            
            return {
                'success': True,
                'transaction_no': transaction_no,
                'consumed_qty': consume_qty,
                'remaining_balance': new_balance,
                'message': f'成功消耗库存 {consume_qty} {unit}'
            }
            
        except Exception as e:
            self.db_manager.rollback_transaction()
            Logger.log_error(f"消耗库存失败: {str(e)}")
            raise
    
    def generate_purchase_suggestions(self, shortage_results):
        """生成采购建议"""
        try:
            purchase_suggestions = []
            
            for shortage in shortage_results['details']:
                if shortage['need_purchase']:
                    # 计算建议采购数量（考虑安全库存）
                    min_stock = self.get_min_stock_quantity(shortage['material_id'], shortage_results['department'])
                    suggested_qty = shortage['shortage_qty'] + min_stock
                    
                    # 获取供应商信息
                    suppliers = self.get_material_suppliers(shortage['material_id'])
                    
                    purchase_suggestions.append({
                        'material_id': shortage['material_id'],
                        'material_name': shortage['material_name'],
                        'shortage_qty': shortage['shortage_qty'],
                        'min_stock_qty': min_stock,
                        'suggested_purchase_qty': suggested_qty,
                        'unit_cost': shortage['unit_cost'],
                        'estimated_amount': suggested_qty * shortage['unit_cost'],
                        'suppliers': suppliers,
                        'priority': self.calculate_purchase_priority(shortage),
                        'department': shortage_results['department']
                    })
            
            return {
                'department': shortage_results['department'],
                'total_suggestions': len(purchase_suggestions),
                'total_estimated_amount': sum(s['estimated_amount'] for s in purchase_suggestions),
                'suggestions': purchase_suggestions
            }
            
        except Exception as e:
            Logger.log_error(f"生成采购建议失败: {str(e)}")
            return {'suggestions': []}
    
    # 辅助方法
    def get_material_cost(self, material_id):
        """获取物料成本"""
        try:
            query = "SELECT cost FROM materials WHERE material_id = %s"
            result = self.db_manager.execute_query(query, (material_id,))
            return float(result[0]['cost']) if result and result[0]['cost'] else 0.0
        except:
            return 0.0
    
    def get_material_unit(self, material_id):
        """获取物料单位"""
        try:
            query = "SELECT base_unit FROM materials WHERE material_id = %s"
            result = self.db_manager.execute_query(query, (material_id,))
            return result[0]['base_unit'] if result else 'PCS'
        except:
            return 'PCS'
    
    def generate_transaction_number(self):
        """生成库存流水单号"""
        try:
            from system.auto_number_module import AutoNumberService
            auto_service = AutoNumberService()
            return auto_service.generate_number(
                rule_code='KCLS',
                business_date=date.today().strftime('%Y-%m-%d'),
                business_type='inventory_transaction',
                user_id=self.current_user.get('user_id')
            )
        except:
            return f"KCLS{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def generate_reservation_number(self):
        """生成库存预留单号"""
        try:
            from system.auto_number_module import AutoNumberService
            auto_service = AutoNumberService()
            return auto_service.generate_number(
                rule_code='KCYL',
                business_date=date.today().strftime('%Y-%m-%d'),
                business_type='inventory_reservation',
                user_id=self.current_user.get('user_id')
            )
        except:
            return f"KCYL{datetime.now().strftime('%Y%m%d%H%M%S')}"