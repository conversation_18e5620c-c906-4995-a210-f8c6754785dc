from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                           QTableWidget, QTableWidgetItem, QLabel, QComboBox,
                           QDateEdit, QFrame, QMessageBox, QHeaderView, QDialog,
                           QTextBrowser, QFileDialog)
from PyQt6.QtCore import Qt, QDate, QSizeF, QMarginsF
from PyQt6.QtPrintSupport import QPrinter
from PyQt6.QtGui import QTextDocument, QPageSize
from core.logger import Logger
import json
import os

class ReportModule(QWidget):
    def __init__(self, return_db):
        super().__init__()
        self.return_db = return_db
        self.is_box_kpi = False  # 添加标志，默认为普通KPI
        self.setup_ui()
        self.load_logistics_companies()  # 初始化时加载物流公司列表
        
    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 第一行搜索条件
        first_search_layout = QHBoxLayout()
        first_search_layout.setSpacing(10)
        
        # 日期范围选择
        first_search_layout.addWidget(QLabel("日期范围:"))
        self.start_date = QDateEdit()
        self.end_date = QDateEdit()
        
        # 设置日期控件属性
        for date_edit in [self.start_date, self.end_date]:
            date_edit.setDisplayFormat("yyyy-MM-dd")
            date_edit.setCalendarPopup(True)
            date_edit.setMinimumWidth(120)
            date_edit.setFixedWidth(150)
            date_edit.setFixedHeight(30)
        
        # 设置默认日期范围
        today = QDate.currentDate()
        self.start_date.setDate(today.addDays(-30))
        self.end_date.setDate(today)
        
        first_search_layout.addWidget(self.start_date)
        first_search_layout.addWidget(QLabel("至"))
        first_search_layout.addWidget(self.end_date)
        
        # 物流公司选择
        first_search_layout.addWidget(QLabel("物流公司:"))
        self.logistics_combo = QComboBox()
        self.logistics_combo.setFixedWidth(200)
        self.logistics_combo.setFixedHeight(30)
        first_search_layout.addWidget(self.logistics_combo)
        
        # 搜索和重置按钮
        self.search_btn = QPushButton("🔍 查询")
        self.reset_btn = QPushButton("重置")
        self.report_btn = QPushButton("📊 显示报表")  # 添加报表按钮
        self.box_kpi_btn = QPushButton("📦 原箱计算KPI")  # 添加原箱计算KPI按钮
        
        # 设置按钮大小
        for btn in [self.search_btn, self.reset_btn, self.report_btn, self.box_kpi_btn]:  # 添加新按钮到列表
            btn.setFixedHeight(30)
            btn.setMinimumWidth(80)
        
        first_search_layout.addWidget(self.search_btn)
        first_search_layout.addWidget(self.reset_btn)
        first_search_layout.addWidget(self.report_btn)
        first_search_layout.addWidget(self.box_kpi_btn)  # 添加原箱计算KPI按钮到布局
        
        first_search_layout.addStretch()
        layout.addLayout(first_search_layout)
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectItems)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 设置表格列
        self.table.setColumnCount(9)
        self.table.setHorizontalHeaderLabels([
            "物流公司", "退货数量", "退货金额", 
            "原料数量", "原料金额", "变形数量", 
            "变形金额", "报废数量", "报废金额"
        ])
        
        # 设置表格列宽
        header = self.table.horizontalHeader()
        for i in range(self.table.columnCount()):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.table)
        
        # 连接信号
        self.search_btn.clicked.connect(self.search_kpi)
        self.reset_btn.clicked.connect(self.reset_search)
        self.report_btn.clicked.connect(self.show_report)
        self.box_kpi_btn.clicked.connect(self.toggle_kpi_mode)  # 连接原箱计算KPI按钮信号
        self.start_date.dateChanged.connect(self.search_kpi)
        self.end_date.dateChanged.connect(self.search_kpi)
        self.logistics_combo.currentIndexChanged.connect(self.search_kpi)
        
        # 存储当前查询结果
        self.current_data = None
    
    def load_logistics_companies(self):
        """加载物流公司列表"""
        try:
            companies = self.return_db.get_logistics_companies()
            self.logistics_combo.clear()
            self.logistics_combo.addItem("全部物流公司")
            for company in companies:
                self.logistics_combo.addItem(company)
            Logger.log_success("加载物流公司列表成功")
        except Exception as e:
            Logger.log_error("加载物流公司列表失败", error=e)
            QMessageBox.warning(self, "警告", f"加载物流公司列表失败：{str(e)}")
    
    def reset_search(self):
        """重置搜索条件"""
        # 重置日期为最近30天
        today = QDate.currentDate()
        self.start_date.setDate(today.addDays(-30))
        self.end_date.setDate(today)
        
        # 重置物流公司选择
        self.logistics_combo.setCurrentIndex(0)
        
        # 执行搜索
        self.search_kpi()
    
    def search_kpi(self):
        """查询KPI数据"""
        try:
            # 获取查询参数
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")
            logistics_company = self.logistics_combo.currentText()
            if logistics_company == "全部物流公司":
                logistics_company = None
            
            # 根据模式调用不同的数据获取方法
            if self.is_box_kpi:
                results = self.return_db.get_box_kpi_data(
                    start_date=start_date,
                    end_date=end_date,
                    logistics_company=logistics_company
                )
            else:
                results = self.return_db.get_kpi_data(
                    start_date=start_date,
                    end_date=end_date,
                    logistics_company=logistics_company
                )
            
            # 保存当前查询结果
            self.current_data = results
            
            # 清空表格
            self.table.setRowCount(0)
            
            # 填充数据
            for row, data in enumerate(results):
                self.table.insertRow(row)
                
                # 设置物流公司
                self.table.setItem(row, 0, QTableWidgetItem(str(data['logistics_company'] or '')))
                
                # 设置拒收数据
                self.table.setItem(row, 1, QTableWidgetItem(str(data['reject_quantity'] or 0)))
                self.table.setItem(row, 2, QTableWidgetItem(str(data['reject_amount'] or 0)))
                
                # 设置原料数据
                self.table.setItem(row, 3, QTableWidgetItem(str(data['raw_quantity'] or 0)))
                self.table.setItem(row, 4, QTableWidgetItem(str(data['raw_amount'] or 0)))
                
                # 设置变形数据
                self.table.setItem(row, 5, QTableWidgetItem(str(data['deformed_quantity'] or 0)))
                self.table.setItem(row, 6, QTableWidgetItem(str(data['deformed_amount'] or 0)))
                
                # 设置报废数据
                self.table.setItem(row, 7, QTableWidgetItem(str(data['scrap_quantity'] or 0)))
                self.table.setItem(row, 8, QTableWidgetItem(str(data['scrap_amount'] or 0)))
                
                # 设置所有单元格居中对齐
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    if item:
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询失败：{str(e)}")
    
    def show_report(self):
        """显示报表"""
        if not self.current_data:
            QMessageBox.warning(self, "提示", "请先查询数据")
            return
            
        # 创建报表查看器
        viewer = ReportViewer(self)
        viewer.show_report(self.current_data)
        viewer.exec()

    def toggle_kpi_mode(self):
        """切换普通KPI和原箱KPI模式"""
        self.is_box_kpi = not self.is_box_kpi
        if self.is_box_kpi:
            self.box_kpi_btn.setText("📊 普通KPI")
        else:
            self.box_kpi_btn.setText("📦 原箱计算KPI")
        self.search_kpi()

class ReportViewer(QDialog):
    """报表查看器窗口"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("报表查看器")
        self.setMinimumSize(800, 600)
        
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 创建文本浏览器
        self.text_browser = QTextBrowser()
        self.text_browser.setOpenExternalLinks(True)  # 允许打开外部链接
        layout.addWidget(self.text_browser)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        # 创建导出PDF按钮
        export_pdf_btn = QPushButton("导出PDF")
        export_pdf_btn.clicked.connect(self.export_to_pdf)
        button_layout.addWidget(export_pdf_btn)
        
        # 创建关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
    def show_report(self, data):
        """显示报表数据"""
        # 生成HTML内容
        html_content = self.generate_html_report(data)
        # 加载HTML内容
        self.text_browser.setHtml(html_content)
        
    def export_to_pdf(self):
        """导出为PDF文件"""
        try:
            # 获取保存文件路径
            file_name, _ = QFileDialog.getSaveFileName(
                self,
                "保存PDF文件",
                f"物流KPI报表_{QDate.currentDate().toString('yyyy-MM-dd')}.pdf",
                "PDF文件 (*.pdf)"
            )
            
            if file_name:
                # 使用 QPrinter 创建 PDF 文件
                from PyQt6.QtPrintSupport import QPrinter
                
                printer = QPrinter()
                printer.setOutputFormat(QPrinter.OutputFormat.PdfFormat)
                printer.setOutputFileName(file_name)
                printer.setPageSize(QPageSize(QPageSize.PageSizeId.A4))
                
                # 使用 QTextBrowser 的 print 方法直接打印为 PDF
                self.text_browser.print(printer)
                
                QMessageBox.information(self, "成功", "PDF文件已成功导出！")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出PDF失败：{str(e)}")
        
    def generate_html_report(self, data):
        """生成HTML报表内容"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>物流KPI报表</title>
            <style>
                body {{
                    font-family: 'Microsoft YaHei', sans-serif;
                    margin: 20px;
                    background-color: #f5f5f5;
                }}
                .report-container {{
                    background-color: white;
                    padding: 20px;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                    font-size: 14px;
                }}
                th, td {{
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: center;
                }}
                th {{
                    background-color: #f2f2f2;
                    font-weight: bold;
                }}
                thead th {{
                    background-color: #e6e6e6;
                }}
                tr:nth-child(even) {{
                    background-color: #f9f9f9;
                }}
                thead tr:first-child th {{
                    background-color: #d9edf7;
                    color: #31708f;
                }}
                /* 数字列的样式 */
                td:nth-child(even) {{
                    color: #e74c3c;
                }}
                td:nth-child(odd):not(:first-child) {{
                    color: #3498db;
                }}
                /* 物流公司列的样式 */
                td:first-child {{
                    color: #2c3e50;
                    font-weight: bold;
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 20px;
                }}
                .header h1 {{
                    color: #333;
                    margin-bottom: 10px;
                }}
                .header p {{
                    color: #666;
                }}
                .summary {{
                    margin-top: 20px;
                    padding: 15px;
                    background-color: #e9f7fe;
                    border-radius: 5px;
                }}
                @media print {{
                    body {{
                        background-color: white;
                    }}
                    .report-container {{
                        box-shadow: none;
                    }}
                    .no-print {{
                        display: none;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="report-container">
                <div class="header">
                    <h1>物流KPI考核报表 {report_type}</h1>
                    <p>统计日期范围：{start_date} 至 {end_date}</p>
                </div>
                
                <table>
                    <thead>
                        <tr>
                            <th rowspan="2">物流公司</th>
                            <th colspan="2">拒收</th>
                            <th colspan="2">原料</th>
                            <th colspan="2">变形</th>
                            <th colspan="2">报废</th>
                        </tr>
                        <tr>
                            <th>数量</th>
                            <th>金额</th>
                            <th>数量</th>
                            <th>金额</th>
                            <th>数量</th>
                            <th>金额</th>
                            <th>数量</th>
                            <th>金额</th>
                        </tr>
                    </thead>
                    <tbody>
                        {table_rows}
                    </tbody>
                </table>
                
                <div class="summary">
                    <h3>统计摘要</h3>
                    <p>总拒收数量：{total_reject_quantity}</p>
                    <p>总拒收金额：{total_reject_amount}</p>
                    <p>总原料数量：{total_raw_quantity}</p>
                    <p>总原料金额：{total_raw_amount}</p>
                    <p>总变形数量：{total_deformed_quantity}</p>
                    <p>总变形金额：{total_deformed_amount}</p>
                    <p>总报废数量：{total_scrap_quantity}</p>
                    <p>总报废金额：{total_scrap_amount}</p>
                </div>
                
                <div class="no-print" style="text-align: center; margin-top: 20px;">
                    <p>打印时间：{print_time}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # 计算总计数据
        total_reject_quantity = sum(float(item['reject_quantity'] or 0) for item in data)
        total_reject_amount = sum(float(item['reject_amount'] or 0) for item in data)
        total_raw_quantity = sum(float(item['raw_quantity'] or 0) for item in data)
        total_raw_amount = sum(float(item['raw_amount'] or 0) for item in data)
        total_deformed_quantity = sum(float(item['deformed_quantity'] or 0) for item in data)
        total_deformed_amount = sum(float(item['deformed_amount'] or 0) for item in data)
        total_scrap_quantity = sum(float(item['scrap_quantity'] or 0) for item in data)
        total_scrap_amount = sum(float(item['scrap_amount'] or 0) for item in data)
        
        # 生成表格行
        table_rows = ""
        for item in data:
            table_rows += f"""
            <tr>
                <td>{item['logistics_company'] or ''}</td>
                <td>{item['reject_quantity'] or 0}</td>
                <td>{item['reject_amount'] or 0}</td>
                <td>{item['raw_quantity'] or 0}</td>
                <td>{item['raw_amount'] or 0}</td>
                <td>{item['deformed_quantity'] or 0}</td>
                <td>{item['deformed_amount'] or 0}</td>
                <td>{item['scrap_quantity'] or 0}</td>
                <td>{item['scrap_amount'] or 0}</td>
            </tr>
            """
        
        # 获取报表类型
        report_type = "(原箱计算)" if self.parent().is_box_kpi else ""
        
        # 格式化HTML
        html = html.format(
            report_type=report_type,
            start_date=self.parent().start_date.date().toString("yyyy-MM-dd"),
            end_date=self.parent().end_date.date().toString("yyyy-MM-dd"),
            table_rows=table_rows,
            total_reject_quantity=total_reject_quantity,
            total_reject_amount=total_reject_amount,
            total_raw_quantity=total_raw_quantity,
            total_raw_amount=total_raw_amount,
            total_deformed_quantity=total_deformed_quantity,
            total_deformed_amount=total_deformed_amount,
            total_scrap_quantity=total_scrap_quantity,
            total_scrap_amount=total_scrap_amount,
            print_time=QDate.currentDate().toString("yyyy-MM-dd")
        )
        
        return html
    

    