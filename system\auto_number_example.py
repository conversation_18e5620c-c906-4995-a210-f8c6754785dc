"""
自动编号使用示例 - 展示如何在其他模块中使用自动编号功能
"""

from .auto_number_module import AutoNumberService
from datetime import date

def example_usage():
    """使用示例"""
    
    # 1. 创建自动编号服务实例
    auto_number_service = AutoNumberService()
    
    # 2. 生成出库单编号
    try:
        delivery_number = auto_number_service.generate_number(
            rule_code='CJ',  # 出库单规则编码
            business_date=date.today().strftime('%Y-%m-%d'),
            business_type='delivery_order',
            business_id='DO001',
            user_id=1
        )
        print(f"生成的出库单编号: {delivery_number}")
        # 输出示例: CJ-20241201-0001
    except Exception as e:
        print(f"生成出库单编号失败: {str(e)}")
    
    # 3. 生成退货单编号
    try:
        return_number = auto_number_service.generate_number(
            rule_code='TH',  # 退货单规则编码
            business_date=date.today().strftime('%Y-%m-%d'),
            business_type='return_order',
            business_id='RO001',
            user_id=1
        )
        print(f"生成的退货单编号: {return_number}")
        # 输出示例: TH-20241201-0001
    except Exception as e:
        print(f"生成退货单编号失败: {str(e)}")
    
    # 4. 生成交接单编号
    try:
        handover_number = auto_number_service.generate_number(
            rule_code='JJ',  # 交接单规则编码
            business_date=date.today().strftime('%Y-%m-%d'),
            business_type='handover_order',
            business_id='HO001',
            user_id=1
        )
        print(f"生成的交接单编号: {handover_number}")
        # 输出示例: JJ-20241201-0001
    except Exception as e:
        print(f"生成交接单编号失败: {str(e)}")
    
    # 5. 获取规则信息
    try:
        rule_info = auto_number_service.get_rule_info('CJ')
        if rule_info:
            print(f"出库单规则信息: {rule_info}")
        else:
            print("规则不存在")
    except Exception as e:
        print(f"获取规则信息失败: {str(e)}")
    
    # 6. 获取编号历史
    try:
        history = auto_number_service.get_number_history(
            rule_code='CJ',
            business_date=date.today().strftime('%Y-%m-%d')
        )
        print(f"今日出库单编号历史: {len(history)} 条记录")
        for record in history:
            print(f"  - {record['generated_number']} ({record['business_type']})")
    except Exception as e:
        print(f"获取编号历史失败: {str(e)}")


# 在退货管理模块中的使用示例
class ReturnOrderManager:
    """退货单管理器示例"""
    
    def __init__(self):
        self.auto_number_service = AutoNumberService()
    
    def create_return_order(self, user_id, order_data):
        """创建退货单"""
        try:
            # 生成退货单编号
            return_number = self.auto_number_service.generate_number(
                rule_code='TH',
                business_date=date.today().strftime('%Y-%m-%d'),
                business_type='return_order',
                business_id=None,  # 先不设置，等创建成功后再更新
                user_id=user_id
            )
            
            # 这里可以添加创建退货单的数据库操作
            # ...
            
            print(f"退货单创建成功，编号: {return_number}")
            return return_number
            
        except Exception as e:
            print(f"创建退货单失败: {str(e)}")
            return None
    
    def create_delivery_order(self, user_id, order_data):
        """创建出库单"""
        try:
            # 生成出库单编号
            delivery_number = self.auto_number_service.generate_number(
                rule_code='CJ',
                business_date=date.today().strftime('%Y-%m-%d'),
                business_type='delivery_order',
                business_id=None,
                user_id=user_id
            )
            
            # 这里可以添加创建出库单的数据库操作
            # ...
            
            print(f"出库单创建成功，编号: {delivery_number}")
            return delivery_number
            
        except Exception as e:
            print(f"创建出库单失败: {str(e)}")
            return None


# 在物料管理模块中的使用示例
class MaterialManager:
    """物料管理器示例"""
    
    def __init__(self):
        self.auto_number_service = AutoNumberService()
    
    def create_material(self, user_id, material_data):
        """创建物料"""
        try:
            # 生成物料编号
            material_number = self.auto_number_service.generate_number(
                rule_code='WL',
                business_date=date.today().strftime('%Y-%m-%d'),
                business_type='material',
                business_id=None,
                user_id=user_id
            )
            
            # 这里可以添加创建物料的数据库操作
            # ...
            
            print(f"物料创建成功，编号: {material_number}")
            return material_number
            
        except Exception as e:
            print(f"创建物料失败: {str(e)}")
            return None


if __name__ == "__main__":
    # 运行示例
    print("=== 自动编号使用示例 ===")
    example_usage()
    
    print("\n=== 退货单管理器示例 ===")
    return_manager = ReturnOrderManager()
    return_manager.create_return_order(1, {})
    return_manager.create_delivery_order(1, {})
    
    print("\n=== 物料管理器示例 ===")
    material_manager = MaterialManager()
    material_manager.create_material(1, {}) 