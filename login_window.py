import sys
import os
import hashlib
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                           QCheckBox, QMessageBox, QFrame, QSplashScreen, QDialog)
from PyQt6.QtCore import Qt, QSettings, QSize
from PyQt6.QtGui import QIcon, QPixmap, QFont

from core.database import DatabasePool
from modules.db_manager import DatabaseManager
from modules.return_db import ReturnDB
from core.config import set_current_user

class LoginWindow(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        print("初始化登录窗口...")
        self.setWindowTitle("退货管理系统 - 登录")
        self.resize(400, 500)
        
        # 确保窗口是模态的
        self.setModal(True)
        
        # 设置窗口样式，但允许正常显示
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint)
        
        # 设置窗口图标
        icon_path = os.path.join(os.path.dirname(__file__), 'resources', 'icons', 'app.ico')
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # 加载QSettings
        self.settings = QSettings("ReturnSystem", "LoginSettings")
        
        # 初始化UI
        self.init_ui()
        
        # 加载保存的用户名和密码
        self.load_saved_credentials()
        
        # 鼠标拖动相关变量
        self.dragging = False
        self.drag_position = None
        
        print("登录窗口初始化完成")
    
    def init_ui(self):
        """初始化UI"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 顶部标题栏
        title_bar = QFrame()
        # title_bar.setFixedHeight(40)
        # title_bar.setStyleSheet("""
        #     QFrame {
        #         background-color: #2C3E50;
        #         border-top-left-radius: 5px;
        #         border-top-right-radius: 5px;
        #     }
        # """)
        
        # title_layout = QHBoxLayout(title_bar)
        # title_layout.setContentsMargins(10, 0, 10, 0)
        
        # # 标题
        # title_label = QLabel("退货管理系统 - 用户登录")
        # title_label.setStyleSheet("color: white; font-size: 14px; font-weight: bold;")
        
        # # 关闭按钮
        # close_button = QPushButton("×")
        # close_button.setFixedSize(26, 26)
        # close_button.setStyleSheet("""
        #     QPushButton {
        #         background-color: transparent;
        #         color: white;
        #         font-size: 20px;
        #         font-weight: bold;
        #         border: none;
        #         margin: 0px;
        #     }
        #     QPushButton:hover {
        #         background-color: #E74C3C;
        #         border-radius: 13px;
        #     }
        #     QPushButton:pressed {
        #         background-color: #C0392B;
        #         border-radius: 13px;
        #     }
        # """)
        # close_button.clicked.connect(self.reject)  # 使用reject来取消登录
        
        # title_layout.addWidget(title_label)
        # title_layout.addStretch()
        # title_layout.addWidget(close_button)
        
        #主内容区域
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-bottom-left-radius: 5px;
                border-bottom-right-radius: 5px;
            }
        """)
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(30, 30, 30, 30)
        content_layout.setSpacing(20)
        
        # Logo区域
        logo_layout = QHBoxLayout()
        logo_label = QLabel()
        logo_path = os.path.join(os.path.dirname(__file__), 'resources', 'icons', 'logo.png')
        if os.path.exists(logo_path):
            logo_pixmap = QPixmap(logo_path)
            logo_label.setPixmap(logo_pixmap.scaled(120, 120, Qt.AspectRatioMode.KeepAspectRatio))
        else:
            # 如果没有logo图片，显示文字
            logo_label = QLabel("退货管理系统")
            logo_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #2C3E50;")
        
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        logo_layout.addStretch()
        logo_layout.addWidget(logo_label)
        logo_layout.addStretch()
        
        # 表单区域
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)
        
        # 用户名
        username_layout = QVBoxLayout()
        username_label = QLabel("用户名")
        username_label.setStyleSheet("font-size: 12px; color: #7F8C8D;")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入用户名")
        self.username_input.setMinimumHeight(35)
        self.username_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #BDC3C7;
                border-radius: 3px;
                padding: 5px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border: 1px solid #3498DB;
            }
        """)
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        
        # 密码
        password_layout = QVBoxLayout()
        password_label = QLabel("密码")
        password_label.setStyleSheet("font-size: 12px; color: #7F8C8D;")
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setMinimumHeight(35)
        self.password_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #BDC3C7;
                border-radius: 3px;
                padding: 5px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border: 1px solid #3498DB;
            }
        """)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        
        # 记住密码选项
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("记住密码")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                color: #7F8C8D;
            }
        """)
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 登录按钮
        self.login_button = QPushButton("登 录")
        self.login_button.setMinimumHeight(40)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
            QPushButton:pressed {
                background-color: #1B6AA1;
            }
        """)
        self.login_button.clicked.connect(self.login)
        
        # 取消按钮
        self.cancel_button = QPushButton("取 消")
        self.cancel_button.setMinimumHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95A5A6;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7F8C8D;
            }
            QPushButton:pressed {
                background-color: #6C7B7D;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        
        # 添加按钮到布局
        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.cancel_button)
        
        # 将所有布局添加到表单布局
        form_layout.addLayout(username_layout)
        form_layout.addLayout(password_layout)
        form_layout.addLayout(remember_layout)
        form_layout.addLayout(button_layout)
        
        # 将所有部分添加到内容布局
        content_layout.addLayout(logo_layout)
        content_layout.addLayout(form_layout)
        content_layout.addStretch()
        
        # 版权信息
        copyright_label = QLabel("© 2023 退货管理系统 版权所有")
        copyright_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        copyright_label.setStyleSheet("font-size: 11px; color: #95A5A6;")
        content_layout.addWidget(copyright_label)
        
        # 将所有内容添加到主布局
        main_layout.addWidget(title_bar)
        main_layout.addWidget(content_frame, 1)
        
        self.setLayout(main_layout)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QWidget {
                font-family: "Microsoft YaHei", Arial;
            }
        """)
        
        # 设置回车键触发登录
        self.password_input.returnPressed.connect(self.login)
        self.username_input.returnPressed.connect(lambda: self.password_input.setFocus())
    
    def login(self):
        """执行登录操作"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, "提示", "用户名和密码不能为空")
            # 聚焦到用户名输入框
            self.username_input.setFocus()
            return
        
        # 密码加密
        md5_password = hashlib.md5(password.encode()).hexdigest()
        
        try:
            # 连接数据库
            db_manager = DatabaseManager()
            
            # 查询用户
            query = """
            SELECT u.user_id, u.username, u.real_name, u.status
            FROM user u
            WHERE u.username = %s AND u.password = %s
            LIMIT 1
            """
            result = db_manager.execute_query(query, (username, md5_password))
            
            if not result:
                QMessageBox.warning(self, "登录失败", "用户名或密码错误")
                # 记录登录失败日志
                self.log_login_attempt(None, username, False, "用户名或密码错误")
                # 清空密码输入框并聚焦
                self.password_input.clear()
                self.password_input.setFocus()
                return
            
            user = result[0]
            
            # 检查用户状态
            if user['status'] == 0:
                QMessageBox.warning(self, "登录失败", "账户已被禁用，请联系管理员")
                self.log_login_attempt(user['user_id'], username, False, "账户已被禁用")
                # 清空输入框并聚焦到用户名
                self.username_input.clear()
                self.password_input.clear()
                self.username_input.setFocus()
                return
            
            # 记住密码
            if self.remember_checkbox.isChecked():
                self.save_credentials(username, password)
            else:
                self.clear_saved_credentials()
            
            # 记录登录成功日志
            self.log_login_attempt(user['user_id'], username, True, "登录成功")
            
            # 更新用户最后登录时间和IP
            self.update_last_login(user['user_id'])
            
            # 登录成功，设置当前用户信息
            db = ReturnDB()
            db.current_user = user['real_name']
            
            # 新增：设置全局当前用户
            set_current_user(user)
            
            # 获取用户权限
            user_permissions = self.get_user_permissions(user['user_id'])
            
            # 保存用户信息和权限信息
            self.user = user
            self.permissions = user_permissions
            
            # 登录成功，关闭对话框
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "系统错误", f"登录时发生错误：{str(e)}")
            print(f"登录错误: {str(e)}")
    
    def get_user_permissions(self, user_id):
        """获取用户的权限列表"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT DISTINCT p.permission_code
            FROM permission p
            JOIN role_permission rp ON p.permission_id = rp.permission_id
            JOIN user_role ur ON rp.role_id = ur.role_id
            WHERE ur.user_id = %s
            AND p.status = 1
            """
            result = db_manager.execute_query(query, (user_id,))
            return [r['permission_code'] for r in result]
            
        except Exception as e:
            print(f"获取用户权限失败: {str(e)}")
            return []
    
    def update_last_login(self, user_id):
        """更新用户最后登录时间和IP"""
        try:
            db_manager = DatabaseManager()
            # 获取本地IP
            client_ip = self.get_local_ip()
            
            # 更新用户信息
            query = """
            UPDATE user
            SET last_login_time = NOW(),
                last_login_ip = %s
            WHERE user_id = %s
            """
            db_manager.execute_update(query, (client_ip, user_id))
        except Exception as e:
            print(f"更新用户登录信息失败: {str(e)}")
    
    def log_login_attempt(self, user_id, username, status, message):
        """记录登录尝试"""
        try:
            db_manager = DatabaseManager()
            client_ip = self.get_local_ip()
            
            query = """
            INSERT INTO login_log 
            (user_id, username, login_ip, status, message, user_agent)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            
            user_agent = f"ReturnSystem Client v1.0 ({sys.platform})"
            
            db_manager.execute_update(
                query, 
                (user_id if user_id else None, username, client_ip, 
                 1 if status else 0, message, user_agent)
            )
        except Exception as e:
            print(f"记录登录日志失败: {str(e)}")
    
    def get_local_ip(self):
        """获取本地IP"""
        try:
            import socket
            hostname = socket.gethostname()
            ip_address = socket.gethostbyname(hostname)
            return ip_address
        except:
            return "127.0.0.1"
    
    def save_credentials(self, username, password):
        """保存用户凭据"""
        self.settings.setValue("username", username)
        self.settings.setValue("password", password)
        self.settings.setValue("remember", True)
    
    def clear_saved_credentials(self):
        """清除保存的用户凭据"""
        self.settings.remove("username")
        self.settings.remove("password")
        self.settings.setValue("remember", False)
    
    def load_saved_credentials(self):
        """加载保存的用户凭据"""
        if self.settings.value("remember", False, type=bool):
            username = self.settings.value("username", "")
            password = self.settings.value("password", "")
            self.username_input.setText(username)
            self.password_input.setText(password)
            self.remember_checkbox.setChecked(True)
    
    def accept(self):
        """登录成功后的操作"""
        # 调用父类的accept方法，确保对话框正确返回Accepted状态
        super().accept()
    
    # 以下方法用于无边框窗口的拖动
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            # 只在标题栏区域允许拖动
            if event.position().y() < 40:  # 标题栏高度
                self.dragging = True
                self.drag_position = event.position().toPoint()
    
    def mouseMoveEvent(self, event):
        if self.dragging and self.drag_position:
            self.move(self.mapToGlobal(event.position().toPoint() - self.drag_position))
    
    def mouseReleaseEvent(self, event):
        self.dragging = False
        self.drag_position = None

# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    login = LoginWindow()
    login.show()
    sys.exit(app.exec()) 