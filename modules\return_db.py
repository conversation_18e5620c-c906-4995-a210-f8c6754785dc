from .db_manager import DatabaseManager
from datetime import datetime, date
import pandas as pd
import random
import socket
from PyQt6.QtGui import QImage
import qrcode
from PIL import Image
import json
from io import BytesIO
from PyQt6.QtGui import QImage, QPixmap
from PyQt6.QtCore import QByteArray, QBuffer

class ReturnDB:
    def __init__(self):
        print("初始化ReturnDB...")
        try:
            self.db = DatabaseManager()
            self.current_user = "管理员"
            self.client_ip = self.get_local_ip()
            self._logistics_companies_cache = None  # 添加缓存
            print("ReturnDB初始化成功")
        except Exception as e:
            print(f"ReturnDB初始化失败: {str(e)}")
            raise
    
    def get_delivery_orders(self, filters=None):
        """获取出库单列表"""
        try:
            total, results = self._query_delivery_orders(filters)
            
            if results:
                # 获取所有出库单号
                order_nos = [r['order_no'] for r in results]
                
                # 批量查询交接状态
                handover_query = """
                SELECT delivery_order_no, COUNT(*) as count
                FROM handover_order 
                WHERE delivery_order_no IN (%s)
                AND handover_status = 0
                GROUP BY delivery_order_no
                """
                
                # 构建IN查询的占位符
                placeholders = ','.join(['%s'] * len(order_nos))
                handover_query = handover_query % placeholders
                
                # 执行批量查询
                handover_results = self.db.execute_query(handover_query, tuple(order_nos))
                
                # 构建查询结果字典
                handover_dict = {r['delivery_order_no']: r['count'] for r in handover_results}
                
                # 将交接单状态添加到结果中
                for result in results:
                    result['has_unaudited_handover'] = handover_dict.get(result['order_no'], 0) > 0
            
            return total, results
            
        except Exception as e:
            print(f"获取出库单列表失败: {str(e)}")
            raise
    
    def _query_delivery_orders(self, filters):
        """实际的数据库查询 - MySQL 5.7 版本"""
        # 构建WHERE子句
        where_clause = ""
        params = []
        
        if filters:
            if filters.get('order_no'):
                where_clause += " AND order_no LIKE %s"
                params.append(f"%{filters['order_no']}%")
                
            if filters.get('start_date'):
                where_clause += " AND confirm_receipt_date >= %s"
                params.append(filters['start_date'])
                
            if filters.get('end_date'):
                where_clause += " AND confirm_receipt_date <= %s"
                params.append(filters['end_date'])
                
            if filters.get('source_order_no'):
                source_order_no = filters['source_order_no'].strip()
                
                # 检查是否包含逗号，判断是否为批量查询
                if ',' in source_order_no:
                    # 批量精确查询模式
                    source_order_nos = [
                        no.strip() 
                        for no in source_order_no.split(',')
                        if no.strip()
                    ]
                    if source_order_nos:
                        placeholders = ','.join(['%s' for _ in source_order_nos])
                        where_clause += """ AND (
                            source_order_no IN ({0}) OR 
                            (source_order_no LIKE 'SO%' AND 
                             SUBSTRING(
                                summary,
                                IF(
                                    LOCATE('SO', summary) > 0,
                                    LOCATE('SO', summary),
                                    1
                                ),
                                20
                            ) IN ({0})
                            )
                        )""".format(placeholders)
                        params.extend(source_order_nos)
                        params.extend(source_order_nos)
                else:
                    # 单个模糊查询模式
                    where_clause += """ AND (
                        source_order_no LIKE %s OR
                        (source_order_no LIKE 'SO%' AND 
                         SUBSTRING(
                            summary,
                            IF(
                                LOCATE('SO', summary) > 0,
                                LOCATE('SO', summary),
                                1
                            ),
                            20
                        ) LIKE %s)
                    )"""
                    search_value = f"%{source_order_no}%"
                    params.extend([search_value, search_value])
            
            if filters.get('logistics_company') and filters['logistics_company'] != "全部物流公司":
                where_clause += " AND logistics_company = %s"
                params.append(filters['logistics_company'])
            
            if filters.get('order_status') is not None:
                where_clause += " AND order_status = %s"
                params.append(filters['order_status'])
        
        # 计算分页参数
        page = filters.get('page', 1)
        page_size = filters.get('page_size', 20)
        offset = (page - 1) * page_size
        
        # 先获取总记录数
        count_query = """
        SELECT COUNT(*) as total 
        FROM (
            SELECT order_no
            FROM delivery_order 
            WHERE 1=1
            """ + where_clause + """
            GROUP BY order_no
        ) t
        """
        count_result = self.db.execute_query(count_query, tuple(params) if params else None)
        total = count_result[0]['total'] if count_result else 0
        
        # 获取分页数据
        query = """
        SELECT 
            order_no,
            MAX(confirm_receipt_date) as confirm_receipt_date,
            source_order_no,
            MAX(summary) as summary,
            MAX(customer) as customer,
            MAX(logistics_company) as logistics_company,
            SUM(remaining_return_quantity) as remaining_return_quantity,
            MAX(CASE order_status 
                WHEN 0 THEN '开启'
                WHEN 1 THEN '关闭'
            END) as status,
            MAX(CASE 
                WHEN source_order_no LIKE 'SO%' AND summary REGEXP '[A-Za-z0-9]+'
                THEN SUBSTRING(
                    summary,
                    IF(
                        LOCATE('SO', summary) > 0,
                        LOCATE('SO', summary),
                        1
                    ),
                    20
                )
                ELSE source_order_no
            END) as original_order_no
        FROM delivery_order 
        WHERE 1=1
        """ + where_clause + """
        GROUP BY order_no
        ORDER BY MAX(business_date) DESC, order_no DESC
        LIMIT %s OFFSET %s
        """
        
        # 添加分页参数
        params.extend([page_size, offset])
        
        # 执行查询
        results = self.db.execute_query(query, tuple(params))
        
        return total, results
    
    def get_handover_orders(self, filters=None):
        """获取交接单列表"""
        try:
            # 构建基础查询
            count_query = """
            SELECT COUNT(*) as total 
            FROM handover_order ho
            WHERE ho.handover_status = 0
            """
            
            query = """
            SELECT 
                ho.handover_no,
                ho.handover_date,
                ho.delivery_order_no,
                ho.source_order_no,
                CASE 
                    WHEN ho.source_order_no LIKE 'SO%' AND ho.summary REGEXP '[A-Za-z0-9]+'
                    THEN SUBSTRING(
                        ho.summary,
                        IF(
                            LOCATE('SO', ho.summary) > 0,
                            LOCATE('SO', ho.summary),
                            1
                        ),
                        20
                    )
                    ELSE ho.source_order_no 
                END as original_order_no,
                ho.customer,
                ho.logistics_company,
                CASE ho.print_status
                    WHEN 0 THEN '未打印'
                    WHEN 1 THEN '已打印'
                END as print_status_text
            FROM handover_order ho
            WHERE ho.handover_status = 0
            """
            
            params = []
            count_params = []
            
            # 添加过滤条件
            if filters:
                # 处理搜索文本
                if filters.get('search_text'):
                    search_value = f"%{filters['search_text']}%"
                    search_type = filters.get('search_type', '原始单号')
                    field_map = {
                        '原始单号': """
                            CASE 
                                WHEN ho.source_order_no LIKE 'SO%' AND ho.summary REGEXP '[A-Za-z0-9]+'
                                THEN SUBSTRING(
                                    ho.summary,
                                    IF(
                                        LOCATE('SO', ho.summary) > 0,
                                        LOCATE('SO', ho.summary),
                                        1
                                    ),
                                    20
                                )
                                ELSE ho.source_order_no 
                            END
                        """,
                        '交接单号': 'ho.handover_no',
                        '出库单号': 'ho.delivery_order_no'
                    }
                    if field := field_map.get(search_type):
                        count_query += f" AND {field} LIKE %s"
                        query += f" AND {field} LIKE %s"
                        count_params.append(search_value)
                        params.append(search_value)
                
                # 处理日期范围
                if filters.get('start_date'):
                    count_query += " AND ho.handover_date >= %s"
                    query += " AND ho.handover_date >= %s"
                    count_params.append(filters['start_date'])
                    params.append(filters['start_date'])
                
                if filters.get('end_date'):
                    count_query += " AND ho.handover_date <= %s"
                    query += " AND ho.handover_date <= %s"
                    count_params.append(filters['end_date'])
                    params.append(filters['end_date'])
                
                # 处理物流公司
                if filters.get('logistics_company') and filters['logistics_company'] != "全部物流公司":
                    count_query += " AND ho.logistics_company = %s"
                    query += " AND ho.logistics_company = %s"
                    count_params.append(filters['logistics_company'])
                    params.append(filters['logistics_company'])
                
                # 处理退货类型
                if filters.get('return_type') is not None and filters['return_type'] != "全部":
                    count_query += " AND ho.return_type = %s"
                    query += " AND ho.return_type = %s"
                    count_params.append(filters['return_type'])
                    params.append(filters['return_type'])
                
                # 处理打印状态
                if filters.get('print_status') is not None and filters['print_status'] != "全部":
                    count_query += " AND ho.print_status = %s"
                    query += " AND ho.print_status = %s"
                    count_params.append(0 if filters['print_status'] == "未打印" else 1)
                    params.append(0 if filters['print_status'] == "未打印" else 1)
            
            # 添加排序
            query += " ORDER BY ho.handover_date DESC, ho.handover_no DESC"
            
            # 添加分页
            if filters and 'page' in filters and 'page_size' in filters:
                offset = (filters['page'] - 1) * filters['page_size']
                query += f" LIMIT {filters['page_size']} OFFSET {offset}"
            
            # 执行查询
            count_result = self.db.execute_query(count_query, tuple(count_params) if count_params else None)
            total = count_result[0]['total'] if count_result else 0
            
            results = self.db.execute_query(query, tuple(params) if params else None)
            
            return total, results
            
        except Exception as e:
            raise Exception(f"获取交接单列表失败: {str(e)}")
    
    def get_return_warehouse_orders(self, filters=None):
        """获取退货入库单列表"""
        try:
            # 构建基础查询
            count_query = """
            SELECT COUNT(*) as total 
            FROM handover_order ho
            WHERE ho.handover_status <> 0  # 排除保存状态的单据
            """
            
            query = """
            SELECT 
                ho.handover_no,
                ho.audit_time,
                ho.delivery_order_no,
                ho.source_order_no,
                CASE 
                    WHEN ho.source_order_no LIKE 'SO%' AND ho.summary REGEXP '[A-Za-z0-9]+'
                    THEN SUBSTRING(
                        ho.summary,
                        IF(
                            LOCATE('SO', ho.summary) > 0,
                            LOCATE('SO', ho.summary),
                            1
                        ),
                        20
                    )
                    ELSE ho.source_order_no 
                END as original_order_no,
                ho.customer,
                ho.department,
                ho.logistics_company,
                CASE ho.return_type
                    WHEN 0 THEN '拒收退货'
                    WHEN 1 THEN '仓退'
                    WHEN 2 THEN '代销退货'
                END as return_type_text,
                CASE ho.handover_status
                    WHEN 1 THEN '已审核'
                    WHEN 2 THEN '已作废'
                    ELSE '保存'
                END as status_text,
                ho.handover_status
            FROM handover_order ho
            WHERE ho.handover_status <> 0  # 排除保存状态的单据
            """
            
            params = []
            count_params = []
            
            # 添加过滤条件
            if filters:
                # 处理搜索文本
                if filters.get('search_text'):
                    search_value = f"%{filters['search_text']}%"
                    search_type = filters.get('search_type', '原始单号')
                    field_map = {
                        '原始单号': """
                            CASE 
                                WHEN ho.source_order_no LIKE 'SO%' AND ho.summary REGEXP '[A-Za-z0-9]+'
                                THEN SUBSTRING(
                                    ho.summary,
                                    IF(
                                        LOCATE('SO', ho.summary) > 0,
                                        LOCATE('SO', ho.summary),
                                        1
                                    ),
                                    20
                                )
                                ELSE ho.source_order_no 
                            END
                        """,
                        '交接单号': 'ho.handover_no',
                        '出库单号': 'ho.delivery_order_no'
                    }
                    if field := field_map.get(search_type):
                        count_query += f" AND {field} LIKE %s"
                        query += f" AND {field} LIKE %s"
                        count_params.append(search_value)
                        params.append(search_value)
                
                # 处理日期范围
                if filters.get('start_date'):
                    count_query += " AND DATE(ho.audit_time) >= %s"  # 修改这里
                    query += " AND DATE(ho.audit_time) >= %s"        # 修改这里
                    count_params.append(filters['start_date'])
                    params.append(filters['start_date'])
                
                if filters.get('end_date'):
                    count_query += " AND DATE(ho.audit_time) <= %s"  # 修改这里
                    query += " AND DATE(ho.audit_time) <= %s"        # 修改这里
                    count_params.append(filters['end_date'])
                    params.append(filters['end_date'])
                
                # 处理物流公司
                if filters.get('logistics_company') and filters['logistics_company'] != "全部物流公司":
                    count_query += " AND ho.logistics_company = %s"
                    query += " AND ho.logistics_company = %s"
                    count_params.append(filters['logistics_company'])
                    params.append(filters['logistics_company'])
                
                # 处理退货类型
                if filters.get('return_type') is not None and filters['return_type'] != "全部":
                    count_query += " AND ho.return_type = %s"
                    query += " AND ho.return_type = %s"
                    count_params.append(filters['return_type'])
                    params.append(filters['return_type'])
                
                # 处理单据状态
                if filters.get('status') is not None and filters['status'] != "全部":
                    count_query += " AND ho.handover_status = %s"
                    query += " AND ho.handover_status = %s"
                    count_params.append(filters['status'])
                    params.append(filters['status'])
            
            # 添加排序
            query += " ORDER BY ho.audit_time DESC, ho.handover_no DESC"
            
            # 添加分页
            if not filters.get('export_all'):
                if filters and 'page' in filters and 'page_size' in filters:
                    offset = (filters['page'] - 1) * filters['page_size']
                    query += f" LIMIT {filters['page_size']} OFFSET {offset}"
            
            # 执行查询
            count_result = self.db.execute_query(count_query, tuple(count_params) if count_params else None)
            total = count_result[0]['total'] if count_result else 0
            
            results = self.db.execute_query(query, tuple(params) if params else None)
            
            return total, results
            
        except Exception as e:
            raise Exception(f"获取退货入库单列表失败: {str(e)}")
    
    def get_logistics_companies(self):
        """获取所有物流公司列表"""
        if self._logistics_companies_cache is None:
            query = "SELECT DISTINCT logistics_company FROM delivery_order WHERE logistics_company IS NOT NULL"
            results = self.db.execute_query(query)
            self._logistics_companies_cache = [company['logistics_company'] for company in results]
        return self._logistics_companies_cache
    
    def add_return(self, return_data):
        """添加退货记录"""
        query = """
        INSERT INTO returns (return_number, order_number, return_date, 
                           amount, reason, status)
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        return self.db.execute_update(query, (
            return_data['return_number'],
            return_data['order_number'],
            return_data['return_date'],
            return_data['amount'],
            return_data['reason'],
            return_data['status']
        ))
    
    def check_duplicate_autoids(self, autoids):
        """检查重的autoid"""
        if not autoids:
            return []
        
        query = """
        SELECT autoid 
        FROM delivery_order 
        WHERE autoid IN (%s)
        """
        placeholders = ','.join(['%s'] * len(autoids))
        query = query % placeholders
        
        results = self.db.execute_query(query, tuple(autoids))
        return [r['autoid'] for r in results]
    
    def import_delivery_orders(self, orders):
        """导入出库单数据"""
        try:
            print("\n执数据导入...")
            print(f"待导记录数: {len(orders)}")
            
            # 执行批量插入
            insert_sql = """
            INSERT INTO delivery_order (
                autoid, business_date, order_no, source_order_no, 
                customer, department, logistics_company, confirm_receipt_date,
                summary, material_code, material_name, unit, batch_no,
                warehouse, reject_reason, delivery_quantity, confirmed_quantity,
                return_quantity, remaining_return_quantity, order_status, unit_price_with_tax
            ) VALUES (
                %(autoid)s, %(business_date)s, %(order_no)s, %(source_order_no)s,
                %(customer)s, %(department)s, %(logistics_company)s, %(confirm_receipt_date)s,
                %(summary)s, %(material_code)s, %(material_name)s, %(unit)s, %(batch_no)s,
                %(warehouse)s, %(reject_reason)s, %(delivery_quantity)s, %(confirmed_quantity)s,
                %(delivery_quantity)s - %(confirmed_quantity)s, %(delivery_quantity)s - %(confirmed_quantity)s, 0, %(unit_price_with_tax)s
            )
            """
            
            affected_rows = self.db.executemany_insert(insert_sql, orders)
            print(f"\n导入完成！")
            print(f"成功导入: {affected_rows} 条")
            
            message = f"导入完成！\n成功导入: {affected_rows} 条"
            return True, message
                
        except Exception as e:
            print(f"\n导入失败: {str(e)}")
            return False, f"导入失败: {str(e)}"
    
    def get_delivery_order_details(self, order_no):
        """获取出库单明细数据"""
        try:
            # 先获取主表数据
            header_query = """
            SELECT DISTINCT
                order_no,
                business_date,
                source_order_no,
                customer,
                department,
                logistics_company,
                summary,
                confirm_receipt_date
            FROM delivery_order
            WHERE order_no = %s
            LIMIT 1
            """
            
            # 获取明细数据，添加拒收原因字段
            detail_query = """
            SELECT 
                autoid,
                material_code,
                material_name,
                unit,
                batch_no,
                warehouse,
                reject_reason,  # 添加拒收原因字段
                unit_price_with_tax,  # 添加含税单价字段
                delivery_quantity,
                IFNULL(confirmed_quantity, 0) as confirmed_quantity,
                IFNULL(remaining_return_quantity,0) as remaining_return_quantity,
                order_status
            FROM delivery_order
            WHERE order_no = %s
            ORDER BY autoid
            """
            
            # 执行查询
            header_result = self.db.execute_query(header_query, (order_no,))
            detail_results = self.db.execute_query(detail_query, (order_no,))
            
            if not header_result or not detail_results:
                return None
            
            # 处理主表数据
            delivery_header = {
                'order_no': header_result[0]['order_no'],
                'business_date': header_result[0]['business_date'],
                'source_order_no': header_result[0]['source_order_no'],
                'customer': header_result[0]['customer'],
                'department': header_result[0]['department'],
                'logistics_company': header_result[0]['logistics_company'],
                'summary': header_result[0]['summary'],
                'confirm_receipt_date': header_result[0]['confirm_receipt_date']
            }
            
            # 处理明细数据
            details = []
            for detail in detail_results:
                details.append({
                    'autoid': detail['autoid'],
                    'material_code': detail['material_code'],
                    'material_name': detail['material_name'],
                    'unit': detail['unit'],
                    'batch_no': detail['batch_no'],
                    'warehouse': detail['warehouse'],
                    'reject_reason': detail['reject_reason'],  # 添加拒收原因
                    'delivery_quantity': detail['delivery_quantity'],
                    'confirmed_quantity': detail['confirmed_quantity'],
                    'remaining_return_quantity': detail['remaining_return_quantity'],
                    'unit_price_with_tax': detail['unit_price_with_tax'],  # 添加含税单价
                    'order_status': detail['order_status']
                })
            
            return {
                'header': delivery_header,
                'details': details
            }
            
        except Exception as e:
            raise Exception(f"获取出库单明细失败: {str(e)}")
    
    def update_delivery_order(self, order_no, updates):
        """更新出库单"""
        try:
            # 执行数据库更新
            result = self._update_delivery_order_db(order_no, updates)
            return result
            
        except Exception as e:
            raise Exception(f"更新出库单失败: {str(e)}")
                      
    def _update_delivery_order_db(self, order_no, updates):
        """实际的数据更新"""
        # 开始事务
        self.db.begin_transaction()
        
        # 更新主表摘要和物流公司
        if updates.get('summary') is not None or updates.get('logistics_company') is not None:
            main_query = """
            UPDATE delivery_order 
            SET summary = %s,
                logistics_company = %s
            WHERE order_no = %s
            """
            self.db.execute_update(main_query, (
                updates['summary'],
                updates['logistics_company'],
                order_no
            ))
        
        # 更新明细数，以autoid为依据
        detail_query = """
        UPDATE delivery_order 
        SET 
            delivery_quantity = %s,
            confirmed_quantity = %s,
            order_status = %s
        WHERE autoid = %s
        """
        
        for detail in updates['details']:
            self.db.execute_update(detail_query, (
                detail['delivery_quantity'],
                detail['confirmed_quantity'],
                detail['order_status'],
                detail['autoid']
            ))
        
        # 提交事务
        self.db.commit_transaction()
        return True
    
    def generate_handover_qrcode(self, handover_no):
        """生成交接单二维码"""
        try:
            # 获取交接单数据
            handover_data = self.get_handover_details(handover_no)
            if not handover_data:
                raise Exception("未找到交接单数据")
            
            # 构造二维码数据
            qr_data = {
                "handover_no": handover_no,
                "delivery_order_no": handover_data['main']['delivery_order_no'],
                "source_order_no": handover_data['main']['source_order_no'],
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # 转换为JSON字符串
            qr_json = json.dumps(qr_data)
            
            # 生成二维码
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_json)
            qr.make(fit=True)
            
            # 创建图像
            qr_image = qr.make_image(fill_color="black", back_color="white")
            
            # 转换为QImage
            buffer = BytesIO()
            qr_image.save(buffer, format='PNG')
            buffer.seek(0)
            
            qt_image = QImage()
            qt_image.loadFromData(buffer.getvalue())
            
            return qt_image
            
        except Exception as e:
            print(f"生成二维码失败: {str(e)}")
            return None
    
    def generate_unique_handover_no(self):
        """生成唯一的交接单号"""
        max_attempts = 10  # 最大尝试次数
        for _ in range(max_attempts):
            now = datetime.now()
            random_num = str(random.randint(100, 999))
            handover_no = f"JS-{now.strftime('%Y%m%d%H%M%S')}{random_num}"
            
            # 检查编号是否已存在
            check_query = "SELECT COUNT(*) as count FROM handover_order WHERE handover_no = %s"
            result = self.db.execute_query(check_query, (handover_no,))
            
            if result[0]['count'] == 0:
                return handover_no
            
        raise Exception("无法生成唯一的交接单号，请稍后重试")
    
    def generate_unique_detail_no(self):
        """生成唯一的明细编号"""
        max_attempts = 10  # 最大尝试次数
        for _ in range(max_attempts):
            now = datetime.now()
            detail_random = str(random.randint(100, 999))
            detail_no = f"JSMX-{now.strftime('%Y%m%d%H%M%S')}{detail_random}"
            
            # 检查编号是否已存在
            check_query = "SELECT COUNT(*) as count FROM handover_detail WHERE detail_no = %s"
            result = self.db.execute_query(check_query, (detail_no,))
            
            if result[0]['count'] == 0:
                return detail_no
            
        raise Exception("无法生成唯一的明细编号，请稍后重试")
    
    def generate_handover_order(self, delivery_data, selected_details):
        """生成交接单"""
        try:
            # 首先检查是否已存在未审核的交接单
            check_query = """
            SELECT handover_no 
            FROM handover_order 
            WHERE delivery_order_no = %s 
            AND handover_status = 0
            LIMIT 1
            """
            existing_handover = self.db.execute_query(check_query, (delivery_data['order_no'],))
            
            if existing_handover:
                raise Exception(f"当前存在未完成的交接单（交接单号：{existing_handover[0]['handover_no']}），不能重复生成")
            
            # 如果检查通过，继续原有的交接单生成逻辑
            self.db.begin_transaction()
            
            # 生成交接单号
            handover_no = self.generate_unique_handover_no()
            
            # 插入交接单主表
            main_query = """
            INSERT INTO handover_order (
                handover_no, handover_date, delivery_order_no, source_order_no, customer, 
                logistics_company, department, summary, 
                handover_status, return_type
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            # 获取原始单号
            source_order_query = """
            SELECT source_order_no FROM delivery_order 
            WHERE order_no = %s LIMIT 1
            """
            source_order_result = self.db.execute_query(source_order_query, (delivery_data['order_no'],))
            source_order_no = source_order_result[0]['source_order_no'] if source_order_result else None
            
            self.db.execute_update(main_query, (
                handover_no,                    # 接单号
                datetime.now().strftime('%Y-%m-%d'),      # 交接日期，使用 strftime 而不是 date
                delivery_data['order_no'],      # 出库单号
                source_order_no,               # 原始单号
                delivery_data['customer'],      # 送货客户
                delivery_data['logistics_company'], # 物流公司
                delivery_data.get('department', ''), # 部门
                delivery_data.get('summary', ''),   # 摘要
                0,                              # 交接单状态(默认0)
                (0 if source_order_no and source_order_no.startswith('SO')  # 拒收退货
                 else 1 if source_order_no and source_order_no.startswith('TH')  # 仓退 
                 else 2)  # 代销退货
            ))
            
            # 插入交接单明细
            detail_query = """
            INSERT INTO handover_detail (
                detail_no, handover_no, delivery_autoid, 
                material_code, material_name, unit, 
                batch_no, warehouse, reject_reason, 
                return_quantity, received_quantity
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NULL)
            """
            
            for detail in selected_details:
                # 生成明细编号
                detail_no = self.generate_unique_detail_no()
                
                self.db.execute_update(detail_query, (
                    detail_no,                  # 明细编号
                    handover_no,                # 交接单号
                    detail['autoid'],           # delivery_autoid
                    detail['material_code'],    # 物料编码
                    detail['material_name'],    # 物料名称
                    detail['unit'],             # 计量单位
                    detail['batch_no'],         # 批次
                    detail['warehouse'],        # 仓库
                    detail['reject_reason'],    # 客户拒收原因
                    detail['remaining_return_quantity']  # 退货数量
                ))
            
            # 添加操作日志
            self.add_operation_log(
                operation_type="新增",
                operator=self.current_user,
                document_no=handover_no,  # 使用的交接单号
                operation_content=f"生成交接单 {handover_no}，来源单据：{delivery_data['order_no']}",
                ip_address=self.client_ip
            )
            
            # 生成二维码数据
            qr_data = json.dumps({
                "handover_no": handover_no,
                "delivery_order_no": delivery_data['order_no'],
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }, ensure_ascii=False)
            
            # 更新交接单主表，添加二维码数据
            update_qr_query = """
            UPDATE handover_order 
            SET qrcode_data = %s,
                qrcode_create_time = NOW()
            WHERE handover_no = %s
            """
            self.db.execute_update(update_qr_query, (qr_data, handover_no))
            
            self.db.commit_transaction()
            return True
            
        except Exception as e:
            self.db.rollback_transaction()
            print(f"生成交接单失败: {str(e)}")
            raise Exception(f"生成交接单失败: {str(e)}")
    
    def get_handover_details(self, handover_no):
        """获取交接单明细数据"""
        try:
            # 获取交接单主表数据
            main_query = """
            SELECT 
                ho.handover_no,
                ho.handover_date,
                ho.delivery_order_no,
                ho.source_order_no,          # 添加原始单号
                ho.customer,
                ho.logistics_company,
                ho.department,
                ho.summary,
                ho.handover_status,          # 单据状态(0:保存,1:审核)
                ho.return_type,              # 退货类型(0:拒收退货,1:仓退,2:代销退货)
                ho.print_status,
                CASE ho.handover_status      # 添加状态文本转换
                    WHEN 0 THEN '保存'
                    WHEN 1 THEN '审核'
                    ELSE '未知'
                END as status_text,
                CASE ho.return_type          # 添加退货类型文本转换
                    WHEN 0 THEN '拒收退货'
                    WHEN 1 THEN '仓退'
                    WHEN 2 THEN '代销退货'
                    ELSE '未知'
                END as return_type_text
            FROM handover_order ho
            WHERE ho.handover_no = %s
            """
            
            # 获取交接单明细数据
            detail_query = """
            SELECT 
                hd.detail_no,
                hd.handover_no,
                hd.delivery_autoid,
                hd.material_code,
                hd.material_name,
                hd.unit,
                hd.batch_no,
                hd.warehouse,
                hd.reject_reason,
                hd.return_quantity,
                hd.received_quantity,
                hd.return_warehouse_no,
                hd.stock_status,
                CASE hd.stock_status
                    WHEN 'raw' THEN '原料'
                    WHEN 'deformed' THEN '变形'
                    WHEN 'to_scrap' THEN '待报废'
                    WHEN 'scrapped' THEN '报废'
                    ELSE ''
                END as stock_status_text
            FROM handover_detail hd
            WHERE hd.handover_no = %s
            ORDER BY hd.detail_no
            """
            
            # 执行查询
            main_data = self.db.execute_query(main_query, (handover_no,))
            detail_data = self.db.execute_query(detail_query, (handover_no,))
            
            if not main_data:
                raise Exception("未找到交接单数据")
            
            # 处理主表数据中的None值
            main_data = main_data[0]
            processed_main = {
                'handover_no': main_data['handover_no'] or '',
                'handover_date': main_data['handover_date'] or '',
                'delivery_order_no': main_data['delivery_order_no'] or '',
                'source_order_no': main_data['source_order_no'] or '',
                'customer': main_data['customer'] or '',
                'logistics_company': main_data['logistics_company'] or '',
                'department': main_data['department'] or '',
                'summary': main_data['summary'] or '',
                'handover_status': main_data['handover_status'] or 0,
                'return_type': main_data['return_type'] or 0,
                'print_status': main_data['print_status'] or 0,
                'status_text': main_data['status_text'] or '',
                'return_type_text': main_data['return_type_text'] or '',
            }
            
            # 处理明细数据中的None值
            processed_details = []
            for detail in detail_data:
                processed_detail = {
                    'detail_no': detail['detail_no'] or '',
                    'handover_no': detail['handover_no'] or '',
                    'delivery_autoid': detail['delivery_autoid'] or '',
                    'material_code': detail['material_code'] or '',
                    'material_name': detail['material_name'] or '',
                    'unit': detail['unit'] or '',
                    'batch_no': detail['batch_no'] or '',
                    'warehouse': detail['warehouse'] or '',
                    'reject_reason': detail['reject_reason'] or '',
                    'return_quantity': detail['return_quantity'] or 0,
                    'received_quantity': detail['received_quantity'] or '',
                    'return_warehouse_no': detail['return_warehouse_no'] or '',
                    'stock_status': detail['stock_status'] or '',
                    'stock_status_text': detail['stock_status_text'] or ''
                }
                processed_details.append(processed_detail)
            
            # 返回处理后的数据
            result = {
                'main': processed_main,
                'details': processed_details
            }
            
            return result
            
        except Exception as e:
            raise Exception(f"获取交接单明细失败: {str(e)}")
    
    def delete_handover_detail(self, handover_no, detail_no):
        """删除交接单明细"""
        try:
            # 开始事务
            self.db.begin_transaction()
            
            # 首先检查交接单状态
            status_query = "SELECT handover_status FROM handover_order WHERE handover_no = %s"
            status_result = self.db.execute_query(status_query, (handover_no,))
            
            if not status_result:
                raise Exception("交接单不存在")
            
            if status_result[0]['handover_status'] == 1:
                raise Exception("已审核的交接单不能删除明细")
            
            # 检查明细数量
            count_query = "SELECT COUNT(*) as count FROM handover_detail WHERE handover_no = %s"
            count_result = self.db.execute_query(count_query, (handover_no,))
            
            if count_result[0]['count'] <= 1:
                raise Exception("至少需要保留一行明细")
            
            # 删除明细
            delete_query = """
            DELETE FROM handover_detail 
            WHERE handover_no = %s AND detail_no = %s
            """
            
            self.db.execute_update(delete_query, (handover_no, detail_no))
            
            # 添加操作日志
            self.add_operation_log(
                operation_type="删除明细",
                operator=self.current_user,
                document_no=handover_no,
                operation_content=f"删除交接单 {handover_no} 的明细 {detail_no}",
                ip_address=self.client_ip
            )
            
            # 提交事务
            self.db.commit_transaction()
            return True
            
        except Exception as e:
            self.db.rollback_transaction()
            raise Exception(f"删除明细失败: {str(e)}")
    
    def update_handover_order(self, handover_no, updates):
        """更新交接单
        
        Args:
            handover_no: 交接单号
            updates: {
                'logistics_company': 物流公司,
                'return_type': 退货类型,
                'details': [{
                    'detail_no': 明细编号,
                    'return_quantity': 退货数量,
                    'received_quantity': 收货数量,
                    'return_warehouse_no': 退货入库单号,
                    'stock_status': 退货状态
                }]
            }
        """
        try:
            self.db.begin_transaction()
            
            # 检查交接单状态
            status_query = "SELECT handover_status FROM handover_order WHERE handover_no = %s"
            status_result = self.db.execute_query(status_query, (handover_no,))
            
            if not status_result:
                raise Exception("交接单不存在")
            
            if status_result[0]['handover_status'] == 1:
                raise Exception("已审核的交接单不能修改")
            
            # 更新交接单主表
            update_query = """
            UPDATE handover_order 
            SET return_type = %s,
                logistics_company = %s,
                update_time = CURRENT_TIMESTAMP
            WHERE handover_no = %s
            """
            
            self.db.execute_update(update_query, (
                updates['return_type'],
                updates['logistics_company'],
                handover_no
            ))
            
            # 更新明细表
            if updates.get('details'):
                # 修改获取明细的退货数量查询
                detail_query = """
                SELECT 
                    hd.detail_no,
                    IFNULL(hd.return_quantity, 0) as return_quantity  # 保非空
                FROM handover_detail hd
                WHERE hd.handover_no = %s
                """
                detail_result = self.db.execute_query(detail_query, (handover_no,))
                
                # 打印调试信息
                print("退货数量查询结果:", detail_result)
                
                detail_dict = {}
                for d in detail_result:
                    detail_dict[d['detail_no']] = float(d['return_quantity'])
                
                detail_update_query = """
                UPDATE handover_detail 
                SET 
                    return_quantity = %s,
                    batch_no = %s,
                    received_quantity = %s,
                    return_warehouse_no = %s,
                    stock_status = %s,
                    update_time = CURRENT_TIMESTAMP
                WHERE handover_no = %s AND detail_no = %s
                """
                
                for detail in updates['details']:
                    print(f"处理明细: {detail['detail_no']}")
                    print(f"退货数量: {detail_dict.get(detail['detail_no'], 0)}")
                    print(f"收货数量: {detail['received_quantity']}")
                    print(f"退货入库单号: {detail['return_warehouse_no']}")
                    print(f"退货状态: {detail['stock_status']}")
                    
                    # 验证收货数量
                    if detail['received_quantity'] is None or detail['received_quantity'] <= 0:
                        raise Exception(f"明细 {detail['detail_no']} 的收货数量必须大于0")
                    
                    # 验证退货入库单号
                    if not detail['return_warehouse_no']:
                        raise Exception(f"明细 {detail['detail_no']} 的退货入库单号不能为空")
                    
                    # 验证退货状态
                    if not detail.get('stock_status'):
                        raise Exception(f"明细 {detail['detail_no']} 的退货状态不能为空")
                    
                    valid_statuses = ['raw', 'deformed', 'to_scrap', 'scrapped']
                    if detail['stock_status'] not in valid_statuses:
                        raise Exception(f"明细 {detail['detail_no']} 的退货状态值无效")
                    
                    # 验证收货数量是否小于等于退货数量
                    return_qty = detail_dict.get(detail['detail_no'], 0)
                    received_qty = float(detail['received_quantity'])
                    
                    if received_qty > return_qty:
                        raise Exception(
                            f"明细 {detail['detail_no']} 的收货数量({received_qty})"
                            f"不能大于退货数量({return_qty})"
                        )
                    
                    # 执行更新
                    self.db.execute_update(detail_update_query, (
                        detail['return_quantity'],
                        detail['batch_no'],
                        detail['received_quantity'],
                        detail['return_warehouse_no'],
                        detail['stock_status'],
                        handover_no,
                        detail['detail_no']
                    ))
            
            # 添加操作日志
            self.add_operation_log(
                operation_type="修改",
                operator=self.current_user,
                document_no=handover_no,
                operation_content=f"修改交接单 {handover_no}",
                ip_address=self.client_ip
            )
            
            self.db.commit_transaction()
            return True
            
        except Exception as e:
            self.db.rollback_transaction()
            raise Exception(f"更新交接单失败: {str(e)}")
    
    def delete_handover_order(self, handover_no):
        """删除交接单及其明细"""
        try:
            self.db.begin_transaction()
            
            # 检查交接单状态
            status_query = "SELECT handover_status FROM handover_order WHERE handover_no = %s"
            status_result = self.db.execute_query(status_query, (handover_no,))
            
            if not status_result:
                raise Exception("交接单不存在")
            
            if status_result[0]['handover_status'] == 1:
                raise Exception("已审核的交接单不能删除")
            
            # 先删除明细
            delete_details_query = "DELETE FROM handover_detail WHERE handover_no = %s"
            self.db.execute_update(delete_details_query, (handover_no,))
            
            # 删除主表（包含二维码相关字段）
            delete_main_query = """
            DELETE FROM handover_order 
            WHERE handover_no = %s
            """
            self.db.execute_update(delete_main_query, (handover_no,))
            
            # 添加操作日志
            self.add_operation_log(
                operation_type="删除",
                operator=self.current_user,
                document_no=handover_no,
                operation_content=f"删除交接单 {handover_no}",
                ip_address=self.client_ip
            )
            
            self.db.commit_transaction()
            return True
            
        except Exception as e:
            self.db.rollback_transaction()
            raise Exception(f"删除交接单失败: {str(e)}")
    
    def audit_handover_order(self, handover_no):
        """审核交接单"""
        try:
            self.db.begin_transaction()
            
            # 1. 检查交接单状态
            status_query = """
            SELECT handover_status, print_status, return_type 
            FROM handover_order 
            WHERE handover_no = %s
            """
            status_result = self.db.execute_query(status_query, (handover_no,))
            
            if not status_result:
                raise Exception("交接单不存在")
            
            if status_result[0]['handover_status'] == 1:
                raise Exception("交接单已审核")
            
            if status_result[0]['print_status'] == 0:
                raise Exception("交接单未打印，请先打印后再审核")
            
            # 2. 检查明细数据
            detail_query = """
            SELECT
                detail_no,
                delivery_autoid,
                SUM(return_quantity) as return_quantity,
                SUM(received_quantity) as received_quantity,
                MAX(return_warehouse_no) as return_warehouse_no  # 使用MAX获取非空的入库单号
            FROM handover_detail 
            WHERE handover_no = %s
            GROUP BY delivery_autoid
            """
            detail_result = self.db.execute_query(detail_query, (handover_no,))
            
            if not detail_result:
                raise Exception("未找到交接单明细")
            
            # 3. 验证明细数据
            for detail in detail_result:
                if not detail['return_warehouse_no']:
                    raise Exception("存在未填写退货入库单号的明细")
                    
                return_quantity = float(detail['return_quantity'] or 0)
                received_quantity = float(detail['received_quantity'] or 0)
                
                if return_quantity <= 0:
                    raise Exception("退货数量必须大于0")
                    
                if received_quantity <= 0:
                    raise Exception("收货数量必须大于0")
                    
                if received_quantity > return_quantity:
                    raise Exception(f"收货数量({received_quantity})不能大于退货数量({return_quantity})")
            
            # 4. 更新出库单的退货数量并检查是否需要关闭
            for detail in detail_result:
                # 先获取出库单当前信息
                delivery_query = """
                SELECT 
                    remaining_return_quantity,
                    return_quantity
                FROM delivery_order 
                WHERE autoid = %s
                """
                delivery_info = self.db.execute_query(delivery_query, (detail['delivery_autoid'],))
                
                if not delivery_info:
                    raise Exception("未找到对应的出库单记录")
                    
                # 计算新的退货数量
                new_remaining_qty = float(delivery_info[0]['remaining_return_quantity']) - float(detail['received_quantity'])
                return_quantity = float(delivery_info[0]['return_quantity'])
                
                # 验证退货数量是否合理
                if new_remaining_qty > return_quantity:
                    raise Exception(f"剩余退货数量({new_remaining_qty})不能大于退货数量({return_quantity})")
                
                # 更新出库单
                update_delivery_query = """
                UPDATE delivery_order 
                SET remaining_return_quantity = %s,
                    order_status = CASE 
                        WHEN remaining_return_quantity = 0 THEN 1  -- 关闭状态
                        ELSE 0  -- 开启状态
                    END,
                    update_time = CURRENT_TIMESTAMP
                WHERE autoid = %s
                """
                self.db.execute_update(
                    update_delivery_query, 
                    (new_remaining_qty, detail['delivery_autoid'])
                )
            
            # 5. 更新交接单状态
            update_handover_query = """
            UPDATE handover_order 
            SET handover_status = 1,
                audit_time = CURRENT_TIMESTAMP,
                update_time = CURRENT_TIMESTAMP
            WHERE handover_no = %s
            """
            self.db.execute_update(update_handover_query, (handover_no,))
            
            # 添加操作日志
            self.add_operation_log(
                operation_type="审核",
                operator=self.current_user  ,
                document_no=handover_no,
                operation_content=f"审核交接单 {handover_no}",
                ip_address=self.client_ip
            )
            
            self.db.commit_transaction()
            return True
            
        except Exception as e:
            self.db.rollback_transaction()
            raise Exception(f"审核交接单失败: {str(e)}")
    
    def unaudit_handover_order(self, handover_no):
        """反审核交接单"""
        try:
            self.db.begin_transaction()
            
            # 1. 检查交接单状态
            status_query = """
            SELECT handover_status 
            FROM handover_order 
            WHERE handover_no = %s
            """
            status_result = self.db.execute_query(status_query, (handover_no,))
            
            if not status_result:
                raise Exception("交接单不存在")
            
            if status_result[0]['handover_status'] == 0:
                raise Exception("交接单未审核，不能反审核")
            
            # 2. 获取明细数据
            detail_query = """
            SELECT 
                delivery_autoid,
                sum(received_quantity) as received_quantity,
                d.order_no
            FROM handover_detail h
            LEFT JOIN delivery_order d ON h.delivery_autoid = d.autoid
            WHERE h.handover_no = %s
            GROUP BY delivery_autoid
            """
            detail_result = self.db.execute_query(detail_query, (handover_no,))
            
            if not detail_result:
                raise Exception("未找到交接单明细")
            
            # 3. 更新出库单的退货数量和状态
            for detail in detail_result:
                # 先获取出库单当前状态和退货数量
                check_delivery_query = """
                SELECT order_status, remaining_return_quantity,return_quantity
                FROM delivery_order 
                WHERE autoid = %s
                """
                delivery_result = self.db.execute_query(
                    check_delivery_query, 
                    (detail['delivery_autoid'],)
                )
                
                if not delivery_result:
                    raise Exception(f"未找到出库单: {detail['order_no']}")
                    
                current_return_qty = float(delivery_result[0]['remaining_return_quantity'] or 0)
                return_quantity = float(delivery_result[0]['return_quantity'] or 0)
                received_qty = float(detail['received_quantity'] or 0)
                
                if return_quantity < received_qty+ current_return_qty:
                    raise Exception(f"出库单 {detail['order_no']} 的剩余退货数量不足无法反审核")
                
                # 更新出库单
                update_delivery_query = """
                UPDATE delivery_order 
                SET remaining_return_quantity = remaining_return_quantity + %s,
                    order_status = CASE 
                        WHEN remaining_return_quantity + %s > 0 THEN 0  -- 如果还有剩余退货数量，改为开启
                        ELSE 1  -- 开启状态
                    END,
                    update_time = CURRENT_TIMESTAMP
                WHERE autoid = %s
                """
                self.db.execute_update(
                    update_delivery_query, 
                    (received_qty, received_qty, detail['delivery_autoid'])
                )
            
            # 4. 更新交接单状态
            update_handover_query = """
            UPDATE handover_order 
            SET handover_status = 0,
                audit_time = NULL,
                update_time = CURRENT_TIMESTAMP
            WHERE handover_no = %s
            """
            self.db.execute_update(update_handover_query, (handover_no,))
            
            # 添加操作日志
            self.add_operation_log(
                operation_type="反审核",
                operator=self.current_user,
                document_no=handover_no,
                operation_content=f"反审核交接单 {handover_no}",
                ip_address=self.client_ip
            )
            
            self.db.commit_transaction()
            return True
            
        except Exception as e:
            self.db.rollback_transaction()
            raise Exception(f"反审核交接单失败: {str(e)}")
    
    def update_print_status(self, handover_no):
        """更新交接单打印状态"""
        try:
            # 开始事务
            self.db.begin_transaction()
            
            # 检查交接单是否存在
            check_query = """
            SELECT handover_no, print_status 
            FROM handover_order 
            WHERE handover_no = %s
            """
            result = self.db.execute_query(check_query, (handover_no,))
            
            if not result:
                raise Exception("交接单不存在")
            
            # 更新打印状态
            update_query = """
            UPDATE handover_order 
            SET print_status = 1,
                update_time = CURRENT_TIMESTAMP
            WHERE handover_no = %s
            """
            self.db.execute_update(update_query, (handover_no,))
            
            # 添加操作日志
            self.add_operation_log(
                operation_type="打印",
                operator=self.current_user,
                document_no=handover_no,
                operation_content=f"打印交接单 {handover_no}",
                ip_address=self.client_ip
            )
            
            # 提交事务
            self.db.commit_transaction()
            return True
            
        except Exception as e:
            self.db.rollback_transaction()
            raise Exception(f"更新打印状态失败: {str(e)}")
    
    def get_unaudited_handover_count(self, order_no):
        """获取出库单未审核的交接单数量"""
        try:
            query = """
            SELECT COUNT(*) as count
            FROM handover_order 
            WHERE delivery_order_no = %s 
            AND handover_status = 0
            """
            result = self.db.execute_query(query, (order_no,))
            return result[0]['count'] if result else 0
        except Exception as e:
            print(f"获取未审核交接单数量失败: {str(e)}")
            return 0
    
    def get_operation_logs(self, filters=None):
        """获取操作日志列表"""
        try:
            # 构建基础查询
            count_query = """
            SELECT COUNT(*) as total 
            FROM operation_log
            WHERE 1=1
            """
            
            query = """
            SELECT 
                operation_time,
                operation_type,
                operator,
                document_no,
                operation_content,
                ip_address
            FROM operation_log
            WHERE 1=1
            """
            
            params = []
            count_params = []
            
            # 添加过滤条件
            if filters:
                if filters.get('start_date'):
                    count_query += " AND DATE(operation_time) >= %s"
                    query += " AND DATE(operation_time) >= %s"
                    count_params.append(filters['start_date'])
                    params.append(filters['start_date'])
                
                if filters.get('end_date'):
                    count_query += " AND DATE(operation_time) <= %s"
                    query += " AND DATE(operation_time) <= %s"
                    count_params.append(filters['end_date'])
                    params.append(filters['end_date'])
                
                if filters.get('operation_type') and filters['operation_type'] != "全部":
                    count_query += " AND operation_type = %s"
                    query += " AND operation_type = %s"
                    count_params.append(filters['operation_type'])
                    params.append(filters['operation_type'])
            
            # 添加排序
            query += " ORDER BY operation_time DESC"
            
            # 添加分页
            if filters and 'page' in filters and 'page_size' in filters:
                offset = (filters['page'] - 1) * filters['page_size']
                query += f" LIMIT {filters['page_size']} OFFSET {offset}"
            
            # 执行查询
            count_result = self.db.execute_query(count_query, tuple(count_params) if count_params else None)
            total = count_result[0]['total'] if count_result else 0
            
            results = self.db.execute_query(query, tuple(params) if params else None)
            
            return total, results
            
        except Exception as e:
            raise Exception(f"获取操作日志失败: {str(e)}")
    
    def add_operation_log(self, operation_type, operator, document_no, operation_content, ip_address):
        """添加操作日志"""
        try:
            query = """
            INSERT INTO operation_log (
                operation_type, operator, document_no, 
                operation_content, ip_address
            ) VALUES (%s, %s, %s, %s, %s)
            """
            
            self.db.execute_update(query, (
                operation_type,
                operator,
                document_no,
                operation_content,
                ip_address
            ))
            
            return True
        except Exception as e:
            print(f"添加操作日志失败: {str(e)}")
            return False    
    def clear_operation_logs(self, filters=None):
        """清空操作日志"""
        try:
            self.db.begin_transaction()
            
            # 构建删除语句
            query = "DELETE FROM operation_log WHERE 1=1"
            params = []
            
            # 添加过滤条件
            if filters:
                if filters.get('start_date'):
                    query += " AND DATE(operation_time) >= %s"
                    params.append(filters['start_date'])
                
                if filters.get('end_date'):
                    query += " AND DATE(operation_time) <= %s"
                    params.append(filters['end_date'])
                
                if filters.get('operation_type') and filters['operation_type'] != "全部":
                    query += " AND operation_type = %s"
                    params.append(filters['operation_type'])
            
            # 执行删除
            result = self.db.execute_update(query, tuple(params) if params else None)
            
            # 提交事务
            self.db.commit_transaction()
            return result
            
        except Exception as e:
            self.db.rollback_transaction()
            raise Exception(f"清空日志失败: {str(e)}")
    
    def get_local_ip(self):
        """获取本机IP地址"""
        try:
            # 创建一UDP套接字
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            try:
                # 连接一个外部地址（不需要真实连接）
                s.connect(('*******', 80))
                # 获取本机IP
                ip = s.getsockname()[0]
            finally:
                s.close()
            return ip
        except Exception:
            try:
                # 备选方案：获取主机名对应的IP
                hostname = socket.gethostname()
                ip = socket.gethostbyname(hostname)
                return ip
            except Exception:
                return "127.0.0.1"  # 如果都失败了，返回本地回环地址
    
    def split_handover_details(self, handover_no, details):
        """拆分交接单明细
        
        Args:
            handover_no: 交接单号
            details: 要拆分的明细列表，每个明细包含完整的明细信息
        """
        try:
            self.db.begin_transaction()
            
            # 检查交接单状态
            status_query = """
            SELECT handover_status 
            FROM handover_order 
            WHERE handover_no = %s
            """
            status_result = self.db.execute_query(status_query, (handover_no,))
            
            if not status_result:
                raise Exception("交接单不存在")
            
            if status_result[0]['handover_status'] == 1:
                raise Exception("已审核的交接单不能拆分明细")
            
            # 为每个选中的明细创建新行
            now = datetime.now()
            
            for detail in details:
                # 生成新的明细编号
                detail_random = str(random.randint(100, 999))
                new_detail_no = f"JSMX-{now.strftime('%Y%m%d%H%M%S')}{detail_random}"
                
                # 插入新的明细记录
                insert_query = """
                INSERT INTO handover_detail (
                    detail_no, handover_no, delivery_autoid,
                    material_code, material_name, unit,
                    batch_no, warehouse, reject_reason,
                    return_quantity, received_quantity, return_warehouse_no
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                self.db.execute_update(insert_query, (
                    new_detail_no,
                    handover_no,
                    detail['delivery_autoid'],
                    detail['material_code'],
                    detail['material_name'],
                    detail['unit'],
                    detail['batch_no'],
                    detail['warehouse'],
                    detail['reject_reason'],
                    detail['return_quantity'],
                    detail['received_quantity'],
                    detail['return_warehouse_no']
                ))
            
            # 添加操作日志
            self.add_operation_log(
                operation_type="拆分",
                operator=self.current_user,
                document_no=handover_no,
                operation_content=f"拆分交接单明细 {handover_no}",
                ip_address=self.client_ip
            )
            
            self.db.commit_transaction()
            return True
            
        except Exception as e:
            self.db.rollback_transaction()
            raise Exception(f"拆分明细失败: {str(e)}")
    
    def batch_create_handover_orders(self, order_nos, progress_callback=None):
        """批量生成交接单"""
        try:
            self.db.begin_transaction()
            
            success_count = 0
            for i, order_no in enumerate(order_nos):
                try:
                    # 更新进度
                    if progress_callback:
                        progress_callback(i)
                    
                    # 获取出库单主表数据
                    main_query = """
                    SELECT 
                        d.order_no,
                        d.source_order_no,
                        d.customer,
                        d.logistics_company,
                        d.department,
                        d.summary
                    FROM delivery_order d
                    WHERE d.order_no = %s
                    """
                    
                    # 获取出库单明细数据
                    detail_query = """
                    SELECT 
                        d.autoid as delivery_autoid,
                        d.material_code,
                        d.material_name,
                        d.unit,
                        d.batch_no,
                        d.warehouse,
                        d.reject_reason,
                        d.remaining_return_quantity
                    FROM delivery_order d
                    WHERE d.order_no = %s
                        
                    """
                    
                    # 执行查询
                    main_data = self.db.execute_query(main_query, (order_no,))
                    detail_data = self.db.execute_query(detail_query, (order_no,))
                    
                    if not main_data:
                        raise Exception(f"未找到出库单数据")
                    
                    main_data = main_data[0]  # 获取第一条记录
                    
                    if not detail_data:
                        raise Exception(f"未找到可退货的明细数据")
                    
                    # 获取订单明细
                    order_details = self.get_delivery_order_details(order_no)
                    if not order_details or 'details' not in order_details:
                        continue
                    
                    # 筛选符合条件的明细：状态为"开启"且剩余退货数量大于0
                    selected_details = []
                    for detail in order_details['details']:
                        if detail['order_status'] == 0 and detail['remaining_return_quantity'] > 0:
                            selected_details.append(detail)
                    
                    # 如果没有符合条件的明细，跳过当前订单
                    if not selected_details:
                        continue
                    
                    # 准备出库单数据
                    delivery_data = {
                        'order_no': order_no,
                        'customer': order_details['header']['customer'],
                        'logistics_company': order_details['header']['logistics_company'],
                        'department': order_details['header']['department'],
                        'summary': order_details['header'].get('summary', '')
                    }
                    
                    # 生成交接单号
                    handover_no = self.generate_unique_handover_no()
                    
                    # 插入交接单主表
                    insert_main_query = """
                    INSERT INTO handover_order (
                        handover_no, handover_date, delivery_order_no,
                        source_order_no, customer, logistics_company,
                        department, summary, handover_status,
                        return_type, print_status, create_time
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                    """
                    
                    self.db.execute_update(insert_main_query, (
                        handover_no,
                        datetime.now().strftime('%Y-%m-%d'),      # 交接日期，使用 strftime 而不是 date
                        main_data['order_no'],
                        main_data['source_order_no'],
                        main_data['customer'],
                        main_data['logistics_company'],
                        main_data['department'],
                        main_data['summary'],
                        0,  # handover_status: 0-保存
                        0,  # return_type: 0-拒收退货
                        0   # print_status: 0-未打印
                    ))
                    
                    # 插入明细表
                    insert_detail_query = """
                    INSERT INTO handover_detail (
                        detail_no, handover_no, delivery_autoid,
                        material_code, material_name, unit,
                        batch_no, warehouse, reject_reason,
                        return_quantity, received_quantity, return_warehouse_no,
                        stock_status, create_time
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                    """
                    
                    for detail in selected_details:
                        # 生成明细编号
                        detail_no = self.generate_unique_detail_no()
                        
                        self.db.execute_update(insert_detail_query, (
                            detail_no,
                            handover_no,
                            detail['autoid'],  # 修改这里，使用 'autoid' 而不是 'delivery_autoid'
                            detail['material_code'],
                            detail['material_name'],
                            detail['unit'],
                            detail['batch_no'],
                            detail['warehouse'],
                            detail['reject_reason'],
                            detail['remaining_return_quantity'],
                            None,  # received_quantity
                            None,  # return_warehouse_no
                            None   # stock_status 默认为原料
                        ))
                    
                    # 添加操作日志
                    self.add_operation_log(
                        operation_type="新增",
                        operator=self.current_user,
                        document_no=handover_no,
                        operation_content=f"批量生成交接单 {handover_no}",
                        ip_address=self.client_ip
                    )
                    
                    success_count += 1
                    
                except Exception as e:
                    print(f"处理出库单 {order_no} 失败: {str(e)}")
                    raise
            
            # 最终进度
            if progress_callback:
                progress_callback(len(order_nos))
            
            self.db.commit_transaction()
            return True
            
        except Exception as e:
            self.db.rollback_transaction()
            raise Exception(f"批量生成交接单失败: {str(e)}")
    
    def void_handover_order(self, handover_no):
        """作废交接单"""
        try:
            self.db.begin_transaction()
            
            # 检查交接单状态
            status_query = """
            SELECT handover_status 
            FROM handover_order 
            WHERE handover_no = %s
            """
            status_result = self.db.execute_query(status_query, (handover_no,))
            
            if not status_result:
                raise Exception("交接单不存在")
            
            if status_result[0]['handover_status'] == 2:
                raise Exception("交接单已作废")
            
            # 更新交接单状态为作废
            update_query = """
            UPDATE handover_order 
            SET handover_status = 2,
                update_time = CURRENT_TIMESTAMP,
                audit_time = CURRENT_TIMESTAMP
            WHERE handover_no = %s
            """
            self.db.execute_update(update_query, (handover_no,))
            
            # 添加操作日志
            self.add_operation_log(
                operation_type="作废",
                operator=self.current_user,
                document_no=handover_no,
                operation_content=f"作废交接单 {handover_no}",
                ip_address=self.client_ip
            )
            
            self.db.commit_transaction()
            return True
            
        except Exception as e:
            self.db.rollback_transaction()
            raise Exception(f"作废交接单失败: {str(e)}")
    
    def get_all_original_order_nos(self, source_order_nos):
        """获取所有匹配的原始单号
        
        Args:
            source_order_nos: 要查询的原始单号列表
            
        Returns:
            set: 所有匹配的原始单号集合
        """
        try:
            # 构建查询语句
            query = """
            SELECT DISTINCT
                CASE 
                    WHEN source_order_no LIKE 'SO%' AND summary REGEXP '[A-Za-z0-9]+'
                    THEN SUBSTRING(
                        summary,
                        IF(
                            LOCATE('SO', summary) > 0,
                            LOCATE('SO', summary),
                            1
                        ),
                        20
                    )
                    ELSE source_order_no 
                END as original_order_no
            FROM delivery_order 
            WHERE 1=1
            """
            
            # 处理单号列表
            placeholders = ','.join(['%s' for _ in source_order_nos])
            query += f""" AND (
                source_order_no IN ({placeholders}) OR 
                (source_order_no LIKE 'SO%' AND 
                 SUBSTRING(
                    summary,
                    IF(
                        LOCATE('SO', summary) > 0,
                        LOCATE('SO', summary),
                        1
                    ),
                    20
                ) IN ({placeholders})
                )
            )"""
            
            # 参数需要传递两次，因为在查询条件中使用了两次
            params = source_order_nos + source_order_nos
            
            # 执行查询
            results = self.db.execute_query(query, tuple(params))
            
            # 返回结果集合
            return {str(r['original_order_no']).strip().upper() for r in results if r['original_order_no']}
            
        except Exception as e:
            print(f"获取原始单号失败: {str(e)}")
            return set()
    
    def get_split_details(self, handover_no, delivery_autoids):
        """获取拆分后的明细数据
        
        Args:
            handover_no: 交接单号
            delivery_autoids: 原始的delivery_autoid列表
            
        Returns:
            list: 拆分后的明细数据列表
        """
        try:
            # 构建查询语句，只获取指定的delivery_autoid对应的明细
            query = """
            SELECT 
                hd.detail_no,
                hd.handover_no,
                hd.delivery_autoid,
                hd.material_code,
                hd.material_name,
                hd.unit,
                hd.batch_no,
                hd.warehouse,
                hd.reject_reason,
                hd.return_quantity,
                hd.received_quantity,
                hd.return_warehouse_no,
                hd.stock_status,
                CASE hd.stock_status
                    WHEN 'raw' THEN '原料'
                    WHEN 'deformed' THEN '变形'
                    WHEN 'to_scrap' THEN '待报废'
                    WHEN 'scrapped' THEN '报废'
                    ELSE ''
                END as stock_status_text
            FROM handover_detail hd
            WHERE hd.handover_no = %s
            AND hd.delivery_autoid IN ({})
            ORDER BY hd.detail_no
            """
            
            # 构建IN查询的占位符
            placeholders = ','.join(['%s'] * len(delivery_autoids))
            query = query.format(placeholders)
            
            # 构建参数列表
            params = [handover_no] + delivery_autoids
            
            # 执行查询
            results = self.db.execute_query(query, tuple(params))
            
            # 处理查询结果，确保所有字段都有值（即使是空值）
            processed_results = []
            for detail in results:
                processed_detail = {
                    'detail_no': detail['detail_no'] or '',
                    'handover_no': detail['handover_no'] or '',
                    'delivery_autoid': detail['delivery_autoid'] or '',
                    'material_code': detail['material_code'] or '',
                    'material_name': detail['material_name'] or '',
                    'unit': detail['unit'] or '',
                    'batch_no': detail['batch_no'] or '',
                    'warehouse': detail['warehouse'] or '',
                    'reject_reason': detail['reject_reason'] or '',
                    'return_quantity': detail['return_quantity'] or 0,
                    'received_quantity': detail['received_quantity'] or '',
                    'return_warehouse_no': detail['return_warehouse_no'] or '',
                    'stock_status': detail['stock_status'] or '',
                    'stock_status_text': detail['stock_status_text'] or ''
                }
                processed_results.append(processed_detail)
            
            return processed_results
            
        except Exception as e:
            print(f"获取拆分明细数据失败: {str(e)}")
            raise Exception(f"获取拆分明细数据失败: {str(e)}")
    
    def export_delivery_orders(self, filters=None):
        """导出出库单数据"""
        try:
            # 构建WHERE子句
            where_clause = ""
            params = []
            
            if filters:
                if filters.get('order_no'):
                    where_clause += " AND order_no LIKE %s"
                    params.append(f"%{filters['order_no']}%")
                    
                if filters.get('start_date'):
                    where_clause += " AND confirm_receipt_date >= %s"
                    params.append(filters['start_date'])
                    
                if filters.get('end_date'):
                    where_clause += " AND confirm_receipt_date <= %s"
                    params.append(filters['end_date'])
                    
                if filters.get('source_order_no'):
                    source_order_no = filters['source_order_no'].strip()
                    
                    # 检查是否包含逗号，判断是否为批量查询
                    if ',' in source_order_no:
                        # 批量精确查询模式
                        source_order_nos = [
                            no.strip() 
                            for no in source_order_no.split(',')
                            if no.strip()
                        ]
                        if source_order_nos:
                            placeholders = ','.join(['%s' for _ in source_order_nos])
                            where_clause += """ AND (
                                source_order_no IN ({0}) OR 
                                (source_order_no LIKE 'SO%' AND 
                                 SUBSTRING(
                                    summary,
                                    IF(
                                        LOCATE('SO', summary) > 0,
                                        LOCATE('SO', summary),
                                        1
                                    ),
                                    20
                                ) IN ({0})
                                )
                            )""".format(placeholders)
                            params.extend(source_order_nos)
                            params.extend(source_order_nos)
                    else:
                        # 单个模糊查询模式
                        where_clause += """ AND (
                            source_order_no LIKE %s OR
                            (source_order_no LIKE 'SO%' AND 
                             SUBSTRING(
                                summary,
                                IF(
                                    LOCATE('SO', summary) > 0,
                                    LOCATE('SO', summary),
                                    1
                                ),
                                20
                            ) LIKE %s)
                        )"""
                        search_value = f"%{source_order_no}%"
                        params.extend([search_value, search_value])
                    
                if filters.get('logistics_company'):
                    where_clause += " AND logistics_company = %s"
                    params.append(filters['logistics_company'])
                    
                if filters.get('order_status') is not None:
                    where_clause += " AND order_status = %s"
                    params.append(filters['order_status'])

            # 构建查询语句
            query = """
            SELECT 
                order_no,
                MAX(confirm_receipt_date) as confirm_receipt_date,
                source_order_no,
                MAX(summary) as summary,
                MAX(customer) as customer,
                MAX(logistics_company) as logistics_company,
                SUM(remaining_return_quantity) as remaining_return_quantity,
                MAX(CASE order_status 
                    WHEN 0 THEN '开启'
                    WHEN 1 THEN '关闭'
                END) as status,
                MAX(CASE 
                    WHEN source_order_no LIKE 'SO%' AND summary REGEXP '[A-Za-z0-9]+'
                    THEN SUBSTRING(
                        summary,
                        IF(
                            LOCATE('SO', summary) > 0,
                            LOCATE('SO', summary),
                            1
                        ),
                        20
                    )
                    ELSE source_order_no
                END) as original_order_no
            FROM delivery_order 
            WHERE 1=1
            """ + where_clause + """
            GROUP BY order_no
            ORDER BY MAX(business_date) DESC, order_no DESC
            """
            
            # 执行查询
            results = self.db.execute_query(query, tuple(params) if params else None)
            
            return results
            
        except Exception as e:
            raise Exception(f"导出出库单数据失败: {str(e)}")
    
    def batch_close_orders(self, order_nos, progress_callback=None):
        """批量关闭订单
        
        Args:
            order_nos: 订单号列表
            progress_callback: 进度回调函数
            
        Returns:
            int: 成功关闭的订单数量
        """
        try:
            if not order_nos:
                print("批量关闭：没有提供订单号")
                return 0
                
            # 打印调试信息
            print(f"批量关闭订单，传入的订单号列表: {order_nos}")
            print(f"订单号列表长度: {len(order_nos)}")
                
            # 开始事务
            self.db.begin_transaction()
            
            # 确保只处理传入的订单号列表
            if len(order_nos) > 0:
                # 获取未关闭的订单
                placeholders = ','.join(['%s'] * len(order_nos))
                query = f"""
                SELECT do.order_no, do.autoid
                FROM delivery_order do
                WHERE do.order_no IN ({placeholders}) AND do.order_status = 0
                """
                
                print(f"SQL查询: {query}")
                print(f"参数: {order_nos}")
                
                result = self.db.execute_query(query, order_nos)
                
                print(f"查询结果: {result}")
                print(f"找到 {len(result)} 个未关闭的订单")
                
                if not result:
                    print("没有找到需要关闭的订单")
                    self.db.commit_transaction()
                    return 0
                    
                total_orders = len(result)
                updated_count = 0
                
                # 更新订单状态为关闭(1)
                for i, row in enumerate(result):
                    order_no = row['order_no']
                    print(f"正在关闭订单: {order_no}")
                    
                    # 更新订单状态 - 注意：只更新特定订单号的记录
                    update_query = """
                    UPDATE delivery_order
                    SET order_status = 1,
                        update_time = CURRENT_TIMESTAMP
                    WHERE order_no = %s AND order_status = 0
                    """
                    
                    affected = self.db.execute_update(update_query, (order_no,))
                    print(f"更新影响的行数: {affected}")
                    
                    if affected > 0:
                        # 添加操作日志
                        self.add_operation_log(
                            operation_type="关闭订单",
                            operator=self.current_user,
                            document_no=order_no,
                            operation_content=f"批量关闭订单操作，订单号：{order_no}",
                            ip_address=self.get_local_ip()
                        )
                        
                        updated_count += 1
                    
                    # 更新进度
                    if progress_callback:
                        progress_value = int((i + 1) * 100 / total_orders)
                        if not progress_callback(progress_value):
                            # 用户取消操作
                            print("用户取消操作")
                            self.db.rollback_transaction()
                            return 0
                
                print(f"成功关闭 {updated_count} 个订单")
                # 提交事务
                self.db.commit_transaction()
                return updated_count
            else:
                print("订单号列表为空")
                self.db.commit_transaction()
                return 0
            
        except Exception as e:
            # 回滚事务
            print(f"批量关闭订单失败: {str(e)}")
            self.db.rollback_transaction()
            raise Exception(f"批量关闭订单失败: {str(e)}")
    
    def delete_delivery_order(self, order_no):
        """删除出库单及其明细
        
        Args:
            order_no: 出库单号
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 开始事务
            self.db.begin_transaction()
            
            # 检查是否存在任何状态的交接单
            check_handover_query = """
            SELECT COUNT(*) as count 
            FROM handover_order 
            WHERE delivery_order_no = %s
            """
            handover_result = self.db.execute_query(check_handover_query, (order_no,))
            
            if handover_result and handover_result[0]['count'] > 0:
                raise Exception("存在关联的交接单，不能删除出库单")
            
            # 删除出库单明细
            delete_details_query = "DELETE FROM delivery_order WHERE order_no = %s"
            self.db.execute_update(delete_details_query, (order_no,))
            
            # 添加操作日志
            self.add_operation_log(
                operation_type="删除",
                operator=self.current_user,
                document_no=order_no,
                operation_content=f"删除出库单 {order_no}",
                ip_address=self.client_ip
            )
            
            # 提交事务
            self.db.commit_transaction()
            return True
            
        except Exception as e:
            # 回滚事务
            self.db.rollback_transaction()
            raise Exception(f"删除出库单失败: {str(e)}")
    
    def get_kpi_data(self, start_date=None, end_date=None, logistics_company=None):
        """获取KPI统计数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            logistics_company: 物流公司名称
            
        Returns:
            list: KPI统计数据列表，包含各个维度的统计结果
        """
        try:
            # 构建基础查询
            query = """
            SELECT 
                do.logistics_company,
                -- 拒收数据（物流原因）
                SUM(CASE 
                    WHEN do.reject_reason = '物流问题' THEN do.return_quantity
                    ELSE 0 
                END) as reject_quantity,
                SUM(CASE 
                    WHEN do.reject_reason = '物流问题' THEN do.return_quantity * do.unit_price_with_tax
                    ELSE 0 
                END) as reject_amount,
                
                -- 原料数据
                SUM(CASE 
                    WHEN hd.stock_status = 'raw' AND do.reject_reason = '物流问题' THEN hd.received_quantity 
                    ELSE 0 
                END) as raw_quantity,
                SUM(CASE 
                    WHEN hd.stock_status = 'raw' AND do.reject_reason = '物流问题' THEN hd.received_quantity * do.unit_price_with_tax
                    ELSE 0 
                END) as raw_amount,
                
                -- 变形数据
                SUM(CASE 
                    WHEN hd.stock_status = 'deformed' AND do.reject_reason = '物流问题' THEN hd.received_quantity 
                    ELSE 0 
                END) as deformed_quantity,
                SUM(CASE 
                    WHEN hd.stock_status = 'deformed' AND do.reject_reason = '物流问题' THEN hd.received_quantity * do.unit_price_with_tax
                    ELSE 0 
                END) as deformed_amount,
                
                -- 报废数据
                SUM(CASE 
                    WHEN hd.stock_status = 'to_scrap' AND do.reject_reason = '物流问题' THEN hd.received_quantity 
                    ELSE 0 
                END) as scrap_quantity,
                SUM(CASE 
                    WHEN hd.stock_status = 'to_scrap' AND do.reject_reason = '物流问题' THEN hd.received_quantity * do.unit_price_with_tax
                    ELSE 0 
                END) as scrap_amount
                
            FROM delivery_order do 
            LEFT JOIN handover_detail hd ON do.autoid = hd.delivery_autoid
            WHERE 1=1
            """
            
            params = []
            
            # 添加日期过滤
            if start_date:
                query += " AND DATE(do.confirm_receipt_date) >= %s"
                params.append(start_date)
            
            if end_date:
                query += " AND DATE(do.confirm_receipt_date) <= %s"
                params.append(end_date)
            
            # 添加物流公司过滤
            if logistics_company and logistics_company != "全部物流公司":
                query += " AND do.logistics_company = %s"
                params.append(logistics_company)
            
            # 分组和排序
            query += """
            GROUP BY do.logistics_company
            ORDER BY do.logistics_company
            """
            
            # 执行查询
            results = self.db.execute_query(query, tuple(params) if params else None)
            
            return results
            
        except Exception as e:
            print(f"获取KPI数据失败: {str(e)}")
            raise Exception(f"获取KPI数据失败: {str(e)}")
    
    def get_box_kpi_data(self, start_date=None, end_date=None, logistics_company=None):
        """获取按原箱计算的KPI统计数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            logistics_company: 物流公司名称
            
        Returns:
            list: 按原箱计算的KPI统计数据列表
        """
        try:
            # 构建基础查询，需要连接物料表获取计量换算和原箱规格
            query = """
            SELECT 
                do.logistics_company,
                -- 拒收数据（物流原因）
                SUM(CASE 
                    WHEN do.reject_reason = '物流问题' 
                    THEN 
                        CASE 
                            -- 如果有计量换算和原箱规格，则进行换算
                            WHEN IFNULL(m.conversion_rate, 0) > 0 AND IFNULL(m.original_box_unit, 0) > 0 
                            THEN (do.return_quantity * IFNULL(m.conversion_rate, 1)) / IFNULL(m.original_box_unit, 1)
                            -- 否则直接返回原数量
                            ELSE do.return_quantity
                        END
                    ELSE 0 
                END) as reject_quantity,
                
                SUM(CASE 
                    WHEN do.reject_reason = '物流问题' 
                    THEN do.return_quantity * do.unit_price_with_tax
                    ELSE 0 
                END) as reject_amount,
                
                -- 原料数据
                SUM(CASE 
                    WHEN hd.stock_status = 'raw' AND do.reject_reason = '物流问题' 
                    THEN 
                        CASE 
                            WHEN IFNULL(m.conversion_rate, 0) > 0 AND IFNULL(m.original_box_unit, 0) > 0 
                            THEN (hd.received_quantity * IFNULL(m.conversion_rate, 1)) / IFNULL(m.original_box_unit, 1)
                            ELSE hd.received_quantity
                        END
                    ELSE 0 
                END) as raw_quantity,
                
                SUM(CASE 
                    WHEN hd.stock_status = 'raw' AND do.reject_reason = '物流问题' 
                    THEN hd.received_quantity * do.unit_price_with_tax
                    ELSE 0 
                END) as raw_amount,
                
                -- 变形数据
                SUM(CASE 
                    WHEN hd.stock_status = 'deformed' AND do.reject_reason = '物流问题' 
                    THEN 
                        CASE 
                            WHEN IFNULL(m.conversion_rate, 0) > 0 AND IFNULL(m.original_box_unit, 0) > 0 
                            THEN (hd.received_quantity * IFNULL(m.conversion_rate, 1)) / IFNULL(m.original_box_unit, 1)
                            ELSE hd.received_quantity
                        END
                    ELSE 0 
                END) as deformed_quantity,
                
                SUM(CASE 
                    WHEN hd.stock_status = 'deformed' AND do.reject_reason = '物流问题' 
                    THEN hd.received_quantity * do.unit_price_with_tax
                    ELSE 0 
                END) as deformed_amount,
                
                -- 报废数据
                SUM(CASE 
                    WHEN hd.stock_status = 'to_scrap' AND do.reject_reason = '物流问题' 
                    THEN 
                        CASE 
                            WHEN IFNULL(m.conversion_rate, 0) > 0 AND IFNULL(m.original_box_unit, 0) > 0 
                            THEN (hd.received_quantity * IFNULL(m.conversion_rate, 1)) / IFNULL(m.original_box_unit, 1)
                            ELSE hd.received_quantity
                        END
                    ELSE 0 
                END) as scrap_quantity,
                
                SUM(CASE 
                    WHEN hd.stock_status = 'to_scrap' AND do.reject_reason = '物流问题' 
                    THEN hd.received_quantity * do.unit_price_with_tax
                    ELSE 0 
                END) as scrap_amount
                
            FROM delivery_order do 
            LEFT JOIN handover_detail hd ON do.autoid = hd.delivery_autoid
            LEFT JOIN materials m ON do.material_code = m.material_id
            WHERE 1=1
            """
            
            params = []
            
            # 添加日期过滤
            if start_date:
                query += " AND DATE(do.confirm_receipt_date) >= %s"
                params.append(start_date)
            
            if end_date:
                query += " AND DATE(do.confirm_receipt_date) <= %s"
                params.append(end_date)
            
            # 添加物流公司过滤
            if logistics_company and logistics_company != "全部物流公司":
                query += " AND do.logistics_company = %s"
                params.append(logistics_company)
            
            # 分组和排序
            query += """
            GROUP BY do.logistics_company
            ORDER BY do.logistics_company
            """
            
            # 执行查询
            results = self.db.execute_query(query, tuple(params) if params else None)
            
            # 处理结果，确保小数点后保留两位
            processed_results = []
            for row in results:
                processed_row = {
                    'logistics_company': row['logistics_company'],
                    'reject_quantity': round(float(row['reject_quantity'] or 0), 2),
                    'reject_amount': round(float(row['reject_amount'] or 0), 2),
                    'raw_quantity': round(float(row['raw_quantity'] or 0), 2),
                    'raw_amount': round(float(row['raw_amount'] or 0), 2),
                    'deformed_quantity': round(float(row['deformed_quantity'] or 0), 2),
                    'deformed_amount': round(float(row['deformed_amount'] or 0), 2),
                    'scrap_quantity': round(float(row['scrap_quantity'] or 0), 2),
                    'scrap_amount': round(float(row['scrap_amount'] or 0), 2)
                }
                processed_results.append(processed_row)
            
            return processed_results
            
        except Exception as e:
            print(f"获取按原箱计算的KPI数据失败: {str(e)}")
            raise Exception(f"获取按原箱计算的KPI数据失败: {str(e)}")
