# 运营管理系统

## 项目简介
这是一个基于PyQt6开发的管理系统，主要用于管理正常退货流程，东方退货管理，系统基本框架，物料管理，BOM管理

## 🏗️ 技术架构规范

### 核心技术栈
- **GUI框架：** PyQt6 >= 6.4.0
- **数据库：** MySQL 8.0+
- **架构模式：** MVC分层架构
- **设计模式：** 对话框模式、观察者模式、单例模式

### 统一技术规范

#### 🎨 界面样式管理
- **样式文件：** 所有UI组件统一使用 `styles.qss` 样式文件
- **样式应用：** 通过 `setStyleSheet()` 方法统一加载样式
- **组件规范：** 按钮、表格、对话框等组件遵循统一的视觉规范

#### 🔢 业务编码管理
- **编号服务：** 所有自动编号功能统一使用 `AutoNumberService()` 服务
- **编号格式：** 前缀-日期-序号（如：CJ-20241201-0001）
- **支持类型：** CJ（出库单）、TH（退货单）、JJ（交接单）、WL（物料）、CJRK（入库收货单）、BOM（物料清单）
- **并发安全：** 通过数据库存储过程确保编号唯一性

#### 👤 用户管理规范
- **用户获取：** 统一使用 `get_current_user()` 函数获取当前用户信息
- **权限控制：** 基于用户角色的功能权限管理
- **操作日志：** 所有关键操作自动记录用户和时间信息

#### 🗄️ 数据库操作规范
- **连接管理：** 所有数据库操作统一使用 `DatabaseManager` 类管理
- **操作方法：** 使用 `execute_query()` 和 `execute_update()` 方法
- **事务处理：** 支持 `begin_transaction()`、`commit_transaction()`、`rollback_transaction()`
- **连接池：** 自动管理连接池，避免连接泄漏
- **异常处理：** 统一的异常处理和日志记录机制

## 项目结构
```
return_system_list-version/
├── core/                    # 核心功能模块
│   ├── async_manager.py     # 异步任务管理器
│   ├── config.py           # 配置文件和用户管理
│   ├── database.py         # 数据库连接管理（已弃用）
│   ├── exceptions.py       # 自定义异常类
│   └── logger.py           # 日志管理
│
├── modules/                 # 功能模块
│   ├── batch_search_dialog.py    # 批量搜索对话框
│   ├── db_manager.py             # 数据库管理器（统一入口）
│   ├── detail_dialog.py          # 详情对话框
│   ├── handover_detail_dialog.py # 交接单详情
│   ├── import_dialog.py          # 导入对话框
│   ├── print_preview_dialog.py   # 打印预览
│   ├── return_db.py              # 退货数据库操作
│   └── return_module.py          # 退货主模块
│
├── material/                # 物料管理模块
│   ├── material_db.py       # ✅ 物料数据库操作（已重构）
│   ├── material_dialog.py   # ✅ 物料新增/编辑对话框
│   ├── material_model.py    # ✅ 物料数据模型
│   ├── material_module.py   # ✅ 物料管理主模块
│   ├── init_material_db.py  # ✅ 物料数据库初始化
│   ├── bom_db.py           # ✅ BOM数据库操作（已完成）
│   ├── bom_module.py       # 🔄 BOM管理主模块（开发中）
│   ├── bom_dialog.py       # 🔄 BOM新增/编辑对话框（开发中）
│   ├── 📋 TODO: bom_detail_dialog.py   # BOM明细添加/编辑对话框
│   ├── 📋 TODO: bom_print_dialog.py    # BOM打印预览对话框
│   ├── 📋 TODO: bom_import_dialog.py   # BOM批量导入对话框
│   ├── 📋 TODO: material_search_dialog.py # 物料选择对话框
│   └── 📋 TODO: bom_copy_dialog.py     # BOM复制对话框
│
├── cj_return/              # ✅ 东方退货管理模块（已完成）
│   ├── dongfang_return.sql              # ✅ 数据库表结构
│   ├── dongfang_return_module.py        # ✅ 退货申请主界面
│   ├── dongfang_return_dialog.py        # ✅ 退货申请对话框
│   ├── dongfang_return_db.py            # ✅ 数据库操作层
│   ├── dongfang_detail_dialog.py        # ✅ 明细添加对话框
│   ├── warehouse_receipt.sql            # ✅ 收货单数据库表结构
│   ├── warehouse_receipt_module.py      # ✅ 收货单主界面
│   ├── warehouse_receipt_dialog.py      # ✅ 收货单对话框
│   ├── warehouse_receipt_print_dialog.py # ✅ 打印预览对话框
│   └── goods_select_dialog.py           # ✅ 货品选择对话框
│
├── report/                 # 报表相关模块
│   └── report_module.py    # 物流KPI考核等报表功能
│
├── system/                 # ✅ 系统管理模块
│   ├── auto_number_module.py # ✅ 自动编号管理（已完成）
│   └── auto_number_example.py # ✅ 使用示例
│
├── resources/               # 资源文件
│   └── icons/              # 图标资源
│
├── home_module.py          # 首页模块
├── return_window.py        # 主窗口程序
├── return_build.spec       # PyInstaller打包配置
├── requirements.txt        # 项目依赖
├── styles.qss              # ✅ 统一样式表文件
└── migosys.sql             # ✅ 完整数据库结构
```

## 核心功能模块说明

### core模块
- `async_manager.py`: 管理异步任务，处理耗时操作
- `config.py`: 系统配置管理，包含数据库连接和用户管理
- `database.py`: ⚠️ 已弃用，使用 `modules/db_manager.py` 替代
- `exceptions.py`: 自定义异常类定义
- `logger.py`: 日志记录管理

### modules模块
- `db_manager.py`: ✅ **统一数据库管理器**（所有模块的数据库操作入口）
- `return_db.py`: 退货相关的数据库操作
- `return_module.py`: 退货主功能模块
- `detail_dialog.py`: 详情查看对话框
- `handover_detail_dialog.py`: 交接单详情处理
- `import_dialog.py`: 数据导入功能
- `print_preview_dialog.py`: 打印预览功能
- `batch_search_dialog.py`: 批量搜索功能

### material模块（物料管理）
- `material_db.py`: ✅ **物料数据库操作**（已重构，使用统一db_manager）
- `material_dialog.py`: ✅ 物料新增/编辑对话框
- `material_model.py`: ✅ 物料数据模型
- `material_module.py`: ✅ 物料管理主模块
- `init_material_db.py`: ✅ 物料数据库初始化
- `bom_db.py`: ✅ **BOM数据库操作**（已完成，集成自动编号）
- `bom_module.py`: 🔄 **BOM管理主模块**（基础框架存在，功能开发中）
- `bom_dialog.py`: 🔄 **BOM编辑对话框**（开发中）

### cj_return模块（东方退货管理）✅
- `dongfang_return_db.py`: ✅ 退货申请数据库操作（集成自动编号）
- `dongfang_return_module.py`: ✅ 退货申请主界面
- `dongfang_return_dialog.py`: ✅ 退货申请对话框
- `dongfang_detail_dialog.py`: ✅ 明细添加对话框
- `warehouse_receipt_module.py`: ✅ 入库收货单主界面
- `warehouse_receipt_dialog.py`: ✅ 入库收货单对话框
- `warehouse_receipt_print_dialog.py`: ✅ 打印预览对话框
- `goods_select_dialog.py`: ✅ 货品选择对话框

### system模块（系统管理）✅
- `auto_number_module.py`: ✅ **自动编号服务**（统一编号管理）
- `auto_number_example.py`: ✅ 使用示例和最佳实践

### report模块
- `report_module.py`: 物流KPI考核等报表统计与展示功能

## 🔗 文件关联关系

### 1. 主程序入口
- `return_window.py`: 程序入口，创建主窗口
- 依赖: `core/*`, `modules/*`, `report/*`, `material/*`, `cj_return/*`, `styles.qss`, `home_module.py`

### 2. 数据库操作架构
```
统一数据库管理架构：
├── modules/db_manager.py          # 统一数据库管理器
├── material/material_db.py        # 物料数据库操作
├── material/bom_db.py            # BOM数据库操作
├── cj_return/dongfang_return_db.py # 东方退货数据库操作
└── system/auto_number_module.py   # 自动编号服务
```

### 3. UI界面架构
- `return_window.py`: 主窗口
- `styles.qss`: ✅ **统一样式表**（所有界面组件的样式定义）
- `modules/*_dialog.py`: 各种功能对话框
- `material/*_module.py`: 物料管理界面
- `cj_return/*_module.py`: 东方退货管理界面
- `home_module.py`: 首页界面

### 4. 异步处理
- `core/async_manager.py`: 管理异步任务
- 被`return_window.py`和各个模块调用

## 🚀 当前开发进度

### ✅ 已完成功能

#### 1. 东方退货申请模块 ✅
**状态：已完成**
- ✅ 退货申请主表和明细表设计
- ✅ 退货申请的增删改查功能
- ✅ 审核/反审核功能
- ✅ 拆分功能和作废功能
- ✅ 批量导入Excel功能
- ✅ 数据导出功能
- ✅ 界面交互和用户体验优化
- ✅ 三种单据状态管理：保存(0)、审核(1)、关闭(2)
- ✅ 分页查询（每页20条记录）
- ✅ 多条件搜索（业务编号、日期范围、状态）
- ✅ 右键菜单操作（查看、编辑、删除、审核、关闭）
- ✅ 双击编辑功能
- ✅ 批量删除功能

#### 2. 入库收货单模块 ✅
**状态：已完成**
- ✅ 入库收货单主表和明细表设计
- ✅ 从退货申请生成入库收货单功能
- ✅ 收货单号自动生成（格式：CJRK-YYYYMMDD-序号）
- ✅ 收货单的查询、编辑、审核功能
- ✅ 收货单详情对话框（查看/编辑模式）
- ✅ 收货单打印预览功能
- ✅ 库存状态管理（原料/变形/待报废/报废）
- ✅ 数量平衡验证和汇总计算
- ✅ 审核/反审核功能
- ✅ 删除收货单功能
- ✅ 数据导出Excel功能
- ✅ 分页查询和搜索功能
- ✅ 完整的打印预览界面
- ✅ 专业的打印样式和布局
- ✅ 签名区域（收货人签名、仓库管理员签名）

#### 3. 自动编号管理模块 ✅
**状态：已完成**
- ✅ 自动编号规则表设计
- ✅ 编号历史记录表设计
- ✅ 支持多种业务类型编号生成
- ✅ 编号格式：前缀-日期-序号（如：CJ-20241201-0001）
- ✅ 管理界面：规则增删改查
- ✅ 每日序号重置功能
- ✅ 并发安全处理

**支持的编号类型：**
- `CJ-`：出库单编号
- `TH-`：退货单编号  
- `JJ-`：交接单编号
- `WL-`：物料编号
- `CJRK-`：入库收货单编号
- `BOM-`：BOM编码

#### 4. 数据库操作层重构 ✅
**状态：已完成**
- ✅ 统一数据库管理器 (`modules/db_manager.py`)
- ✅ 物料数据库操作重构 (`material/material_db.py`)
- ✅ BOM数据库操作完成 (`material/bom_db.py`)
- ✅ 连接池管理和异常处理
- ✅ 事务处理机制
- ✅ 自动编号服务集成
- ✅ 用户信息管理集成

### 🔄 开发中功能

#### BOM管理模块 🔄
**状态：开发中**

##### **已完成部分 ✅**
- ✅ BOM数据库操作类 (`material/bom_db.py`)
- ✅ 自动编号规则集成（BOM-YYYYMMDD-XXX）
- ✅ 用户信息管理集成
- ✅ 完整的CRUD操作（增删改查）
- ✅ BOM主表和明细表操作
- ✅ 物料选择和分类查询
- ✅ 事务处理和错误处理
- ✅ 操作日志记录
- ✅ BOM主界面模块 (`material/bom_module.py`)
- ✅ BOM编辑对话框 (`material/bom_dialog.py`)
- ✅ 物料选择对话框 (`material/bom_material_search_dialog.py`)
- ✅ BOM明细对话框 (`material/bom_detail_dialog.py`)
- ✅ 多条件搜索（BOM编码、名称、成品物料）
- ✅ 新增/编辑/删除BOM功能
- ✅ 右键菜单操作
- ✅ 双击编辑功能
- ✅ 分页查询和数据刷新
- ✅ BOM主表信息编辑界面
- ✅ BOM明细表格管理
- ✅ 明细添加/编辑/删除功能
- ✅ 数量计算和验证
- ✅ 状态控制和版本管理

##### **开发中部分 🔄**
1. **BOM扩展功能优化** 🔄
   - 🔄 BOM复制功能完善
   - 🔄 导出Excel功能优化
   - 🔄 打印预览功能开发

### 📋 待开发功能

#### 模块开发优先级和步骤

##### **Phase 1: BOM模块完善（1-2周）- 当前阶段**
1. **📋 TODO: BOM扩展功能完善**
   - 📋 TODO: 创建BOM复制对话框 (`material/bom_copy_dialog.py`)
   - 📋 TODO: 完善Excel导出BOM清单功能
   - 📋 TODO: 创建BOM打印预览 (`material/bom_print_dialog.py`)
   - 📋 TODO: BOM清单打印格式设计
   - 📋 TODO: 导出PDF功能

2. **📋 TODO: BOM高级功能**
   - 📋 TODO: 创建BOM批量导入 (`material/bom_import_dialog.py`)
   - 📋 TODO: BOM成本计算功能
   - 📋 TODO: BOM版本对比功能

##### **Phase 2: 供应商管理模块（4-5周）- 下一阶段**

###### **第一步：供应商基础架构（1-2周）**
1. **📋 TODO: 供应商数据库设计** (`supplier/supplier_db.py`)
   - 📋 TODO: 供应商主表设计 (suppliers)
   - 📋 TODO: 供应商联系人表设计 (supplier_contacts)
   - 📋 TODO: 供应商资质证照表设计 (supplier_qualifications)
   - 📋 TODO: 供应商评价记录表设计 (supplier_evaluations)
   - 📋 TODO: 供应商物料关联表设计 (supplier_materials)

2. **📋 TODO: 供应商基础信息管理** (`supplier/supplier_module.py`)
   - 📋 TODO: 供应商档案管理（编码、名称、联系方式、地址）
   - 📋 TODO: 供应商分类管理（原料供应商、包材供应商、服务供应商）
   - 📋 TODO: 供应商状态管理（合格、暂停、黑名单）
   - 📋 TODO: 供应商评级管理（A/B/C级分类）

###### **第二步：供应商界面开发（1-2周）**
3. **📋 TODO: 供应商对话框** (`supplier/supplier_dialog.py`)
   - 📋 TODO: 供应商新增/编辑对话框
   - 📋 TODO: 多标签页设计（基本信息、联系人、资质、评价）
   - 📋 TODO: 数据验证和保存逻辑
   - 📋 TODO: 附件上传功能

4. **📋 TODO: 供应商选择对话框** (`supplier/supplier_select_dialog.py`)
   - 📋 TODO: 按物料筛选供应商
   - 📋 TODO: 供应商信息预览
   - 📋 TODO: 多供应商比较功能

###### **第三步：供应商业务功能（1-2周）**
5. **📋 TODO: 供应商业务管理**
   - 📋 TODO: 供应商可供应物料配置
   - 📋 TODO: 物料供应商优先级设置
   - 📋 TODO: 供应商报价管理
   - 📋 TODO: 历史采购记录查询

6. **📋 TODO: 供应商评价体系**
   - 📋 TODO: 供应商绩效评价（质量、交期、服务、价格）
   - 📋 TODO: 评价记录管理
   - 📋 TODO: 供应商排名统计
   - 📋 TODO: 评价报表生成

##### **Phase 3: 需求计划申请单模块（4-5周）** ✅

###### **第一步：需求计划基础架构（1-2周）** ✅
1. **✅ 需求计划数据库设计** (`demand/demand_plan_db.py`)
   - ✅ 需求计划主表设计 (demand_plans)
   - ✅ 需求计划明细表设计 (demand_plan_details)
   - ✅ BOM展开物料表设计 (demand_bom_materials)
   - ✅ 需求执行跟踪表设计 (demand_executions)

2. **✅ 需求计划主表管理** (`demand/demand_plan_module.py`)
   - ✅ 需求计划单据管理（编号、日期、申请部门、状态）
   - ✅ 需求类型管理（生产需求、销售需求、库存补充）
   - ✅ 紧急程度管理（普通、紧急、特急）
   - ✅ BOM展开确认流程

###### **第二步：需求计划界面开发（1-2周）** ✅
3. **✅ 需求计划对话框** (`demand/demand_plan_dialog.py`)
   - ✅ 需求计划新增/编辑对话框
   - ✅ 需求明细表格管理
   - ✅ BOM展开预览和确认

4. **✅ 需求明细对话框** (`demand/demand_detail_dialog.py`)
   - ✅ 物料选择集成
   - ✅ 需求数量输入和验证
   - ✅ 需求日期设置
   - ✅ 用途说明和备注

###### **第三步：BOM展开功能（1周）** ✅
5. **✅ BOM展开核心逻辑** (`demand/demand_plan_db.py`)
   - ✅ 成品物料BOM查询和验证
   - ✅ 原料和包材需求计算（含损耗率）
   - ✅ 展开结果保存到 demand_bom_materials 表
   - ✅ 展开预览和确认机制

6. **✅ BOM展开界面** (`demand/bom_expand_confirm_dialog.py`)
   - ✅ 展开结果预览树形界面
   - ✅ 原料和包材分类显示
   - ✅ 数量计算和成本统计
   - ✅ 批量确认展开功能

##### **Phase 4: 库存管理模块（3-4周）** 🚧

###### **第一步：库存数据库设计（1周）**
1. **📋 TODO: 库存管理数据库设计** (`inventory/inventory_db.py`)
   - 📋 TODO: 期初库存表设计 (inventory_initial)
   - 📋 TODO: 库存流水表设计 (inventory_transactions)
   - 📋 TODO: 部门库存归属表设计 (department_inventory)
   - 📋 TODO: 库存可用量视图设计

2. **📋 TODO: 库存业务逻辑** (`inventory/inventory_service.py`)
   - 📋 TODO: 库存可用量计算逻辑
   - 📋 TODO: 按部门库存分配管理
   - 📋 TODO: 库存不足检查和采购建议
   - 📋 TODO: 生产扣减和采购入库处理

###### **第二步：生产计划集成（1-2周）**
3. **📋 TODO: 生产计划增强** (`production/production_plan_db.py`)
   - 📋 TODO: 与需求计划的关联
   - 📋 TODO: 库存检查和扣减逻辑
   - 📋 TODO: 物料可用性验证
   - 📋 TODO: 生产计划状态管理

4. **📋 TODO: 采购订单生成** (`purchase/purchase_order_db.py`)
   - 📋 TODO: 基于库存不足自动生成采购订单
   - 📋 TODO: 采购订单与需求计划关联
   - 📋 TODO: 采购入库后库存更新
   - 📋 TODO: 采购执行状态跟踪

###### **第三步：库存管理界面（1周）**
5. **📋 TODO: 库存管理界面** (`inventory/inventory_module.py`)
   - 📋 TODO: 库存查询和统计界面
   - 📋 TODO: 部门库存分配管理
   - 📋 TODO: 库存流水查询
   - 📋 TODO: 库存预警和报表

##### **Phase 5: 完整业务流程集成（1-2周）**
6. **📋 TODO: 端到端业务流程**
   - 📋 TODO: 需求计划 → BOM展开 → 库存检查 → 生产/采购决策
   - 📋 TODO: 采购入库 → 库存更新 → 生产计划 → 库存扣减
   - 📋 TODO: 全流程状态跟踪和报表
   - 📋 TODO: 异常处理和回滚机制

#### 总体开发时间线

```
当前状态：BOM核心功能已完成 ✅

Phase 1: BOM模块完善        [1-2周]  📅 即将开始
├── BOM复制功能
├── 导出打印功能  
└── 批量导入功能

Phase 2: 供应商管理模块     [4-5周]  📅 下一阶段
├── 数据库设计 (1-2周)
├── 界面开发 (1-2周)
└── 业务功能 (1-2周)

Phase 3: 需求计划模块       [4-5周]  � 第三阶段
├── 基础架构 (1-2周)
├── 界面开发 (1-2周)
└── BOM展开和审批 (1-2周)

Phase 4: 高级功能集成       [2-3周]  📅 第四阶段
├── 分析报表 (1周)
└── 系统集成 (1-2周)

Phase 5: 测试文档          [1周]    📅 最后阶段
├── 功能测试
└── 文档完善

总计：12-16周 (约3-4个月)
```

## 🎯 东方退货系统 - 完整功能汇总 ✅

### 📋 业务流程控制
**状态流转：**
```
退货申请：保存 → 审核 → 关闭
收货单：  保存 → 审核 → 关闭
```

**业务规则：**
- ✅ 已审核的退货申请可生成入库收货单
- ✅ 生成收货单后退货申请被锁定（is_locked=1）
- ✅ 收货单审核后对应退货申请状态变为关闭(2)
- ✅ 删除收货单会解锁对应的退货申请
- ✅ 反审核收货单会将退货申请状态恢复为审核(1)

### 🎨 用户界面特性
**界面组件：**
- ✅ 标签页式界面设计
- ✅ 工具栏按钮（新增、编辑、删除、审核、反审核、导出、打印）
- ✅ 状态指示器（不同颜色显示不同状态）
- ✅ 进度对话框（导出、批量操作）
- ✅ 右键上下文菜单
- ✅ 双击快速编辑

**数据验证：**
- ✅ 必填字段验证
- ✅ 数量平衡验证
- ✅ 状态权限控制
- ✅ 业务规则检查

### 📊 数据管理功能
**查询功能：**
- ✅ 分页查询（支持大数据量）
- ✅ 多条件组合搜索
- ✅ 日期范围筛选
- ✅ 状态筛选

**导入导出：**
- ✅ Excel数据导出（支持主表+明细表合并导出）
- ✅ 批量Excel导入（退货申请明细）
- ✅ 导出进度显示

### 🔧 系统集成功能
**数据库集成：**
- ✅ 与CJ_GOODS表关联（东方货号映射）
- ✅ 与materials表关联（物料信息）
- ✅ 自动编号规则管理
- ✅ 事务处理保证数据一致性

**权限控制：**
- ✅ 基于用户角色的功能权限
- ✅ 操作日志记录
- ✅ 数据安全控制

## 开发环境配置

### 1. 创建虚拟环境
```powershell
Set-ExecutionPolicy RemoteSigned -Scope Process
.\venv\Scripts\Activate.ps1
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行程序
```bash
python return_window.py
```

### 4. 初始化物料数据库（首次运行）
```bash
python -m material.init_material_db
```

### 5. 打包程序
```bash
pyinstaller return_build.spec
```

## 依赖说明
- **PyQt6 >= 6.4.0**: GUI框架
- **mysql-connector-python >= 8.0.0**: MySQL数据库连接
- **pandas >= 1.5.0**: 数据处理
- **numpy >= 1.23.0**: 数值计算
- **openpyxl >= 3.0.0**: Excel文件处理
- **Pillow >= 9.0.0**: 图像处理
- **qrcode >= 7.3.0**: 二维码生成
- **et_xmlfile >= 1.1.0**: XML文件处理

## 版本控制
项目使用Git进行版本控制，主要跟踪以下文件和目录：
- core/
- modules/
- material/
- cj_return/
- report/
- system/
- return_window.py
- return_build.spec
- requirements.txt
- styles.qss
- migosys.sql

## 📈 开发工作量预估

### BOM模块剩余工作量
- **第一阶段（核心功能）：** 8-12个工作日
- **第二阶段（扩展功能）：** 5-8个工作日  
- **第三阶段（高级功能）：** 6-10个工作日
- **测试和优化：** 2-3个工作日
- **总计：** 21-33个工作日（约4-6周）

## 🔧 维护要点

### 数据库操作规范
1. **统一管理：** 通过 `DatabaseManager` 统一管理所有数据库连接
2. **用户信息：** 通过 `get_current_user()` 获取当前用户信息
3. **自动编号：** 通过 `AutoNumberService` 生成业务编号
4. **日志记录：** 使用 `Logger` 类记录操作日志
5. **异常处理：** 所有数据库操作都有完整的异常处理
6. **界面刷新：** 标签页切换时自动刷新数据
7. **状态同步：** 操作完成后自动刷新相关界面

### 代码规范
- **样式管理：** 统一使用 `styles.qss` 样式文件
- **编码规范：** 遵循PEP 8 Python编码规范
- **注释规范：** 关键方法和复杂逻辑必须添加注释
- **错误处理：** 所有可能出错的地方都要有异常处理

## 📝 版本更新记录
- **v4.2.0**: 完成数据库操作层重构，统一使用DatabaseManager
- **v4.1.0**: 完成BOM数据库操作层，集成自动编号和用户管理
- **v4.0.0**: 完成东方退货系统完整模块，包含退货申请和入库收货单的完整业务流程
- **v3.9.0**: 完成入库收货单模块，包含完整的CRUD操作、打印功能
- **v3.8.9**: 优化拆分更新审核逻辑
- **v3.1.5**: 增加作废查询及功能
- **v3.1.4**: 增加作废功能、拆分功能、删除明细功能
- **v3.1.3**: 修复审核及反审核问题，明细拆分修改自动排序

## 🎯 下一步开发计划

### 短期目标（本周）
1. **完善BOM主界面模块**
   - 修复现有按钮功能问题
   - 实现多条件搜索功能
   - 完善数据刷新机制
   
2. **创建BOM编辑对话框**
   - 设计BOM主表编辑界面
   - 实现明细表格管理功能

### 中期目标（本月）
1. **完成BOM核心功能**
   - 物料选择对话框
   - BOM明细编辑对话框
   - 基础的增删改查功能
   
2. **BOM扩展功能**
   - BOM复制功能
   - 导出Excel功能
   - 打印预览功能

### 长期目标（下月）
1. **BOM高级功能**
   - 批量导入功能
   - 成本计算分析
   - 版本管理和对比
   
2. **系统整体优化**
   - 性能优化和测试
   - 用户体验改进
   - 文档完善

### 技术债务
- 需要优化BOM数据库查询性能
- 考虑添加BOM循环引用检查机制
- 界面响应性能优化
- 完善BOM业务规则验证
#### 🛡️ 权限系统安全升级方案

##### **Phase 0.1: 权限框架搭建（1周）- 零影响**
1. **📋 TODO: 创建权限管理工具类**
   - 📋 TODO: 创建 `system/permission_manager.py`（新文件，不影响现有代码）
   - 📋 TODO: 创建按钮权限装饰器（可选使用）
   - 📋 TODO: 保持现有 `has_permission()` 方法不变

2. **📋 TODO: 数据权限表结构**
   - 📋 TODO: 新增数据权限相关表（不影响现有表）
   - 📋 TODO: 保持现有权限表结构不变

##### **Phase 0.2: 新模块权限集成（1周）- 仅影响新模块**
3. **📋 TODO: 仅在新模块中使用新权限系统**
   - 📋 TODO: BOM模块按钮权限（可选升级）
   - 📋 TODO: 供应商模块完整权限控制（新模块）
   - 📋 TODO: 需求计划模块完整权限控制（新模块）

##### **Phase 0.3: 现有模块可选升级（1周）- 可选执行**
4. **📋 TODO: 现有模块权限升级（可选）**
   - 📋 TODO: 物料管理模块按钮权限
   - 📋 TODO: 用户管理模块按钮权限
   - 📋 TODO: 系统管理模块按钮权限


---
🔄 第一阶段（1周）：框架搭建
├── 创建权限工具类（新文件）
├── 设计数据权限表（新表）
└── 保持现有代码100%不变

🔄 第二阶段（1周）：新模块集成
├── 供应商模块使用新权限系统
├── 需求计划模块使用新权限系统  
└── 现有模块完全不受影响

🔄 第三阶段（可选）：现有模块升级
├── BOM模块可选升级按钮权限
├── 其他模块可选升级
└── 完全向下兼容

## 📞 技术支持
如有技术问题，请联系开发团队或查看项目文档。

**项目状态：** 🔄 积极开发中  
**最后更新：** 2025-7-23  
**当前版本：** v4.2.0
