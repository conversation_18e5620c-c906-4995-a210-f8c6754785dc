from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
from datetime import datetime, date
import traceback
from .dongfang_return_db import DongfangReturnDB

class DongfangReturnDialog(QDialog):
    """东方退货申请对话框"""
    
    def __init__(self, parent=None, return_id=None, mode='add'):
        super().__init__(parent)
        self.return_id = return_id
        self.mode = mode  # add, edit, view
        self.dongfang_db = DongfangReturnDB()
        self.detail_data = []
        
        self.setWindowTitle({
            'add': '新增退货申请',
            'edit': '编辑退货申请', 
            'view': '查看退货申请'
        }.get(mode, '退货申请'))
        
        self.setModal(True)
        self.resize(1024, 760)
        self.init_ui()
        
        if return_id:
            self.load_data()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(0)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 设置整体样式 - ERP风格
        self.setStyleSheet("""
            QDialog {
                background-color: #f0f0f0;
            }
            QWidget {
                font-family: "Microsoft YaHei", "SimHei", sans-serif;
                font-size: 12px;
            }
            QLabel {
                color: #333333;
                font-weight: normal;
                padding: 2px;
            }
            QLineEdit, QTextEdit, QComboBox, QDateEdit {
                border: 1px solid #cccccc;
                padding: 4px 6px;
                background-color: white;
                font-size: 12px;
                height: 20px;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus {
                border-color: #71c4ef;
            }
            QLineEdit:read-only, QTextEdit:read-only, QDateEdit:disabled {
                background-color: #f5f5f5;
                color: #666666;
                border-color: #e0e0e0;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
            QTableWidget {
                background-color: white;
                gridline-color: #cccccc;
                border: 1px solid #cccccc;
                selection-background-color: #71c4ef;
                selection-color: white;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                color: #333333;
                padding: 8px;
                border: none;
                border-right: 1px solid #cccccc;
                border-bottom: 1px solid #cccccc;
                font-weight: bold;
                font-size: 12px;
                height: 30px;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #f0f0f0;
                height: 25px;
            }
            QTableWidget::item:selected {
                background-color: #71c4ef;
                color: white;
            }
        """)
        
        # 工具栏
        toolbar = QToolBar()
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #f5f5f5;
                border: none;
                padding: 5px;
                spacing: 5px;
            }
            QToolBar QToolButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-weight: normal;
                min-width: 60px;
            }
            QToolBar QToolButton:hover {
                background-color: #40a9ff;
            }
            QToolBar QToolButton:pressed {
                background-color: #096dd9;
            }
        """)
        
        # 保存按钮
        self.save_action = QAction("保存", self)
        self.save_action.triggered.connect(self.save_data)
        toolbar.addAction(self.save_action)
        
        # 重新生成编号按钮
        if self.mode == 'add':
            self.regenerate_action = QAction("重新生成编号", self)
            self.regenerate_action.triggered.connect(self.regenerate_number)
            toolbar.addAction(self.regenerate_action)
        
        # 审核相关按钮
        if self.mode in ['edit', 'view'] and self.return_id:
            self.audit_action = QAction("审核", self)
            self.audit_action.triggered.connect(self.audit_return)
            toolbar.addAction(self.audit_action)
            
            self.unaudit_action = QAction("反审核", self)
            self.unaudit_action.triggered.connect(self.unaudit_return)
            toolbar.addAction(self.unaudit_action)
            
            # 生成入库收货单按钮 - 只在编辑和查看模式下显示
            self.generate_receipt_action = QAction("生成入库收货单", self)
            self.generate_receipt_action.triggered.connect(self.generate_warehouse_receipt)
            toolbar.addAction(self.generate_receipt_action)
        
        # 关闭按钮
        self.close_action = QAction("关闭", self)
        self.close_action.triggered.connect(self.reject)
        toolbar.addAction(self.close_action)
        layout.addWidget(toolbar)
        
        # 主要内容区域
        content_widget = QWidget()
        content_widget.setStyleSheet("background-color: #f0f0f0;")
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(10, 10, 10, 10)
        content_layout.setSpacing(10)
        
        # 基本信息区域
        header_group = QGroupBox("基本信息")
        header_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #333333;
                border: 1px solid #cccccc;
                margin-top: 10px;
                padding-top: 5px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background-color: #f0f0f0;
            }
        """)
        
        header_layout = QVBoxLayout(header_group)
        header_layout.setContentsMargins(15, 15, 15, 15)
        
        # 使用网格布局
        grid_layout = QGridLayout()
        grid_layout.setHorizontalSpacing(15)
        grid_layout.setVerticalSpacing(8)
        
        # 第一行
        grid_layout.addWidget(QLabel("业务编号:"), 0, 0)
        self.yewu_no_input = QLineEdit()
        if self.mode == 'add':
            try:
                auto_number = self.dongfang_db.generate_auto_number()
                self.yewu_no_input.setText(auto_number)
            except Exception as e:
                from datetime import datetime
                now = datetime.now()
                self.yewu_no_input.setText(f"DF{now.strftime('%Y%m%d%H%M%S')}")
        self.yewu_no_input.setReadOnly(True)
        grid_layout.addWidget(self.yewu_no_input, 0, 1)
        
        grid_layout.addWidget(QLabel("退货日期:"), 0, 2)
        self.return_date = QDateEdit()
        self.return_date.setDate(QDate.currentDate())
        self.return_date.setCalendarPopup(True)
        grid_layout.addWidget(self.return_date, 0, 3)
        
        # 第二行
        grid_layout.addWidget(QLabel("客户名称:"), 1, 0)
        self.customer_input = QLineEdit()
        self.customer_input.setText("东方购物")
        self.customer_input.setReadOnly(True)
        grid_layout.addWidget(self.customer_input, 1, 1)
        
        grid_layout.addWidget(QLabel("物流公司:"), 1, 2)
        self.logistics_combo = QComboBox()
        self.logistics_combo.addItems(["邮政", "顺丰"])
        self.logistics_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #cccccc;
                padding: 4px 6px;
                background-color: white;
                font-size: 12px;
                height: 20px;
                color: #333333;
                selection-background-color: #1890ff;
            }
            QComboBox:focus {
                border-color: #71c4ef;
            }
            QComboBox:hover {
                border-color: #8fd0f2;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
                background-color: transparent;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMgNC41TDYgNy41TDkgNC41IiBzdHJva2U9IiM2NjY2NjYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }
            QComboBox QAbstractItemView {
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: white;
                selection-background-color: #e6f7ff;
                selection-color: #1890ff;
                outline: none;
                font-size: 12px;
                padding: 2px;
            }
            QComboBox QAbstractItemView::item {
                height: 25px;
                padding: 4px 8px;
                border: none;
                color: #333333;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #71c4ef;
                color: white;
                font-weight: 500;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e6f7ff;
                color: #71c4ef;
            }
        """)
        grid_layout.addWidget(self.logistics_combo, 1, 3)
        
        # 第三行
        grid_layout.addWidget(QLabel("单据状态:"), 2, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["保存", "审核", "关闭"])
        self.status_combo.setEnabled(False)
        grid_layout.addWidget(self.status_combo, 2, 1)
        
        grid_layout.addWidget(QLabel("备注:"), 2, 2)
        self.remark_input = QLineEdit()
        grid_layout.addWidget(self.remark_input, 2, 3)
        
        header_layout.addLayout(grid_layout)
        content_layout.addWidget(header_group)
        
        # 明细信息区域
        detail_group = QGroupBox("明细信息")
        detail_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #333333;
                border: 1px solid #cccccc;
                margin-top: 10px;
                padding-top: 5px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background-color: #f0f0f0;
            }
        """)
        
        detail_layout = QVBoxLayout(detail_group)
        detail_layout.setContentsMargins(15, 15, 15, 15)
        
        # 明细操作按钮区域
        detail_header = QHBoxLayout()
        detail_header.addStretch()
        
        if self.mode != 'view':
            self.add_detail_btn = QPushButton("添加明细")
            self.add_detail_btn.setStyleSheet("""
                QPushButton {
                    background-color: #52c41a;
                    color: white;
                    border: none;
                    padding: 6px 15px;
                    border-radius: 3px;
                    font-weight: normal;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #73d13d;
                }
                QPushButton:pressed {
                    background-color: #389e0d;
                }
            """)
            self.add_detail_btn.clicked.connect(self.add_detail)
            detail_header.addWidget(self.add_detail_btn)
            
            # 导入明细按钮
            self.import_detail_btn = QPushButton("导入明细")
            self.import_detail_btn.setStyleSheet("""
                QPushButton {
                    background-color: #71c4ef;
                    color: white;
                    border: none;
                    padding: 6px 15px;
                    border-radius: 3px;
                    font-weight: normal;
                    min-width: 80px;
                    margin-left: 5px;
                }
                QPushButton:hover {
                    background-color: #8fd0f2;
                }
                QPushButton:pressed {
                    background-color: #5bb3e8;
                }
            """)
            self.import_detail_btn.clicked.connect(self.import_detail)
            detail_header.addWidget(self.import_detail_btn)
        
        detail_layout.addLayout(detail_header)
        
        # 明细表格
        self.detail_table = QTableWidget()
        self.detail_table.setColumnCount(7)
        self.detail_table.setHorizontalHeaderLabels([
            "操作", "运单号码", "东方货号", "货品名称", "数量", "物料编码", "物料名称"
        ])
        
        # 设置表格属性 - 禁用编辑
        self.detail_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.detail_table.setAlternatingRowColors(True)
        self.detail_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.detail_table.verticalHeader().setVisible(False)
        
        # 设置表头高度和列宽
        header = self.detail_table.horizontalHeader()
        header.setDefaultSectionSize(100)
        header.setMinimumSectionSize(60)
        
        # 设置列宽
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Stretch)
        
        self.detail_table.setColumnWidth(0, 60)
        
        # 设置默认行高
        self.detail_table.verticalHeader().setDefaultSectionSize(30)
        
        detail_layout.addWidget(self.detail_table)
        content_layout.addWidget(detail_group)
        
        layout.addWidget(content_widget)
        
        # 设置视图模式
        if self.mode == 'view':
            self.set_readonly(True)
    
    def set_readonly(self, readonly):
        """设置只读模式"""
        self.yewu_no_input.setReadOnly(readonly)
        self.return_date.setEnabled(not readonly)
        self.customer_input.setReadOnly(readonly)
        self.logistics_combo.setEnabled(not readonly)
        self.status_combo.setEnabled(not readonly)
        self.remark_input.setReadOnly(readonly)
        
        # 安全地处理按钮显示/隐藏
        if hasattr(self, 'add_detail_btn'):
            self.add_detail_btn.setVisible(not readonly)
        if hasattr(self, 'import_detail_btn'):
            self.import_detail_btn.setVisible(not readonly)
        
        # 明细表格始终不可编辑
        self.detail_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
    
    def load_data(self):
        """加载数据"""
        try:
            # 获取主表数据
            main_query = f"""
            SELECT yewu_no, return_date, customer, logistics, remark, document_status,
                   is_locked, warehouse_receipt_no
            FROM return_application
            WHERE id = %s
            """
            main_result = self.dongfang_db.db.execute_query(main_query, (self.return_id,))
            
            if main_result:
                data = main_result[0]
                if isinstance(data, dict):
                    self.yewu_no_input.setText(data.get('yewu_no', ''))
                    if data.get('return_date'):
                        self.return_date.setDate(QDate.fromString(str(data['return_date']), "yyyy-MM-dd"))
                    self.customer_input.setText(data.get('customer', '东方购物'))
                    # 设置物流公司下拉框
                    logistics = data.get('logistics', '邮政')
                    if logistics in ["邮政", "顺丰"]:
                        self.logistics_combo.setCurrentText(logistics)
                    else:
                        self.logistics_combo.setCurrentText("邮政")
                    self.remark_input.setText(data.get('remark', ''))
                    # 设置单据状态
                    self.status_combo.setCurrentIndex(data.get('document_status', 0))
                    
                    # 保存状态信息用于按钮控制
                    self.document_status = data.get('document_status', 0)
                    self.is_locked = data.get('is_locked', 0)
                    self.warehouse_receipt_no = data.get('warehouse_receipt_no', '')
                    
                else:
                    # 处理元组格式数据（兼容性）
                    self.yewu_no_input.setText(data[0] or '')
                    if data[1]:
                        self.return_date.setDate(QDate.fromString(str(data[1]), "yyyyMMdd"))
                    self.customer_input.setText(data[2] or '东方购物')
                    # ... 其他字段处理
                    
                    # 保存状态信息
                    self.document_status = data[5] if len(data) > 5 else 0
                    self.is_locked = data[6] if len(data) > 6 else 0
                    self.warehouse_receipt_no = data[7] if len(data) > 7 else ''
                
                # 根据单据状态设置界面权限
                document_status = data.get('document_status', 0) if isinstance(data, dict) else (data[5] if len(data) > 5 else 0)
                self.set_ui_state(document_status)
            
            # 获取明细数据
            details = self.dongfang_db.get_return_application_details(self.return_id)
            self.detail_data = []
            for detail in details:
                if isinstance(detail, dict):
                    self.detail_data.append({
                        'waybill_no': detail.get('waybill_no', ''),
                        'dongfang_code': detail.get('dongfang_code', ''),
                        'quantity': detail.get('quantity', 0),
                        'goods_name': detail.get('goods_name', ''),
                        'material_code': detail.get('material_code', ''),
                        'material_name': detail.get('material_name', '')
                    })
                else:
                    self.detail_data.append({
                        'waybill_no': detail[1],
                        'dongfang_code': detail[2],
                        'quantity': detail[3],
                        'goods_name': detail[8] if len(detail) > 8 else '',
                        'material_code': detail[9] if len(detail) > 9 else '',
                        'material_name': detail[10] if len(detail) > 10 else ''
                    })
            
            self.update_detail_table()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载数据失败：{str(e)}")
            print(f"加载数据失败: {str(e)}")
            traceback.print_exc()
    
    def add_detail(self):
        """添加明细"""
        # 检查单据状态
        if hasattr(self, 'document_status') and self.document_status in [1, 2]:
            QMessageBox.warning(self, "警告", "已审核或已关闭的单据不允许添加明细！")
            return
            
        from .dongfang_detail_dialog import DongfangDetailDialog
        dialog = DongfangDetailDialog(self)
        # 传递当前单据状态
        if hasattr(self, 'document_status'):
            dialog.set_document_status(self.document_status)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            detail_data = dialog.get_data()
            self.detail_data.append(detail_data)
            self.update_detail_table()
    
    def update_detail_table(self):
        """更新明细表格"""
        self.detail_table.setRowCount(len(self.detail_data))
        
        # 根据单据状态设置表格编辑权限
        is_readonly = hasattr(self, 'document_status') and self.document_status in [1, 2]
        
        for row, detail in enumerate(self.detail_data):
            # 操作按钮 - 红色X标记
            if self.mode != 'view' and not is_readonly:
                delete_btn = QPushButton("×")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: transparent;
                        color: #ff4d4f;
                        border: none;
                        padding: 0px;
                        font-size: 14px;
                        font-weight: bold;
                        min-width: 20px;
                        max-width: 20px;
                        min-height: 20px;
                        max-height: 20px;
                    }
                    QPushButton:hover {
                        background-color: #fff1f0;
                        color: #ff7875;
                        border-radius: 10px;
                    }
                    QPushButton:pressed {
                        background-color: #ffccc7;
                    }
                """)
                delete_btn.clicked.connect(lambda checked, r=row: self.delete_detail(r))
                
                # 创建一个容器widget来居中按钮
                container = QWidget()
                container_layout = QHBoxLayout(container)
                container_layout.setContentsMargins(0, 0, 0, 0)
                container_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
                container_layout.addWidget(delete_btn)
                
                self.detail_table.setCellWidget(row, 0, container)
                
            elif is_readonly or self.mode == 'view':
                # 只读状态显示禁用的删除按钮
                delete_btn = QPushButton("×")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: transparent;
                        color: #d9d9d9;
                        border: none;
                        padding: 0px;
                        font-size: 14px;
                        font-weight: bold;
                        min-width: 20px;
                        max-width: 20px;
                        min-height: 20px;
                        max-height: 20px;
                    }
                """)
                delete_btn.setEnabled(False)
                
                # 创建一个容器widget来居中按钮
                container = QWidget()
                container_layout = QHBoxLayout(container)
                container_layout.setContentsMargins(0, 0, 0, 0)
                container_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
                container_layout.addWidget(delete_btn)
                
                self.detail_table.setCellWidget(row, 0, container)
            
            # 运单号码
            waybill_item = QTableWidgetItem(detail.get('waybill_no', ''))
            waybill_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.detail_table.setItem(row, 1, waybill_item)
            
            # 东方货号
            code_item = QTableWidgetItem(detail.get('dongfang_code', ''))
            code_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.detail_table.setItem(row, 2, code_item)
            
            # 货品名称
            name_item = QTableWidgetItem(detail.get('goods_name', ''))
            name_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.detail_table.setItem(row, 3, name_item)
            
            # 数量
            qty_item = QTableWidgetItem(str(detail.get('quantity', 0)))
            qty_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.detail_table.setItem(row, 4, qty_item)
            
            # 物料编码
            material_code_item = QTableWidgetItem(detail.get('material_code', ''))
            material_code_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.detail_table.setItem(row, 5, material_code_item)
            
            # 物料名称
            material_name_item = QTableWidgetItem(detail.get('material_name', ''))
            material_name_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.detail_table.setItem(row, 6, material_name_item)
        
        # 设置行高以确保按钮有足够空间
        for row in range(self.detail_table.rowCount()):
            self.detail_table.setRowHeight(row, 30)

    def delete_detail(self, row):
        """删除明细"""
        # 检查单据状态
        if hasattr(self, 'document_status') and self.document_status in [1, 2]:
            QMessageBox.warning(self, "警告", "已审核或已关闭的单据不允许删除明细！")
            return
            
        if 0 <= row < len(self.detail_data):
            reply = QMessageBox.question(
                self,
                "确认删除",
                "确定要删除这条明细记录吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                try:
                    del self.detail_data[row]
                    self.update_detail_table()
                    QMessageBox.information(self, "成功", "明细删除成功！")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"删除明细失败：{str(e)}")

    def save_data(self):
        """保存数据"""
        try:
            # 验证数据
            if not self.yewu_no_input.text().strip():
                QMessageBox.warning(self, "警告", "请输入业务编号！")
                return
            
            if not self.customer_input.text().strip():
                QMessageBox.warning(self, "警告", "请输入客户名称！")
                return
            
            if not self.detail_data:
                QMessageBox.warning(self, "警告", "请添加明细信息！")
                return
            
            # 准备数据
            main_data = {
                'yewu_no': self.yewu_no_input.text().strip(),
                'return_date': self.return_date.date().toPyDate(),
                'customer': self.customer_input.text().strip(),
                'logistics': self.logistics_combo.currentText(),  # 改为从下拉框获取
                'remark': self.remark_input.text().strip(),
                'document_status': self.status_combo.currentIndex()
            }
            
            # 保存数据
            if self.mode == 'add':
                self.dongfang_db.save_return_application(main_data, self.detail_data)
                QMessageBox.information(self, "成功", "保存成功！")
            else:
                self.dongfang_db.update_return_application(self.return_id, main_data, self.detail_data)
                QMessageBox.information(self, "成功", "更新成功！")

            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败：{str(e)}")

    def regenerate_number(self):
        """重新生成业务编号"""
        try:
            auto_number = self.dongfang_db.generate_auto_number()
            self.yewu_no_input.setText(auto_number)
            QMessageBox.information(self, "成功", f"已重新生成编号: {auto_number}")
        except Exception as e:
            QMessageBox.warning(self, "警告", f"重新生成编号失败: {str(e)}")

    def set_ui_state(self, document_status):
        """根据单据状态设置界面控件状态"""
        self.document_status = document_status
        is_audited = document_status == 1
        is_closed = document_status == 2
        is_readonly = is_audited or is_closed
        
        # 设置表单控件状态和样式
        self.yewu_no_input.setReadOnly(True)
        self.return_date.setEnabled(False)
        self.customer_input.setReadOnly(True)
        self.logistics_combo.setEnabled(not is_readonly)
        self.status_combo.setCurrentIndex(document_status)
        self.remark_input.setReadOnly(is_readonly)
        
        # 设置明细表格和按钮状态
        if hasattr(self, 'add_detail_btn'):
            self.add_detail_btn.setVisible(not is_readonly)
        
        if hasattr(self, 'import_detail_btn'):
            self.import_detail_btn.setVisible(not is_readonly)
        
        # 更新明细表格状态
        self.update_detail_table()
        
        # 设置工具栏按钮状态
        if hasattr(self, 'save_action'):
            self.save_action.setEnabled(not is_readonly)
        
        if hasattr(self, 'regenerate_action'):
            self.regenerate_action.setEnabled(not is_readonly)
        
        if hasattr(self, 'audit_action'):
            self.audit_action.setEnabled(document_status == 0)  # 只有保存状态可以审核
        
        if hasattr(self, 'unaudit_action'):
            self.unaudit_action.setEnabled(document_status == 1)  # 只有审核状态可以反审核
        
        # 生成入库收货单按钮 - 只在审核状态显示
        if hasattr(self, 'generate_receipt_action'):
            self.generate_receipt_action.setVisible(document_status == 1)
            self.generate_receipt_action.setEnabled(document_status == 1)
        
        # 更新工具栏样式
        self.update_toolbar_styles()

    def audit_return(self):
        """审核退货申请"""
        try:
            reply = QMessageBox.question(
                self,
                "确认审核",
                "确定要审核该退货申请吗？\n审核后数据将不能修改！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 先保存当前数据（如果有修改）
                if self.mode == 'edit':
                    main_data = {
                        'yewu_no': self.yewu_no_input.text().strip(),
                        'return_date': self.return_date.date().toPyDate(),
                        'customer': self.customer_input.text().strip(),
                        'logistics': self.logistics_combo.currentText(),  # 改为从下拉框获取
                        'remark': self.remark_input.text().strip(),
                        'document_status': 0  # 保持保存状态，审核操作会单独更新
                    }
                    
                    # 更新基本信息
                    self.dongfang_db.update_return_application(self.return_id, main_data, self.detail_data)
                
                # 执行审核操作
                self.dongfang_db.audit_return_application(self.return_id)
                
                QMessageBox.information(self, "成功", "退货申请审核成功！")
                self.accept()  # 关闭窗口并返回成功状态
                return True
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"审核失败：{str(e)}")
            return False

    def unaudit_return(self):
        """反审核退货申请"""
        try:
            # 检查是否已生成入库收货单
            if hasattr(self, 'warehouse_receipt_no') and self.warehouse_receipt_no:
                QMessageBox.warning(
                    self, "无法反审核", 
                    f"该退货申请已生成入库收货单：{self.warehouse_receipt_no}\n"
                    "请先处理相关入库收货单后再进行反审核操作！"
                )
                return False
            
            reply = QMessageBox.question(
                self,
                "确认反审核",
                "确定要反审核该退货申请吗？\n反审核后将恢复到保存状态！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 执行反审核操作
                self.dongfang_db.unaudit_return_application(self.return_id)
                
                QMessageBox.information(self, "成功", "退货申请反审核成功！")
                self.accept()  # 关闭窗口并返回成功状态
                return True
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"反审核失败：{str(e)}")
            return False

    def import_detail(self):
        """导入明细"""
        # 检查单据状态
        if hasattr(self, 'document_status') and self.document_status in [1, 2]:
            QMessageBox.warning(self, "警告", "已审核或已关闭的单据不允许导入明细！")
            return
            
        try:
            # 选择Excel文件
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择Excel文件",
                "",
                "Excel files (*.xlsx *.xls)"
            )
            
            if not file_path:
                return
                
            # 导入Excel数据
            import pandas as pd
            
            try:
                df = pd.read_excel(file_path)
            except Exception as e:
                QMessageBox.critical(self, "错误", f"读取Excel文件失败：{str(e)}")
                return
            
            # 检查必要的列
            required_columns = ['运单号码', '东方货号', '数量']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                QMessageBox.warning(
                    self, 
                    "警告", 
                    f"Excel文件缺少必要的列：{', '.join(missing_columns)}\n"
                    f"请确保Excel文件包含以下列：{', '.join(required_columns)}"
                )
                return
            
            # 处理数据
            imported_count = 0
            error_rows = []
            
            for index, row in df.iterrows():
                try:
                    # 更安全的数据处理
                    waybill_no = str(row['运单号码']).strip() if pd.notna(row['运单号码']) else ""
                    dongfang_code = str(row['东方货号']).strip() if pd.notna(row['东方货号']) else ""
                    
                    # 处理数量字段，支持浮点数转整数
                    try:
                        quantity = float(row['数量']) if pd.notna(row['数量']) else 0
                        quantity = int(quantity)  # 转换为整数
                    except (ValueError, TypeError):
                        error_rows.append(f"第{index+2}行：数量格式错误")
                        continue
                    
                    # 验证数据
                    if not waybill_no:
                        error_rows.append(f"第{index+2}行：运单号码为空")
                        continue
                    if not dongfang_code:
                        error_rows.append(f"第{index+2}行：东方货号为空")
                        continue
                    if quantity <= 0:
                        error_rows.append(f"第{index+2}行：数量必须大于0")
                        continue
                    
                    print(f"处理第{index+2}行: 运单号={waybill_no}, 东方货号={dongfang_code}, 数量={quantity}")
                    
                    # 查询货品信息
                    goods_info = self.dongfang_db.get_dongfang_goods(dongfang_code)
                    goods_name = ""
                    material_code = ""
                    material_name = ""
                    
                    if goods_info:
                        print(f"找到货品信息: {goods_info}")
                        for goods in goods_info:
                            # 处理字典格式数据
                            if isinstance(goods, dict):
                                if goods.get('cj_code') == dongfang_code:
                                    goods_name = goods.get('cj_name', '')
                                    material_code = goods.get('material_code', '')
                                    material_name = goods.get('material_name', '')
                                    break
                            else:
                                # 处理元组格式数据（兼容性）
                                if goods[0] == dongfang_code:  # cj_code
                                    goods_name = goods[1] if len(goods) > 1 else ""  # cj_name
                                    material_code = goods[2] if len(goods) > 2 else ""  # material_code
                                    material_name = goods[3] if len(goods) > 3 else ""  # material_name
                                    break
                    else:
                        print(f"未找到东方货号 {dongfang_code} 的货品信息")
                    
                    # 添加到明细数据
                    detail_data = {
                        'waybill_no': waybill_no,
                        'dongfang_code': dongfang_code,
                        'quantity': quantity,
                        'goods_name': goods_name,
                        'material_code': material_code,
                        'material_name': material_name
                    }
                    
                    self.detail_data.append(detail_data)
                    imported_count += 1
                    print(f"成功添加明细: {detail_data}")
                    
                except Exception as e:
                    print(f"处理第{index+2}行时出错: {str(e)}")
                    error_rows.append(f"第{index+2}行：{str(e)}")
            
            # 更新明细表格
            if imported_count > 0:
                self.update_detail_table()
            
            # 显示导入结果
            result_msg = f"导入完成！\n成功导入：{imported_count} 条记录"
            if error_rows:
                result_msg += f"\n错误记录：{len(error_rows)} 条"
                if len(error_rows) <= 5:
                    result_msg += "\n" + "\n".join(error_rows)
                else:
                    result_msg += "\n" + "\n".join(error_rows[:5]) + f"\n... 还有 {len(error_rows)-5} 条错误"
            
            if imported_count > 0:
                QMessageBox.information(self, "导入成功", result_msg)
            else:
                QMessageBox.warning(self, "导入失败", result_msg)
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入明细失败：{str(e)}")

    def update_toolbar_styles(self):
        """更新工具栏按钮样式 - ERP风格"""
        toolbar = self.findChild(QToolBar)
        if not toolbar:
            return
            
        # 统一的按钮样式基础
        base_style = """
            QToolButton {
                color: white;
                border: none;
                padding: 6px 15px;
                margin: 2px;
                font-weight: normal;
                min-width: 60px;
                border-radius: 3px;
            }
        """
        
        # 保存按钮样式
        if hasattr(self, 'save_action'):
            widget = toolbar.widgetForAction(self.save_action)
            if widget:
                widget.setStyleSheet(base_style + """
                    QToolButton {
                        background-color: #71c4ef;
                    }
                    QToolButton:hover {
                        background-color: #8fd0f2;
                    }
                    QToolButton:disabled {
                        background-color: #cccccc;
                        color: #666666;
                    }
                """)
        
        # 重新生成编号按钮样式
        if hasattr(self, 'regenerate_action'):
            widget = toolbar.widgetForAction(self.regenerate_action)
            if widget:
                widget.setStyleSheet(base_style + """
                    QToolButton {
                        background-color: #52c41a;
                    }
                    QToolButton:hover {
                        background-color: #73d13d;
                    }
                    QToolButton:disabled {
                        background-color: #cccccc;
                        color: #666666;
                    }
                """)
        
        # 审核按钮样式
        if hasattr(self, 'audit_action'):
            widget = toolbar.widgetForAction(self.audit_action)
            if widget:
                widget.setStyleSheet(base_style + """
                    QToolButton {
                        background-color: #52c41a;
                    }
                    QToolButton:hover {
                        background-color: #73d13d;
                    }
                    QToolButton:disabled {
                        background-color: #cccccc;
                        color: #666666;
                    }
                """)
        
        # 反审核按钮样式
        if hasattr(self, 'unaudit_action'):
            widget = toolbar.widgetForAction(self.unaudit_action)
            if widget:
                widget.setStyleSheet(base_style + """
                    QToolButton {
                        background-color: #faad14;
                    }
                    QToolButton:hover {
                        background-color: #ffd666;
                    }
                    QToolButton:disabled {
                        background-color: #cccccc;
                        color: #666666;
                    }
                """)
        
        # 生成入库收货单按钮样式
        if hasattr(self, 'generate_receipt_action'):
            widget = toolbar.widgetForAction(self.generate_receipt_action)
            if widget:
                widget.setStyleSheet(base_style + """
                    QToolButton {
                        background-color: #52c41a;
                    }
                    QToolButton:hover {
                        background-color: #73d13d;
                    }
                    QToolButton:disabled {
                        background-color: #cccccc;
                        color: #666666;
                    }
                """)
        
        # 关闭按钮样式
        if hasattr(self, 'close_action'):
            widget = toolbar.widgetForAction(self.close_action)
            if widget:
                widget.setStyleSheet(base_style + """
                    QToolButton {
                        background-color: #ff4d4f;
                    }
                    QToolButton:hover {
                        background-color: #ff7875;
                    }
                """)

    def generate_warehouse_receipt(self):
        """生成入库收货单"""
        try:
            # 确认操作
            reply = QMessageBox.question(
                self, "确认生成", 
                "确定要生成入库收货单吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply != QMessageBox.StandardButton.Yes:
                return
            
            # 使用更轻量的进度提示
            self.setEnabled(False)  # 禁用整个对话框
            QApplication.setOverrideCursor(Qt.CursorShape.WaitCursor)  # 设置等待光标
            
            try:
                # 在后台线程中执行（可选，如果需要更好的用户体验）
                receipt_no = self.dongfang_db.generate_warehouse_receipt(self.return_id)
                
                # 显示成功消息
                QMessageBox.information(
                    self, "生成成功", 
                    f"入库收货单生成成功！\n收货单号：{receipt_no}"
                )
                
                # 更新状态
                self.is_locked = 1
                self.warehouse_receipt_no = receipt_no
                self.update_button_states()
                
                # 刷新父窗口的入库收货单列表
                self.refresh_parent_receipt_list()
                
            except Exception as e:
                error_msg = str(e)
                if "只有审核状态" in error_msg:
                    QMessageBox.warning(self, "状态错误", error_msg)
                elif "已生成入库收货单" in error_msg:
                    QMessageBox.warning(self, "重复操作", error_msg)
                else:
                    QMessageBox.critical(self, "生成失败", f"生成入库收货单失败：\n{error_msg}")
            
            finally:
                self.setEnabled(True)
                QApplication.restoreOverrideCursor()
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"操作失败：{str(e)}")

    def refresh_parent_receipt_list(self):
        """刷新父窗口的入库收货单列表"""
        try:
            # 查找主窗口
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'tab_widget'):
                main_window = main_window.parent()
            
            if main_window and hasattr(main_window, 'tab_widget'):
                # 查找入库收货单标签页
                for i in range(main_window.tab_widget.count()):
                    tab_text = main_window.tab_widget.tabText(i)
                    if "入库收货单" in tab_text or "收货单" in tab_text:
                        tab_widget = main_window.tab_widget.widget(i)
                        if hasattr(tab_widget, 'load_data'):
                            tab_widget.load_data()
                            print(f"已刷新入库收货单列表")
                            break
            
            # 如果找不到主窗口，尝试直接刷新父窗口
            if hasattr(self.parent(), 'load_data'):
                self.parent().load_data()
                print("已刷新父窗口数据")
                
        except Exception as e:
            print(f"刷新入库收货单列表失败: {str(e)}")

    def update_button_states(self):
        """更新按钮状态"""
        # 获取当前单据状态
        document_status = getattr(self, 'document_status', 0)
        is_locked = getattr(self, 'is_locked', 0)
        
        # 判断是否为只读模式
        is_readonly = (self.mode == 'view' or document_status in [1, 2])
        
        # 更新明细表格状态
        self.update_detail_table()
        
        # 设置工具栏按钮状态
        if hasattr(self, 'save_action'):
            self.save_action.setEnabled(not is_readonly)
        
        if hasattr(self, 'regenerate_action'):
            self.regenerate_action.setEnabled(not is_readonly)
        
        if hasattr(self, 'audit_action'):
            self.audit_action.setEnabled(document_status == 0)  # 只有保存状态可以审核
        
        if hasattr(self, 'unaudit_action'):
            self.unaudit_action.setEnabled(document_status == 1)  # 只有审核状态可以反审核
        
        # 生成入库收货单按钮 - 只在审核状态且未锁定时启用
        if hasattr(self, 'generate_receipt_action'):
            can_generate = (document_status == 1 and is_locked == 0)
            self.generate_receipt_action.setVisible(document_status == 1)
            self.generate_receipt_action.setEnabled(can_generate)
            
            # 如果已生成，更新按钮文本
            if is_locked == 1 and hasattr(self, 'warehouse_receipt_no') and self.warehouse_receipt_no:
                self.generate_receipt_action.setText(f"已生成收货单: {self.warehouse_receipt_no}")
            else:
                self.generate_receipt_action.setText("生成入库收货单")
        
        # 更新工具栏样式
        self.update_toolbar_styles()



















