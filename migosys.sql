/*
 Navicat Premium Dump SQL

 Source Server         : mysql.even888.cn
 Source Server Type    : MySQL
 Source Server Version : 50740 (5.7.40-log)
 Source Host           : mysql.even888.cn:3306
 Source Schema         : migosys

 Target Server Type    : MySQL
 Target Server Version : 50740 (5.7.40-log)
 File Encoding         : 65001

 Date: 21/07/2025 17:07:05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for auto_number_history
-- ----------------------------
DROP TABLE IF EXISTS `auto_number_history`;
CREATE TABLE `auto_number_history`  (
  `history_id` int(11) NOT NULL AUTO_INCREMENT,
  `rule_id` int(11) NOT NULL COMMENT '关联规则ID',
  `generated_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '生成的完整编号',
  `business_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务日期',
  `sequence_number` int(11) NOT NULL COMMENT '序号',
  `business_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务类型',
  `business_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务ID',
  `created_by` int(11) NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`history_id`) USING BTREE,
  INDEX `idx_rule_date`(`rule_id`, `business_date`) USING BTREE,
  INDEX `idx_generated_number`(`generated_number`) USING BTREE,
  INDEX `idx_business`(`business_type`, `business_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 114 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自动编号历史表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for auto_number_rule
-- ----------------------------
DROP TABLE IF EXISTS `auto_number_rule`;
CREATE TABLE `auto_number_rule`  (
  `rule_id` int(11) NOT NULL AUTO_INCREMENT,
  `rule_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则编码，如CJ、TH等',
  `rule_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则名称',
  `prefix` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编号前缀，如CJ-',
  `date_format` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'YYYYMMDD' COMMENT '日期格式',
  `sequence_length` int(11) NULL DEFAULT 4 COMMENT '序号长度',
  `reset_type` tinyint(4) NULL DEFAULT 1 COMMENT '重置类型：1-每日重置，2-每月重置，3-每年重置，4-永不重置',
  `current_business_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '当前业务日期，格式YYYY-MM-DD',
  `current_sequence` int(11) NULL DEFAULT 0 COMMENT '当前序号',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '0-禁用,1-启用',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`rule_id`) USING BTREE,
  UNIQUE INDEX `rule_code`(`rule_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自动编号规则表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cj_goods
-- ----------------------------
DROP TABLE IF EXISTS `cj_goods`;
CREATE TABLE `cj_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cj_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '东方货号',
  `cj_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '东方名称',
  `material_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物料编号',
  `box_spec` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '箱规',
  `split_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拆分编码',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_cj_code`(`cj_code`) USING BTREE,
  INDEX `idx_material_id`(`material_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 228 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '东方货品信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for delivery_order
-- ----------------------------
DROP TABLE IF EXISTS `delivery_order`;
CREATE TABLE `delivery_order`  (
  `autoid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'autoid',
  `business_date` date NULL DEFAULT NULL COMMENT '业务日期',
  `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单据编号',
  `source_order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '源单据编号',
  `customer` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '送货客户',
  `department` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门',
  `logistics_company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流公司',
  `summary` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '摘要',
  `confirm_receipt_date` date NULL DEFAULT NULL COMMENT '确认收货日期',
  `material_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料编码',
  `material_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料名称',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计量单位',
  `batch_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次',
  `warehouse` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '仓库',
  `reject_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户拒收原因',
  `delivery_quantity` decimal(18, 2) NULL DEFAULT NULL COMMENT '出库数量(求和)',
  `confirmed_quantity` decimal(18, 2) NULL DEFAULT NULL COMMENT '确认收货数量(求和)',
  `return_quantity` decimal(18, 2) NULL DEFAULT NULL COMMENT '退货数量',
  `remaining_return_quantity` decimal(18, 2) NULL DEFAULT NULL COMMENT '剩余退货数量',
  `order_status` tinyint(4) NULL DEFAULT 0 COMMENT '单据状态(0:开启,1:关闭)',
  `unit_price_with_tax` decimal(10, 2) NULL DEFAULT NULL COMMENT '含税单价',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`autoid`) USING BTREE,
  INDEX `idx_business_date`(`business_date`) USING BTREE,
  INDEX `idx_order_no`(`order_no`) USING BTREE,
  INDEX `idx_source_order_no`(`source_order_no`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '出库单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for handover_detail
-- ----------------------------
DROP TABLE IF EXISTS `handover_detail`;
CREATE TABLE `handover_detail`  (
  `detail_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '交接单明细编号',
  `handover_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交接单编号',
  `delivery_autoid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出库单autoid',
  `return_warehouse_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退货入库单号',
  `material_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料编码',
  `material_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料名称',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计量单位',
  `batch_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次',
  `warehouse` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '仓库',
  `reject_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户拒收原因',
  `return_quantity` decimal(18, 2) NULL DEFAULT NULL COMMENT '退货数量',
  `received_quantity` decimal(18, 2) NULL DEFAULT NULL COMMENT '收货数量',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `stock_status` enum('raw','deformed','to_scrap','scrapped') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入库状态(raw:原料,deformed:变形,to_scrap:待报废,scrapped:报废)',
  PRIMARY KEY (`detail_no`) USING BTREE,
  INDEX `handover_no`(`handover_no`) USING BTREE,
  INDEX `delivery_autoid`(`delivery_autoid`) USING BTREE,
  CONSTRAINT `handover_detail_ibfk_1` FOREIGN KEY (`handover_no`) REFERENCES `handover_order` (`handover_no`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `handover_detail_ibfk_2` FOREIGN KEY (`delivery_autoid`) REFERENCES `delivery_order` (`autoid`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '交接单明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for handover_order
-- ----------------------------
DROP TABLE IF EXISTS `handover_order`;
CREATE TABLE `handover_order`  (
  `handover_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '交接单编号',
  `handover_date` date NULL DEFAULT NULL COMMENT '交接单日期',
  `handover_status` tinyint(4) NULL DEFAULT 0 COMMENT '交接单状态(0:保存,1:审核)',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `delivery_order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出库单号',
  `source_order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原始单号',
  `customer` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '送货客户',
  `logistics_company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流公司',
  `department` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门',
  `summary` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '摘要',
  `return_type` tinyint(4) NULL DEFAULT NULL COMMENT '退货类型(0:拒收退货,1:仓退,2:代销退货)',
  `print_status` tinyint(4) NULL DEFAULT 0 COMMENT '打印状态(0:未打印,1:已打印)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `qrcode` blob NULL COMMENT 'APP回传信息',
  `qrcode_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '二维码JSON数据',
  `qrcode_create_time` datetime NULL DEFAULT NULL COMMENT '二维码生成时间',
  PRIMARY KEY (`handover_no`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '交接单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for login_log
-- ----------------------------
DROP TABLE IF EXISTS `login_log`;
CREATE TABLE `login_log`  (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `login_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `status` tinyint(4) NULL DEFAULT NULL COMMENT '0-失败,1-成功',
  `message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录消息',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户代理信息',
  PRIMARY KEY (`log_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  CONSTRAINT `login_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '登录日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for material_categories
-- ----------------------------
DROP TABLE IF EXISTS `material_categories`;
CREATE TABLE `material_categories`  (
  `category_id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `parent_id` int(11) NULL DEFAULT NULL,
  `level` int(11) NULL DEFAULT 1,
  `sort_order` int(11) NULL DEFAULT 0,
  PRIMARY KEY (`category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物料分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for materials
-- ----------------------------
DROP TABLE IF EXISTS `materials`;
CREATE TABLE `materials`  (
  `material_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物料编码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物料名称',
  `spec_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格型号',
  `base_unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '基本计量单位',
  `barcode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '条形码',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '物料状态 0停用 1启用',
  `length_unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '长度单位',
  `length` decimal(10, 2) NULL DEFAULT NULL COMMENT '长度',
  `width` decimal(10, 2) NULL DEFAULT NULL COMMENT '宽度',
  `height` decimal(10, 2) NULL DEFAULT NULL COMMENT '高度',
  `weight_unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重量单位',
  `gross_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '毛重',
  `volume_unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '体积单位',
  `volume` decimal(10, 4) NULL DEFAULT NULL COMMENT '体积',
  `category_id` int(11) NULL DEFAULT NULL COMMENT '分类ID',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `cost` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '物料成本',
  `original_box_unit` int(20) NULL DEFAULT NULL COMMENT '原箱计量单位',
  `conversion_rate` int(11) NULL DEFAULT NULL COMMENT '计量换算',
  `is_east_material` tinyint(1) NULL DEFAULT 0 COMMENT '是否东方货物：0-否，1-是',
  PRIMARY KEY (`material_id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  CONSTRAINT `materials_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `material_categories` (`category_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物料表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for operation_log
-- ----------------------------
DROP TABLE IF EXISTS `operation_log`;
CREATE TABLE `operation_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `operation_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `operation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `operator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `document_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `operation_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_operation_time`(`operation_time`) USING BTREE,
  INDEX `idx_operation_type`(`operation_type`) USING BTREE,
  INDEX `idx_document_no`(`document_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3281 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for permission
-- ----------------------------
DROP TABLE IF EXISTS `permission`;
CREATE TABLE `permission`  (
  `permission_id` int(11) NOT NULL AUTO_INCREMENT,
  `permission_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `permission_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限标识符',
  `permission_type` tinyint(4) NOT NULL COMMENT '1-菜单,2-按钮,3-数据',
  `parent_id` int(11) NULL DEFAULT 0 COMMENT '父权限ID，0表示顶级权限',
  `menu_icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单图标',
  `menu_path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单路径',
  `menu_component` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单组件',
  `menu_sort` int(11) NULL DEFAULT 0 COMMENT '菜单排序',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '0-禁用,1-启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`permission_id`) USING BTREE,
  UNIQUE INDEX `permission_name`(`permission_name`) USING BTREE,
  UNIQUE INDEX `permission_code`(`permission_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for return_application
-- ----------------------------
DROP TABLE IF EXISTS `return_application`;
CREATE TABLE `return_application`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `yewu_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务编号',
  `return_date` date NOT NULL COMMENT '退货日期',
  `customer` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '送货客户',
  `logistics` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流公司/方式',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后更新人',
  `document_status` int(11) NOT NULL DEFAULT 0 COMMENT '单据状态：0=保存，1=审核，2=关闭',
  `is_locked` tinyint(4) NOT NULL DEFAULT 0 COMMENT '锁定状态：0=未锁定，1=已锁定（已生成入库收货单）',
  `warehouse_receipt_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联的入库收货单号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_return_application_status`(`document_status`) USING BTREE,
  INDEX `idx_is_locked`(`is_locked`) USING BTREE,
  INDEX `idx_warehouse_receipt_no`(`warehouse_receipt_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '东方退货申请主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for return_application_detail
-- ----------------------------
DROP TABLE IF EXISTS `return_application_detail`;
CREATE TABLE `return_application_detail`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `return_id` int(11) NOT NULL COMMENT '退货申请ID（外键）',
  `waybill_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运单号码',
  `dongfang_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '东方货号',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后更新人',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `return_id`(`return_id`) USING BTREE,
  CONSTRAINT `return_application_detail_ibfk_1` FOREIGN KEY (`return_id`) REFERENCES `return_application` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '东方退货明细子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `role_id` int(11) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role_desc` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '0-禁用,1-启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`role_id`) USING BTREE,
  UNIQUE INDEX `role_name`(`role_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for role_permission
-- ----------------------------
DROP TABLE IF EXISTS `role_permission`;
CREATE TABLE `role_permission`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_role_permission`(`role_id`, `permission_id`) USING BTREE,
  INDEX `permission_id`(`permission_id`) USING BTREE,
  CONSTRAINT `role_permission_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `role` (`role_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `role_permission_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permission` (`permission_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 62 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色权限关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_tab
-- ----------------------------
DROP TABLE IF EXISTS `system_tab`;
CREATE TABLE `system_tab`  (
  `tab_id` int(11) NOT NULL AUTO_INCREMENT,
  `tab_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Tab页名称',
  `tab_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Tab页标识符',
  `tab_module` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属模块',
  `permission_id` int(11) NULL DEFAULT NULL COMMENT '关联权限ID',
  `tab_sort` int(11) NULL DEFAULT 0 COMMENT 'Tab页排序',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '0-禁用,1-启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`tab_id`) USING BTREE,
  UNIQUE INDEX `tab_code`(`tab_code`) USING BTREE,
  INDEX `permission_id`(`permission_id`) USING BTREE,
  CONSTRAINT `system_tab_ibfk_1` FOREIGN KEY (`permission_id`) REFERENCES `permission` (`permission_id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统Tab页表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '0-禁用,1-启用',
  `last_login_time` datetime NULL DEFAULT NULL,
  `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE INDEX `username`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_config
-- ----------------------------
DROP TABLE IF EXISTS `user_config`;
CREATE TABLE `user_config`  (
  `config_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`config_id`) USING BTREE,
  UNIQUE INDEX `uk_user_config`(`user_id`, `config_key`) USING BTREE,
  CONSTRAINT `user_config_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS `user_role`;
CREATE TABLE `user_role`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_role`(`user_id`, `role_id`) USING BTREE,
  INDEX `role_id`(`role_id`) USING BTREE,
  CONSTRAINT `user_role_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `user_role_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `role` (`role_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for warehouse_receipt
-- ----------------------------
DROP TABLE IF EXISTS `warehouse_receipt`;
CREATE TABLE `warehouse_receipt`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `receipt_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收货单号（自动生成，格式：CJRK-YYYYMMDD-序号）',
  `return_application_id` int(11) NOT NULL COMMENT '关联的退货申请ID',
  `return_yewu_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的退货申请业务编号',
  `receipt_date` date NOT NULL COMMENT '收货日期',
  `receiver` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人',
  `warehouse` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货仓库',
  `receipt_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '收货状态：0=保存，1=审核，2=关闭',
  `total_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '总收货数量',
  `total_raw_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '总原料数量',
  `total_deformed_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '总变形数量',
  `total_to_scrap_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '总待报废数量',
  `total_scrapped_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '总报废数量',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后更新人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `receipt_no`(`receipt_no`) USING BTREE,
  INDEX `fk_warehouse_receipt_return_app`(`return_application_id`) USING BTREE,
  INDEX `idx_receipt_no`(`receipt_no`) USING BTREE,
  INDEX `idx_return_yewu_no`(`return_yewu_no`) USING BTREE,
  INDEX `idx_receipt_date`(`receipt_date`) USING BTREE,
  INDEX `idx_receipt_status`(`receipt_status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  CONSTRAINT `fk_warehouse_receipt_return_app` FOREIGN KEY (`return_application_id`) REFERENCES `return_application` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '入库收货单主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for warehouse_receipt_detail
-- ----------------------------
DROP TABLE IF EXISTS `warehouse_receipt_detail`;
CREATE TABLE `warehouse_receipt_detail`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `receipt_id` int(11) NOT NULL COMMENT '收货单ID（外键）',
  `receipt_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收货单号（冗余字段，便于查询）',
  `return_detail_id` int(11) NOT NULL COMMENT '关联的退货申请明细ID',
  `waybill_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运单号码',
  `dongfang_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '东方货号',
  `material_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料编码（从CJ_GOODS表关联获取）',
  `material_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料名称（从materials表关联获取）',
  `return_quantity` int(11) NOT NULL COMMENT '退货数量（来自退货申请）',
  `received_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '实际收货数量',
  `box_spec` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '箱规（从CJ_GOODS表获取）',
  `split_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拆分编码（从CJ_GOODS表获取）',
  `split_quantity` int(11) NULL DEFAULT 0 COMMENT '拆分数量（基于箱规计算）',
  `raw_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '原料数量',
  `deformed_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '变形数量',
  `to_scrap_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '待报废数量',
  `scrapped_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '报废数量',
  `batch_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次号',
  `storage_location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '存储位置',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '明细备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后更新人',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_receipt_id`(`receipt_id`) USING BTREE,
  INDEX `idx_receipt_no`(`receipt_no`) USING BTREE,
  INDEX `idx_return_detail_id`(`return_detail_id`) USING BTREE,
  INDEX `idx_waybill_no`(`waybill_no`) USING BTREE,
  INDEX `idx_dongfang_code`(`dongfang_code`) USING BTREE,
  INDEX `idx_material_code`(`material_code`) USING BTREE,
  INDEX `idx_split_code`(`split_code`) USING BTREE,
  CONSTRAINT `fk_receipt_detail_receipt` FOREIGN KEY (`receipt_id`) REFERENCES `warehouse_receipt` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_receipt_detail_return_detail` FOREIGN KEY (`return_detail_id`) REFERENCES `return_application_detail` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '入库收货单明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Function structure for GenerateNumber
-- ----------------------------
DROP FUNCTION IF EXISTS `GenerateNumber`;
delimiter ;;
CREATE FUNCTION `GenerateNumber`(p_rule_code VARCHAR(50),
            p_business_date VARCHAR(10))
 RETURNS varchar(50) CHARSET utf8mb4
  READS SQL DATA 
  DETERMINISTIC
BEGIN
            DECLARE v_generated_number VARCHAR(50);
            
            CALL GetNextNumber(p_rule_code, p_business_date, v_generated_number);
            
            RETURN v_generated_number;
        END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for GetNextNumber
-- ----------------------------
DROP PROCEDURE IF EXISTS `GetNextNumber`;
delimiter ;;
CREATE PROCEDURE `GetNextNumber`(IN p_rule_code VARCHAR(50),
            IN p_business_date VARCHAR(10),
            OUT p_generated_number VARCHAR(50))
BEGIN
            DECLARE v_rule_id INT;
            DECLARE v_prefix VARCHAR(20);
            DECLARE v_date_format VARCHAR(20);
            DECLARE v_sequence_length INT;
            DECLARE v_current_sequence INT;
            DECLARE v_formatted_date VARCHAR(20);
            DECLARE v_next_sequence INT;
            DECLARE v_number_exists INT DEFAULT 0;
            
            START TRANSACTION;
            
            -- 获取规则信息
            SELECT rule_id, prefix, date_format, sequence_length, current_sequence
            INTO v_rule_id, v_prefix, v_date_format, v_sequence_length, v_current_sequence
            FROM auto_number_rule 
            WHERE rule_code = p_rule_code AND status = 1
            FOR UPDATE;
            
            IF v_rule_id IS NULL THEN
                SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '编号规则不存在或已禁用';
            END IF;
            
            -- 格式化日期
            SET v_formatted_date = DATE_FORMAT(STR_TO_DATE(p_business_date, '%Y-%m-%d'), '%Y%m%d');
            
            -- 检查是否需要重置序号
            IF v_current_sequence = 0 OR 
               (SELECT COUNT(*) FROM auto_number_history 
                WHERE rule_id = v_rule_id AND business_date = p_business_date) = 0 THEN
                SET v_next_sequence = 1;
            ELSE
                SET v_next_sequence = v_current_sequence + 1;
            END IF;
            
            -- 生成编号
            SET p_generated_number = CONCAT(v_prefix, v_formatted_date,
                                           LPAD(v_next_sequence, v_sequence_length, '0'));
            
            -- 检查编号是否已存在
            SELECT COUNT(*) INTO v_number_exists 
            FROM auto_number_history 
            WHERE generated_number = p_generated_number;
            
            IF v_number_exists > 0 THEN
                SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '生成的编号已存在，请重试';
            END IF;
            
            -- 更新规则表的当前序号
            UPDATE auto_number_rule 
            SET current_sequence = v_next_sequence,
                current_business_date = p_business_date,
                update_time = NOW()
            WHERE rule_id = v_rule_id;
            
            COMMIT;
        END
;;
delimiter ;

-- =====================================================
-- BOM主表 (bom_header) - 核心版
-- =====================================================
DROP TABLE IF EXISTS `bom_header`;
CREATE TABLE `bom_header` (
  `bom_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'BOM主键',
  `bom_code` varchar(50) NOT NULL COMMENT 'BOM编码',
  `parent_material_id` varchar(50) NOT NULL COMMENT '成品物料编码',
  `bom_name` varchar(200) NULL DEFAULT NULL COMMENT 'BOM名称',
  `bom_version` varchar(20) NOT NULL DEFAULT '1.0' COMMENT 'BOM版本',
  `production_unit` varchar(20) NOT NULL COMMENT '生产单位（提、个、箱等）',
  `standard_output_qty` decimal(18,4) NOT NULL DEFAULT 1.0000 COMMENT '标准产出数量（如6提）',
  `package_qty_per_box` decimal(18,4) NULL DEFAULT NULL COMMENT '装箱数量（如6提装1箱）',
  `effective_date` date NOT NULL COMMENT '生效日期',
  `expiry_date` date NULL DEFAULT NULL COMMENT '失效日期',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-停用，1-启用',
  `remark` text NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(50) NULL DEFAULT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) NULL DEFAULT NULL,
  
  PRIMARY KEY (`bom_id`) USING BTREE,
  UNIQUE INDEX `uk_bom_code`(`bom_code`) USING BTREE,
  INDEX `idx_parent_material`(`parent_material_id`) USING BTREE,
  CONSTRAINT `fk_bom_parent_material` FOREIGN KEY (`parent_material_id`) REFERENCES `materials` (`material_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'BOM主表' ROW_FORMAT = DYNAMIC;

-- =====================================================
-- BOM包材明细表 (bom_package_detail) - 包装材料用量
-- =====================================================
DROP TABLE IF EXISTS `bom_package_detail`;
CREATE TABLE `bom_package_detail` (
  `detail_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细主键',
  `bom_id` int(11) NOT NULL COMMENT 'BOM主键',
  `sequence_no` int(11) NOT NULL COMMENT '序号',
  `package_material_id` varchar(50) NOT NULL COMMENT '包材物料编码',
  `material_category_id` int(11) NULL DEFAULT NULL COMMENT '物料分类ID',
  `required_qty` decimal(18,4) NOT NULL COMMENT '单位需求数量（基于标准产出）',
  `required_unit` varchar(20) NOT NULL COMMENT '需求单位',
  `original_box_qty` decimal(18,4) NULL DEFAULT NULL COMMENT '原箱规格数量',
  `original_box_unit` varchar(20) NULL DEFAULT NULL COMMENT '原箱单位',
  `scrap_rate` decimal(5,2) NULL DEFAULT 0.00 COMMENT '损耗率（%）',
  `unit_cost` decimal(10,4) NULL DEFAULT NULL COMMENT '单位成本',
  `total_cost` decimal(18,4) NULL DEFAULT NULL COMMENT '总成本（自动计算）',
  `supplier_id` varchar(50) NULL DEFAULT NULL COMMENT '供应商ID',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(50) NULL DEFAULT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) NULL DEFAULT NULL,
  
  PRIMARY KEY (`detail_id`) USING BTREE,
  INDEX `idx_bom_id`(`bom_id`) USING BTREE,
  INDEX `idx_package_material`(`package_material_id`) USING BTREE,
  INDEX `idx_material_category`(`material_category_id`) USING BTREE,
  CONSTRAINT `fk_bom_package_header` FOREIGN KEY (`bom_id`) REFERENCES `bom_header` (`bom_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_bom_package_material` FOREIGN KEY (`package_material_id`) REFERENCES `materials` (`material_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_bom_package_category` FOREIGN KEY (`material_category_id`) REFERENCES `material_categories` (`category_id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'BOM包材明细表' ROW_FORMAT = DYNAMIC;

-- =====================================================
-- BOM原料明细表 (bom_material_detail) - 原材料用量
-- =====================================================
DROP TABLE IF EXISTS `bom_material_detail`;
CREATE TABLE `bom_material_detail` (
  `detail_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细主键',
  `bom_id` int(11) NOT NULL COMMENT 'BOM主键',
  `sequence_no` int(11) NOT NULL COMMENT '序号',
  `raw_material_id` varchar(50) NOT NULL COMMENT '原料物料编码',
  `material_category_id` int(11) NULL DEFAULT NULL COMMENT '物料分类ID',
  `required_qty` decimal(18,4) NOT NULL COMMENT '单位需求数量（基于标准产出）',
  `required_unit` varchar(20) NOT NULL COMMENT '需求单位',
  `original_box_qty` decimal(18,4) NULL DEFAULT NULL COMMENT '原箱规格数量',
  `original_box_unit` varchar(20) NULL DEFAULT NULL COMMENT '原箱单位',
-- =====================================================
-- 需求计划管理模块（简化版）
-- =====================================================

-- ----------------------------
-- Table structure for demand_plans
-- ----------------------------
DROP TABLE IF EXISTS `demand_plans`;
CREATE TABLE `demand_plans` (
  `plan_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '需求计划主键',
  `plan_no` varchar(50) NOT NULL COMMENT '需求计划编号（自动生成，格式：XQJH-YYYYMMDD-序号）',
  `plan_date` date NOT NULL COMMENT '计划日期',
  `plan_name` varchar(200) NULL DEFAULT NULL COMMENT '计划名称',
  `department` varchar(100) NOT NULL COMMENT '申请部门',
  `applicant` varchar(50) NOT NULL COMMENT '申请人',
  `demand_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '需求类型：1-生产需求，2-销售需求，3-库存补充，4-其他需求',
  `priority_level` tinyint(4) NOT NULL DEFAULT 1 COMMENT '紧急程度：1-普通，2-紧急，3-特急',
  `plan_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '计划状态：0-草稿，1-已确认，2-执行中，3-已完成，9-已取消',
  `total_amount` decimal(18,2) NULL DEFAULT 0.00 COMMENT '计划总金额（预估）',
  `remark` text NULL COMMENT '备注说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(50) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `update_user` varchar(50) NULL DEFAULT NULL COMMENT '最后更新人',
  `is_locked` tinyint(4) NOT NULL DEFAULT 0 COMMENT '锁定状态：0-未锁定，1-已锁定（已生成生产计划）',
  `production_plan_no` varchar(50) NULL DEFAULT NULL COMMENT '关联的生产计划编号',
  PRIMARY KEY (`plan_id`) USING BTREE,
  UNIQUE INDEX `uk_plan_no`(`plan_no`) USING BTREE,
  INDEX `idx_plan_date`(`plan_date`) USING BTREE,
  INDEX `idx_department`(`department`) USING BTREE,
  INDEX `idx_plan_status`(`plan_status`) USING BTREE,
  INDEX `idx_demand_type`(`demand_type`) USING BTREE,
  INDEX `idx_priority_level`(`priority_level`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '需求计划主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for demand_plan_details
-- ----------------------------
DROP TABLE IF EXISTS `demand_plan_details`;
CREATE TABLE `demand_plan_details` (
  `detail_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细主键',
  `plan_id` int(11) NOT NULL COMMENT '需求计划主键',
  `plan_no` varchar(50) NOT NULL COMMENT '需求计划编号（冗余字段，便于查询）',
  `sequence_no` int(11) NOT NULL COMMENT '序号',
  `material_id` varchar(50) NOT NULL COMMENT '物料编码',
  `material_name` varchar(200) NULL DEFAULT NULL COMMENT '物料名称（冗余字段）',
  `material_category_id` int(11) NULL DEFAULT NULL COMMENT '物料分类ID',
  `required_qty` decimal(18,4) NOT NULL COMMENT '需求数量',
  `required_unit` varchar(20) NOT NULL COMMENT '需求单位',
  `unit_price` decimal(10,4) NULL DEFAULT NULL COMMENT '预估单价',
  `total_amount` decimal(18,4) NULL DEFAULT NULL COMMENT '预估总金额',
  `required_date` date NOT NULL COMMENT '需求日期',
  `usage_purpose` varchar(200) NULL DEFAULT NULL COMMENT '用途说明',
  `supplier_id` varchar(50) NULL DEFAULT NULL COMMENT '建议供应商ID',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `execution_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '执行状态：0-未执行，1-部分执行，2-已完成',
  `executed_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '已执行数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(50) NULL DEFAULT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) NULL DEFAULT NULL,
  PRIMARY KEY (`detail_id`) USING BTREE,
  INDEX `idx_plan_id`(`plan_id`) USING BTREE,
  INDEX `idx_plan_no`(`plan_no`) USING BTREE,
  INDEX `idx_material_id`(`material_id`) USING BTREE,
  INDEX `idx_material_category`(`material_category_id`) USING BTREE,
  INDEX `idx_required_date`(`required_date`) USING BTREE,
  INDEX `idx_execution_status`(`execution_status`) USING BTREE,
  CONSTRAINT `fk_demand_detail_plan` FOREIGN KEY (`plan_id`) REFERENCES `demand_plans` (`plan_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_demand_detail_material` FOREIGN KEY (`material_id`) REFERENCES `materials` (`material_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_demand_detail_category` FOREIGN KEY (`material_category_id`) REFERENCES `material_categories` (`category_id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '需求计划明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for demand_executions
-- ----------------------------
DROP TABLE IF EXISTS `demand_executions`;
CREATE TABLE `demand_executions` (
  `execution_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '执行记录主键',
  `plan_id` int(11) NOT NULL COMMENT '需求计划主键',
  `detail_id` int(11) NOT NULL COMMENT '需求明细主键',
  `plan_no` varchar(50) NOT NULL COMMENT '需求计划编号',
  `material_id` varchar(50) NOT NULL COMMENT '物料编码',
  `execution_type` tinyint(4) NOT NULL COMMENT '执行类型：1-采购执行，2-生产执行，3-调拨执行',
  `execution_no` varchar(50) NULL DEFAULT NULL COMMENT '执行单号（采购单号/生产单号等）',
  `executed_qty` decimal(18,4) NOT NULL COMMENT '执行数量',
  `execution_date` date NOT NULL COMMENT '执行日期',
  `executor` varchar(50) NOT NULL COMMENT '执行人',
  `execution_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '执行状态：1-已执行，2-已完成，3-已取消',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '执行备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(50) NULL DEFAULT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) NULL DEFAULT NULL,
  PRIMARY KEY (`execution_id`) USING BTREE,
  INDEX `idx_plan_id`(`plan_id`) USING BTREE,
  INDEX `idx_detail_id`(`detail_id`) USING BTREE,
  INDEX `idx_plan_no`(`plan_no`) USING BTREE,
  INDEX `idx_material_id`(`material_id`) USING BTREE,
  INDEX `idx_execution_type`(`execution_type`) USING BTREE,
  INDEX `idx_execution_date`(`execution_date`) USING BTREE,
  CONSTRAINT `fk_execution_plan` FOREIGN KEY (`plan_id`) REFERENCES `demand_plans` (`plan_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_execution_detail` FOREIGN KEY (`detail_id`) REFERENCES `demand_plan_details` (`detail_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '需求执行跟踪表' ROW_FORMAT = DYNAMIC;

-- =====================================================
-- 生产加工模块
-- =====================================================

-- ----------------------------
-- Table structure for production_plans
-- ----------------------------
DROP TABLE IF EXISTS `production_plans`;
CREATE TABLE `production_plans` (
  `plan_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '生产计划主键',
  `plan_no` varchar(50) NOT NULL COMMENT '生产计划编号（自动生成，格式：SCJH-YYYYMMDD-序号）',
  `plan_date` date NOT NULL COMMENT '计划日期',
  `plan_name` varchar(200) NULL DEFAULT NULL COMMENT '计划名称',
  `demand_plan_id` int(11) NULL DEFAULT NULL COMMENT '关联需求计划ID',
  `demand_plan_no` varchar(50) NULL DEFAULT NULL COMMENT '关联需求计划编号',
  `product_material_id` varchar(50) NOT NULL COMMENT '成品物料编码',
  `product_material_name` varchar(200) NULL DEFAULT NULL COMMENT '成品物料名称（冗余字段）',
  `bom_id` int(11) NOT NULL COMMENT '关联BOM主键',
  `bom_code` varchar(50) NOT NULL COMMENT '关联BOM编码',
  `planned_qty` decimal(18,4) NOT NULL COMMENT '计划生产数量',
  `production_unit` varchar(20) NOT NULL COMMENT '生产单位',
  `planned_start_date` date NOT NULL COMMENT '计划开始日期',
  `planned_end_date` date NOT NULL COMMENT '计划完成日期',
  `actual_start_date` date NULL DEFAULT NULL COMMENT '实际开始日期',
  `actual_end_date` date NULL DEFAULT NULL COMMENT '实际完成日期',
  `plan_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '计划状态：0-草稿，1-已下达，2-生产中，3-已完成，4-已暂停，9-已取消',
  `priority_level` tinyint(4) NOT NULL DEFAULT 1 COMMENT '优先级：1-普通，2-紧急，3-特急',
  `production_line` varchar(100) NULL DEFAULT NULL COMMENT '生产线',
  `shift_team` varchar(50) NULL DEFAULT NULL COMMENT '班组',
  `supervisor` varchar(50) NULL DEFAULT NULL COMMENT '生产主管',
  `total_completed_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '累计完成数量',
  `completion_rate` decimal(5,2) NULL DEFAULT 0.00 COMMENT '完成率（%）',
  `remark` text NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(50) NULL DEFAULT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) NULL DEFAULT NULL,
  PRIMARY KEY (`plan_id`) USING BTREE,
  UNIQUE INDEX `uk_plan_no`(`plan_no`) USING BTREE,
  INDEX `idx_plan_date`(`plan_date`) USING BTREE,
  INDEX `idx_demand_plan_id`(`demand_plan_id`) USING BTREE,
  INDEX `idx_product_material`(`product_material_id`) USING BTREE,
  INDEX `idx_bom_id`(`bom_id`) USING BTREE,
  INDEX `idx_plan_status`(`plan_status`) USING BTREE,
  INDEX `idx_priority_level`(`priority_level`) USING BTREE,
  CONSTRAINT `fk_production_plan_demand` FOREIGN KEY (`demand_plan_id`) REFERENCES `demand_plans` (`plan_id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_production_plan_material` FOREIGN KEY (`product_material_id`) REFERENCES `materials` (`material_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_production_plan_bom` FOREIGN KEY (`bom_id`) REFERENCES `bom_header` (`bom_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '生产计划主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for production_orders
-- ----------------------------
DROP TABLE IF EXISTS `production_orders`;
CREATE TABLE `production_orders` (
  `order_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '生产加工单主键',
  `order_no` varchar(50) NOT NULL COMMENT '生产加工单号（自动生成，格式：SCJG-YYYYMMDD-序号）',
  `plan_id` int(11) NOT NULL COMMENT '关联生产计划主键',
  `plan_no` varchar(50) NOT NULL COMMENT '关联生产计划编号',
  `production_date` date NOT NULL COMMENT '生产日期',
  `product_material_id` varchar(50) NOT NULL COMMENT '成品物料编码',
  `product_material_name` varchar(200) NULL DEFAULT NULL COMMENT '成品物料名称',
  `bom_id` int(11) NOT NULL COMMENT '使用的BOM主键',
  `bom_code` varchar(50) NOT NULL COMMENT '使用的BOM编码',
  `planned_qty` decimal(18,4) NOT NULL COMMENT '计划生产数量',
  `actual_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '实际生产数量',
  `qualified_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '合格品数量',
  `defective_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '不合格品数量',
  `production_unit` varchar(20) NOT NULL COMMENT '生产单位',
  `production_line` varchar(100) NULL DEFAULT NULL COMMENT '生产线',
  `shift_team` varchar(50) NULL DEFAULT NULL COMMENT '班组',
  `operator` varchar(50) NULL DEFAULT NULL COMMENT '操作员',
  `supervisor` varchar(50) NULL DEFAULT NULL COMMENT '生产主管',
  `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `production_duration` int(11) NULL DEFAULT NULL COMMENT '生产时长（分钟）',
  `order_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '加工单状态：0-待生产，1-生产中，2-已完成，3-已暂停，9-已取消',
  `quality_status` tinyint(4) NULL DEFAULT 0 COMMENT '质检状态：0-待检验，1-检验中，2-检验合格，3-检验不合格',
  `quality_inspector` varchar(50) NULL DEFAULT NULL COMMENT '质检员',
  `quality_time` datetime NULL DEFAULT NULL COMMENT '质检时间',
  `remark` text NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(50) NULL DEFAULT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) NULL DEFAULT NULL,
  PRIMARY KEY (`order_id`) USING BTREE,
  UNIQUE INDEX `uk_order_no`(`order_no`) USING BTREE,
  INDEX `idx_plan_id`(`plan_id`) USING BTREE,
  INDEX `idx_production_date`(`production_date`) USING BTREE,
  INDEX `idx_product_material`(`product_material_id`) USING BTREE,
  INDEX `idx_bom_id`(`bom_id`) USING BTREE,
  INDEX `idx_order_status`(`order_status`) USING BTREE,
  INDEX `idx_quality_status`(`quality_status`) USING BTREE,
  CONSTRAINT `fk_production_order_plan` FOREIGN KEY (`plan_id`) REFERENCES `production_plans` (`plan_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_production_order_material` FOREIGN KEY (`product_material_id`) REFERENCES `materials` (`material_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_production_order_bom` FOREIGN KEY (`bom_id`) REFERENCES `bom_header` (`bom_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '生产加工单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for production_material_consumptions
-- ----------------------------
DROP TABLE IF EXISTS `production_material_consumptions`;
CREATE TABLE `production_material_consumptions` (
  `consumption_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用料记录主键',
  `order_id` int(11) NOT NULL COMMENT '生产加工单主键',
  `order_no` varchar(50) NOT NULL COMMENT '生产加工单号',
  `material_type` tinyint(4) NOT NULL COMMENT '物料类型：1-原料，2-包材',
  `material_id` varchar(50) NOT NULL COMMENT '物料编码',
  `material_name` varchar(200) NULL DEFAULT NULL COMMENT '物料名称',
  `material_category_id` int(11) NULL DEFAULT NULL COMMENT '物料分类ID',
  `bom_required_qty` decimal(18,4) NOT NULL COMMENT 'BOM标准用量',
  `planned_consumption_qty` decimal(18,4) NOT NULL COMMENT '计划消耗数量',
  `actual_consumption_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '实际消耗数量',
  `consumption_unit` varchar(20) NOT NULL COMMENT '消耗单位',
  `batch_no` varchar(50) NULL DEFAULT NULL COMMENT '批次号',
  `warehouse` varchar(50) NULL DEFAULT NULL COMMENT '出库仓库',
  `unit_cost` decimal(10,4) NULL DEFAULT NULL COMMENT '单位成本',
  `total_cost` decimal(18,4) NULL DEFAULT NULL COMMENT '总成本',
  `consumption_rate` decimal(5,2) NULL DEFAULT NULL COMMENT '消耗率（实际/计划*100%）',
  `waste_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '废料数量',
  `waste_rate` decimal(5,2) NULL DEFAULT 0.00 COMMENT '废料率（%）',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(50) NULL DEFAULT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) NULL DEFAULT NULL,
  PRIMARY KEY (`consumption_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_order_no`(`order_no`) USING BTREE,
  INDEX `idx_material_id`(`material_id`) USING BTREE,
  INDEX `idx_material_type`(`material_type`) USING BTREE,
  INDEX `idx_batch_no`(`batch_no`) USING BTREE,
  CONSTRAINT `fk_consumption_order` FOREIGN KEY (`order_id`) REFERENCES `production_orders` (`order_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_consumption_material` FOREIGN KEY (`material_id`) REFERENCES `materials` (`material_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '生产用料明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for production_outputs
-- ----------------------------
DROP TABLE IF EXISTS `production_outputs`;
CREATE TABLE `production_outputs` (
  `output_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '产出记录主键',
  `order_id` int(11) NOT NULL COMMENT '生产加工单主键',
  `order_no` varchar(50) NOT NULL COMMENT '生产加工单号',
  `product_material_id` varchar(50) NOT NULL COMMENT '成品物料编码',
  `product_material_name` varchar(200) NULL DEFAULT NULL COMMENT '成品物料名称',
  `output_qty` decimal(18,4) NOT NULL COMMENT '产出数量',
  `output_unit` varchar(20) NOT NULL COMMENT '产出单位',
  `quality_grade` tinyint(4) NOT NULL DEFAULT 1 COMMENT '质量等级：1-合格品，2-次品，3-废品',
  `batch_no` varchar(50) NULL DEFAULT NULL COMMENT '生产批次号',
  `warehouse` varchar(50) NULL DEFAULT NULL COMMENT '入库仓库',
  `storage_location` varchar(100) NULL DEFAULT NULL COMMENT '存储位置',
  `unit_cost` decimal(10,4) NULL DEFAULT NULL COMMENT '单位成本',
  `total_cost` decimal(18,4) NULL DEFAULT NULL COMMENT '总成本',
  `production_date` date NOT NULL COMMENT '生产日期',
  `expiry_date` date NULL DEFAULT NULL COMMENT '保质期至',
  `quality_inspector` varchar(50) NULL DEFAULT NULL COMMENT '质检员',
  `quality_result` varchar(200) NULL DEFAULT NULL COMMENT '质检结果',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(50) NULL DEFAULT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) NULL DEFAULT NULL,
  PRIMARY KEY (`output_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_order_no`(`order_no`) USING BTREE,
  INDEX `idx_product_material`(`product_material_id`) USING BTREE,
  INDEX `idx_quality_grade`(`quality_grade`) USING BTREE,
  INDEX `idx_batch_no`(`batch_no`) USING BTREE,
  INDEX `idx_production_date`(`production_date`) USING BTREE,
  CONSTRAINT `fk_output_order` FOREIGN KEY (`order_id`) REFERENCES `production_orders` (`order_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_output_material` FOREIGN KEY (`product_material_id`) REFERENCES `materials` (`material_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '生产产出明细表' ROW_FORMAT = DYNAMIC;

-- =====================================================
-- 扩展自动编号规则，支持新模块
-- =====================================================

-- 插入需求计划编号规则
INSERT INTO `auto_number_rule` (`rule_code`, `rule_name`, `prefix`, `date_format`, `sequence_length`, `reset_type`, `status`, `description`) 
VALUES ('XQJH', '需求计划编号', 'XQJH', 'YYYYMMDD', 4, 1, 1, '需求计划管理模块编号规则');

-- 插入生产计划编号规则
INSERT INTO `auto_number_rule` (`rule_code`, `rule_name`, `prefix`, `date_format`, `sequence_length`, `reset_type`, `status`, `description`) 
VALUES ('SCJH', '生产计划编号', 'SCJH', 'YYYYMMDD', 4, 1, 1, '生产计划管理模块编号规则');

-- 插入生产加工单编号规则
INSERT INTO `auto_number_rule` (`rule_code`, `rule_name`, `prefix`, `date_format`, `sequence_length`, `reset_type`, `status`, `description`) 
VALUES ('SCJG', '生产加工单编号', 'SCJG', 'YYYYMMDD', 4, 1, 1, '生产加工单模块编号规则');

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 临时库存表 (tmp_material_stock) - 用于物料搜索显示库存
-- =====================================================
DROP TABLE IF EXISTS `tmp_material_stock`;
CREATE TABLE `tmp_material_stock` (
  `material_id` varchar(50) NOT NULL COMMENT '物料编码',
  `stock_quantity` decimal(18,4) NOT NULL DEFAULT 0.0000 COMMENT '库存数量',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`material_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='临时物料库存表-用于搜索显示';
