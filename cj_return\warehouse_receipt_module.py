from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
from datetime import datetime, date, timedelta
import traceback
from .dongfang_return_db import DongfangReturnDB
from .warehouse_receipt_dialog import WarehouseReceiptDialog

class WarehouseReceiptTab(QWidget):
    """入库收货单管理标签页"""
    
    def __init__(self, return_db=None):
        super().__init__()
        self.return_db = return_db
        self.dongfang_db = DongfangReturnDB()
        self.current_page = 1
        self.page_size = 20
        self.total_records = 0
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建搜索区域
        self.create_search_area(layout)
        
        # 创建工具栏
        self.create_toolbar(layout)
        
        # 创建表格
        self.create_table(layout)
        
        # 创建分页控件
        self.create_pagination(layout)
    
    def create_search_area(self, parent_layout):
        """创建搜索区域"""
        search_frame = QFrame()
        search_frame.setFrameStyle(QFrame.Shape.Box)
        # search_frame.setStyleSheet("""
        #     QFrame {
        #         border: 1px solid #d9d9d9;
        #         border-radius: 6px;
        #         background-color: #fafafa;
        #         padding: 8px;
        #     }
        # """)
        
        search_layout = QVBoxLayout(search_frame)
        search_layout.setSpacing(12)
        search_layout.setContentsMargins(16, 12, 16, 12)
        
        # 第一行：收货单号、退货申请号、收货状态
        first_row = QHBoxLayout()
        first_row.setSpacing(20)
        
        # 收货单号组
        receipt_group = QHBoxLayout()
        receipt_label = QLabel("收货单号:")
        receipt_label.setMinimumWidth(80)
        receipt_label.setStyleSheet("font-weight: 500; color: #333333;")
        receipt_group.addWidget(receipt_label)
        
        self.receipt_no_input = QLineEdit()
        self.receipt_no_input.setPlaceholderText("请输入收货单号")
        self.receipt_no_input.setFixedWidth(180)
        self.receipt_no_input.returnPressed.connect(self.search_receipts)  # 添加回车搜索
        self.receipt_no_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                padding: 6px 8px;
                background-color: white;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #1890ff;
                border-width: 2px;
            }
            QLineEdit:hover {
                border-color: #40a9ff;
            }
        """)
        receipt_group.addWidget(self.receipt_no_input)
        first_row.addLayout(receipt_group)
        
        # 退货申请号组
        return_group = QHBoxLayout()
        return_label = QLabel("退货申请号:")
        return_label.setMinimumWidth(80)
        return_label.setStyleSheet("font-weight: 500; color: #333333;")
        return_group.addWidget(return_label)
        
        self.return_yewu_no_input = QLineEdit()
        self.return_yewu_no_input.setPlaceholderText("请输入退货申请业务编号")
        self.return_yewu_no_input.setFixedWidth(180)
        self.return_yewu_no_input.returnPressed.connect(self.search_receipts)  # 添加回车搜索
        self.return_yewu_no_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                padding: 6px 8px;
                background-color: white;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #1890ff;
                border-width: 2px;
            }
            QLineEdit:hover {
                border-color: #40a9ff;
            }
        """)
        return_group.addWidget(self.return_yewu_no_input)
        first_row.addLayout(return_group)
        
        # 收货状态组
        status_group = QHBoxLayout()
        status_label = QLabel("收货状态:")
        status_label.setMinimumWidth(80)
        # status_label.setStyleSheet("font-weight: 500; color: #333333;")
        status_group.addWidget(status_label)
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["全部状态", "保存", "审核", "关闭"])
        self.status_combo.setFixedWidth(120)
        # self.status_combo.setStyleSheet("""
        #     QComboBox {
        #         border: 1px solid #d9d9d9;
        #         border-radius: 4px;
        #         padding: 6px 8px;
        #         background-color: white;
        #         font-size: 12px;
        #         color: #333333;
        #         selection-background-color: #1890ff;
        #     }
        #     QComboBox:focus {
        #         border-color: #1890ff;
        #     }
        #     QComboBox:hover {
        #         border-color: #40a9ff;
        #     }
        #     QComboBox::drop-down {
        #         border: none;
        #         width: 20px;
        #         background-color: transparent;
        #     }
        #     QComboBox::down-arrow {
        #         width: 12px;
        #         height: 12px;
        #         image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMgNC41TDYgNy41TDkgNC41IiBzdHJva2U9IiM2NjY2NjYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
        #     }
        #     QComboBox QAbstractItemView {
        #         border: 1px solid #d9d9d9;
        #         border-radius: 4px;
        #         background-color: white;
        #         selection-background-color: #e6f7ff;
        #         selection-color: #1890ff;
        #         outline: none;
        #         font-size: 12px;
        #         padding: 2px;
        #     }
        #     QComboBox QAbstractItemView::item {
        #         height: 28px;
        #         padding: 4px 8px;
        #         border: none;
        #         color: #333333;
        #     }
        #     QComboBox QAbstractItemView::item:selected {
        #         background-color: #1890ff;
        #         color: white;
        #         font-weight: 500;
        #     }
        #     QComboBox QAbstractItemView::item:hover {
        #         background-color: #e6f7ff;
        #         color: #1890ff;
        #     }
        # """)
        status_group.addWidget(self.status_combo)
        first_row.addLayout(status_group)
        
        first_row.addStretch()
        search_layout.addLayout(first_row)
        #收货状态更改 自动刷新
        self.status_combo.currentIndexChanged.connect(self.search_receipts)
        
        # 第二行：收货日期、按钮
        second_row = QHBoxLayout()
        second_row.setSpacing(20)
        
        # 收货日期组
        date_group = QHBoxLayout()
        date_label = QLabel("收货日期:")
        date_label.setMinimumWidth(80)
        date_label.setStyleSheet("font-weight: 500; color: #333333;")
        date_group.addWidget(date_label)
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        self.start_date.setFixedWidth(120)
        # self.start_date.setStyleSheet("""
        #     QDateEdit {
        #         border: 1px solid #d9d9d9;
        #         border-radius: 4px;
        #         padding: 6px 8px;
        #         background-color: white;
        #         font-size: 12px;
        #     }
        #     QDateEdit:focus {
        #         border-color: #1890ff;
        #     }
        #     QDateEdit:hover {
        #         border-color: #40a9ff;
        #     }
        #     QDateEdit::drop-down {
        #         border: none;
        #         width: 20px;
        #     }
        # """)
        date_group.addWidget(self.start_date)
        
        date_to_label = QLabel("至")
        date_to_label.setStyleSheet("color: #666666; margin: 0 8px;")
        date_group.addWidget(date_to_label)
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        self.end_date.setFixedWidth(120)
        # self.end_date.setStyleSheet("""
        #     QDateEdit {
        #         border: 1px solid #d9d9d9;
        #         border-radius: 4px;
        #         padding: 6px 8px;
        #         background-color: white;
        #         font-size: 12px;
        #     }
        #     QDateEdit:focus {
        #         border-color: #1890ff;
        #     }
        #     QDateEdit:hover {
        #         border-color: #40a9ff;
        #     }
        #     QDateEdit::drop-down {
        #         border: none;
        #         width: 20px;
        #     }
        # """)
        date_group.addWidget(self.end_date)
        second_row.addLayout(date_group)
        
        # 按钮组
        button_group = QHBoxLayout()
        button_group.setSpacing(12)
        
        # 搜索按钮
        search_btn = QPushButton("🔍 搜索")
        search_btn.setFixedSize(80, 32)
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
            QPushButton:pressed {
                background-color: #096dd9;
            }
        """)
        search_btn.clicked.connect(self.search_receipts)
        button_group.addWidget(search_btn)
        
        # 重置按钮
        reset_btn = QPushButton("🔄 重置")
        reset_btn.setFixedSize(80, 32)
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                color: #333333;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #e6f7ff;
                border-color: #40a9ff;
                color: #1890ff;
            }
            QPushButton:pressed {
                background-color: #bae7ff;
            }
        """)
        reset_btn.clicked.connect(self.reset_search)
        button_group.addWidget(reset_btn)
        
        second_row.addLayout(button_group)
        second_row.addStretch()
        search_layout.addLayout(second_row)
        
        parent_layout.addWidget(search_frame)
    
    def create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar_layout = QHBoxLayout()
        
        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.clicked.connect(self.load_data)
        toolbar_layout.addWidget(refresh_btn)
        
        # 导出按钮
        export_btn = QPushButton("📤 导出")
        export_btn.clicked.connect(self.export_data)
        toolbar_layout.addWidget(export_btn)
        
        toolbar_layout.addStretch()
        
        # 统计信息标签
        self.stats_label = QLabel("总计: 0 条记录")
        toolbar_layout.addWidget(self.stats_label)
        
        parent_layout.addLayout(toolbar_layout)
    
    def create_table(self, parent_layout):
        """创建表格"""
        self.table = QTableWidget()
        self.table.setColumnCount(12)
        
        headers = [
            "收货单号", "退货申请号", "收货日期", "收货人", "收货仓库",
            "收货状态", "总数量", "原料数量", "变形数量", "待报废数量", 
            "报废数量", "创建时间"
        ]
        self.table.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.table.setSortingEnabled(True)
        
        # 设置表格不可编辑
        self.table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        
        # 设置列宽自适应
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 收货单号
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 退货申请号
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 收货日期
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 收货人
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 收货仓库
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 收货状态
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # 总数量
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # 原料数量
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.ResizeToContents)  # 变形数量
        header.setSectionResizeMode(9, QHeaderView.ResizeMode.ResizeToContents)  # 待报废数量
        header.setSectionResizeMode(10, QHeaderView.ResizeMode.ResizeToContents) # 报废数量
        header.setSectionResizeMode(11, QHeaderView.ResizeMode.Stretch)          # 创建时间 - 拉伸填充剩余空间
        
        # 双击事件
        self.table.doubleClicked.connect(self.view_receipt_detail)
        
        # 右键菜单
        self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)
        
        parent_layout.addWidget(self.table)
    
    def create_pagination(self, parent_layout):
        """创建分页控件"""
        pagination_layout = QHBoxLayout()
        
        # 页面信息
        self.page_info_label = QLabel("第 1 页，共 1 页")
        pagination_layout.addWidget(self.page_info_label)
        
        pagination_layout.addStretch()
        
        # 分页按钮
        self.first_btn = QPushButton("首页")
        self.first_btn.clicked.connect(lambda: self.go_to_page(1))
        pagination_layout.addWidget(self.first_btn)
        
        self.prev_btn = QPushButton("上一页")
        self.prev_btn.clicked.connect(self.prev_page)
        pagination_layout.addWidget(self.prev_btn)
        
        self.next_btn = QPushButton("下一页")
        self.next_btn.clicked.connect(self.next_page)
        pagination_layout.addWidget(self.next_btn)
        
        self.last_btn = QPushButton("末页")
        self.last_btn.clicked.connect(self.go_to_last_page)
        pagination_layout.addWidget(self.last_btn)
        
        # 页面大小选择
        pagination_layout.addWidget(QLabel("每页:"))
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["10", "20", "50", "100"])
        self.page_size_combo.setCurrentText(str(self.page_size))
        self.page_size_combo.currentTextChanged.connect(self.change_page_size)
        pagination_layout.addWidget(self.page_size_combo)
        
        parent_layout.addLayout(pagination_layout)
    
    def load_data(self):
        """加载数据"""
        try:
            filters = self.get_search_filters()
            total, results = self.dongfang_db.get_warehouse_receipts(filters)
            
            self.total_records = total
            self.update_table(results)
            self.update_pagination()
            self.update_stats()
            
            print(f"入库收货单列表已刷新，共{total}条记录")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载数据失败：{str(e)}")
            print(f"加载入库收货单数据失败：{str(e)}")
    
    def get_search_filters(self):
        """获取搜索条件"""
        filters = {
            'page': self.current_page,
            'page_size': self.page_size
        }
        
        # 收货单号
        if self.receipt_no_input.text().strip():
            filters['receipt_no'] = self.receipt_no_input.text().strip()
        
        # 退货申请号
        if self.return_yewu_no_input.text().strip():
            filters['return_yewu_no'] = self.return_yewu_no_input.text().strip()
        
        # 日期范围
        filters['start_date'] = self.start_date.date().toPyDate()
        filters['end_date'] = self.end_date.date().toPyDate()
        
        # 状态
        status_text = self.status_combo.currentText()
        if status_text != "全部状态":
            status_map = {"保存": 0, "审核": 1, "关闭": 2}
            filters['receipt_status'] = status_map.get(status_text)
        
        return filters
    
    def update_table(self, results):
        """更新表格数据"""
        self.table.setRowCount(len(results))
        
        for row, data in enumerate(results):
            # 收货单号
            item = QTableWidgetItem(str(data.get('receipt_no', '')))
            item.setData(Qt.ItemDataRole.UserRole, data.get('id'))
            self.table.setItem(row, 0, item)
            
            # 退货申请号
            self.table.setItem(row, 1, QTableWidgetItem(str(data.get('return_yewu_no', ''))))
            
            # 收货日期
            receipt_date = data.get('receipt_date', '')
            if receipt_date:
                self.table.setItem(row, 2, QTableWidgetItem(str(receipt_date)))
            
            # 收货人
            self.table.setItem(row, 3, QTableWidgetItem(str(data.get('receiver', ''))))
            
            # 收货仓库
            self.table.setItem(row, 4, QTableWidgetItem(str(data.get('warehouse', ''))))
            
            # 收货状态
            status_item = QTableWidgetItem(str(data.get('status_text', '')))
            if data.get('receipt_status') == 0:
                status_item.setBackground(QColor(255, 248, 220))  # 浅黄色
            elif data.get('receipt_status') == 1:
                status_item.setBackground(QColor(240, 255, 240))  # 浅绿色
            elif data.get('receipt_status') == 2:
                status_item.setBackground(QColor(255, 240, 240))  # 浅红色
            self.table.setItem(row, 5, status_item)
            
            # 数量信息
            self.table.setItem(row, 6, QTableWidgetItem(str(data.get('total_quantity', 0))))
            self.table.setItem(row, 7, QTableWidgetItem(str(data.get('total_raw_quantity', 0))))
            self.table.setItem(row, 8, QTableWidgetItem(str(data.get('total_deformed_quantity', 0))))
            self.table.setItem(row, 9, QTableWidgetItem(str(data.get('total_to_scrap_quantity', 0))))
            self.table.setItem(row, 10, QTableWidgetItem(str(data.get('total_scrapped_quantity', 0))))
            
            # 创建时间
            create_time = data.get('create_time', '')
            if create_time:
                self.table.setItem(row, 11, QTableWidgetItem(str(create_time)))
    
    def search_receipts(self):
        """搜索收货单"""
        self.current_page = 1
        self.load_data()
    
    def reset_search(self):
        """重置搜索条件"""
        self.receipt_no_input.clear()
        self.return_yewu_no_input.clear()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.end_date.setDate(QDate.currentDate())
        self.status_combo.setCurrentIndex(0)
        self.search_receipts()
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.table.itemAt(position) is None:
            return
        
        row = self.table.rowAt(position.y())
        if row < 0:
            return
        
        # 获取收货单状态
        status_item = self.table.item(row, 5)
        status_text = status_item.text() if status_item else ""
        
        menu = QMenu(self)
        
        # 查看详情
        view_action = menu.addAction("📋 查看详情")
        view_action.triggered.connect(lambda: self.view_receipt_detail_by_row_mode(row, 'view'))
        
        # 编辑（只有保存状态可以编辑）
        if status_text == "保存":
            edit_action = menu.addAction("✏️ 编辑")
            edit_action.triggered.connect(lambda: self.view_receipt_detail_by_row_mode(row, 'edit'))
            
            menu.addSeparator()
            
            # 删除（只有保存状态可以删除）
            delete_action = menu.addAction("🗑️ 删除")
            delete_action.triggered.connect(lambda: self.delete_receipt_by_row(row))
        
        # 状态操作
        if status_text == "审核":
            menu.addSeparator()
            # 反审核（只有审核状态可以反审核）
            unaudit_action = menu.addAction("↩️ 反审核")
            unaudit_action.triggered.connect(lambda: self.unaudit_receipt_by_row(row))
            
            # 关闭（只有审核状态可以关闭）
            close_action = menu.addAction("❌ 关闭")
            close_action.triggered.connect(lambda: self.close_receipt_by_row(row))
        menu.exec(self.table.mapToGlobal(position))

    def view_receipt_detail(self, index):
        """查看收货单详情"""
        row = index.row()
        self.view_receipt_detail_by_row(row)

    def view_receipt_detail_by_row(self, row):
        """根据行号查看收货单详情"""
        try:
            receipt_id = self.table.item(row, 0).data(Qt.ItemDataRole.UserRole)
            if not receipt_id:
                QMessageBox.warning(self, "警告", "无法获取收货单ID")
                return
            
            # 获取收货单状态，决定打开模式
            status_item = self.table.item(row, 5)  # 状态列
            status_text = status_item.text() if status_item else ""
            
            # 如果是保存状态，默认打开编辑模式；否则打开查看模式
            mode = 'edit' if status_text == "保存" else 'view'
            
            dialog = WarehouseReceiptDialog(self, receipt_id, mode=mode)
            result = dialog.exec()
            
            # 无论对话框如何关闭，都刷新数据
            self.load_data()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开详情失败：{str(e)}")

    def view_receipt_detail_by_row_mode(self, row, mode):
        """根据行号和指定模式打开收货单详情"""
        try:
            receipt_id = self.table.item(row, 0).data(Qt.ItemDataRole.UserRole)
            if not receipt_id:
                QMessageBox.warning(self, "警告", "无法获取收货单ID")
                return
            
            dialog = WarehouseReceiptDialog(self, receipt_id, mode=mode)
            result = dialog.exec()
            
            # 无论对话框如何关闭，都刷新数据
            self.load_data()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开详情失败：{str(e)}")

    def delete_receipt_by_row(self, row):
        """根据行号删除收货单"""
        try:
            receipt_id = self.table.item(row, 0).data(Qt.ItemDataRole.UserRole)
            if not receipt_id:
                QMessageBox.warning(self, "警告", "无法获取收货单ID")
                return
            
            receipt_no = self.table.item(row, 1).text()  # 收货单号
            
            # 确认删除
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除收货单 {receipt_no} 吗？\n删除后将恢复退货申请的锁定状态。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.dongfang_db.delete_warehouse_receipt(receipt_id)
                QMessageBox.information(self, "成功", "删除成功！")
                self.load_data()  # 刷新数据
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除失败：{str(e)}")

    def unaudit_receipt_by_row(self, row):
        """根据行号反审核收货单"""
        try:
            receipt_id = self.table.item(row, 0).data(Qt.ItemDataRole.UserRole)
            if not receipt_id:
                QMessageBox.warning(self, "警告", "无法获取收货单ID")
                return
            
            receipt_no = self.table.item(row, 0).text()  # 收货单号
            
            # 确认反审核
            reply = QMessageBox.question(
                self, "确认反审核",
                f"确定要反审核收货单 {receipt_no} 吗？\n反审核后将恢复到保存状态，可以重新编辑。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.dongfang_db.unaudit_warehouse_receipt(receipt_id)
                QMessageBox.information(self, "成功", "反审核成功！")
                self.load_data()  # 刷新数据
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"反审核失败：{str(e)}")
    
    def close_receipt_by_row(self, row):
        """根据行号关闭收货单"""
        try:
            receipt_id = self.table.item(row, 0).data(Qt.ItemDataRole.UserRole)
            if not receipt_id:
                QMessageBox.warning(self, "警告", "无法获取收货单ID")
                return
            
            receipt_no = self.table.item(row, 0).text()  # 收货单号
            
            # 确认关闭
            reply = QMessageBox.question(
                self, "确认关闭",
                f"确定要关闭收货单 {receipt_no} 吗？\n关闭后将不能再进行任何操作。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.dongfang_db.close_warehouse_receipt(receipt_id)
                QMessageBox.information(self, "成功", "关闭成功！")
                self.load_data()  # 刷新数据
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"关闭失败：{str(e)}")
    
    def export_data(self):
        """导出数据"""
        try:
            # 获取所有数据（不分页）
            filters = self.get_search_filters()
            filters.pop('page', None)
            filters.pop('page_size', None)
            
            total, results = self.dongfang_db.get_warehouse_receipts(filters)
            
            if not results:
                QMessageBox.information(self, "提示", "没有数据可导出")
                return
            
            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出入库收货单", 
                f"入库收货单_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )
            
            if not file_path:
                return
            
            # 创建进度对话框
            progress_dialog = QProgressDialog("正在导出数据...", "取消", 0, len(results), self)
            progress_dialog.setWindowTitle("导出进度")
            progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
            progress_dialog.setMinimumDuration(0)
            progress_dialog.show()
            
            # 导入openpyxl
            from openpyxl import Workbook
            from openpyxl.styles import Font
            
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "入库收货单"
            
            # 设置表头
            headers = [
                "收货单号", "退货申请号", "收货日期", "收货人", "收货仓库", "收货状态",
                "总数量", "原料数量", "变形数量", "待报废数量", "报废数量",
                "运单号码", "物料编码", "更新料号", "物料名称", "退货数量", 
                "拆分编码", "箱规", "拆分数量", "收货数量", "原料数量(明细)", 
                "变形数量(明细)", "待报废数量(明细)", "报废数量(明细)", "批号", 
                "存储位置", "明细备注", "创建时间", "创建人"
            ]
            ws.append(headers)
            
            # 写入数据
            for i, main_data in enumerate(results):
                # 更新进度
                progress_dialog.setValue(i)
                progress_dialog.setLabelText(f"正在处理第 {i+1}/{len(results)} 条数据")
                
                # 检查是否取消
                if progress_dialog.wasCanceled():
                    wb.close()
                    return
                
                # 获取明细数据
                details = self.dongfang_db.get_warehouse_receipt_details(main_data['id'])
                
                if details and details.get('details'):
                    # 如果有明细，每个明细一行
                    for detail in details['details']:
                        ws.append([
                            main_data.get('receipt_no', ''),                    # 收货单号
                            main_data.get('return_yewu_no', ''),               # 退货申请号
                            str(main_data.get('receipt_date', '')),            # 收货日期
                            main_data.get('receiver', ''),                     # 收货人
                            main_data.get('warehouse', ''),                    # 收货仓库
                            main_data.get('status_text', ''),                  # 收货状态
                            main_data.get('total_quantity', 0),                # 总数量
                            main_data.get('total_raw_quantity', 0),            # 原料数量
                            main_data.get('total_deformed_quantity', 0),       # 变形数量
                            main_data.get('total_to_scrap_quantity', 0),       # 待报废数量
                            main_data.get('total_scrapped_quantity', 0),       # 报废数量
                            detail.get('waybill_no', ''),                      # 运单号码
                            detail.get('material_code', ''),                   # 物料编码
                            detail.get('update_material_code', ''),            # 更新料号
                            detail.get('material_name', ''),                   # 物料名称
                            detail.get('return_quantity', 0),                  # 退货数量
                            detail.get('split_code', ''),                      # 拆分编码
                            detail.get('box_spec', ''),                        # 箱规
                            detail.get('split_quantity', 0),                   # 拆分数量
                            detail.get('received_quantity', 0),                # 收货数量
                            detail.get('raw_quantity', 0),                     # 原料数量(明细)
                            detail.get('deformed_quantity', 0),                # 变形数量(明细)
                            detail.get('to_scrap_quantity', 0),                # 待报废数量(明细)
                            detail.get('scrapped_quantity', 0),                # 报废数量(明细)
                            detail.get('batch_no', ''),                        # 批号
                            detail.get('storage_location', ''),                # 存储位置
                            detail.get('remark', ''),                          # 明细备注
                            str(main_data.get('create_time', '')),             # 创建时间
                            main_data.get('create_user', ''),                  # 创建人
                        ])
                else:
                    # 如果没有明细，只导出主表数据
                    ws.append([
                        main_data.get('receipt_no', ''),                    # 收货单号
                        main_data.get('return_yewu_no', ''),               # 退货申请号
                        str(main_data.get('receipt_date', '')),            # 收货日期
                        main_data.get('receiver', ''),                     # 收货人
                        main_data.get('warehouse', ''),                    # 收货仓库
                        main_data.get('status_text', ''),                  # 收货状态
                        main_data.get('total_quantity', 0),                # 总数量
                        main_data.get('total_raw_quantity', 0),            # 原料数量
                        main_data.get('total_deformed_quantity', 0),       # 变形数量
                        main_data.get('total_to_scrap_quantity', 0),       # 待报废数量
                        main_data.get('total_scrapped_quantity', 0),       # 报废数量
                        '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',  # 明细字段为空
                        str(main_data.get('create_time', '')),             # 创建时间
                        main_data.get('create_user', ''),                  # 创建人
                    ])
                
                QApplication.processEvents()  # 处理事件，保持UI响应
            
            # 调整列宽
            for column in ws.columns:
                max_length = 0
                column = list(column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
                ws.column_dimensions[column[0].column_letter].width = adjusted_width
            
            # 冻结首行
            ws.freeze_panes = "A2"
            
            # 设置首行样式
            for cell in ws[1]:
                cell.font = Font(name='Calibri', size=9, bold=True)
            
            # 设置其他单元格字体
            for row in ws.iter_rows(min_row=2):
                for cell in row:
                    cell.font = Font(name='Calibri', size=9)
            
            # 完成进度
            progress_dialog.setValue(len(results))
            
            # 保存文件
            wb.save(file_path)
            QMessageBox.information(self, "成功", f"数据导出成功！\n共导出 {len(results)} 条记录")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败：{str(e)}")
        finally:
            if 'progress_dialog' in locals():
                progress_dialog.close()
    
    def update_pagination(self):
        """更新分页信息"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        if total_pages == 0:
            total_pages = 1
        
        self.page_info_label.setText(f"第 {self.current_page} 页，共 {total_pages} 页")
        
        # 更新按钮状态
        self.first_btn.setEnabled(self.current_page > 1)
        self.prev_btn.setEnabled(self.current_page > 1)
        self.next_btn.setEnabled(self.current_page < total_pages)
        self.last_btn.setEnabled(self.current_page < total_pages)
    
    def update_stats(self):
        """更新统计信息"""
        self.stats_label.setText(f"总计: {self.total_records} 条记录")
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_data()
    
    def next_page(self):
        """下一页"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        if self.current_page < total_pages:
            self.current_page += 1
            self.load_data()
    
    def go_to_page(self, page):
        """跳转到指定页"""
        self.current_page = page
        self.load_data()
    
    def go_to_last_page(self):
        """跳转到最后一页"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        self.current_page = total_pages
        self.load_data()
    
    def change_page_size(self, size_text):
        """改变页面大小"""
        self.page_size = int(size_text)
        self.current_page = 1
        self.load_data()














