from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QTableWidget, QPushButton, QTableWidgetItem, QMessageBox)
from PyQt6.QtCore import Qt, pyqtSignal
from material.material_db import MaterialDB
from PyQt6.QtWidgets import QHeaderView # Added missing import

class MaterialSearchDialog(QDialog):
    """物料搜索对话框"""
    
    material_selected = pyqtSignal(dict)  # 选中物料信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.material_db = MaterialDB()
        
        self.setWindowTitle("物料搜索")
        self.resize(800, 600)
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        
        # 搜索条件
        search_layout = QHBoxLayout()
        
        # 物料编码
        self.material_id_edit = QLineEdit()
        self.material_id_edit.setPlaceholderText("输入物料编码")
        self.material_id_edit.returnPressed.connect(self.search_materials)  # 添加回车事件
        search_layout.addWidget(QLabel("物料编码:"))
        search_layout.addWidget(self.material_id_edit)
        
        # 物料名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入物料名称")
        self.name_edit.returnPressed.connect(self.search_materials)  # 添加回车事件
        search_layout.addWidget(QLabel("物料名称:"))
        search_layout.addWidget(self.name_edit)
        
        # 搜索按钮
        self.search_btn = QPushButton("搜索")
        self.search_btn.clicked.connect(self.search_materials)
        search_layout.addWidget(self.search_btn)
        
        layout.addLayout(search_layout)
        
        # 物料列表
        self.table = QTableWidget()
        self.table.setColumnCount(2)
        self.table.setHorizontalHeaderLabels(["物料编码", "物料名称"])
        
        # 设置表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.table.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        
        self.table.doubleClicked.connect(self.on_material_selected)
        
        # 设置表格列宽
        self.table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        
        layout.addWidget(self.table)
        
        # 按钮
        button_layout = QHBoxLayout()
        self.select_btn = QPushButton("选择")
        self.select_btn.clicked.connect(self.on_material_selected)
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.select_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        # 初始化时执行一次搜索
        self.search_materials()
    
    def search_materials(self):
        """搜索物料"""
        try:
            params = {
                "material_id": self.material_id_edit.text().strip(),
                "name": self.name_edit.text().strip(),
                "page": 1,
                "page_size": 100
            }
            
            # 使用修改后的 get_materials_for_cj_goods 方法
            # 该方法现在会查询 is_east_material = 1 的物料
            total, materials = self.material_db.get_materials_for_cj_goods(params)
            
            # 更新表格
            self.table.setRowCount(len(materials))
            for row, material in enumerate(materials):
                self.table.setItem(row, 0, QTableWidgetItem(material.get("material_id", "")))
                self.table.setItem(row, 1, QTableWidgetItem(material.get("name", "")))
                # 保存完整的物料数据
                self.table.item(row, 0).setData(Qt.ItemDataRole.UserRole, material)
        
        except Exception as e:
            QMessageBox.warning(self, "警告", f"搜索物料失败：{str(e)}")
    
    def on_material_selected(self):
        """物料选择处理"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            # 获取完整的物料数据
            material = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
            self.material_selected.emit(material)
            self.accept() 
