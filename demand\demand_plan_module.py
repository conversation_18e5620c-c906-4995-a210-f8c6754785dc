"""
需求计划管理模块
包含需求计划的查询、新增、编辑、删除等功能
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                           QTableWidgetItem, QPushButton, QLineEdit, QLabel, 
                           QHeaderView, QMessageBox, QFrame, QStyle, QFileDialog,
                           QProgressDialog, QComboBox, QDateEdit, QMenu, QCheckBox,
                           QSplitter, QTextEdit, QGroupBox, QFormLayout, QSpinBox,
                           QDialog)  # 添加 QDialog 导入
from PyQt6.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QIcon, QCursor, QColor
import pandas as pd
import os
from datetime import datetime, date
from demand.demand_plan_db import DemandPlanDB
from demand.demand_plan_dialog import DemandPlanDialog
from demand.bom_expand_confirm_dialog import BOMExpandConfirmDialog
from core.logger import Logger

class DemandPlanTab(QWidget):
    """需求计划管理标签页"""
    
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.demand_plan_db = DemandPlanDB()
        self.current_page = 1
        self.page_size = 20
        self.total_records = 0
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """设置UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建搜索区域
        self.create_search_area(main_layout)
        
        # 创建工具栏
        self.create_toolbar(main_layout)
       
        # 创建表格
        self.create_table(main_layout)
        
        # 创建分页控件
        self.create_pagination(main_layout)
        
        # 创建统计信息
        self.create_stats(main_layout)
    
    def create_search_area(self, parent_layout):
        """创建搜索区域"""
        search_frame = QFrame()
        
        search_layout = QVBoxLayout(search_frame)
        search_layout.setSpacing(10)
        
        # 第一行搜索条件
        first_row = QHBoxLayout()
        first_row.setSpacing(15)
        
        # 计划编号
        first_row.addWidget(QLabel("计划编号:"))
        self.plan_no_edit = QLineEdit()
        self.plan_no_edit.setPlaceholderText("请输入计划编号")
        self.plan_no_edit.setFixedWidth(150)
        self.plan_no_edit.returnPressed.connect(self.search_plans)  # 添加回车键搜索功能
        first_row.addWidget(self.plan_no_edit)
        
        # 申请部门
        first_row.addWidget(QLabel("申请部门:"))
        self.department_edit = QComboBox()
        self.department_edit.addItems(["全部", "通路一", "通路二", "电商-京东", "电商-猫超", "市场部-东方电视", "市场达博"])
        self.department_edit.setFixedWidth(120)
        first_row.addWidget(self.department_edit)
        self.department_edit.currentTextChanged.connect(self.search_plans)
        
        # 申请人
        first_row.addWidget(QLabel("申请人:"))
        self.applicant_edit = QLineEdit()
        self.applicant_edit.setPlaceholderText("请输入申请人")
        self.applicant_edit.setFixedWidth(100)
        self.applicant_edit.returnPressed.connect(self.search_plans)  # 添加回车键搜索功能
        first_row.addWidget(self.applicant_edit)
        
        # 需求类型
        first_row.addWidget(QLabel("需求类型:"))
        self.demand_type_combo = QComboBox()
        self.demand_type_combo.addItems(["全部", "生产需求", "销售需求","库存补充","其他需求"])
        self.demand_type_combo.setFixedWidth(100)
        first_row.addWidget(self.demand_type_combo)
        self.demand_type_combo.currentTextChanged.connect(self.search_plans)
        #计划状态
        first_row.addWidget(QLabel("计划状态:"))
        self.plan_status_combo = QComboBox()
        self.plan_status_combo.addItems(["全部", "草稿", "已确认", "执行中", "已完成", "已取消"])
        self.plan_status_combo.setFixedWidth(100)
        first_row.addWidget(self.plan_status_combo)
        self.plan_status_combo.currentTextChanged.connect(self.search_plans)
          # 优先级
        first_row.addWidget(QLabel("优先级:"))
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["全部", "普通", "紧急", "特急"])
        self.priority_combo.setFixedWidth(80)
        first_row.addWidget(self.priority_combo)
        self.priority_combo.currentTextChanged.connect(self.search_plans)
        first_row.addStretch()
        search_layout.addLayout(first_row)
        
        # 第二行搜索条件
        second_row = QHBoxLayout()
        second_row.setSpacing(15)
        
        
        # 计划日期范围
        second_row.addWidget(QLabel("计划日期:"))
        self.plan_date_start = QDateEdit()
        self.plan_date_start.setDate(QDate.currentDate().addDays(-30))
        self.plan_date_start.setFixedWidth(120)
        second_row.addWidget(self.plan_date_start)
        
        second_row.addWidget(QLabel("至"))
        self.plan_date_end = QDateEdit()
        self.plan_date_end.setDate(QDate.currentDate())
        self.plan_date_end.setFixedWidth(120)
        second_row.addWidget(self.plan_date_end)
        
        # 移除需求日期范围
      
        
        # 搜索按钮
        search_btn = QPushButton("🔍 搜索")
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        search_btn.clicked.connect(self.search_plans)
        second_row.addWidget(search_btn)
         
          # 重置按钮
        reset_btn = QPushButton("🔄 重置")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #d9d9d9;
                color: #333;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #bfbfbf;
            }
        """)
        reset_btn.clicked.connect(self.reset_search)
        second_row.addWidget(reset_btn)

        second_row.addStretch()
        search_layout.addLayout(second_row)
        
        # 第三行：需求日期和按钮
        third_row = QHBoxLayout()
        third_row.setSpacing(15)
        
        # 新增按钮
        self.add_btn = QPushButton("➕ 新增需求计划")
        self.add_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
        """)
        self.add_btn.clicked.connect(self.add_plan)
        third_row.addWidget(self.add_btn)
        
        # 编辑按钮
        self.edit_btn = QPushButton("✏️ 编辑")
        self.edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #bfbfbf;
            }
        """)
        self.edit_btn.clicked.connect(self.edit_selected_plan)
        self.edit_btn.setEnabled(False)
        third_row.addWidget(self.edit_btn)
        
        # 批量删除按钮
        self.batch_delete_btn = QPushButton("🗑️ 批量删除")
        self.batch_delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4d4f;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #ff7875;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #bfbfbf;
            }
        """)
        self.batch_delete_btn.clicked.connect(self.batch_delete_plans)
        self.batch_delete_btn.setEnabled(False)
        third_row.addWidget(self.batch_delete_btn)
        
        # 导出按钮
        self.export_btn = QPushButton("📊 导出Excel")
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #13c2c2;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #36cfc9;
            }
        """)
        self.export_btn.clicked.connect(self.export_to_excel)
        third_row.addWidget(self.export_btn)
        
        search_layout.addLayout(third_row)
        
        parent_layout.addWidget(search_frame)
        third_row.addStretch()
    def create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar_frame = QFrame()
        toolbar_layout = QVBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(0, 5, 0, 5)
        
        # 工具栏：批量状态操作和生成操作按钮
        toolbar_row = QHBoxLayout()
        
        # 批量状态操作按钮组
        status_group = QGroupBox("批量状态操作")
        status_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        status_layout = QHBoxLayout(status_group)
        
        # 确认按钮
        self.confirm_btn = QPushButton("✅ 确认计划")
        self.confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #bfbfbf;
            }
        """)
        self.confirm_btn.clicked.connect(lambda: self.batch_update_status(1))
        self.confirm_btn.setEnabled(False)
        status_layout.addWidget(self.confirm_btn)
        
        # 开始执行按钮
        self.start_execute_btn = QPushButton("🚀 开始执行")
        self.start_execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #bfbfbf;
            }
        """)
        self.start_execute_btn.clicked.connect(self.start_execute_plans)  # 修改为新的处理方法
        self.start_execute_btn.setEnabled(False)
        status_layout.addWidget(self.start_execute_btn)
        
        # 完成按钮
        self.complete_btn = QPushButton("✔️ 完成计划")
        self.complete_btn.setStyleSheet("""
            QPushButton {
                background-color: #13c2c2;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #36cfc9;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #bfbfbf;
            }
        """)
        self.complete_btn.clicked.connect(lambda: self.batch_update_status(3))
        self.complete_btn.setEnabled(False)
        status_layout.addWidget(self.complete_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("❌ 取消计划")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4d4f;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ff7875;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #bfbfbf;
            }
        """)
        self.cancel_btn.clicked.connect(lambda: self.batch_update_status(9))
        self.cancel_btn.setEnabled(False)
        status_layout.addWidget(self.cancel_btn)
        
        toolbar_row.addWidget(status_group)
        
        # 生成操作按钮组
        generate_group = QGroupBox("生成操作")
        generate_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        generate_layout = QHBoxLayout(generate_group)
        
        # 生成加工表按钮
        self.generate_process_btn = QPushButton("🏭 生成加工表")
        self.generate_process_btn.setStyleSheet("""
            QPushButton {
                background-color: #722ed1;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #9254de;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #bfbfbf;
            }
        """)
        self.generate_process_btn.clicked.connect(self.generate_process_sheet)
        self.generate_process_btn.setEnabled(False)
        generate_layout.addWidget(self.generate_process_btn)
        
        # 生成采购申请按钮
        self.generate_purchase_btn = QPushButton("🛒 生成采购申请")
        self.generate_purchase_btn.setStyleSheet("""
            QPushButton {
                background-color: #eb2f96;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #f759ab;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #bfbfbf;
            }
        """)
        self.generate_purchase_btn.clicked.connect(self.generate_purchase_request)
        self.generate_purchase_btn.setEnabled(False)
        generate_layout.addWidget(self.generate_purchase_btn)
        
        toolbar_row.addWidget(generate_group)
        toolbar_row.addStretch()
        
        toolbar_layout.addLayout(toolbar_row)
        parent_layout.addWidget(toolbar_frame)
    
    def create_table(self, parent_layout):
        """创建表格"""
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.ExtendedSelection)  # 多选
        self.table.setSortingEnabled(True)
        
        # 设置表头 - 移除生产计划号列
        headers = [
            "计划编号", "计划日期", "计划名称", "申请部门", "申请人",
            "需求类型", "优先级", "计划状态", "预估金额", "明细数量",
            "是否锁定", "创建时间", "创建人"
        ]
        self.table.setColumnCount(len(headers))
        self.table.setHorizontalHeaderLabels(headers)
        
        # 设置列宽
        header = self.table.horizontalHeader()
        # header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # 计划名称
        
        self.table.setColumnWidth(0, 120)  # 计划编号
        self.table.setColumnWidth(1, 100)  # 计划日期
        self.table.setColumnWidth(2, 100)  # 计划名称
        self.table.setColumnWidth(3, 100)  # 申请部门
        self.table.setColumnWidth(4, 80)   # 申请人
        self.table.setColumnWidth(5, 80)   # 需求类型
        self.table.setColumnWidth(6, 60)   # 优先级
        self.table.setColumnWidth(7, 80)   # 计划状态
        self.table.setColumnWidth(8, 100)  # 预估金额
        self.table.setColumnWidth(9, 80)  # 明细数量
        self.table.setColumnWidth(11, 80)  # 是否锁定
        self.table.setColumnWidth(13, 150) # 创建时间
        self.table.setColumnWidth(14, 80)  # 创建人
        
        # 双击查看
        self.table.doubleClicked.connect(self.view_selected_plan)
        
        # 右键菜单
        self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)
        
        # 设置表格属性
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 选择变化事件
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        
        parent_layout.addWidget(self.table)
    
    def create_pagination(self, parent_layout):
        """创建分页控件"""
        page_frame = QFrame()
        page_layout = QHBoxLayout(page_frame)
        page_layout.setContentsMargins(0, 5, 0, 5)
        
        # 总记录数
        self.total_label = QLabel("总记录数: 0")
        page_layout.addWidget(self.total_label)
        
        
        page_layout.addStretch()
        
        # 每页记录数
        page_layout.addWidget(QLabel("每页显示:"))
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["20", "50", "100"])
        self.page_size_combo.setCurrentText(str(self.page_size))
        self.page_size_combo.currentTextChanged.connect(self.on_page_size_changed)
        page_layout.addWidget(self.page_size_combo)
        
        page_layout.addWidget(QLabel("条"))
        
        # 分页按钮
        self.first_btn = QPushButton("首页")
        self.first_btn.clicked.connect(self.first_page)
        page_layout.addWidget(self.first_btn)
        
        self.prev_btn = QPushButton("上一页")
        self.prev_btn.clicked.connect(self.prev_page)
        page_layout.addWidget(self.prev_btn)
        
        # 页码信息
        self.page_label = QLabel("第 1 页，共 1 页")
        page_layout.addWidget(self.page_label)
        
        self.next_btn = QPushButton("下一页")
        self.next_btn.clicked.connect(self.next_page)
        page_layout.addWidget(self.next_btn)
        
        self.last_btn = QPushButton("末页")
        self.last_btn.clicked.connect(self.last_page)
        page_layout.addWidget(self.last_btn)
        
        parent_layout.addWidget(page_frame)
    
    def create_stats(self, parent_layout):
        """创建统计信息"""
       
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setSpacing(20)
        
        # 统计标签字典
        self.stats_labels = {}
        
        # 统计项目
        stats_items = [
            ('total', '总计划', '#1890ff'),
            ('draft', '草稿', '#faad14'),
            ('confirmed', '已确认', '#52c41a'),
            ('executing', '执行中', '#722ed1'),
            ('completed', '已完成', '#13c2c2'),
            ('cancelled', '已取消', '#ff4d4f'),
            ('total_amount', '预估总金额', '#eb2f96')
        ]
        
        for key, label, color in stats_items:
            stat_label = QLabel(f"{label}: 0")
            stat_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-weight: bold;
                    font-size: 14px;
                    padding: 5px 10px;
                    background-color: white;
                    border: 1px solid {color};
                    border-radius: 4px;
                }}
            """)
            self.stats_labels[key] = stat_label
            stats_layout.addWidget(stat_label)
            
            # 添加分隔符（除了最后一个）
            if key != 'total_amount':
                separator = QFrame()
                separator.setFrameShape(QFrame.Shape.VLine)
                separator.setFrameShadow(QFrame.Shadow.Sunken)
                separator.setStyleSheet("color: #d9d9d9;")
                stats_layout.addWidget(separator)
        
        stats_layout.addStretch()
        parent_layout.addWidget(stats_frame)
    
    def load_data(self):
        """加载数据"""
        try:
            # 获取搜索条件
            filters = self.get_search_filters()
            
            # 查询数据
            result = self.demand_plan_db.get_demand_plans(
                filters=filters,
                page=self.current_page,
                page_size=self.page_size
            )
            
            # 更新表格
            self.update_table(result['data'])
            
            # 更新分页信息
            self.total_records = result['total']
            self.update_pagination_info()
            
            # 更新统计信息
            self.update_statistics(filters)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载数据失败: {str(e)}")
    
    def get_search_filters(self):
        """获取搜索条件"""
        filters = {}
        
        if self.plan_no_edit.text().strip():
            filters['plan_no'] = self.plan_no_edit.text().strip()
        
        if self.department_edit.currentText() != "全部":
            filters['department'] = self.department_edit.currentText()
        
        if self.applicant_edit.text().strip():
            filters['applicant'] = self.applicant_edit.text().strip()
        
        if self.demand_type_combo.currentText() != "全部":
            # 需求类型映射
            demand_type_map = {
                "生产需求": 1,
                "销售需求": 2,
                "库存补充": 3,
                "其他需求": 4
            }
            filters['demand_type'] = demand_type_map.get(self.demand_type_combo.currentText(), 1)
        
        if self.plan_status_combo.currentText() != "全部":
            # 状态映射
            status_map = {
                "草稿": 0,
                "已确认": 1,
                "执行中": 2,
                "已完成": 3,
                "已取消": 9
            }
            filters['plan_status'] = status_map.get(self.plan_status_combo.currentText(), 0)
        
        if self.priority_combo.currentText() != "全部":
            # 优先级映射
            priority_map = {
                "普通": 1,
                "紧急": 2,
                "特急": 3
            }
            filters['priority_level'] = priority_map.get(self.priority_combo.currentText(), 1)
        
        # 计划日期范围
        filters['plan_date_start'] = self.plan_date_start.date().toPyDate()
        filters['plan_date_end'] = self.plan_date_end.date().toPyDate()
        
        # 移除需求日期范围过滤（因为主表已无此字段）
        # 如果需要按需求日期过滤，应该通过明细表关联查询
        
        return filters
    
    def update_table(self, data):
        """更新表格数据"""
        self.table.setRowCount(len(data))
        
        for row, item in enumerate(data):
            # 数据列（去掉选择框列，索引从0开始）
            self.table.setItem(row, 0, QTableWidgetItem(str(item.get('plan_no', ''))))
            self.table.setItem(row, 1, QTableWidgetItem(str(item.get('plan_date', ''))))
            self.table.setItem(row, 2, QTableWidgetItem(str(item.get('plan_name', ''))))
            self.table.setItem(row, 3, QTableWidgetItem(str(item.get('department', ''))))
            self.table.setItem(row, 4, QTableWidgetItem(str(item.get('applicant', ''))))
            self.table.setItem(row, 5, QTableWidgetItem(str(item.get('demand_type_name', ''))))
            
            # 处理优先级列（索引6）并设置样式
            priority_level = item.get('priority_level', 1)  # 获取优先级数值（1-3）
            priority_name = item.get('priority_level_name', '')
            priority_item = QTableWidgetItem(priority_name)
            self.table.setItem(row, 6, priority_item)
            
            # 根据优先级设置背景色和文字颜色
            priority_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            font = QFont()
            
            if priority_level == 3:  # 特急
                priority_item.setBackground(QColor("#ffcccc"))  # 浅红色背景
                priority_item.setForeground(QColor("#cc0000"))  # 红色文字
                font.setBold(True)
            elif priority_level == 2:  # 紧急
                priority_item.setBackground(QColor("#fff2cc"))  # 浅黄色背景
                priority_item.setForeground(QColor("#e69900"))  # 橙色文字
                font.setBold(True)
            else:  # 普通（默认1）
                priority_item.setBackground(QColor("#d9ead3"))  # 浅绿色背景
                priority_item.setForeground(QColor("#38761d"))  # 深绿色文字
            
            priority_item.setFont(font)
            priority_item.setData(Qt.ItemDataRole.UserRole, priority_level)
            priority_item.setData(Qt.ItemDataRole.ToolTipRole, f"优先级: {priority_name}")
         
            # 处理状态列（索引8）并设置样式
            status_name = item.get('plan_status_name', '')  # 获取状态名称
            status_item = QTableWidgetItem(status_name)
            self.table.setItem(row, 7, status_item)
            
            # 根据状态设置背景色和文字颜色
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            status_font = QFont()
            
            if status_name == "草稿": 
                status_item.setBackground(QColor("#ffffff"))  # 白色背景
                status_item.setForeground(QColor("#666666"))  # 灰色文字
            elif status_name == "已确认":
                status_item.setBackground(QColor("#e6f7ff"))  # 浅蓝色背景
                status_item.setForeground(QColor("#1890ff"))  # 蓝色文字
                status_font.setBold(True)
            elif status_name == "执行中":
                status_item.setBackground(QColor("#fff7e6"))  # 浅橙色背景
                status_item.setForeground(QColor("#fa8c16"))  # 橙色文字
                status_font.setBold(True)
            elif status_name == "已完成":
                status_item.setBackground(QColor("#f6ffed"))  # 浅绿色背景
                status_item.setForeground(QColor("#52c41a"))  # 绿色文字
                status_font.setBold(True)
            else:  # 已取消或其他状态
                status_item.setBackground(QColor("#fff1f0"))  # 浅红色背景
                status_item.setForeground(QColor("#f5222d"))  # 红色文字
                status_font.setBold(True)
            
            status_item.setFont(status_font)
            status_item.setData(Qt.ItemDataRole.UserRole, item.get('plan_status', 0))
            status_item.setData(Qt.ItemDataRole.ToolTipRole, f"状态: {status_name}")
            
            self.table.setItem(row, 8, QTableWidgetItem(f"¥{item.get('total_amount', 0):.2f}"))
            self.table.setItem(row, 9, QTableWidgetItem(str(item.get('detail_count', 0))))
            self.table.setItem(row, 10, QTableWidgetItem("是" if item.get('is_locked') == 1 else "否"))
            self.table.setItem(row, 11, QTableWidgetItem(str(item.get('create_time', ''))))
            self.table.setItem(row, 12, QTableWidgetItem(str(item.get('create_user', ''))))
            
            # 存储行数据到第一列（计划编号列）
            self.table.item(row, 0).setData(Qt.ItemDataRole.UserRole, item)
    
    def create_action_buttons(self, row, item):
        """创建操作按钮"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)
        
        # 查看按钮
        view_btn = QPushButton("查看")
        view_btn.setMaximumSize(50, 25)
        view_btn.clicked.connect(lambda: self.view_plan(item))
        layout.addWidget(view_btn)
        
        # 编辑按钮（只有草稿状态可编辑）
        if item.get('plan_status') == 0:
            edit_btn = QPushButton("编辑")
            edit_btn.setMaximumSize(50, 25)
            edit_btn.clicked.connect(lambda: self.edit_plan(item))
            layout.addWidget(edit_btn)
        
        # 删除按钮（只有草稿状态可删除）
        if item.get('plan_status') == 0 and item.get('is_locked') != 1:
            delete_btn = QPushButton("删除")
            delete_btn.setMaximumSize(50, 25)
            delete_btn.setStyleSheet("QPushButton { color: red; }")
            delete_btn.clicked.connect(lambda: self.delete_plan(item))
            layout.addWidget(delete_btn)
        
        self.table.setCellWidget(row, 16, widget)
    
    def search_plans(self):
        """搜索需求计划"""
        self.current_page = 1
        self.load_data()
    
    def reset_search(self):
        """重置搜索条件"""
        self.plan_no_edit.clear()
        self.department_edit.setCurrentIndex(0)
        self.applicant_edit.clear()
        self.demand_type_combo.setCurrentIndex(0)
        self.plan_status_combo.setCurrentIndex(0)
        self.priority_combo.setCurrentIndex(0)
        self.plan_date_start.setDate(QDate.currentDate().addDays(-30))
        self.plan_date_end.setDate(QDate.currentDate())
        self.current_page = 1
        self.load_data()
    
    def add_plan(self):
        """新增需求计划"""
        try:
            dialog = DemandPlanDialog(self, mode='add')
            dialog.data_saved.connect(self.refresh_data)  # 连接保存成功信号
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开新增需求计划对话框失败：{str(e)}")
    
    def view_plan(self, item):
        """查看需求计划"""
        try:
            dialog = DemandPlanDialog(self, plan_id=item['plan_id'], mode='view')
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开查看需求计划对话框失败：{str(e)}")
    
    def edit_plan(self, item):
        """编辑需求计划"""
        try:
            dialog = DemandPlanDialog(self, plan_id=item['plan_id'], mode='edit')
            dialog.data_saved.connect(self.refresh_data)  # 连接保存成功信号
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开编辑需求计划对话框失败：{str(e)}")
    
    def copy_plan(self, item_data):
        """复制需求计划"""
        try:
            dialog = DemandPlanDialog(self, plan_id=item_data['plan_id'], mode='copy')
            dialog.data_saved.connect(self.refresh_data)  # 连接保存成功信号
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"复制需求计划失败: {str(e)}")
    
    def refresh_data(self):
        """刷新数据"""
        self.load_data()
    
    def delete_plan(self, item):
        """删除需求计划"""
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除需求计划 [{item['plan_no']}] 吗？\n删除后将无法恢复！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.demand_plan_db.delete_demand_plan(item['plan_id'])
                QMessageBox.information(self, "成功", "需求计划删除成功！")
                self.refresh_data()  # 删除成功后刷新数据
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除失败: {str(e)}")
    
    def batch_delete_plans(self):
        """批量删除需求计划"""
        selected_items = self.get_selected_items()
        if not selected_items:
            QMessageBox.warning(self, "提示", "请先选择要删除的记录！")
            return
        
        # 检查是否都可以删除
        cannot_delete = []
        for item in selected_items:
            if item.get('plan_status') != 0 or item.get('is_locked') == 1:
                cannot_delete.append(item['plan_no'])
        
        if cannot_delete:
            QMessageBox.warning(
                self, "提示",
                f"以下计划不能删除（非草稿状态或已锁定）：\n{', '.join(cannot_delete[:5])}" +
                (f"\n... 还有{len(cannot_delete)-5}项" if len(cannot_delete) > 5 else "")
            )
            return
        
        reply = QMessageBox.question(
            self, "确认批量删除",
            f"确定要删除选中的 {len(selected_items)} 个需求计划吗？\n删除后将无法恢复！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            success_count = 0
            error_count = 0
            
            for item in selected_items:
                try:
                    self.demand_plan_db.delete_demand_plan(item['plan_id'])
                    success_count += 1
                except Exception as e:
                    error_count += 1
                    print(f"删除计划 {item['plan_no']} 失败: {str(e)}")
            
            if success_count > 0:
                QMessageBox.information(
                    self, "批量删除结果",
                    f"成功删除 {success_count} 个计划" +
                    (f"，失败 {error_count} 个" if error_count > 0 else "")
                )
                self.load_data()
    
    def export_to_excel(self):
        """导出到Excel"""
        try:
            # 获取当前搜索条件的所有数据
            filters = self.get_search_filters()
            result = self.demand_plan_db.get_demand_plans(
                filters=filters,
                page=1,
                page_size=10000  # 导出所有数据
            )
            
            if not result['data']:
                QMessageBox.warning(self, "提示", "没有数据可导出！")
                return
            
            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出需求计划",
                f"需求计划_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )
            
            if file_path:
                # 准备导出数据
                export_data = []
                for item in result['data']:
                    export_data.append({
                        '计划编号': item.get('plan_no', ''),
                        '计划日期': item.get('plan_date', ''),
                        '计划名称': item.get('plan_name', ''),
                        '申请部门': item.get('department', ''),
                        '申请人': item.get('applicant', ''),
                        '需求类型': item.get('demand_type_name', ''),
                        '优先级': item.get('priority_level_name', ''),
                        '计划状态': item.get('plan_status_name', ''),
                        '预估金额': item.get('total_amount', 0),
                        '明细数量': item.get('detail_count', 0),
                        '是否锁定': '是' if item.get('is_locked') == 1 else '否',
                        '备注': item.get('remark', ''),
                        '创建时间': item.get('create_time', ''),
                        '创建人': item.get('create_user', '')
                    })
                
                # 导出到Excel
                df = pd.DataFrame(export_data)
                df.to_excel(file_path, index=False, engine='openpyxl')
                
                QMessageBox.information(self, "成功", f"数据已导出到：\n{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    def get_selected_items(self):
        """获取选中的项目（通过表格多选）"""
        selected_items = []
        selected_rows = set()
        
        # 获取选中的项目
        for item in self.table.selectedItems():
            row = item.row()
            if row not in selected_rows:
                selected_rows.add(row)
                # 从第一列获取存储的数据
                item_data = self.table.item(row, 0).data(Qt.ItemDataRole.UserRole)
                if item_data:
                    selected_items.append(item_data)
        
        return selected_items
    
    def on_selection_changed(self):
        """选择变化事件"""
        selected_items = self.get_selected_items()
        selected_count = len(selected_items)
        
        # # 更新选择信息
        # self.selected_label.setText(f"已选中: {selected_count} 项")
        
        # 更新按钮状态
        has_selection = selected_count > 0
        single_selection = selected_count == 1
        
        # 基础操作按钮
        self.edit_btn.setEnabled(single_selection)
        self.batch_delete_btn.setEnabled(has_selection)
        
        # 状态操作按钮
        self.confirm_btn.setEnabled(has_selection)
        self.start_execute_btn.setEnabled(has_selection)
        self.complete_btn.setEnabled(has_selection)
        self.cancel_btn.setEnabled(has_selection)
        
        # 生成操作按钮
        self.generate_process_btn.setEnabled(has_selection)
        self.generate_purchase_btn.setEnabled(has_selection)
    
    def on_cell_double_clicked(self, row, column):
        """双击单元格事件"""
        if column != 0 and column != 16:  # 不是选择框和操作列
            item_data = self.table.item(row, 1).data(Qt.ItemDataRole.UserRole)
            if item_data:
                self.view_plan(item_data)
    
    def update_pagination_info(self):
        """更新分页信息"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        total_pages = max(1, total_pages)
        
        self.page_label.setText(f"第 {self.current_page} 页，共 {total_pages} 页")
        self.total_label.setText(f"总记录数: {self.total_records}")
        
        # 更新按钮状态
        self.first_btn.setEnabled(self.current_page > 1)
        self.prev_btn.setEnabled(self.current_page > 1)
        self.next_btn.setEnabled(self.current_page < total_pages)
        self.last_btn.setEnabled(self.current_page < total_pages)
    
    def update_statistics(self, filters):
        """更新统计信息"""
        try:
            stats = self.demand_plan_db.get_demand_statistics(filters)
            
            self.stats_labels['total'].setText(f"总计划: {stats.get('total_plans', 0)}")
            self.stats_labels['draft'].setText(f"草稿: {stats.get('draft_count', 0)}")
            self.stats_labels['confirmed'].setText(f"已确认: {stats.get('confirmed_count', 0)}")
            self.stats_labels['executing'].setText(f"执行中: {stats.get('executing_count', 0)}")
            self.stats_labels['completed'].setText(f"已完成: {stats.get('completed_count', 0)}")
            self.stats_labels['cancelled'].setText(f"已取消: {stats.get('cancelled_count', 0)}")
            self.stats_labels['total_amount'].setText(f"预估总金额: ¥{stats.get('total_amount', 0):.2f}")
            
        except Exception as e:
            print(f"更新统计信息失败: {str(e)}")
    
    def on_page_size_changed(self, text):
        """每页记录数变化"""
        self.page_size = int(text)
        self.current_page = 1
        self.load_data()
    
    def first_page(self):
        """首页"""
        self.current_page = 1
        self.load_data()
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_data()
    
    def next_page(self):
        """下一页"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        if self.current_page < total_pages:
            self.current_page += 1
            self.load_data()
    
    def last_page(self):
        """末页"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        self.current_page = max(1, total_pages)
        self.load_data()
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.table.itemAt(position) is None:
            return
        
        current_row = self.table.rowAt(position.y())
        if current_row < 0:
            return
        
        # 获取选中的计划数据
        item_data = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
        if not item_data:
            return
        
        menu = QMenu(self)
        
        # 查看操作
        view_action = menu.addAction("👁️ 查看需求计划")
        view_action.triggered.connect(lambda: self.view_plan(item_data))
        
        # 编辑操作（只有草稿状态可编辑）
        if item_data.get('plan_status') == 0:
            edit_action = menu.addAction("✏️ 编辑需求计划")
            edit_action.triggered.connect(lambda: self.edit_plan(item_data))
        
        menu.addSeparator()
        
        # 复制操作
        copy_action = menu.addAction("📋 复制需求计划")
        copy_action.triggered.connect(lambda: self.copy_plan(item_data))
        
        menu.addSeparator()
        
        # 删除操作（只有草稿状态且未锁定可删除）
        if item_data.get('plan_status') == 0 and item_data.get('is_locked') != 1:
            delete_action = menu.addAction("🗑️ 删除需求计划")
            delete_action.triggered.connect(lambda: self.delete_plan(item_data))
        
        menu.exec(self.table.mapToGlobal(position))
    
    def get_selected_plan_data(self):
        """获取选中的计划数据"""
        current_row = self.table.currentRow()
        if current_row < 0:
            return None
        
        item = self.table.item(current_row, 0)
        if item:
            return item.data(Qt.ItemDataRole.UserRole)
        return None
    
    def edit_selected_plan(self):
        """编辑选中的需求计划"""
        selected_items = self.get_selected_items()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请选择要编辑的需求计划")
            return
        
        if len(selected_items) > 1:
            QMessageBox.warning(self, "警告", "只能选择一个需求计划进行编辑")
            return
        
        item_data = selected_items[0]
        
        if item_data.get('plan_status') != 0:
            QMessageBox.warning(self, "警告", "只有草稿状态的需求计划才能编辑")
            return
        
        try:
            dialog = DemandPlanDialog(self, plan_id=item_data['plan_id'], mode='edit')
            dialog.data_saved.connect(self.refresh_data)  # 连接保存成功信号
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开编辑需求计划对话框失败：{str(e)}")
    
    def view_selected_plan(self):
        """查看选中的需求计划"""
        current_row = self.table.currentRow()
        if current_row < 0:
            return
        
        item_data = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
        if not item_data:
            return
        
        try:
            dialog = DemandPlanDialog(self, plan_id=item_data['plan_id'], mode='view')
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开查看需求计划对话框失败：{str(e)}")
    
    def delete_selected_plan(self):
        """删除选中的需求计划"""
        item_data = self.get_selected_plan_data()
        if not item_data:
            QMessageBox.warning(self, "警告", "请选择要删除的需求计划")
            return
        
        if item_data.get('plan_status') != 0:
            QMessageBox.warning(self, "警告", "只有草稿状态的需求计划才能删除")
            return
        
        if item_data.get('is_locked') == 1:
            QMessageBox.warning(self, "警告", "已锁定的需求计划不能删除")
            return
        
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除需求计划 [{item_data['plan_no']}] 吗？\n删除后将无法恢复！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.demand_plan_db.delete_demand_plan(item_data['plan_id'])
                QMessageBox.information(self, "成功", "需求计划删除成功！")
                self.refresh_data()  # 删除成功后刷新数据
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除失败: {str(e)}")
    
    def copy_plan(self, item_data):
        """复制需求计划"""
        try:
            dialog = DemandPlanDialog(self, plan_id=item_data['plan_id'], mode='copy')
            dialog.data_saved.connect(self.refresh_data)  # 连接保存成功信号
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"复制需求计划失败: {str(e)}")

    def batch_update_status(self, new_status):
        """批量更新状态"""
        selected_items = self.get_selected_items()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请选择要操作的需求计划")
            return
        
        status_names = {
            1: "确认",
            2: "开始执行", 
            3: "完成",
            9: "取消"
        }
        
        status_name = status_names.get(new_status, "更新")
        
        # 检查状态转换的合理性
        invalid_items = []
        for item in selected_items:
            current_status = item.get('plan_status', 0)
            if not self.is_valid_status_transition(current_status, new_status):
                invalid_items.append(f"{item['plan_no']}(当前状态: {item.get('plan_status_name', '未知')})")
        
        if invalid_items:
            QMessageBox.warning(
                self, "状态转换错误",
                f"以下计划无法{status_name}：\n" + "\n".join(invalid_items[:5]) +
                (f"\n... 还有{len(invalid_items)-5}项" if len(invalid_items) > 5 else "")
            )
            return
        
        reply = QMessageBox.question(
            self, f"确认{status_name}",
            f"确定要{status_name}选中的 {len(selected_items)} 个需求计划吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            success_count = 0
            error_count = 0
            
            for item in selected_items:
                try:
                    self.demand_plan_db.update_plan_status(item['plan_id'], new_status)
                    success_count += 1
                except Exception as e:
                    error_count += 1
                    print(f"更新计划 {item['plan_no']} 状态失败: {str(e)}")
            
            if success_count > 0:
                # 如果状态是"已完成"，添加锁定提示信息
                lock_message = ""
                if new_status == 3:
                    lock_message = "\n\n注意：已完成的需求计划已被自动锁定，不能再修改或删除。"
                
                QMessageBox.information(
                    self, f"批量{status_name}结果",
                    f"成功{status_name} {success_count} 个计划" +
                    (f"，失败 {error_count} 个" if error_count > 0 else "") +
                    lock_message
                )
                self.refresh_data()
    
    def is_valid_status_transition(self, current_status, new_status):
        """检查状态转换是否合理"""
        # 状态转换规则
        valid_transitions = {
            0: [1, 9],      # 草稿 -> 确认、取消
            1: [2, 9],      # 确认 -> 执行、取消
            2: [3, 9],      # 执行 -> 完成、取消
            3: [],          # 完成 -> 无
            9: []           # 取消 -> 无
        }
        
        return new_status in valid_transitions.get(current_status, [])
    
    def generate_process_sheet(self):
        """生成加工表"""
        selected_items = self.get_selected_items()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请选择要生成加工表的需求计划")
            return
        
        # TODO: 实现生成加工表逻辑
        QMessageBox.information(
            self, "功能开发中",
            f"生成加工表功能开发中...\n已选择 {len(selected_items)} 个需求计划"
        )
    
    def generate_purchase_request(self):
        """生成采购申请"""
        selected_items = self.get_selected_items()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请选择要生成采购申请的需求计划")
            return
        
        # TODO: 实现生成采购申请逻辑
        QMessageBox.information(
            self, "功能开发中", 
            f"生成采购申请功能开发中...\n已选择 {len(selected_items)} 个需求计划"
        )
    
    def delete_selected_plans(self):
        """删除选中的需求计划"""
        selected_items = self.get_selected_items()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请选择要删除的需求计划")
            return
        
        # 检查是否都可以删除
        cannot_delete = []
        for item in selected_items:
            if item.get('plan_status') != 0 or item.get('is_locked') == 1:
                cannot_delete.append(item['plan_no'])
        
        if cannot_delete:
            QMessageBox.warning(
                self, "提示",
                f"以下计划不能删除（非草稿状态或已锁定）：\n{', '.join(cannot_delete[:5])}" +
                (f"\n... 还有{len(cannot_delete)-5}项" if len(cannot_delete) > 5 else "")
            )
            return
        
        reply = QMessageBox.question(
            self, "确认批量删除",
            f"确定要删除选中的 {len(selected_items)} 个需求计划吗？\n删除后将无法恢复！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            success_count = 0
            error_count = 0
            
            for item in selected_items:
                try:
                    self.demand_plan_db.delete_demand_plan(item['plan_id'])
                    success_count += 1
                except Exception as e:
                    error_count += 1
                    print(f"删除计划 {item['plan_no']} 失败: {str(e)}")
            
            if success_count > 0:
                QMessageBox.information(
                    self, "批量删除结果",
                    f"成功删除 {success_count} 个计划" +
                    (f"，失败 {error_count} 个" if error_count > 0 else "")
                )
                self.refresh_data()
    
    def start_execute_plans(self):
        """开始执行需求计划（BOM展开确认流程）"""
        selected_items = self.get_selected_items()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请选择要执行的需求计划")
            return
        
        # 检查状态转换的合理性
        invalid_items = []
        valid_items = []
        
        for item in selected_items:
            current_status = item.get('plan_status', 0)
            if current_status != 1:  # 只有确认状态的计划才能执行
                invalid_items.append(f"{item['plan_no']}(当前状态: {item.get('plan_status_name', '未知')})")
            else:
                valid_items.append(item)
        
        if invalid_items:
            QMessageBox.warning(
                self, "状态转换错误",
                f"以下计划无法执行（必须是确认状态）：\n" + "\n".join(invalid_items[:5]) +
                (f"\n... 还有{len(invalid_items)-5}项" if len(invalid_items) > 5 else "")
            )
            return
        
        if not valid_items:
            QMessageBox.warning(self, "警告", "没有可执行的需求计划")
            return
        
        try:
            # 打开BOM展开确认对话框
            from demand.bom_expand_confirm_dialog import BOMExpandConfirmDialog
            
            dialog = BOMExpandConfirmDialog(self, selected_plans=valid_items)
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # 用户确认后，执行实际的BOM展开和状态更新
                self.execute_bom_expand_and_update_status(valid_items)
            
        except ImportError as e:
            QMessageBox.critical(self, "错误", f"无法加载BOM展开确认对话框: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开BOM展开确认对话框失败: {str(e)}")
    
    def execute_bom_expand_and_update_status(self, selected_items):
        """执行BOM展开和状态更新"""
        try:
            # 创建进度对话框
            progress = QProgressDialog("正在执行BOM展开...", "取消", 0, len(selected_items), self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.setAutoClose(True)
            progress.show()
            
            success_count = 0
            error_count = 0
            error_messages = []
            
            for i, item in enumerate(selected_items):
                if progress.wasCanceled():
                    break
                
                progress.setValue(i)
                progress.setLabelText(f"正在处理计划: {item['plan_no']}")
                
                try:
                    # 执行需求计划（包含BOM展开和执行计划生成）
                    result = self.demand_plan_db.execute_demand_plan(item['plan_id'])
                    
                    if result.get('success'):
                        success_count += 1
                        Logger.log_info(f"需求计划执行成功: {item['plan_no']}")
                    else:
                        error_count += 1
                        error_msg = result.get('message', '未知错误')
                        error_messages.append(f"{item['plan_no']}: {error_msg}")
                        Logger.log_error(f"需求计划执行失败: {item['plan_no']}, 错误: {error_msg}")
                        
                except Exception as e:
                    error_count += 1
                    error_msg = str(e)
                    error_messages.append(f"{item['plan_no']}: {error_msg}")
                    Logger.log_error(f"需求计划执行异常: {item['plan_no']}, 错误: {error_msg}")
            
            progress.setValue(len(selected_items))
            progress.close()
            
            # 显示执行结果
            if success_count > 0:
                result_message = f"成功执行 {success_count} 个需求计划"
                if error_count > 0:
                    result_message += f"，失败 {error_count} 个"
                    
                    # 显示详细错误信息
                    if error_messages:
                        detail_dialog = QMessageBox(self)
                        detail_dialog.setWindowTitle("执行结果详情")
                        detail_dialog.setText(result_message)
                        detail_dialog.setDetailedText("失败详情：\n" + "\n".join(error_messages))
                        detail_dialog.setIcon(QMessageBox.Icon.Information)
                        detail_dialog.exec()
                    else:
                        QMessageBox.information(self, "执行结果", result_message)
                else:
                    QMessageBox.information(self, "执行成功", result_message)
                
                # 刷新数据
                self.refresh_data()
            else:
                # 全部失败
                error_detail = "\n".join(error_messages[:10])  # 最多显示10个错误
                if len(error_messages) > 10:
                    error_detail += f"\n... 还有 {len(error_messages) - 10} 个错误"
                
                QMessageBox.critical(
                    self, "执行失败", 
                    f"所有需求计划执行失败：\n{error_detail}"
                )
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"执行BOM展开失败: {str(e)}")




