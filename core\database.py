"""
数据库连接管理模块 - 单例模式实现数据库连接池
"""
import mysql.connector
from .config import Config
from .logger import Logger
from .exceptions import DatabaseError
import time

class DatabasePool:
    _instance = None
    _connection = None
    _last_ping = None
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        if DatabasePool._connection is None:
            self._init_connection()
    
    def _init_connection(self):
        """初始化数据库连接"""
        try:
            Logger.log_db_connection("正在初始化数据库连接...")
            dbconfig = self._get_db_config()
            DatabasePool._connection = mysql.connector.connect(**dbconfig)
            DatabasePool._last_ping = time.time()
            Logger.log_db_connection("数据库连接初始化成功", extra={
                'host': dbconfig['host'],
                'database': dbconfig['database']
            })
        except Exception as e:
            error_msg = f"数据库连接初始化失败: {str(e)}"
            Logger.log_db_connection(error_msg, 'error', extra={
                'error_type': type(e).__name__,
                'error_details': str(e)
            })
            raise DatabaseError(error_msg)
    
    def _get_db_config(self):
        """获取数据库配置"""
        dbconfig = Config.DB_CONFIG.copy()
        # 移除不支持的配置项
        for key in ['pool_name', 'pool_size', 'pool_reset_session']:
            dbconfig.pop(key, None)
        return dbconfig
    
    def _should_ping(self):
        """检查是否需要ping服务器"""
        if not self._last_ping:
            return True
        current_idle_time = time.time() - self._last_ping
        
        # 记录空闲时间
        if current_idle_time > 60:  # 空闲超过1分钟就记录日志
            Logger.log_db_connection(
                f"数据库连接空闲时间: {current_idle_time}秒",
                extra={'idle_time': current_idle_time}
            )
        
        return current_idle_time >= Config.DB_CONNECTION_CONFIG['ping_interval']
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            if self._connection and self._connection.is_connected():
                if self._should_ping():
                    self._ping_connection()
                return self._connection
            
            Logger.log_db_connection("重新建立数据库连接")
            self._init_connection()
            return self._connection
            
        except Exception as e:
            error_msg = f"获取数据库连接失败: {str(e)}"
            Logger.log_db_connection(error_msg, 'error', extra={
                'error_type': type(e).__name__,
                'connection_age': time.time() - (self._last_ping or 0)
            })
            raise DatabaseError(error_msg)
    
    def _ping_connection(self):
        """Ping数据库连接"""
        try:
            if not self._connection:
                self._init_connection()
                return
            
            self._connection.ping(reconnect=True, attempts=3, delay=0)
            self._last_ping = time.time()
            Logger.log_db_connection("Ping数据库成功")
        except Exception as e:
            Logger.log_db_connection(
                f"Ping失败: {str(e)}", 
                'warning',
                extra={'error_type': type(e).__name__}
            )
            # 记录当前连接状态
            if self._connection:
                try:
                    is_connected = self._connection.is_connected()
                    Logger.log_db_connection(
                        f"当前连接状态: {'已连接' if is_connected else '已断开'}"
                    )
                except:
                    pass
            self._connection = None
    
    def close_all_connections(self):
        """关闭所有连接"""
        if self._connection:
            try:
                self._connection.close()
                Logger.log_db_connection("数据库连接已关闭")
            except Exception as e:
                Logger.log_db_connection(f"关闭数据库连接失败: {str(e)}", 'error')


class Database:
    """
    数据库操作类，作为DatabasePool的代理类
    提供简化的数据库操作接口
    """
    
    def __init__(self):
        self.pool = DatabasePool.get_instance()
    
    def get_connection(self):
        """获取数据库连接"""
        return self.pool.get_connection()
    
    def close_all_connections(self):
        """关闭所有连接"""
        self.pool.close_all_connections()
    
    # def cursor(self, dictionary=True):
    #     """获取数据库游标的上下文管理器"""
    #     class CursorContextManager:
    #         def __init__(self, db):
    #             self.db = db
    #             self.conn = None
    #             self.cursor = None
                
    #         def __enter__(self):
    #             self.conn = self.db.get_connection()
    #             self.cursor = self.conn.cursor(dictionary=dictionary)
    #             return self.cursor
                
    #         def __exit__(self, exc_type, exc_val, exc_tb):
    #             try:
    #                 if exc_type is None:
    #                     self.conn.commit()
    #                 else:
    #                     self.conn.rollback()
    #             finally:
    #                 if self.cursor:
    #                     self.cursor.close()
    #                 if self.conn:
    #                     self.conn.close()
                
    #     return CursorContextManager(self)
    

