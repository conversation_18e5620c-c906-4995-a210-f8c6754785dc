-- =====================================================
-- 库存管理模块数据库表设计
-- =====================================================

-- ----------------------------
-- 期初库存表 (inventory_initial)
-- ----------------------------
DROP TABLE IF EXISTS `inventory_initial`;
CREATE TABLE `inventory_initial` (
  `initial_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '期初库存主键',
  `material_id` varchar(50) NOT NULL COMMENT '物料编码',
  `material_name` varchar(200) NULL DEFAULT NULL COMMENT '物料名称（冗余字段）',
  `material_category_id` int(11) NULL DEFAULT NULL COMMENT '物料分类ID',
  `department` varchar(100) NOT NULL COMMENT '归属部门（如：通路一、通路二等）',
  `warehouse_code` varchar(50) NULL DEFAULT NULL COMMENT '仓库编码',
  `warehouse_name` varchar(100) NULL DEFAULT NULL COMMENT '仓库名称',
  `location_code` varchar(50) NULL DEFAULT NULL COMMENT '库位编码',
  `initial_qty` decimal(18,4) NOT NULL DEFAULT 0.0000 COMMENT '期初数量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `unit_cost` decimal(10,4) NULL DEFAULT 0.0000 COMMENT '单位成本',
  `total_cost` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '总成本',
  `batch_no` varchar(50) NULL DEFAULT NULL COMMENT '批次号',
  `production_date` date NULL DEFAULT NULL COMMENT '生产日期',
  `expiry_date` date NULL DEFAULT NULL COMMENT '过期日期',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-冻结，3-报废',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(50) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(50) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`initial_id`) USING BTREE,
  UNIQUE INDEX `uk_material_dept_warehouse`(`material_id`, `department`, `warehouse_code`, `batch_no`) USING BTREE,
  INDEX `idx_material_id`(`material_id`) USING BTREE,
  INDEX `idx_department`(`department`) USING BTREE,
  INDEX `idx_warehouse`(`warehouse_code`) USING BTREE,
  CONSTRAINT `fk_initial_material` FOREIGN KEY (`material_id`) REFERENCES `materials` (`material_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '期初库存表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- 库存流水表 (inventory_transactions)
-- ----------------------------
DROP TABLE IF EXISTS `inventory_transactions`;
CREATE TABLE `inventory_transactions` (
  `transaction_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '流水主键',
  `transaction_no` varchar(50) NOT NULL COMMENT '流水单号（自动生成）',
  `transaction_date` date NOT NULL COMMENT '业务日期',
  `transaction_type` tinyint(4) NOT NULL COMMENT '业务类型：1-期初，2-采购入库，3-生产领料，4-生产入库，5-销售出库，6-调拨，7-盘点，8-报废，9-其他',
  `material_id` varchar(50) NOT NULL COMMENT '物料编码',
  `material_name` varchar(200) NULL DEFAULT NULL COMMENT '物料名称（冗余字段）',
  `material_category_id` int(11) NULL DEFAULT NULL COMMENT '物料分类ID',
  `department` varchar(100) NOT NULL COMMENT '归属部门',
  `warehouse_code` varchar(50) NULL DEFAULT NULL COMMENT '仓库编码',
  `warehouse_name` varchar(100) NULL DEFAULT NULL COMMENT '仓库名称',
  `location_code` varchar(50) NULL DEFAULT NULL COMMENT '库位编码',
  `batch_no` varchar(50) NULL DEFAULT NULL COMMENT '批次号',
  `in_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '入库数量',
  `out_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '出库数量',
  `balance_qty` decimal(18,4) NOT NULL COMMENT '结存数量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `unit_cost` decimal(10,4) NULL DEFAULT 0.0000 COMMENT '单位成本',
  `total_cost` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '总成本',
  `source_type` varchar(50) NULL DEFAULT NULL COMMENT '来源类型（purchase_order, production_plan, demand_plan等）',
  `source_no` varchar(50) NULL DEFAULT NULL COMMENT '来源单号',
  `source_id` int(11) NULL DEFAULT NULL COMMENT '来源单据ID',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(50) NULL DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`transaction_id`) USING BTREE,
  UNIQUE INDEX `uk_transaction_no`(`transaction_no`) USING BTREE,
  INDEX `idx_material_dept`(`material_id`, `department`) USING BTREE,
  INDEX `idx_transaction_date`(`transaction_date`) USING BTREE,
  INDEX `idx_transaction_type`(`transaction_type`) USING BTREE,
  INDEX `idx_source`(`source_type`, `source_no`) USING BTREE,
  CONSTRAINT `fk_transaction_material` FOREIGN KEY (`material_id`) REFERENCES `materials` (`material_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '库存流水表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- 部门库存归属表 (department_inventory)
-- ----------------------------
DROP TABLE IF EXISTS `department_inventory`;
CREATE TABLE `department_inventory` (
  `dept_inventory_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '部门库存主键',
  `material_id` varchar(50) NOT NULL COMMENT '物料编码',
  `material_name` varchar(200) NULL DEFAULT NULL COMMENT '物料名称（冗余字段）',
  `material_category_id` int(11) NULL DEFAULT NULL COMMENT '物料分类ID',
  `department` varchar(100) NOT NULL COMMENT '归属部门',
  `warehouse_code` varchar(50) NULL DEFAULT NULL COMMENT '仓库编码',
  `warehouse_name` varchar(100) NULL DEFAULT NULL COMMENT '仓库名称',
  `total_qty` decimal(18,4) NOT NULL DEFAULT 0.0000 COMMENT '总库存数量',
  `available_qty` decimal(18,4) NOT NULL DEFAULT 0.0000 COMMENT '可用数量（总数量-预留数量）',
  `reserved_qty` decimal(18,4) NOT NULL DEFAULT 0.0000 COMMENT '预留数量（已分配给生产计划但未扣减）',
  `frozen_qty` decimal(18,4) NOT NULL DEFAULT 0.0000 COMMENT '冻结数量（质量问题等）',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `avg_unit_cost` decimal(10,4) NULL DEFAULT 0.0000 COMMENT '平均单位成本',
  `total_cost` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '总成本',
  `min_stock_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '最小库存量（安全库存）',
  `max_stock_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '最大库存量',
  `last_in_date` date NULL DEFAULT NULL COMMENT '最后入库日期',
  `last_out_date` date NULL DEFAULT NULL COMMENT '最后出库日期',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(50) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`dept_inventory_id`) USING BTREE,
  UNIQUE INDEX `uk_material_dept_warehouse`(`material_id`, `department`, `warehouse_code`) USING BTREE,
  INDEX `idx_material_id`(`material_id`) USING BTREE,
  INDEX `idx_department`(`department`) USING BTREE,
  INDEX `idx_available_qty`(`available_qty`) USING BTREE,
  CONSTRAINT `fk_dept_inventory_material` FOREIGN KEY (`material_id`) REFERENCES `materials` (`material_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门库存归属表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- 库存预留记录表 (inventory_reservations)
-- ----------------------------
DROP TABLE IF EXISTS `inventory_reservations`;
CREATE TABLE `inventory_reservations` (
  `reservation_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '预留记录主键',
  `reservation_no` varchar(50) NOT NULL COMMENT '预留单号',
  `material_id` varchar(50) NOT NULL COMMENT '物料编码',
  `department` varchar(100) NOT NULL COMMENT '归属部门',
  `warehouse_code` varchar(50) NULL DEFAULT NULL COMMENT '仓库编码',
  `reserved_qty` decimal(18,4) NOT NULL COMMENT '预留数量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `source_type` varchar(50) NOT NULL COMMENT '预留来源类型（production_plan, demand_plan等）',
  `source_no` varchar(50) NOT NULL COMMENT '来源单号',
  `source_id` int(11) NOT NULL COMMENT '来源单据ID',
  `reservation_date` date NOT NULL COMMENT '预留日期',
  `expected_use_date` date NULL DEFAULT NULL COMMENT '预计使用日期',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-已预留，2-已使用，3-已取消',
  `used_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '已使用数量',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(50) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(50) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`reservation_id`) USING BTREE,
  UNIQUE INDEX `uk_reservation_no`(`reservation_no`) USING BTREE,
  INDEX `idx_material_dept`(`material_id`, `department`) USING BTREE,
  INDEX `idx_source`(`source_type`, `source_no`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  CONSTRAINT `fk_reservation_material` FOREIGN KEY (`material_id`) REFERENCES `materials` (`material_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '库存预留记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- 库存可用量视图 (view_inventory_available)
-- ----------------------------
DROP VIEW IF EXISTS `view_inventory_available`;
CREATE VIEW `view_inventory_available` AS
SELECT 
    di.material_id,
    di.material_name,
    di.material_category_id,
    di.department,
    di.warehouse_code,
    di.warehouse_name,
    di.total_qty,
    di.available_qty,
    di.reserved_qty,
    di.frozen_qty,
    di.unit,
    di.avg_unit_cost,
    di.total_cost,
    di.min_stock_qty,
    di.max_stock_qty,
    -- 库存状态判断
    CASE 
        WHEN di.available_qty <= 0 THEN '缺货'
        WHEN di.available_qty <= di.min_stock_qty THEN '库存不足'
        WHEN di.available_qty >= di.max_stock_qty THEN '库存过量'
        ELSE '正常'
    END as stock_status,
    -- 可用天数（基于平均消耗）
    CASE 
        WHEN avg_daily_out.avg_out_qty > 0 THEN 
            ROUND(di.available_qty / avg_daily_out.avg_out_qty, 1)
        ELSE NULL
    END as available_days,
    di.last_in_date,
    di.last_out_date,
    di.update_time
FROM department_inventory di
LEFT JOIN (
    SELECT 
        material_id, 
        department,
        warehouse_code,
        AVG(out_qty) as avg_out_qty
    FROM inventory_transactions 
    WHERE transaction_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        AND out_qty > 0
    GROUP BY material_id, department, warehouse_code
) avg_daily_out ON di.material_id = avg_daily_out.material_id 
    AND di.department = avg_daily_out.department
    AND di.warehouse_code = avg_daily_out.warehouse_code;

-- ----------------------------
-- 自动编号规则数据 - 库存相关
-- ----------------------------
INSERT INTO `auto_number_rule` (`rule_code`, `rule_name`, `prefix`, `date_format`, `sequence_length`, `description`, `status`) VALUES
('KCLS', '库存流水单号', 'KCLS', 'YYYYMMDD', 4, '库存流水单号：KCLS-YYYYMMDD-0001', 1),
('KCYL', '库存预留单号', 'KCYL', 'YYYYMMDD', 4, '库存预留单号：KCYL-YYYYMMDD-0001', 1),
('CGDD', '采购订单号', 'CGDD', 'YYYYMMDD', 4, '采购订单号：CGDD-YYYYMMDD-0001', 1);


INSERT INTO `permission` (permission_name, permission_code, permission_type, parent_id, menu_icon, menu_path, menu_component, menu_sort, status) VALUES
('库存管理', 'inventory:view', 1, 0, 'inventory', '/inventory', 'inventory/InventoryModule', 50, 1);

-- 先获取库存管理的permission_id
SET @inventory_view_id = (SELECT permission_id FROM `permission` WHERE permission_code = 'inventory:view');

-- 插入子菜单
INSERT INTO `permission` (permission_name, permission_code, permission_type, parent_id, menu_icon, menu_path, menu_component, menu_sort, status) VALUES
('库存总览', 'inventory:overview', 1, @inventory_view_id, 'dashboard', '/inventory/overview', 'inventory/OverviewModule', 10, 1),
('期初库存', 'inventory:initial', 1, @inventory_view_id, 'edit', '/inventory/initial', 'inventory/InitialModule', 20, 1),
('库存调拨', 'inventory:transfer', 1, @inventory_view_id, 'swap', '/inventory/transfer', 'inventory/TransferModule', 30, 1),
('库存盘点', 'inventory:count', 1, @inventory_view_id, 'calculator', '/inventory/count', 'inventory/CountModule', 40, 1),
('库存报表', 'inventory:report', 1, @inventory_view_id, 'bar-chart', '/inventory/report', 'inventory/ReportModule', 50, 1),
('库存管理', 'inventory:manage', 2, @inventory_view_id, NULL, NULL, NULL, 0, 1),
('库存冻结', 'inventory:freeze', 2, @inventory_view_id, NULL, NULL, NULL, 0, 1),
('库存预留', 'inventory:reserve', 2, @inventory_view_id, NULL, NULL, NULL, 0, 1),
('库存消耗', 'inventory:consume', 2, @inventory_view_id, NULL, NULL, NULL, 0, 1),
('库存导出', 'inventory:export', 2, @inventory_view_id, NULL, NULL, NULL, 0, 1),
('部门库存数据', 'inventory:dept:data', 3, @inventory_view_id, NULL, NULL, NULL, 0, 1),
('仓库数据权限', 'inventory:warehouse:data', 3, @inventory_view_id, NULL, NULL, NULL, 0, 1);