"""
Excel格式美化工具
用于美化BOM导出的Excel文件
"""
from openpyxl import load_workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter

class ExcelFormatter:
    """Excel格式美化器"""
    
    def __init__(self, file_path):
        self.file_path = file_path
        self.workbook = load_workbook(file_path)
    
    def format_bom_excel(self):
        """格式化BOM Excel文件"""
        try:
            for sheet_name in self.workbook.sheetnames:
                worksheet = self.workbook[sheet_name]
                self.format_worksheet(worksheet, sheet_name)
            
            self.workbook.save(self.file_path)
            return True
            
        except Exception as e:
            print(f"Excel格式化失败: {e}")
            return False
    
    def format_worksheet(self, worksheet, sheet_name):
        """格式化工作表"""
        if worksheet.max_row <= 1:  # 空工作表
            return
        
        # 设置标题行样式
        self.format_header_row(worksheet)
        
        # 设置数据行样式
        self.format_data_rows(worksheet)
        
        # 自动调整列宽
        self.auto_adjust_column_width(worksheet)
        
        # 添加边框
        self.add_borders(worksheet)
        
        # 冻结首行
        worksheet.freeze_panes = 'A2'
    
    def format_header_row(self, worksheet):
        """格式化标题行"""
        header_font = Font(name='微软雅黑', size=11, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
        header_alignment = Alignment(horizontal='center', vertical='center')
        
        for cell in worksheet[1]:
            if cell.value:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
    
    def format_data_rows(self, worksheet):
        """格式化数据行"""
        data_font = Font(name='微软雅黑', size=10)
        data_alignment = Alignment(horizontal='left', vertical='center')
        
        # 交替行颜色
        light_fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')
        
        for row_num in range(2, worksheet.max_row + 1):
            for cell in worksheet[row_num]:
                cell.font = data_font
                cell.alignment = data_alignment
                
                # 交替行背景色
                if row_num % 2 == 0:
                    cell.fill = light_fill
    
    def auto_adjust_column_width(self, worksheet):
        """自动调整列宽"""
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            # 设置列宽，最小8，最大50
            adjusted_width = min(max(max_length + 2, 8), 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def add_borders(self, worksheet):
        """添加边框"""
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in worksheet.iter_rows(min_row=1, max_row=worksheet.max_row, 
                                     min_col=1, max_col=worksheet.max_column):
            for cell in row:
                if cell.value is not None:
                    cell.border = thin_border


def format_bom_excel_file(file_path):
    """格式化BOM Excel文件的便捷函数"""
    formatter = ExcelFormatter(file_path)
    return formatter.format_bom_excel()