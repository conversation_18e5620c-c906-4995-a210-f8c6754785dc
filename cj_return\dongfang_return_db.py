from modules.db_manager import DatabaseManager
from core.logger import Logger
import traceback
from core.config import get_current_user
from datetime import datetime

# 主表表名
MAIN_TABLE = "return_application"
# 子表表名
DETAIL_TABLE = "return_application_detail" 

class DongfangReturnDB:
    """东方退货数据库操作类"""
    def __init__(self):
        self.db = DatabaseManager()
        self.current_user = get_current_user()
        self.main_table = MAIN_TABLE
        self.detail_table = DETAIL_TABLE
        # 确保自动编号规则存在
        try:
            self.ensure_auto_number_rule()
            self.ensure_receipt_auto_number_rule()  # 添加这行
        except Exception as e:
            Logger.log_error(f"初始化自动编号规则失败: {str(e)}")

    def get_return_applications(self, filters=None):
        """获取退货申请列表"""
        try:
            # 构建查询条件
            where_conditions = []
            params = []
            
            if filters:
                if filters.get('yewu_no'):
                    where_conditions.append("yewu_no LIKE %s")
                    params.append(f"%{filters['yewu_no']}%")
                
                if filters.get('customer'):
                    where_conditions.append("customer LIKE %s")
                    params.append(f"%{filters['customer']}%")
                
                if filters.get('start_date'):
                    where_conditions.append("return_date >= %s")
                    params.append(filters['start_date'])
                
                if filters.get('end_date'):
                    where_conditions.append("return_date <= %s")
                    params.append(filters['end_date'])
                
                if filters.get('logistics'):
                    where_conditions.append("logistics LIKE %s")
                    params.append(f"%{filters['logistics']}%")
                
                # 添加状态过滤
                if filters.get('document_status') is not None:
                    where_conditions.append("document_status = %s")
                    params.append(filters['document_status'])
            
            where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
            
            # 查询总数
            count_query = f"SELECT COUNT(*) as total FROM {MAIN_TABLE}{where_clause}"
            count_result = self.db.execute_query(count_query, params)
            total = count_result[0]['total'] if count_result else 0
            
            # 查询数据
            query = f"""
            SELECT id, yewu_no, return_date, customer, logistics, remark,
                   document_status,
                   CASE document_status
                       WHEN 0 THEN '保存'
                       WHEN 1 THEN '审核'
                       WHEN 2 THEN '关闭'
                       ELSE '未知'
                   END as status_text,
                   create_time, create_user, update_time, update_user
            FROM {MAIN_TABLE}
            {where_clause}
            ORDER BY create_time DESC
            """
            
            # 分页处理
            if filters and filters.get('page') and filters.get('page_size'):
                offset = (filters['page'] - 1) * filters['page_size']
                query += f" LIMIT {filters['page_size']} OFFSET {offset}"
            
            results = self.db.execute_query(query, params)
            
            # 确保返回的是字典格式的结果
            if results and isinstance(results[0], tuple):
                # 如果返回的是元组，转换为字典格式
                columns = ['id', 'yewu_no', 'return_date', 'customer', 'logistics', 'remark',
                          'document_status', 'status_text', 'create_time', 'create_user', 'update_time', 'update_user']
                results = [dict(zip(columns, row)) for row in results]
            
            return total, results
            
        except Exception as e:
            Logger.log_error(f"查询退货申请失败: {str(e)}")
            raise Exception(f"查询退货申请失败: {str(e)}")

    def get_return_application_details(self, return_id):
        """获取退货申请明细"""
        try:
            query = f"""
            SELECT d.id, d.waybill_no, d.dongfang_code, d.quantity,
                   d.create_time, d.create_user, d.update_time, d.update_user,
                   g.cj_name as goods_name, 
                   g.material_id as material_code, 
                   m.name as material_name
            FROM {DETAIL_TABLE} d
            LEFT JOIN CJ_GOODS g ON d.dongfang_code = g.cj_code
            LEFT JOIN materials m ON g.material_id = m.material_id
            WHERE d.return_id = %s
            ORDER BY d.create_time
            """
            return self.db.execute_query(query, (return_id,))
        except Exception as e:
            Logger.log_error(f"查询退货明细失败: {str(e)}")
            raise

    def save_return_application(self, main_data, detail_data):
        """保存退货申请"""
        try:
            self.db.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 保存主表
            main_query = f"""
            INSERT INTO {MAIN_TABLE} 
            (yewu_no, return_date, customer, logistics, remark, document_status, 
             create_time, create_user, update_time, update_user)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            main_params = (
                main_data['yewu_no'],
                main_data['return_date'],
                main_data['customer'],
                main_data.get('logistics', ''),
                main_data.get('remark', ''),
                main_data.get('document_status', 0),  # 默认为保存状态
                current_time,
                current_user,
                current_time,
                current_user
            )
            
            self.db.execute_update(main_query, main_params)
            return_id_result = self.db.execute_query("SELECT LAST_INSERT_ID() as id")
            if return_id_result and len(return_id_result) > 0:
                if isinstance(return_id_result[0], dict):
                    return_id = return_id_result[0]['id']
                else:
                    return_id = return_id_result[0][0]
            else:
                raise Exception("无法获取插入的记录ID")
            
            # 保存明细
            if detail_data:
                detail_query = f"""
                INSERT INTO {DETAIL_TABLE}
                (return_id, waybill_no, dongfang_code, quantity, create_time, create_user, update_time, update_user)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                for detail in detail_data:
                    detail_params = (
                        return_id,
                        detail['waybill_no'],
                        detail['dongfang_code'],
                        detail['quantity'],
                        current_time,
                        current_user,
                        current_time,
                        current_user
                    )
                    self.db.execute_update(detail_query, detail_params)
            
            self.db.commit_transaction()
            return return_id
            
        except Exception as e:
            self.db.rollback_transaction()
            Logger.log_error(f"保存退货申请失败: {str(e)}")
            raise

    def update_return_application(self, return_id, main_data, detail_data):
        """更新退货申请"""
        try:
            # 检查单据状态是否允许修改
            status_query = f"SELECT document_status FROM {MAIN_TABLE} WHERE id = %s"
            status_result = self.db.execute_query(status_query, (return_id,))
            
            if status_result and status_result[0]['document_status'] in [1, 2]:
                raise Exception("已审核或已关闭的单据不允许修改")
            
            self.db.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 更新主表
            main_query = f"""
            UPDATE {MAIN_TABLE} 
            SET yewu_no=%s, return_date=%s, customer=%s, logistics=%s, remark=%s, document_status=%s,
                update_time=%s, update_user=%s
            WHERE id=%s
            """
            
            main_params = (
                main_data['yewu_no'],
                main_data['return_date'],
                main_data['customer'],
                main_data.get('logistics', ''),
                main_data.get('remark', ''),
                main_data.get('document_status', 0),
                current_time,
                current_user,
                return_id
            )
            
            self.db.execute_update(main_query, main_params)
            
            # 删除原有明细
            self.db.execute_update(f"DELETE FROM {DETAIL_TABLE} WHERE return_id = %s", (return_id,))
            
            # 重新插入明细
            if detail_data:
                detail_query = f"""
                INSERT INTO {DETAIL_TABLE}
                (return_id, waybill_no, dongfang_code, quantity, create_time, create_user, update_time, update_user)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                for detail in detail_data:
                    detail_params = (
                        return_id,
                        detail['waybill_no'],
                        detail['dongfang_code'],
                        detail['quantity'],
                        current_time,
                        current_user,
                        current_time,
                        current_user
                    )
                    self.db.execute_update(detail_query, detail_params)
            
            self.db.commit_transaction()
            
        except Exception as e:
            self.db.rollback_transaction()
            Logger.log_error(f"更新退货申请失败: {str(e)}")
            raise

    def delete_return_application(self, return_id):
        """删除退货申请"""
        try:
            # 检查单据状态是否允许删除
            status_query = f"SELECT document_status FROM {MAIN_TABLE} WHERE id = %s"
            status_result = self.db.execute_query(status_query, (return_id,))
            
            if status_result and status_result[0]['document_status'] in [1, 2]:
                raise Exception("已审核或已关闭的单据不允许删除")
            
            self.db.begin_transaction()
            
            # 删除明细
            self.db.execute_update(f"DELETE FROM {DETAIL_TABLE} WHERE return_id = %s", (return_id,))
            
            # 删除主表
            self.db.execute_update(f"DELETE FROM {MAIN_TABLE} WHERE id = %s", (return_id,))
            
            self.db.commit_transaction()
            
        except Exception as e:
            self.db.rollback_transaction()
            Logger.log_error(f"删除退货申请失败: {str(e)}")
            raise

    def audit_return_application(self, return_id):
        """审核退货申请"""
        try:
            # 检查当前状态
            status_query = f"SELECT document_status FROM {MAIN_TABLE} WHERE id = %s"
            status_result = self.db.execute_query(status_query, (return_id,))
            
            if not status_result:
                raise Exception("退货申请不存在")
            
            if status_result[0]['document_status'] != 0:
                raise Exception("只有保存状态的单据才能审核")
            
            self.db.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 更新状态为审核
            update_query = f"""
            UPDATE {MAIN_TABLE} 
            SET document_status = 1, update_time = %s, update_user = %s
            WHERE id = %s
            """
            
            self.db.execute_update(update_query, (current_time, current_user, return_id))
            self.db.commit_transaction()
            
        except Exception as e:
            self.db.rollback_transaction()
            Logger.log_error(f"审核退货申请失败: {str(e)}")
            raise

    def close_return_application(self, return_id):
        """关闭退货申请"""
        try:
            # 检查当前状态
            status_query = f"SELECT document_status FROM {MAIN_TABLE} WHERE id = %s"
            status_result = self.db.execute_query(status_query, (return_id,))
            
            if not status_result:
                raise Exception("退货申请不存在")
            
            if status_result[0]['document_status'] == 2:
                raise Exception("单据已经是关闭状态")
            
            self.db.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 更新状态为关闭
            update_query = f"""
            UPDATE {MAIN_TABLE} 
            SET document_status = 2, update_time = %s, update_user = %s
            WHERE id = %s
            """
            
            self.db.execute_update(update_query, (current_time, current_user, return_id))
            self.db.commit_transaction()
            
        except Exception as e:
            self.db.rollback_transaction()
            Logger.log_error(f"关闭退货申请失败: {str(e)}")
            raise

    def unaudit_return_application(self, return_id):
        """反审核退货申请"""
        try:
            # 检查当前状态和入库单号
            status_query = f"SELECT document_status, warehouse_receipt_no FROM {MAIN_TABLE} WHERE id = %s"
            status_result = self.db.execute_query(status_query, (return_id,))
            
            if not status_result:
                raise Exception("退货申请不存在")
            
            if status_result[0]['document_status'] != 1:
                raise Exception("只有审核状态的单据才能反审核")
            
            # 检查是否已生成入库收货单
            warehouse_receipt_no = status_result[0].get('warehouse_receipt_no')
            if warehouse_receipt_no:
                raise Exception(f"该退货申请已生成入库收货单：{warehouse_receipt_no}，请先处理相关入库收货单后再进行反审核操作")
            
            self.db.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 更新状态为保存
            update_query = f"""
            UPDATE {MAIN_TABLE} 
            SET document_status = 0, update_time = %s, update_user = %s
            WHERE id = %s
            """
            
            self.db.execute_update(update_query, (current_time, current_user, return_id))
            self.db.commit_transaction()
            
        except Exception as e:
            self.db.rollback_transaction()
            Logger.log_error(f"反审核退货申请失败: {str(e)}")
            raise

    def get_dongfang_goods(self, search_text=None):
        """获取东方货品列表"""
        try:
            if search_text:
                query = """
                SELECT g.cj_code, g.cj_name, 
                       g.material_id as material_code, 
                       m.name as material_name
                FROM CJ_GOODS g
                LEFT JOIN materials m ON g.material_id = m.material_id
                WHERE g.cj_code LIKE %s OR g.cj_name LIKE %s
                ORDER BY g.cj_code
                """
                search_param = f"%{search_text}%"
                return self.db.execute_query(query, (search_param, search_param))
            else:
                query = """
                SELECT g.cj_code, g.cj_name, 
                       g.material_id as material_code, 
                       m.name as material_name
                FROM CJ_GOODS g
                LEFT JOIN materials m ON g.material_id = m.material_id
                ORDER BY g.cj_code
                """
                return self.db.execute_query(query)
        except Exception as e:
            Logger.log_error(f"查询东方货品失败: {str(e)}")
            raise Exception(f"查询东方货品失败: {str(e)}")

    def generate_auto_number(self):
        """使用自动编号服务生成业务编号"""
        try:
            from system.auto_number_module import AutoNumberService
            from datetime import date
            
            auto_number_service = AutoNumberService()
            
            # 使用CJ规则生成编号
            return_number = auto_number_service.generate_number(
                rule_code='CJ',
                business_date=date.today().strftime('%Y-%m-%d'),
                business_type='dongfang_return',
                business_id=None,
                user_id=self.current_user.get('user_id') if self.current_user else None
            )
            
            return return_number
            
        except Exception as e:
            Logger.log_error(f"生成自动编号失败: {str(e)}")
            # 如果自动编号失败，回退到手动编号
            return self.generate_yewu_no()


    def ensure_auto_number_rule(self):
        """确保自动编号规则存在"""
        try:
            # 检查CJ规则是否存在
            check_query = "SELECT COUNT(*) as count FROM auto_number_rule WHERE rule_code = 'CJ'"
            result = self.db.execute_query(check_query)
            
            if result and result[0].get('count', 0) == 0:
                # 创建CJ规则
                insert_query = """
                INSERT INTO auto_number_rule 
                (rule_code, rule_name, prefix, date_format, sequence_length, reset_type, status, description)
                VALUES ('CJ', '出库单编号', 'CJ-', 'YYYYMMDD', 4, 1, 1, '出库单自动编号规则')
                """
                self.db.execute_update(insert_query)
                Logger.log_info("已创建CJ自动编号规则")
                
        except Exception as e:
            Logger.log_error(f"检查自动编号规则失败: {str(e)}")

    def generate_warehouse_receipt(self, return_id):
        """生成入库收货单（优化版本）"""
        try:
            # 1. 预先验证数据完整性
            self._validate_return_application(return_id)
            
            # 2. 获取退货申请数据
            return_data = self._get_return_application_data(return_id)
            
            # 3. 生成收货单号
            receipt_no = self.generate_receipt_number()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 4. 使用改进的事务处理
            self.db.begin_transaction()
            
            try:
                # 插入主表并确保获取正确的ID
                receipt_id = self._insert_receipt_main(
                    receipt_no, return_id, return_data, current_user, current_time
                )
                
                # 插入明细表
                detail_count = self._insert_receipt_details(
                    receipt_id, receipt_no, return_data['details'], current_user, current_time
                )
                
                # 锁定退货申请
                self._lock_return_application(return_id, receipt_no, current_user, current_time)
                
                self.db.commit_transaction()
                print(f"成功插入 {detail_count} 条明细记录")
                
                return receipt_no
                
            except Exception as inner_e:
                raise inner_e
            
        except Exception as e:
            self.db.rollback_transaction()
            Logger.log_error(f"生成入库收货单失败: {str(e)}")
            raise

    def _validate_return_application(self, return_id):
        """验证退货申请状态"""
        query = f"""
        SELECT document_status, is_locked 
        FROM {MAIN_TABLE} 
        WHERE id = %s
        """
        result = self.db.execute_query(query, (return_id,))
        
        if not result:
            raise Exception("退货申请不存在")
        
        if result[0]['document_status'] != 1:
            raise Exception("只有审核状态的退货申请才能生成入库收货单")
        
        if result[0].get('is_locked', 0) == 1:
            raise Exception("该退货申请已生成入库收货单，不能重复生成")

    def _get_return_application_data(self, return_id):
        """获取退货申请完整数据"""
        # 分别查询主表和明细表，避免复杂关联
        main_query = f"""
        SELECT id, yewu_no, return_date, customer, remark
        FROM {MAIN_TABLE} 
        WHERE id = %s
        """
        main_data = self.db.execute_query(main_query, (return_id,))
        
        detail_query = f"""
        SELECT d.id as detail_id, d.waybill_no, d.dongfang_code, d.quantity,
               g.cj_name, g.material_id, 
               COALESCE(g.box_spec, '') as box_spec, 
               COALESCE(g.split_code, '') as split_code,
               m.name as material_name
        FROM {DETAIL_TABLE} d
        LEFT JOIN CJ_GOODS g ON d.dongfang_code = g.cj_code
        LEFT JOIN materials m ON g.material_id = m.material_id
        WHERE d.return_id = %s
        """
        detail_data = self.db.execute_query(detail_query, (return_id,))
        
        # 调试输出
        print(f"获取退货申请数据，明细数量: {len(detail_data) if detail_data else 0}")
        for detail in detail_data or []:
            print(f"明细: 东方货号={detail.get('dongfang_code')}, 数量={detail.get('quantity')}, 箱规={detail.get('box_spec')}")
        
        return {
            'main': main_data[0] if main_data else None,
            'details': detail_data
        }

    def _insert_receipt_main(self, receipt_no, return_id, return_data, current_user, current_time):
        """插入收货单主表"""
        # 所有数量字段默认为0，等待仓库人员填入
        receipt_query = """
        INSERT INTO warehouse_receipt 
        (receipt_no, return_application_id, return_yewu_no, receipt_date, 
         receiver, warehouse, receipt_status, total_quantity, total_raw_quantity,
         total_deformed_quantity, total_to_scrap_quantity, total_scrapped_quantity,
         create_time, create_user, update_time, update_user)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        receipt_params = (
            receipt_no, return_id, return_data['main']['yewu_no'], current_time.date(),
            current_user, '默认仓库', 0, 
            0, 0, 0, 0, 0,  # 所有数量字段默认为0
            current_time, current_user, current_time, current_user
        )
        
        # 使用lastrowid确保获取正确的ID
        cursor = self.db.execute_update(receipt_query, receipt_params)
        receipt_id = cursor.lastrowid if hasattr(cursor, 'lastrowid') else None
        
        if not receipt_id:
            # 备用方案：查询获取ID
            check_query = "SELECT id FROM warehouse_receipt WHERE receipt_no = %s"
            check_result = self.db.execute_query(check_query, (receipt_no,))
            if check_result:
                receipt_id = check_result[0]['id']
            else:
                raise Exception("插入入库收货单主表失败")
        
        return receipt_id

    def _insert_receipt_details(self, receipt_id, receipt_no, details, current_user, current_time):
        """插入收货单明细"""
        detail_insert_query = """
        INSERT INTO warehouse_receipt_detail
        (receipt_id, receipt_no, return_detail_id, waybill_no, dongfang_code,
         material_code, material_name, return_quantity, received_quantity,
         box_spec, split_code, split_quantity, raw_quantity, deformed_quantity,
         to_scrap_quantity, scrapped_quantity,
         create_time, create_user, update_time, update_user)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        detail_count = 0
        for row in details:
            # 验证必要字段
            if not row.get('detail_id'):
                continue
            
            # 拆分数量直接等于箱规
            split_quantity = 0
            box_spec = row.get('box_spec')
            
            print(f"处理明细: 东方货号={row.get('dongfang_code')}, 箱规={box_spec}")
            
            if box_spec and str(box_spec).strip():
                try:
                    split_quantity = int(str(box_spec).strip())
                    print(f"拆分数量 = 箱规: {split_quantity}")
                except (ValueError, TypeError) as e:
                    print(f"箱规转换失败: {box_spec}, 错误: {e}")
                    split_quantity = 0
            else:
                print(f"箱规为空或无效: {box_spec}")
        
            detail_params = (
                receipt_id, receipt_no, row['detail_id'], 
                row.get('waybill_no', ''), row.get('dongfang_code', ''),
                row.get('material_id', ''), row.get('material_name', ''), 
                row.get('quantity', 0),  # 退货数量（来自申请单）
                0,  # 实收数量默认为0，等待仓库填入
                row.get('box_spec', ''), row.get('split_code', ''), 
                split_quantity,  # 拆分数量直接等于箱规
                0, 0, 0, 0,  # 原料、变形、待报废、报废数量都默认为0
                current_time, current_user, current_time, current_user
            )
        
            print(f"插入明细参数: 拆分数量={split_quantity}")
            self.db.execute_update(detail_insert_query, detail_params)
            detail_count += 1
        
        return detail_count

    def _lock_return_application(self, return_id, receipt_no, current_user, current_time):
        """锁定退货申请"""
        lock_query = f"""
        UPDATE {MAIN_TABLE} 
        SET is_locked = 1, warehouse_receipt_no = %s, update_time = %s, update_user = %s
        WHERE id = %s
        """
        self.db.execute_update(lock_query, (receipt_no, current_time, current_user, return_id))

    def generate_receipt_number(self):
        """生成入库收货单号"""
        try:
            from system.auto_number_module import AutoNumberService
            from datetime import date
            
            auto_number_service = AutoNumberService()
            
            # 使用CJRK规则生成编号
            receipt_number = auto_number_service.generate_number(
                rule_code='CJRK',
                business_date=date.today().strftime('%Y-%m-%d'),
                business_type='warehouse_receipt',
                business_id=None,
                user_id=self.current_user.get('user_id') if self.current_user else None
            )
            
            return receipt_number
            
        except Exception as e:
            Logger.log_error(f"生成收货单号失败: {str(e)}")
            # 如果自动编号失败，回退到手动编号
            return self.generate_manual_receipt_no()


    def get_warehouse_receipts(self, filters=None):
        """获取入库收货单列表"""
        try:
            where_conditions = []
            params = []
            
            if filters:
                if filters.get('receipt_no'):
                    where_conditions.append("receipt_no LIKE %s")
                    params.append(f"%{filters['receipt_no']}%")
                
                if filters.get('return_yewu_no'):
                    where_conditions.append("return_yewu_no LIKE %s")
                    params.append(f"%{filters['return_yewu_no']}%")
                
                if filters.get('start_date'):
                    where_conditions.append("receipt_date >= %s")
                    params.append(filters['start_date'])
                
                if filters.get('end_date'):
                    where_conditions.append("receipt_date <= %s")
                    params.append(filters['end_date'])
                
                if filters.get('receipt_status') is not None:
                    where_conditions.append("receipt_status = %s")
                    params.append(filters['receipt_status'])
            
            where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
            
            # 查询总数
            count_query = f"SELECT COUNT(*) as total FROM warehouse_receipt{where_clause}"
            count_result = self.db.execute_query(count_query, params)
            total = count_result[0]['total'] if count_result else 0
            
            # 查询数据
            query = f"""
            SELECT id, receipt_no, return_yewu_no, receipt_date, receiver, warehouse,
                   receipt_status,
                   CASE receipt_status
                       WHEN 0 THEN '保存'
                       WHEN 1 THEN '审核'
                       WHEN 2 THEN '关闭'
                       ELSE '未知'
                   END as status_text,
                   total_quantity, total_raw_quantity, total_deformed_quantity,
                   total_to_scrap_quantity, total_scrapped_quantity,
                   create_time, create_user, update_time, update_user
            FROM warehouse_receipt
            {where_clause}
            ORDER BY create_time DESC
            """
            
            # 分页处理
            if filters and filters.get('page') and filters.get('page_size'):
                offset = (filters['page'] - 1) * filters['page_size']
                query += f" LIMIT {filters['page_size']} OFFSET {offset}"
            
            results = self.db.execute_query(query, params)
            
            return total, results
            
        except Exception as e:
            Logger.log_error(f"查询入库收货单失败: {str(e)}")
            raise Exception(f"查询入库收货单失败: {str(e)}")

    def get_warehouse_receipt_details(self, receipt_id):
        """获取入库收货单详情"""
        try:
            # 获取主表数据
            main_query = """
            SELECT wr.*, ra.customer, ra.logistics
            FROM warehouse_receipt wr
            LEFT JOIN return_application ra ON wr.return_application_id = ra.id
            WHERE wr.id = %s
            """
            main_data = self.db.execute_query(main_query, (receipt_id,))
            
            if not main_data:
                raise Exception("入库收货单不存在")
            
            # 获取明细数据 - 拆分数量直接等于箱规
            detail_query = """
            SELECT wrd.*, 
                   CASE 
                       WHEN wrd.box_spec IS NOT NULL AND wrd.box_spec != '' 
                            AND CAST(wrd.box_spec AS UNSIGNED) > 0
                       THEN CAST(wrd.box_spec AS UNSIGNED)
                       ELSE wrd.split_quantity 
                   END as calculated_split_quantity
            FROM warehouse_receipt_detail wrd
            WHERE wrd.receipt_id = %s
            ORDER BY wrd.create_time
            """
            detail_data = self.db.execute_query(detail_query, (receipt_id,))
            
            # 更新明细数据中的拆分数量
            for detail in detail_data:
                if detail['calculated_split_quantity'] != detail['split_quantity']:
                    print(f"拆分数量不一致: 存储值={detail['split_quantity']}, 计算值={detail['calculated_split_quantity']}")
                    detail['split_quantity'] = detail['calculated_split_quantity']
            
            print(f"查询到主表数据: {main_data[0] if main_data else 'None'}")
            print(f"查询到明细数据数量: {len(detail_data) if detail_data else 0}")
            if detail_data:
                print(f"第一条明细数据: {detail_data[0]}")
            
            return {
                'main': main_data[0],
                'details': detail_data or []
            }
            
        except Exception as e:
            Logger.log_error(f"查询入库收货单详情失败: {str(e)}")
            print(f"查询入库收货单详情失败: {str(e)}")
            raise

    def ensure_receipt_auto_number_rule(self):
        """确保入库收货单自动编号规则存在"""
        try:
            # 检查CJRK规则是否存在
            check_query = "SELECT COUNT(*) as count FROM auto_number_rule WHERE rule_code = 'CJRK'"
            result = self.db.execute_query(check_query)
            
            if result and result[0].get('count', 0) == 0:
                # 创建CJRK规则
                insert_query = """
                INSERT INTO auto_number_rule 
                (rule_code, rule_name, prefix, date_format, sequence_length, reset_type, status, description)
                VALUES ('CJRK', '入库收货单编号', 'CJRK-', 'YYYYMMDD', 4, 1, 1, '入库收货单自动编号规则')
                """
                self.db.execute_update(insert_query)
                Logger.log_info("已创建CJRK自动编号规则")
                
        except Exception as e:
            Logger.log_error(f"检查入库收货单自动编号规则失败: {str(e)}")

    def update_warehouse_receipt(self, receipt_id, main_data, detail_data):
        """更新入库收货单"""
        try:
            self.db.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 更新主表
            main_query = """
            UPDATE warehouse_receipt 
            SET receipt_date = %s, receiver = %s, warehouse = %s, remark = %s,
                total_quantity = %s, total_raw_quantity = %s, total_deformed_quantity = %s,
                total_to_scrap_quantity = %s, total_scrapped_quantity = %s,
                update_time = %s, update_user = %s
            WHERE id = %s
            """
            
            main_params = (
                main_data.get('receipt_date'), main_data.get('receiver', ''),
                main_data.get('warehouse', ''), main_data.get('remark', ''),
                main_data.get('total_quantity', 0), main_data.get('total_raw_quantity', 0),
                main_data.get('total_deformed_quantity', 0), main_data.get('total_to_scrap_quantity', 0),
                main_data.get('total_scrapped_quantity', 0), current_time, current_user, receipt_id
            )
            
            self.db.execute_update(main_query, main_params)
            
            # 更新明细
            if detail_data:
                for detail in detail_data:
                    detail_query = """
                    UPDATE warehouse_receipt_detail
                    SET material_code = %s, material_name = %s, split_code = %s, box_spec = %s, split_quantity = %s,
                        received_quantity = %s, raw_quantity = %s, deformed_quantity = %s,
                        to_scrap_quantity = %s, scrapped_quantity = %s, batch_no = %s,
                        storage_location = %s, remark = %s, update_time = %s, update_user = %s
                    WHERE id = %s
                    """
                    
                    detail_params = (
                        detail.get('material_code', ''),  # 使用更新料号
                        detail.get('material_name', ''),  # 添加物料名称
                        detail.get('split_code', '') or None,
                        detail.get('box_spec', '') or None,
                        detail.get('split_quantity', 0),
                        detail.get('received_quantity', 0), 
                        detail.get('raw_quantity', 0),
                        detail.get('deformed_quantity', 0), 
                        detail.get('to_scrap_quantity', 0),
                        detail.get('scrapped_quantity', 0), 
                        detail.get('batch_no', '') or None,
                        detail.get('storage_location', '') or None,
                        detail.get('remark', '') or None,
                        current_time, current_user, detail['id']
                    )
                    
                    self.db.execute_update(detail_query, detail_params)
            
            # 验证数量平衡
            self._validate_receipt_quantities(receipt_id)
            
            self.db.commit_transaction()
            
        except Exception as e:
            self.db.rollback_transaction()
            Logger.log_error(f"更新入库收货单失败: {str(e)}")
            raise

    def audit_warehouse_receipt(self, receipt_id):
        """审核入库收货单"""
        try:
            # 检查状态
            check_query = "SELECT receipt_status, return_application_id FROM warehouse_receipt WHERE id = %s"
            result = self.db.execute_query(check_query, (receipt_id,))
            
            if not result:
                raise Exception("收货单不存在")
            
            if result[0]['receipt_status'] != 0:
                raise Exception("只有保存状态的收货单才能审核")
            
            return_application_id = result[0]['return_application_id']
            
            # 验证数量平衡
            self._validate_receipt_quantities(receipt_id)
            
            self.db.begin_transaction()
            
            # 更新收货单状态
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            update_receipt_query = """
            UPDATE warehouse_receipt 
            SET receipt_status = 1, update_time = CURRENT_TIMESTAMP, update_user = %s
            WHERE id = %s
            """
            self.db.execute_update(update_receipt_query, (current_user, receipt_id))
            
            # 更新对应的东方退货申请单状态为关闭(2)
            if return_application_id:
                current_time = datetime.now()
                update_return_query = f"""
                UPDATE {MAIN_TABLE} 
                SET document_status = 2, update_time = %s, update_user = %s
                WHERE id = %s
                """
                self.db.execute_update(update_return_query, (current_time, current_user, return_application_id))
                Logger.log_info(f"已将退货申请单 ID:{return_application_id} 状态更新为关闭")
            
            self.db.commit_transaction()
            Logger.log_info(f"审核入库收货单成功，ID: {receipt_id}")
            
        except Exception as e:
            self.db.rollback_transaction()
            Logger.log_error(f"审核入库收货单失败: {str(e)}")
            raise

    def _validate_receipt_quantities(self, receipt_id):
        """验证收货单数量平衡 - 修改为验证状态数量是否等于拆分数量"""
        query = """
        SELECT id, split_quantity, raw_quantity, deformed_quantity, 
               to_scrap_quantity, scrapped_quantity
        FROM warehouse_receipt_detail 
        WHERE receipt_id = %s
        """
        details = self.db.execute_query(query, (receipt_id,))
        
        for detail in details:
            split_qty = detail['split_quantity'] or 0
            total_status = (detail['raw_quantity'] or 0) + (detail['deformed_quantity'] or 0) + \
                          (detail['to_scrap_quantity'] or 0) + (detail['scrapped_quantity'] or 0)
            
            if split_qty != total_status:
                raise Exception(f"明细ID {detail['id']} 数量不平衡：拆分数量({split_qty}) != 状态数量总和({total_status})")

    def delete_warehouse_receipt(self, receipt_id):
        """删除入库收货单"""
        try:
            self.db.begin_transaction()
            
            # 检查收货单状态
            check_query = "SELECT receipt_status, return_application_id FROM warehouse_receipt WHERE id = %s"
            result = self.db.execute_query(check_query, (receipt_id,))
            
            if not result:
                raise Exception("收货单不存在")
            
            receipt_status = result[0]['receipt_status']
            return_application_id = result[0]['return_application_id']
            
            if receipt_status != 0:
                raise Exception("只有保存状态的收货单才能删除")
            
            # 删除收货单明细
            delete_detail_query = "DELETE FROM warehouse_receipt_detail WHERE receipt_id = %s"
            self.db.execute_update(delete_detail_query, (receipt_id,))
            
            # 删除收货单主表
            delete_main_query = "DELETE FROM warehouse_receipt WHERE id = %s"
            self.db.execute_update(delete_main_query, (receipt_id,))
            
            # 恢复退货申请的锁定状态
            unlock_query = """
            UPDATE return_application 
            SET is_locked = 0, warehouse_receipt_no = NULL 
            WHERE id = %s
            """
            self.db.execute_update(unlock_query, (return_application_id,))
            
            self.db.commit_transaction()
            Logger.log_info(f"删除入库收货单成功，ID: {receipt_id}")
            
        except Exception as e:
            self.db.rollback_transaction()
            Logger.log_error(f"删除入库收货单失败: {str(e)}")
            raise

    def unaudit_warehouse_receipt(self, receipt_id):
        """反审核入库收货单"""
        try:
            # 检查当前状态
            status_query = "SELECT receipt_status, return_application_id FROM warehouse_receipt WHERE id = %s"
            status_result = self.db.execute_query(status_query, (receipt_id,))
            
            if not status_result:
                raise Exception("入库收货单不存在")
            
            if status_result[0]['receipt_status'] != 1:
                raise Exception("只有审核状态的收货单才能反审核")
            
            return_application_id = status_result[0]['return_application_id']
            
            self.db.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 更新收货单状态为保存
            update_query = """
            UPDATE warehouse_receipt 
            SET receipt_status = 0, update_time = %s, update_user = %s
            WHERE id = %s
            """
            self.db.execute_update(update_query, (current_time, current_user, receipt_id))
            
            # 更新对应的东方退货申请单状态为审核(1)
            if return_application_id:
                update_return_query = f"""
                UPDATE {MAIN_TABLE} 
                SET document_status = 1, update_time = %s, update_user = %s
                WHERE id = %s
                """
                self.db.execute_update(update_return_query, (current_time, current_user, return_application_id))
                Logger.log_info(f"已将退货申请单 ID:{return_application_id} 状态恢复为审核")
            
            self.db.commit_transaction()
            Logger.log_info(f"反审核入库收货单成功，ID: {receipt_id}")
            
        except Exception as e:
            self.db.rollback_transaction()
            Logger.log_error(f"反审核入库收货单失败: {str(e)}")
            raise

    def close_warehouse_receipt(self, receipt_id):
        """关闭入库收货单"""
        try:
            # 检查当前状态
            status_query = "SELECT receipt_status, return_application_id FROM warehouse_receipt WHERE id = %s"
            status_result = self.db.execute_query(status_query, (receipt_id,))
            
            if not status_result:
                raise Exception("入库收货单不存在")
            
            current_status = status_result[0]['receipt_status']
            if current_status == 2:
                raise Exception("收货单已经是关闭状态")
            
            if current_status == 0:
                raise Exception("只有审核状态的收货单才能关闭")
            
            self.db.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 更新收货单状态为关闭
            update_query = """
            UPDATE warehouse_receipt 
            SET receipt_status = 2, update_time = %s, update_user = %s
            WHERE id = %s
            """
            self.db.execute_update(update_query, (current_time, current_user, receipt_id))
            
            self.db.commit_transaction()
            Logger.log_info(f"关闭入库收货单成功，ID: {receipt_id}")
            
        except Exception as e:
            self.db.rollback_transaction()
            Logger.log_error(f"关闭入库收货单失败: {str(e)}")
            raise

