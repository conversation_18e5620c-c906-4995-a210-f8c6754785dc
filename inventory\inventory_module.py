"""
库存管理主界面模块
提供库存查询、管理等功能的统一界面
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                           QTableWidgetItem, QHeaderView, QPushButton, QLineEdit,
                           QComboBox, QLabel, QFrame, QSplitter, QTabWidget,
                           QMessageBox, QMenu, QProgressDialog, QDateEdit,
                           QSpinBox, QGroupBox, QGridLayout, QCheckBox, QDialog)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QDate, QTimer
from PyQt6.QtGui import QIcon, QFont, QColor, QAction
from datetime import datetime, date
import traceback

from core.logger import Logger
from core.config import get_current_user
from inventory.inventory_db import InventoryDB
from inventory.inventory_permission_service import InventoryPermissionService


class InventoryModule(QWidget):
    """库存管理主界面"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.inventory_db = InventoryDB()
        self.permission_service = InventoryPermissionService()
        self.current_user = get_current_user()
        
        # 界面状态
        self.current_page = 1
        self.page_size = 50
        self.total_records = 0
        self.current_filters = {}
        
        self.setup_ui()
        self.setup_permissions()
        self.load_initial_data()
    
    def setup_ui(self):
        """设置界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 库存总览标签页
        self.create_inventory_overview_tab()
        
        # 库存流水标签页
        self.create_transaction_tab()
        
        # 库存预留标签页
        self.create_reservation_tab()
    
    def create_inventory_overview_tab(self):
        """创建库存总览标签页"""
        # 创建标签页
        overview_widget = QWidget()
        self.tab_widget.addTab(overview_widget, "📦 库存总览")
        
        # 主布局
        layout = QVBoxLayout(overview_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建搜索区域
        search_frame = self.create_search_frame()
        layout.addWidget(search_frame)
        
        # 创建工具栏
        toolbar_frame = self.create_toolbar_frame()
        layout.addWidget(toolbar_frame)
        
        # 创建数据表格
        self.inventory_table = self.create_inventory_table()
        layout.addWidget(self.inventory_table)
        
        # 创建分页控件
        pagination_frame = self.create_pagination_frame()
        layout.addWidget(pagination_frame)
    
    def create_search_frame(self):
        """创建搜索区域"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(10)
        
        # 第一行：物料编码、物料名称、部门
        layout.addWidget(QLabel("物料编码:"), 0, 0)
        self.material_id_edit = QLineEdit()
        self.material_id_edit.setPlaceholderText("请输入物料编码...")
        layout.addWidget(self.material_id_edit, 0, 1)
        
        layout.addWidget(QLabel("物料名称:"), 0, 2)
        self.material_name_edit = QLineEdit()
        self.material_name_edit.setPlaceholderText("请输入物料名称...")
        layout.addWidget(self.material_name_edit, 0, 3)
        
        layout.addWidget(QLabel("部门:"), 0, 4)
        self.department_combo = QComboBox()
        self.department_combo.addItem("全部", "")
        # 根据权限加载部门列表
        self.load_department_options()
        layout.addWidget(self.department_combo, 0, 5)
        
        # 第二行：仓库、物料分类、库存状态
        layout.addWidget(QLabel("仓库:"), 1, 0)
        self.warehouse_combo = QComboBox()
        self.warehouse_combo.addItem("全部", "")
        self.load_warehouse_options()
        layout.addWidget(self.warehouse_combo, 1, 1)
        
        layout.addWidget(QLabel("物料分类:"), 1, 2)
        self.category_combo = QComboBox()
        self.category_combo.addItem("全部", "")
        self.load_category_options()
        layout.addWidget(self.category_combo, 1, 3)
        
        layout.addWidget(QLabel("库存状态:"), 1, 4)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["全部", "正常", "库存不足", "缺货", "库存过量"])
        layout.addWidget(self.status_combo, 1, 5)
        
        # 第三行：搜索按钮
        button_layout = QHBoxLayout()
        
        self.search_btn = QPushButton("🔍 搜索")
        self.search_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.search_btn.clicked.connect(self.search_inventory)
        button_layout.addWidget(self.search_btn)
        
        self.reset_btn = QPushButton("🔄 重置")
        self.reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                color: #666;
                border: 1px solid #d9d9d9;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e6f7ff;
                border-color: #1890ff;
            }
        """)
        self.reset_btn.clicked.connect(self.reset_search)
        button_layout.addWidget(self.reset_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout, 2, 0, 1, 6)
        
        return frame
    
    def create_toolbar_frame(self):
        """创建工具栏"""
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 左侧按钮组
        self.add_initial_btn = QPushButton("📝 期初库存")
        self.add_initial_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
        """)
        self.add_initial_btn.clicked.connect(self.add_initial_inventory)
        layout.addWidget(self.add_initial_btn)
        
        self.transfer_btn = QPushButton("🔄 库存调拨")
        self.transfer_btn.setStyleSheet("""
            QPushButton {
                background-color: #fa8c16;
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #ffa940;
            }
        """)
        self.transfer_btn.clicked.connect(self.transfer_inventory)
        layout.addWidget(self.transfer_btn)
        
        self.count_btn = QPushButton("📊 库存盘点")
        self.count_btn.setStyleSheet("""
            QPushButton {
                background-color: #722ed1;
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #9254de;
            }
        """)
        self.count_btn.clicked.connect(self.count_inventory)
        layout.addWidget(self.count_btn)
        
        layout.addStretch()
        
        # 右侧按钮组
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.refresh_data)
        layout.addWidget(self.refresh_btn)
        
        self.export_btn = QPushButton("📤 导出")
        self.export_btn.clicked.connect(self.export_data)
        layout.addWidget(self.export_btn)
        
        return frame
    
    def create_inventory_table(self):
        """创建库存数据表格"""
        table = QTableWidget()
        
        # 设置列
        headers = [
            "物料编码", "物料名称", "物料分类", "部门", "仓库",
            "总数量", "可用数量", "预留数量", "冻结数量", "单位",
            "平均成本", "库存金额", "最小库存", "最大库存", "库存状态",
            "最后入库", "最后出库", "更新时间"
        ]
        
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        
        # 设置表格样式
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #f0f0f0;
                background-color: white;
                alternate-background-color: #fafafa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e6f7ff;
                color: #1890ff;
            }
            QHeaderView::section {
                background-color: #fafafa;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #f0f0f0;
                font-weight: bold;
            }
        """)
        
        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        table.setSortingEnabled(True)
        
        # 设置列宽
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 物料编码
        header.resizeSection(0, 120)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 物料名称
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)  # 物料分类
        header.resizeSection(2, 100)
        
        # 右键菜单
        table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        table.customContextMenuRequested.connect(self.show_inventory_context_menu)
        
        # 双击事件
        table.doubleClicked.connect(self.view_inventory_detail)
        
        return table
    
    def create_transaction_tab(self):
        """创建库存流水标签页"""
        # 创建标签页
        transaction_widget = QWidget()
        self.tab_widget.addTab(transaction_widget, "📋 库存流水")
        
        # 主布局
        layout = QVBoxLayout(transaction_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 搜索区域
        search_frame = self.create_transaction_search_frame()
        layout.addWidget(search_frame)
        
        # 数据表格
        self.transaction_table = self.create_transaction_table()
        layout.addWidget(self.transaction_table)
        
        # 分页控件
        transaction_pagination = self.create_transaction_pagination_frame()
        layout.addWidget(transaction_pagination)
    
    def create_reservation_tab(self):
        """创建库存预留标签页"""
        # 创建标签页
        reservation_widget = QWidget()
        self.tab_widget.addTab(reservation_widget, "🔒 库存预留")
        
        # 主布局
        layout = QVBoxLayout(reservation_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 搜索区域
        search_frame = self.create_reservation_search_frame()
        layout.addWidget(search_frame)
        
        # 数据表格
        self.reservation_table = self.create_reservation_table()
        layout.addWidget(self.reservation_table)
        
        # 分页控件
        reservation_pagination = self.create_reservation_pagination_frame()
        layout.addWidget(reservation_pagination)
    
    def create_pagination_frame(self):
        """创建分页控件"""
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 页面信息
        self.page_info_label = QLabel("第 1 页，共 0 条记录")
        layout.addWidget(self.page_info_label)
        
        layout.addStretch()
        
        # 分页按钮
        self.first_page_btn = QPushButton("首页")
        self.first_page_btn.clicked.connect(lambda: self.go_to_page(1))
        layout.addWidget(self.first_page_btn)
        
        self.prev_page_btn = QPushButton("上一页")
        self.prev_page_btn.clicked.connect(self.prev_page)
        layout.addWidget(self.prev_page_btn)
        
        self.page_spin = QSpinBox()
        self.page_spin.setMinimum(1)
        self.page_spin.setValue(1)
        self.page_spin.valueChanged.connect(self.go_to_page)
        layout.addWidget(self.page_spin)
        
        self.next_page_btn = QPushButton("下一页")
        self.next_page_btn.clicked.connect(self.next_page)
        layout.addWidget(self.next_page_btn)
        
        self.last_page_btn = QPushButton("末页")
        self.last_page_btn.clicked.connect(self.go_to_last_page)
        layout.addWidget(self.last_page_btn)
        
        # 每页显示数量
        layout.addWidget(QLabel("每页显示:"))
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["20", "50", "100", "200"])
        self.page_size_combo.setCurrentText("50")
        self.page_size_combo.currentTextChanged.connect(self.change_page_size)
        layout.addWidget(self.page_size_combo)
        
        return frame
    
    def setup_permissions(self):
        """设置权限控制"""
        try:
            # 设置按钮权限
            button_permissions = {
                'inventory:initial': self.add_initial_btn,
                'inventory:transfer': self.transfer_btn,
                'inventory:count': self.count_btn,
                'inventory:export': self.export_btn
            }
            
            self.permission_service.setup_button_permissions(button_permissions)
            
        except Exception as e:
            Logger.log_error(f"设置权限控制失败: {str(e)}")
    
    def load_initial_data(self):
        """加载初始数据"""
        try:
            # 加载库存总览数据
            self.search_inventory()
            
        except Exception as e:
            Logger.log_error(f"加载初始数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载数据失败：{str(e)}")
    
    # ==================== 数据加载方法 ====================
    
    def search_inventory(self):
        """搜索库存数据"""
        try:
            # 构建搜索条件
            filters = {}
            
            if self.material_id_edit.text().strip():
                filters['material_id'] = self.material_id_edit.text().strip()
            
            if self.material_name_edit.text().strip():
                filters['material_name'] = self.material_name_edit.text().strip()
            
            if self.department_combo.currentData():
                filters['department'] = self.department_combo.currentData()
            
            if self.warehouse_combo.currentData():
                filters['warehouse_code'] = self.warehouse_combo.currentData()
            
            if self.category_combo.currentData():
                filters['category_id'] = self.category_combo.currentData()
            
            if self.status_combo.currentText() != "全部":
                filters['stock_status'] = self.status_combo.currentText()
            
            self.current_filters = filters
            self.current_page = 1
            self.load_inventory_data()
            
        except Exception as e:
            Logger.log_error(f"搜索库存数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"搜索失败：{str(e)}")
    
    def load_inventory_data(self):
        """加载库存数据"""
        try:
            # 获取数据
            total, inventory_list = self.inventory_db.get_department_inventory_list(
                filters=self.current_filters,
                page=self.current_page,
                page_size=self.page_size
            )
            
            self.total_records = total
            
            # 更新表格
            self.update_inventory_table(inventory_list)
            
            # 更新分页信息
            self.update_pagination_info()
            
        except Exception as e:
            Logger.log_error(f"加载库存数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载数据失败：{str(e)}")
    
    def update_inventory_table(self, inventory_list):
        """更新库存表格"""
        try:
            self.inventory_table.setRowCount(len(inventory_list))
            
            for row, inventory in enumerate(inventory_list):
                # 物料编码
                self.inventory_table.setItem(row, 0, QTableWidgetItem(str(inventory.get('material_id', ''))))
                
                # 物料名称
                self.inventory_table.setItem(row, 1, QTableWidgetItem(str(inventory.get('material_name', ''))))
                
                # 物料分类
                self.inventory_table.setItem(row, 2, QTableWidgetItem(str(inventory.get('category_name', ''))))
                
                # 部门
                self.inventory_table.setItem(row, 3, QTableWidgetItem(str(inventory.get('department', ''))))
                
                # 仓库
                warehouse_name = inventory.get('warehouse_name') or inventory.get('warehouse_code', '')
                self.inventory_table.setItem(row, 4, QTableWidgetItem(str(warehouse_name)))
                
                # 数量信息
                self.inventory_table.setItem(row, 5, QTableWidgetItem(f"{inventory.get('total_qty', 0):.2f}"))
                self.inventory_table.setItem(row, 6, QTableWidgetItem(f"{inventory.get('available_qty', 0):.2f}"))
                self.inventory_table.setItem(row, 7, QTableWidgetItem(f"{inventory.get('reserved_qty', 0):.2f}"))
                self.inventory_table.setItem(row, 8, QTableWidgetItem(f"{inventory.get('frozen_qty', 0):.2f}"))
                
                # 单位
                self.inventory_table.setItem(row, 9, QTableWidgetItem(str(inventory.get('unit', ''))))
                
                # 成本信息
                self.inventory_table.setItem(row, 10, QTableWidgetItem(f"{inventory.get('avg_unit_cost', 0):.4f}"))
                self.inventory_table.setItem(row, 11, QTableWidgetItem(f"{inventory.get('total_cost', 0):.2f}"))
                
                # 库存限制
                self.inventory_table.setItem(row, 12, QTableWidgetItem(f"{inventory.get('min_stock_qty', 0):.2f}"))
                self.inventory_table.setItem(row, 13, QTableWidgetItem(f"{inventory.get('max_stock_qty', 0):.2f}"))
                
                # 库存状态
                status_item = QTableWidgetItem(str(inventory.get('stock_status', '')))
                status = inventory.get('stock_status', '')
                if status == '缺货':
                    status_item.setBackground(QColor('#ffebee'))
                    status_item.setForeground(QColor('#f44336'))
                elif status == '库存不足':
                    status_item.setBackground(QColor('#fff3e0'))
                    status_item.setForeground(QColor('#ff9800'))
                elif status == '库存过量':
                    status_item.setBackground(QColor('#e8f5e8'))
                    status_item.setForeground(QColor('#4caf50'))
                else:
                    status_item.setBackground(QColor('#f5f5f5'))
                    status_item.setForeground(QColor('#666666'))
                
                self.inventory_table.setItem(row, 14, status_item)
                
                # 日期信息
                last_in_date = inventory.get('last_in_date')
                self.inventory_table.setItem(row, 15, QTableWidgetItem(
                    last_in_date.strftime('%Y-%m-%d') if last_in_date else ''
                ))
                
                last_out_date = inventory.get('last_out_date')
                self.inventory_table.setItem(row, 16, QTableWidgetItem(
                    last_out_date.strftime('%Y-%m-%d') if last_out_date else ''
                ))
                
                update_time = inventory.get('update_time')
                self.inventory_table.setItem(row, 17, QTableWidgetItem(
                    update_time.strftime('%Y-%m-%d %H:%M') if update_time else ''
                ))
                
                # 存储完整数据到第一列
                self.inventory_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, inventory)
            
        except Exception as e:
            Logger.log_error(f"更新库存表格失败: {str(e)}")
    
    # ==================== 事件处理方法 ====================
    
    def reset_search(self):
        """重置搜索条件"""
        self.material_id_edit.clear()
        self.material_name_edit.clear()
        self.department_combo.setCurrentIndex(0)
        self.warehouse_combo.setCurrentIndex(0)
        self.category_combo.setCurrentIndex(0)
        self.status_combo.setCurrentIndex(0)
        self.search_inventory()
    
    def refresh_data(self):
        """刷新数据"""
        self.load_inventory_data()
    
    def add_initial_inventory(self):
        """添加期初库存"""
        try:
            from inventory.inventory_initial_dialog import InventoryInitialDialog
            dialog = InventoryInitialDialog(self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.refresh_data()
        except Exception as e:
            Logger.log_error(f"打开期初库存对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开对话框失败：{str(e)}")
    
    def transfer_inventory(self):
        """库存调拨"""
        try:
            from inventory.inventory_transfer_dialog import InventoryTransferDialog
            dialog = InventoryTransferDialog(self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.refresh_data()
        except Exception as e:
            Logger.log_error(f"打开库存调拨对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开对话框失败：{str(e)}")
    
    def count_inventory(self):
        """库存盘点"""
        try:
            from inventory.inventory_count_dialog import InventoryCountDialog
            dialog = InventoryCountDialog(self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.refresh_data()
        except Exception as e:
            Logger.log_error(f"打开库存盘点对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开对话框失败：{str(e)}")
    
    def export_data(self):
        """导出数据"""
        try:
            # TODO: 实现数据导出功能
            QMessageBox.information(self, "提示", "导出功能开发中...")
        except Exception as e:
            Logger.log_error(f"导出数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导出失败：{str(e)}")
    
    # ==================== 分页控制方法 ====================
    
    def update_pagination_info(self):
        """更新分页信息"""
        try:
            total_pages = (self.total_records + self.page_size - 1) // self.page_size
            total_pages = max(1, total_pages)
            
            self.page_info_label.setText(f"第 {self.current_page} 页，共 {self.total_records} 条记录")
            
            # 更新分页控件状态
            self.page_spin.setMaximum(total_pages)
            self.page_spin.setValue(self.current_page)
            
            self.first_page_btn.setEnabled(self.current_page > 1)
            self.prev_page_btn.setEnabled(self.current_page > 1)
            self.next_page_btn.setEnabled(self.current_page < total_pages)
            self.last_page_btn.setEnabled(self.current_page < total_pages)
            
        except Exception as e:
            Logger.log_error(f"更新分页信息失败: {str(e)}")
    
    def go_to_page(self, page):
        """跳转到指定页"""
        if page != self.current_page:
            self.current_page = page
            self.load_inventory_data()
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.go_to_page(self.current_page - 1)
    
    def next_page(self):
        """下一页"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        if self.current_page < total_pages:
            self.go_to_page(self.current_page + 1)
    
    def go_to_last_page(self):
        """末页"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        self.go_to_page(total_pages)
    
    def change_page_size(self, size_text):
        """改变每页显示数量"""
        self.page_size = int(size_text)
        self.current_page = 1
        self.load_inventory_data()
    
    # ==================== 辅助方法 ====================
    
    def load_department_options(self):
        """加载部门选项"""
        try:
            # 根据用户权限加载可访问的部门
            if self.current_user:
                user_departments = self.current_user.get('departments', [])
                if self.permission_service.has_permission('system:admin'):
                    # 管理员可以看到所有部门
                # TODO: 从数据库加载所有部门
                    departments = ["通路一", "通路二", "生产部", "采购部"]
                else:
                    departments = user_departments or [self.current_user.get('department', '')]
            else:
                # 如果没有用户信息，使用默认部门
                departments = ["通路一", "通路二", "生产部", "采购部"]

            for dept in departments:
                if dept:  # 确保部门不为空
                    self.department_combo.addItem(dept, dept)

        except Exception as e:
            Logger.log_error(f"加载部门选项失败: {str(e)}")
    
    def load_warehouse_options(self):
        """加载仓库选项"""
        try:
            # TODO: 从数据库加载仓库列表
            warehouses = [
                ("WH001", "主仓库"),
                ("WH002", "原料仓"),
                ("WH003", "成品仓")
            ]
            
            for code, name in warehouses:
                self.warehouse_combo.addItem(f"{name}({code})", code)
                
        except Exception as e:
            Logger.log_error(f"加载仓库选项失败: {str(e)}")
    
    def load_category_options(self):
        """加载物料分类选项"""
        try:
            # TODO: 从数据库加载物料分类
            categories = [
                (1, "原材料"),
                (2, "包装材料"),
                (3, "成品"),
                (4, "半成品")
            ]
            
            for category_id, name in categories:
                self.category_combo.addItem(name, category_id)
                
        except Exception as e:
            Logger.log_error(f"加载物料分类选项失败: {str(e)}")
    
    # ==================== 右键菜单和详情查看 ====================
    
    def show_inventory_context_menu(self, position):
        """显示库存右键菜单"""
        try:
            if self.inventory_table.itemAt(position) is None:
                return
            
            current_row = self.inventory_table.rowAt(position.y())
            if current_row < 0:
                return
            
            # 获取选中的库存数据
            item_data = self.inventory_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
            if not item_data:
                return
            
            menu = QMenu(self)
            
            # 查看详情
            view_action = menu.addAction("👁️ 查看详情")
            view_action.triggered.connect(lambda: self.view_inventory_detail_by_data(item_data))
            
            # 查看流水
            transaction_action = menu.addAction("📋 查看流水")
            transaction_action.triggered.connect(lambda: self.view_inventory_transactions(item_data))
            
            menu.addSeparator()
            
            # 库存操作（根据权限显示）
            if self.permission_service.check_transfer_permission():
                transfer_action = menu.addAction("🔄 库存调拨")
                transfer_action.triggered.connect(lambda: self.transfer_inventory_by_data(item_data))
            
            if self.permission_service.check_freeze_permission():
                freeze_action = menu.addAction("❄️ 冻结库存")
                freeze_action.triggered.connect(lambda: self.freeze_inventory_by_data(item_data))
            
            menu.exec(self.inventory_table.mapToGlobal(position))
            
        except Exception as e:
            Logger.log_error(f"显示右键菜单失败: {str(e)}")
    
    def view_inventory_detail(self):
        """查看库存详情"""
        try:
            current_row = self.inventory_table.currentRow()
            if current_row >= 0:
                item_data = self.inventory_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
                if item_data:
                    self.view_inventory_detail_by_data(item_data)
        except Exception as e:
            Logger.log_error(f"查看库存详情失败: {str(e)}")
    
    def view_inventory_detail_by_data(self, inventory_data):
        """根据数据查看库存详情"""
        try:
            from inventory.inventory_detail_dialog import InventoryDetailDialog
            dialog = InventoryDetailDialog(self, inventory_data)
            dialog.exec()
        except Exception as e:
            Logger.log_error(f"打开库存详情对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开详情失败：{str(e)}")
    
    # ==================== 库存流水标签页方法 ====================

    def create_transaction_search_frame(self):
        """创建流水搜索框架"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
                padding: 10px;
            }
        """)

        layout = QGridLayout(frame)
        layout.setSpacing(10)

        # 第一行：流水单号、物料编码、部门
        layout.addWidget(QLabel("流水单号:"), 0, 0)
        self.transaction_no_edit = QLineEdit()
        self.transaction_no_edit.setPlaceholderText("请输入流水单号...")
        layout.addWidget(self.transaction_no_edit, 0, 1)

        layout.addWidget(QLabel("物料编码:"), 0, 2)
        self.trans_material_id_edit = QLineEdit()
        self.trans_material_id_edit.setPlaceholderText("请输入物料编码...")
        layout.addWidget(self.trans_material_id_edit, 0, 3)

        layout.addWidget(QLabel("部门:"), 0, 4)
        self.trans_department_combo = QComboBox()
        self.trans_department_combo.addItem("全部", "")
        self.load_department_options_for_combo(self.trans_department_combo)
        layout.addWidget(self.trans_department_combo, 0, 5)

        # 第二行：业务类型、日期范围
        layout.addWidget(QLabel("业务类型:"), 1, 0)
        self.transaction_type_combo = QComboBox()
        self.transaction_type_combo.addItems([
            "全部", "期初", "采购入库", "生产领料", "生产入库",
            "销售出库", "调拨", "盘点", "报废", "其他"
        ])
        layout.addWidget(self.transaction_type_combo, 1, 1)

        layout.addWidget(QLabel("开始日期:"), 1, 2)
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setCalendarPopup(True)
        layout.addWidget(self.start_date_edit, 1, 3)

        layout.addWidget(QLabel("结束日期:"), 1, 4)
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        layout.addWidget(self.end_date_edit, 1, 5)

        # 第三行：搜索按钮
        button_layout = QHBoxLayout()

        self.search_transaction_btn = QPushButton("🔍 搜索流水")
        self.search_transaction_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.search_transaction_btn.clicked.connect(self.search_transactions)
        button_layout.addWidget(self.search_transaction_btn)

        self.reset_transaction_btn = QPushButton("🔄 重置")
        self.reset_transaction_btn.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                color: #666;
                border: 1px solid #d9d9d9;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e6f7ff;
                border-color: #1890ff;
            }
        """)
        self.reset_transaction_btn.clicked.connect(self.reset_transaction_search)
        button_layout.addWidget(self.reset_transaction_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout, 2, 0, 1, 6)

        return frame

    def create_transaction_table(self):
        """创建流水表格"""
        table = QTableWidget()

        # 设置列
        headers = [
            "流水单号", "业务日期", "业务类型", "物料编码", "物料名称",
            "部门", "仓库", "入库数量", "出库数量", "结存数量",
            "单位", "单位成本", "总成本", "来源单号", "备注", "创建时间"
        ]

        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)

        # 设置表格样式
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #f0f0f0;
                background-color: white;
                alternate-background-color: #fafafa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e6f7ff;
                color: #1890ff;
            }
            QHeaderView::section {
                background-color: #fafafa;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #f0f0f0;
                font-weight: bold;
            }
        """)

        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        table.setSortingEnabled(True)

        # 设置列宽
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 流水单号
        header.resizeSection(0, 150)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)  # 业务日期
        header.resizeSection(1, 100)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)  # 业务类型
        header.resizeSection(2, 100)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # 物料编码
        header.resizeSection(3, 120)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)  # 物料名称

        return table

    def create_transaction_pagination_frame(self):
        """创建流水分页框架"""
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(0, 0, 0, 0)

        # 页面信息
        self.transaction_page_info_label = QLabel("第 1 页，共 0 条记录")
        layout.addWidget(self.transaction_page_info_label)

        layout.addStretch()

        # 分页按钮
        self.transaction_first_page_btn = QPushButton("首页")
        self.transaction_first_page_btn.clicked.connect(lambda: self.go_to_transaction_page(1))
        layout.addWidget(self.transaction_first_page_btn)

        self.transaction_prev_page_btn = QPushButton("上一页")
        self.transaction_prev_page_btn.clicked.connect(self.prev_transaction_page)
        layout.addWidget(self.transaction_prev_page_btn)

        self.transaction_page_spin = QSpinBox()
        self.transaction_page_spin.setMinimum(1)
        self.transaction_page_spin.setValue(1)
        self.transaction_page_spin.valueChanged.connect(self.go_to_transaction_page)
        layout.addWidget(self.transaction_page_spin)

        self.transaction_next_page_btn = QPushButton("下一页")
        self.transaction_next_page_btn.clicked.connect(self.next_transaction_page)
        layout.addWidget(self.transaction_next_page_btn)

        self.transaction_last_page_btn = QPushButton("末页")
        self.transaction_last_page_btn.clicked.connect(self.go_to_transaction_last_page)
        layout.addWidget(self.transaction_last_page_btn)

        return frame

    # ==================== 库存预留标签页方法 ====================

    def create_reservation_search_frame(self):
        """创建预留搜索框架"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
                padding: 10px;
            }
        """)

        layout = QGridLayout(frame)
        layout.setSpacing(10)

        # 第一行：预留单号、物料编码、部门
        layout.addWidget(QLabel("预留单号:"), 0, 0)
        self.reservation_no_edit = QLineEdit()
        self.reservation_no_edit.setPlaceholderText("请输入预留单号...")
        layout.addWidget(self.reservation_no_edit, 0, 1)

        layout.addWidget(QLabel("物料编码:"), 0, 2)
        self.res_material_id_edit = QLineEdit()
        self.res_material_id_edit.setPlaceholderText("请输入物料编码...")
        layout.addWidget(self.res_material_id_edit, 0, 3)

        layout.addWidget(QLabel("部门:"), 0, 4)
        self.res_department_combo = QComboBox()
        self.res_department_combo.addItem("全部", "")
        self.load_department_options_for_combo(self.res_department_combo)
        layout.addWidget(self.res_department_combo, 0, 5)

        # 第二行：预留状态、来源类型
        layout.addWidget(QLabel("预留状态:"), 1, 0)
        self.reservation_status_combo = QComboBox()
        self.reservation_status_combo.addItems(["全部", "已预留", "部分消耗", "已消耗", "已取消"])
        layout.addWidget(self.reservation_status_combo, 1, 1)

        layout.addWidget(QLabel("来源类型:"), 1, 2)
        self.source_type_combo = QComboBox()
        self.source_type_combo.addItems(["全部", "生产计划", "需求计划", "销售订单", "其他"])
        layout.addWidget(self.source_type_combo, 1, 3)

        # 第三行：搜索按钮
        button_layout = QHBoxLayout()

        self.search_reservation_btn = QPushButton("🔍 搜索预留")
        self.search_reservation_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.search_reservation_btn.clicked.connect(self.search_reservations)
        button_layout.addWidget(self.search_reservation_btn)

        self.reset_reservation_btn = QPushButton("🔄 重置")
        self.reset_reservation_btn.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                color: #666;
                border: 1px solid #d9d9d9;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e6f7ff;
                border-color: #1890ff;
            }
        """)
        self.reset_reservation_btn.clicked.connect(self.reset_reservation_search)
        button_layout.addWidget(self.reset_reservation_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout, 2, 0, 1, 6)

        return frame

    def create_reservation_table(self):
        """创建预留表格"""
        table = QTableWidget()

        # 设置列
        headers = [
            "预留单号", "物料编码", "物料名称", "部门", "预留数量",
            "已使用数量", "剩余数量", "单位", "来源类型", "来源单号",
            "预留日期", "预计使用日期", "状态", "备注", "创建时间"
        ]

        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)

        # 设置表格样式
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #f0f0f0;
                background-color: white;
                alternate-background-color: #fafafa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e6f7ff;
                color: #1890ff;
            }
            QHeaderView::section {
                background-color: #fafafa;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #f0f0f0;
                font-weight: bold;
            }
        """)

        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        table.setSortingEnabled(True)

        # 设置列宽
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 预留单号
        header.resizeSection(0, 150)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)  # 物料编码
        header.resizeSection(1, 120)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # 物料名称
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # 部门
        header.resizeSection(3, 100)

        return table

    def create_reservation_pagination_frame(self):
        """创建预留分页框架"""
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(0, 0, 0, 0)

        # 页面信息
        self.reservation_page_info_label = QLabel("第 1 页，共 0 条记录")
        layout.addWidget(self.reservation_page_info_label)

        layout.addStretch()

        # 分页按钮
        self.reservation_first_page_btn = QPushButton("首页")
        self.reservation_first_page_btn.clicked.connect(lambda: self.go_to_reservation_page(1))
        layout.addWidget(self.reservation_first_page_btn)

        self.reservation_prev_page_btn = QPushButton("上一页")
        self.reservation_prev_page_btn.clicked.connect(self.prev_reservation_page)
        layout.addWidget(self.reservation_prev_page_btn)

        self.reservation_page_spin = QSpinBox()
        self.reservation_page_spin.setMinimum(1)
        self.reservation_page_spin.setValue(1)
        self.reservation_page_spin.valueChanged.connect(self.go_to_reservation_page)
        layout.addWidget(self.reservation_page_spin)

        self.reservation_next_page_btn = QPushButton("下一页")
        self.reservation_next_page_btn.clicked.connect(self.next_reservation_page)
        layout.addWidget(self.reservation_next_page_btn)

        self.reservation_last_page_btn = QPushButton("末页")
        self.reservation_last_page_btn.clicked.connect(self.go_to_reservation_last_page)
        layout.addWidget(self.reservation_last_page_btn)

        return frame

    # ==================== 流水标签页业务逻辑 ====================

    def load_department_options_for_combo(self, combo_box):
        """为下拉框加载部门选项"""
        try:
            # 根据用户权限加载可访问的部门
            if self.current_user:
                user_departments = self.current_user.get('departments', [])
                if self.permission_service.has_permission('system:admin'):
                    # 管理员可以看到所有部门
                    departments = ["通路一", "通路二", "生产部", "采购部"]
                else:
                    departments = user_departments or [self.current_user.get('department', '')]
            else:
                # 如果没有用户信息，使用默认部门
                departments = ["通路一", "通路二", "生产部", "采购部"]

            for dept in departments:
                if dept:  # 确保部门不为空
                    combo_box.addItem(dept, dept)

        except Exception as e:
            Logger.log_error(f"加载部门选项失败: {str(e)}")

    def search_transactions(self):
        """搜索库存流水"""
        try:
            # 构建搜索条件
            filters = {}

            if self.transaction_no_edit.text().strip():
                filters['transaction_no'] = self.transaction_no_edit.text().strip()

            if self.trans_material_id_edit.text().strip():
                filters['material_id'] = self.trans_material_id_edit.text().strip()

            if self.trans_department_combo.currentData():
                filters['department'] = self.trans_department_combo.currentData()

            # 业务类型映射
            transaction_type_map = {
                "期初": 1, "采购入库": 2, "生产领料": 3, "生产入库": 4,
                "销售出库": 5, "调拨": 6, "盘点": 7, "报废": 8, "其他": 9
            }

            if self.transaction_type_combo.currentText() != "全部":
                filters['transaction_type'] = transaction_type_map.get(self.transaction_type_combo.currentText())

            # 日期范围
            filters['start_date'] = self.start_date_edit.date().toPython()
            filters['end_date'] = self.end_date_edit.date().toPython()

            self.current_transaction_filters = filters
            self.current_transaction_page = 1
            self.load_transaction_data()

        except Exception as e:
            Logger.log_error(f"搜索库存流水失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"搜索失败：{str(e)}")

    def load_transaction_data(self):
        """加载库存流水数据"""
        try:
            # 获取数据
            total, transaction_list = self.inventory_db.get_inventory_transactions(
                filters=getattr(self, 'current_transaction_filters', {}),
                page=getattr(self, 'current_transaction_page', 1),
                page_size=self.page_size
            )

            self.total_transaction_records = total

            # 更新表格
            self.update_transaction_table(transaction_list)

            # 更新分页信息
            self.update_transaction_pagination_info()

        except Exception as e:
            Logger.log_error(f"加载库存流水数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载数据失败：{str(e)}")

    def update_transaction_table(self, transaction_list):
        """更新流水表格"""
        try:
            self.transaction_table.setRowCount(len(transaction_list))

            for row, transaction in enumerate(transaction_list):
                # 流水单号
                self.transaction_table.setItem(row, 0, QTableWidgetItem(str(transaction.get('transaction_no', ''))))

                # 业务日期
                trans_date = transaction.get('transaction_date')
                self.transaction_table.setItem(row, 1, QTableWidgetItem(
                    trans_date.strftime('%Y-%m-%d') if trans_date else ''
                ))

                # 业务类型
                self.transaction_table.setItem(row, 2, QTableWidgetItem(str(transaction.get('transaction_type_name', ''))))

                # 物料编码
                self.transaction_table.setItem(row, 3, QTableWidgetItem(str(transaction.get('material_id', ''))))

                # 物料名称
                self.transaction_table.setItem(row, 4, QTableWidgetItem(str(transaction.get('material_name', ''))))

                # 部门
                self.transaction_table.setItem(row, 5, QTableWidgetItem(str(transaction.get('department', ''))))

                # 仓库
                warehouse_name = transaction.get('warehouse_name') or transaction.get('warehouse_code', '')
                self.transaction_table.setItem(row, 6, QTableWidgetItem(str(warehouse_name)))

                # 入库数量
                in_qty = transaction.get('in_qty', 0)
                in_item = QTableWidgetItem(f"{in_qty:.4f}" if in_qty > 0 else "")
                if in_qty > 0:
                    in_item.setForeground(QColor('#4caf50'))  # 绿色
                self.transaction_table.setItem(row, 7, in_item)

                # 出库数量
                out_qty = transaction.get('out_qty', 0)
                out_item = QTableWidgetItem(f"{out_qty:.4f}" if out_qty > 0 else "")
                if out_qty > 0:
                    out_item.setForeground(QColor('#f44336'))  # 红色
                self.transaction_table.setItem(row, 8, out_item)

                # 结存数量
                self.transaction_table.setItem(row, 9, QTableWidgetItem(f"{transaction.get('balance_qty', 0):.4f}"))

                # 单位
                self.transaction_table.setItem(row, 10, QTableWidgetItem(str(transaction.get('unit', ''))))

                # 单位成本
                self.transaction_table.setItem(row, 11, QTableWidgetItem(f"{transaction.get('unit_cost', 0):.4f}"))

                # 总成本
                self.transaction_table.setItem(row, 12, QTableWidgetItem(f"{transaction.get('total_cost', 0):.2f}"))

                # 来源单号
                self.transaction_table.setItem(row, 13, QTableWidgetItem(str(transaction.get('source_no', ''))))

                # 备注
                self.transaction_table.setItem(row, 14, QTableWidgetItem(str(transaction.get('remark', ''))))

                # 创建时间
                create_time = transaction.get('create_time')
                self.transaction_table.setItem(row, 15, QTableWidgetItem(
                    create_time.strftime('%Y-%m-%d %H:%M') if create_time else ''
                ))

        except Exception as e:
            Logger.log_error(f"更新流水表格失败: {str(e)}")

    def reset_transaction_search(self):
        """重置流水搜索条件"""
        self.transaction_no_edit.clear()
        self.trans_material_id_edit.clear()
        self.trans_department_combo.setCurrentIndex(0)
        self.transaction_type_combo.setCurrentIndex(0)
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.end_date_edit.setDate(QDate.currentDate())
        self.search_transactions()

    def update_transaction_pagination_info(self):
        """更新流水分页信息"""
        try:
            total_pages = (getattr(self, 'total_transaction_records', 0) + self.page_size - 1) // self.page_size
            total_pages = max(1, total_pages)
            current_page = getattr(self, 'current_transaction_page', 1)

            self.transaction_page_info_label.setText(f"第 {current_page} 页，共 {getattr(self, 'total_transaction_records', 0)} 条记录")

            # 更新分页控件状态
            self.transaction_page_spin.setMaximum(total_pages)
            self.transaction_page_spin.setValue(current_page)

            self.transaction_first_page_btn.setEnabled(current_page > 1)
            self.transaction_prev_page_btn.setEnabled(current_page > 1)
            self.transaction_next_page_btn.setEnabled(current_page < total_pages)
            self.transaction_last_page_btn.setEnabled(current_page < total_pages)

        except Exception as e:
            Logger.log_error(f"更新流水分页信息失败: {str(e)}")

    def go_to_transaction_page(self, page):
        """跳转到指定流水页"""
        if page != getattr(self, 'current_transaction_page', 1):
            self.current_transaction_page = page
            self.load_transaction_data()

    def prev_transaction_page(self):
        """上一页流水"""
        current_page = getattr(self, 'current_transaction_page', 1)
        if current_page > 1:
            self.go_to_transaction_page(current_page - 1)

    def next_transaction_page(self):
        """下一页流水"""
        current_page = getattr(self, 'current_transaction_page', 1)
        total_pages = (getattr(self, 'total_transaction_records', 0) + self.page_size - 1) // self.page_size
        if current_page < total_pages:
            self.go_to_transaction_page(current_page + 1)

    def go_to_transaction_last_page(self):
        """流水末页"""
        total_pages = (getattr(self, 'total_transaction_records', 0) + self.page_size - 1) // self.page_size
        self.go_to_transaction_page(total_pages)

    # ==================== 预留标签页业务逻辑 ====================

    def search_reservations(self):
        """搜索库存预留"""
        try:
            # 构建搜索条件
            filters = {}

            if self.reservation_no_edit.text().strip():
                filters['reservation_no'] = self.reservation_no_edit.text().strip()

            if self.res_material_id_edit.text().strip():
                filters['material_id'] = self.res_material_id_edit.text().strip()

            if self.res_department_combo.currentData():
                filters['department'] = self.res_department_combo.currentData()

            # 预留状态映射
            status_map = {"已预留": 1, "部分消耗": 2, "已消耗": 3, "已取消": 4}
            if self.reservation_status_combo.currentText() != "全部":
                filters['status'] = status_map.get(self.reservation_status_combo.currentText())

            # 来源类型映射
            source_type_map = {
                "生产计划": "production_plan",
                "需求计划": "demand_plan",
                "销售订单": "sales_order",
                "其他": "other"
            }
            if self.source_type_combo.currentText() != "全部":
                filters['source_type'] = source_type_map.get(self.source_type_combo.currentText())

            self.current_reservation_filters = filters
            self.current_reservation_page = 1
            self.load_reservation_data()

        except Exception as e:
            Logger.log_error(f"搜索库存预留失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"搜索失败：{str(e)}")

    def load_reservation_data(self):
        """加载库存预留数据"""
        try:
            # 获取数据
            total, reservation_list = self.inventory_db.get_inventory_reservations(
                filters=getattr(self, 'current_reservation_filters', {}),
                page=getattr(self, 'current_reservation_page', 1),
                page_size=self.page_size
            )

            self.total_reservation_records = total

            # 更新表格
            self.update_reservation_table(reservation_list)

            # 更新分页信息
            self.update_reservation_pagination_info()

        except Exception as e:
            Logger.log_error(f"加载库存预留数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载数据失败：{str(e)}")

    def update_reservation_table(self, reservation_list):
        """更新预留表格"""
        try:
            self.reservation_table.setRowCount(len(reservation_list))

            for row, reservation in enumerate(reservation_list):
                # 预留单号
                self.reservation_table.setItem(row, 0, QTableWidgetItem(str(reservation.get('reservation_no', ''))))

                # 物料编码
                self.reservation_table.setItem(row, 1, QTableWidgetItem(str(reservation.get('material_id', ''))))

                # 物料名称
                self.reservation_table.setItem(row, 2, QTableWidgetItem(str(reservation.get('material_name', ''))))

                # 部门
                self.reservation_table.setItem(row, 3, QTableWidgetItem(str(reservation.get('department', ''))))

                # 预留数量
                reserved_qty = reservation.get('reserved_qty', 0)
                self.reservation_table.setItem(row, 4, QTableWidgetItem(f"{reserved_qty:.4f}"))

                # 已使用数量
                used_qty = reservation.get('used_qty', 0)
                self.reservation_table.setItem(row, 5, QTableWidgetItem(f"{used_qty:.4f}"))

                # 剩余数量
                remaining_qty = reserved_qty - used_qty
                remaining_item = QTableWidgetItem(f"{remaining_qty:.4f}")
                if remaining_qty <= 0:
                    remaining_item.setForeground(QColor('#f44336'))  # 红色
                else:
                    remaining_item.setForeground(QColor('#4caf50'))  # 绿色
                self.reservation_table.setItem(row, 6, remaining_item)

                # 单位
                self.reservation_table.setItem(row, 7, QTableWidgetItem(str(reservation.get('unit', ''))))

                # 来源类型
                source_type_display = {
                    'production_plan': '生产计划',
                    'demand_plan': '需求计划',
                    'sales_order': '销售订单',
                    'other': '其他'
                }.get(reservation.get('source_type', ''), reservation.get('source_type', ''))
                self.reservation_table.setItem(row, 8, QTableWidgetItem(source_type_display))

                # 来源单号
                self.reservation_table.setItem(row, 9, QTableWidgetItem(str(reservation.get('source_no', ''))))

                # 预留日期
                reservation_date = reservation.get('reservation_date')
                self.reservation_table.setItem(row, 10, QTableWidgetItem(
                    reservation_date.strftime('%Y-%m-%d') if reservation_date else ''
                ))

                # 预计使用日期
                expected_use_date = reservation.get('expected_use_date')
                self.reservation_table.setItem(row, 11, QTableWidgetItem(
                    expected_use_date.strftime('%Y-%m-%d') if expected_use_date else ''
                ))

                # 状态
                status_item = QTableWidgetItem(str(reservation.get('status_name', '')))
                status = reservation.get('status_name', '')
                if status == '已取消':
                    status_item.setForeground(QColor('#f44336'))  # 红色
                elif status == '已消耗':
                    status_item.setForeground(QColor('#666666'))  # 灰色
                elif status == '部分消耗':
                    status_item.setForeground(QColor('#ff9800'))  # 橙色
                else:
                    status_item.setForeground(QColor('#4caf50'))  # 绿色
                self.reservation_table.setItem(row, 12, status_item)

                # 备注
                self.reservation_table.setItem(row, 13, QTableWidgetItem(str(reservation.get('remark', ''))))

                # 创建时间
                create_time = reservation.get('create_time')
                self.reservation_table.setItem(row, 14, QTableWidgetItem(
                    create_time.strftime('%Y-%m-%d %H:%M') if create_time else ''
                ))

        except Exception as e:
            Logger.log_error(f"更新预留表格失败: {str(e)}")

    def reset_reservation_search(self):
        """重置预留搜索条件"""
        self.reservation_no_edit.clear()
        self.res_material_id_edit.clear()
        self.res_department_combo.setCurrentIndex(0)
        self.reservation_status_combo.setCurrentIndex(0)
        self.source_type_combo.setCurrentIndex(0)
        self.search_reservations()

    def update_reservation_pagination_info(self):
        """更新预留分页信息"""
        try:
            total_pages = (getattr(self, 'total_reservation_records', 0) + self.page_size - 1) // self.page_size
            total_pages = max(1, total_pages)
            current_page = getattr(self, 'current_reservation_page', 1)

            self.reservation_page_info_label.setText(f"第 {current_page} 页，共 {getattr(self, 'total_reservation_records', 0)} 条记录")

            # 更新分页控件状态
            self.reservation_page_spin.setMaximum(total_pages)
            self.reservation_page_spin.setValue(current_page)

            self.reservation_first_page_btn.setEnabled(current_page > 1)
            self.reservation_prev_page_btn.setEnabled(current_page > 1)
            self.reservation_next_page_btn.setEnabled(current_page < total_pages)
            self.reservation_last_page_btn.setEnabled(current_page < total_pages)

        except Exception as e:
            Logger.log_error(f"更新预留分页信息失败: {str(e)}")

    def go_to_reservation_page(self, page):
        """跳转到指定预留页"""
        if page != getattr(self, 'current_reservation_page', 1):
            self.current_reservation_page = page
            self.load_reservation_data()

    def prev_reservation_page(self):
        """上一页预留"""
        current_page = getattr(self, 'current_reservation_page', 1)
        if current_page > 1:
            self.go_to_reservation_page(current_page - 1)

    def next_reservation_page(self):
        """下一页预留"""
        current_page = getattr(self, 'current_reservation_page', 1)
        total_pages = (getattr(self, 'total_reservation_records', 0) + self.page_size - 1) // self.page_size
        if current_page < total_pages:
            self.go_to_reservation_page(current_page + 1)

    def go_to_reservation_last_page(self):
        """预留末页"""
        total_pages = (getattr(self, 'total_reservation_records', 0) + self.page_size - 1) // self.page_size
        self.go_to_reservation_page(total_pages)