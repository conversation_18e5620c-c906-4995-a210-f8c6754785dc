"""
需求明细添加/编辑对话框
用于添加和编辑需求明细信息
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QComboBox, QFormLayout, QDialogButtonBox, QDoubleSpinBox,
                           QSpinBox, QTextEdit, QMessageBox, QPushButton, QFrame,
                           QGroupBox, QDateEdit, QGridLayout, QWidget)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QIcon
from material.bom_db import BOMDB
from demand.demand_plan_db import DemandPlanDB
from core.logger import Logger
from datetime import timedelta

class DemandDetailDialog(QDialog):
    """需求明细添加/编辑对话框"""
    
    def __init__(self, parent=None, detail_data=None, mode='add'):
        super().__init__(parent)
        self.detail_data = detail_data
        self.mode = mode  # 'add', 'edit', 'view'
        self.bom_db = BOMDB()
        self.demand_plan_db = DemandPlanDB()
        self.selected_material = None
        
        self.setWindowTitle({
            'add': '添加需求明细',
            'edit': '编辑需求明细',
            'view': '查看需求明细'
        }.get(mode, '需求明细信息'))
        
        self.setModal(True)
        self.resize(600, 500)
        self.setup_ui()
        
        # 如果是编辑模式，填充数据
        if detail_data:
            self.fill_form_data()
            
        # 如果是查看模式，设置只读
        if mode == 'view':
            self.set_readonly_mode()
    
    def setup_ui(self):
        """设置界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建物料信息区域
        self.create_material_section(main_layout)
        
        # 创建需求信息区域
        self.create_demand_section(main_layout)
        
        # 创建其他信息区域
        self.create_other_section(main_layout)
        
        # 创建按钮区域
        self.create_button_section(main_layout)
    
    def create_material_section(self, parent_layout):
        """创建物料信息区域"""
        # 物料信息卡片
        material_card = QFrame()
        material_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        material_layout = QVBoxLayout(material_card)
        material_layout.setContentsMargins(20, 15, 20, 20)
        material_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📦 物料信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        material_layout.addWidget(title_label)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)
        
        # 第一行：物料编码、选择按钮
        form_layout.addWidget(QLabel("物料编码:"), 0, 0)
        
        material_row_layout = QHBoxLayout()
        self.material_id_edit = QLineEdit()
        self.material_id_edit.setPlaceholderText("请选择物料")
        self.material_id_edit.setReadOnly(True)
        material_row_layout.addWidget(self.material_id_edit)
        
        self.select_material_btn = QPushButton("选择物料")
        self.select_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
            QPushButton:pressed {
                background-color: #096dd9;
            }
        """)
        self.select_material_btn.clicked.connect(self.select_material)
        material_row_layout.addWidget(self.select_material_btn)
        
        material_widget = QWidget()
        material_widget.setLayout(material_row_layout)
        form_layout.addWidget(material_widget, 0, 1, 1, 3)
        
        # 第二行：物料名称、物料分类
        form_layout.addWidget(QLabel("物料名称:"), 1, 0)
        self.material_name_edit = QLineEdit()
        self.material_name_edit.setReadOnly(True)
        self.material_name_edit.setStyleSheet("background-color: #f5f5f5;")
        form_layout.addWidget(self.material_name_edit, 1, 1)
        
        form_layout.addWidget(QLabel("物料分类:"), 1, 2)
        self.category_name_edit = QLineEdit()
        self.category_name_edit.setReadOnly(True)
        self.category_name_edit.setStyleSheet("background-color: #f5f5f5;")
        form_layout.addWidget(self.category_name_edit, 1, 3)
        
        # 第三行：规格型号、单位
        form_layout.addWidget(QLabel("规格型号:"), 2, 0)
        self.specification_edit = QLineEdit()
        self.specification_edit.setReadOnly(True)
        self.specification_edit.setStyleSheet("background-color: #f5f5f5;")
        form_layout.addWidget(self.specification_edit, 2, 1)
        
        form_layout.addWidget(QLabel("单位:"), 2, 2)
        self.unit_edit = QLineEdit()
        self.unit_edit.setReadOnly(True)
        self.unit_edit.setStyleSheet("background-color: #f5f5f5;")
        form_layout.addWidget(self.unit_edit, 2, 3)
        
        # 设置列宽比例
        form_layout.setColumnStretch(1, 2)
        form_layout.setColumnStretch(3, 2)
        
        material_layout.addWidget(form_widget)
        parent_layout.addWidget(material_card)
    
    def create_demand_section(self, parent_layout):
        """创建需求信息区域"""
        # 需求信息卡片
        demand_card = QFrame()
        demand_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        demand_layout = QVBoxLayout(demand_card)
        demand_layout.setContentsMargins(20, 15, 20, 20)
        demand_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📋 需求信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        demand_layout.addWidget(title_label)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)
        
        # 第一行：需求数量、需求日期
        form_layout.addWidget(QLabel("需求数量:"), 0, 0)
        self.required_qty_edit = QDoubleSpinBox()
        self.required_qty_edit.setRange(0.01, 999999.99)
        self.required_qty_edit.setDecimals(2)
        self.required_qty_edit.setValue(1.0)
        self.required_qty_edit.valueChanged.connect(self.calculate_amounts)
        form_layout.addWidget(self.required_qty_edit, 0, 1)
        
        form_layout.addWidget(QLabel("需求日期:"), 0, 2)
        self.required_date_edit = QDateEdit()
        self.required_date_edit.setCalendarPopup(True)
        # 初始化时设置为最小需求日期
        self.set_minimum_required_date()
        self.required_date_edit.setDisplayFormat("yyyy-MM-dd")
        # 连接日期变化信号
        self.required_date_edit.dateChanged.connect(self.on_required_date_changed)
        form_layout.addWidget(self.required_date_edit, 0, 3)
        
      
        
        # 第三行：单价、金额
        form_layout.addWidget(QLabel("单价:"), 2, 0)
        self.unit_price_edit = QDoubleSpinBox()
        self.unit_price_edit.setRange(0, 999999.99)
        self.unit_price_edit.setDecimals(2)
        self.unit_price_edit.setSuffix(" 元")
        self.unit_price_edit.valueChanged.connect(self.calculate_amounts)
        self.unit_price_edit.setReadOnly(True)
        form_layout.addWidget(self.unit_price_edit, 2, 1)
        
        form_layout.addWidget(QLabel("金额:"), 2, 2)
        self.total_amount_edit = QDoubleSpinBox()
        self.total_amount_edit.setRange(0, 999999999.99)
        self.total_amount_edit.setDecimals(2)
        self.total_amount_edit.setSuffix(" 元")
        self.total_amount_edit.setReadOnly(True)
        self.total_amount_edit.setStyleSheet("background-color: #f5f5f5;")
        self.total_amount_edit.setReadOnly(True)
        form_layout.addWidget(self.total_amount_edit, 2, 3)
        
        # 设置列宽比例
        form_layout.setColumnStretch(1, 2)
        form_layout.setColumnStretch(3, 2)
        
        demand_layout.addWidget(form_widget)
        parent_layout.addWidget(demand_card)
    
    def create_other_section(self, parent_layout):
        """创建其他信息区域"""
        # 其他信息卡片
        other_card = QFrame()
        other_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        other_layout = QVBoxLayout(other_card)
        other_layout.setContentsMargins(20, 15, 20, 20)
        other_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📝 其他信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        other_layout.addWidget(title_label)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)
        
        # 用途说明
        form_layout.addWidget(QLabel("用途说明:"), 0, 0)
        self.usage_purpose_edit = QLineEdit()
        self.usage_purpose_edit.setPlaceholderText("请输入用途说明...")
        form_layout.addWidget(self.usage_purpose_edit, 0, 1, 1, 3)
        
        # 备注
        form_layout.addWidget(QLabel("备注:"), 1, 0)
        self.remark_edit = QTextEdit()
        self.remark_edit.setMaximumHeight(80)
        self.remark_edit.setPlaceholderText("请输入备注信息...")
        form_layout.addWidget(self.remark_edit, 1, 1, 1, 3)
        
        # 设置列宽比例
        form_layout.setColumnStretch(1, 3)
        
        other_layout.addWidget(form_widget)
        parent_layout.addWidget(other_card)
    
    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        if self.mode != 'view':
            # 确定按钮
            self.ok_btn = QPushButton("确定")
            self.ok_btn.setStyleSheet("""
                QPushButton {
                    background-color: #1890ff;
                    color: white;
                    border: none;
                    padding: 10px 24px;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #40a9ff;
                }
                QPushButton:pressed {
                    background-color: #096dd9;
                }
            """)
            self.ok_btn.clicked.connect(self.accept_data)
            button_layout.addWidget(self.ok_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #d9d9d9;
                color: #595959;
                border: 1px solid #d9d9d9;
                padding: 10px 24px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #bfbfbf;
                border-color: #bfbfbf;
            }
            QPushButton:pressed {
                background-color: #a6a6a6;
                border-color: #a6a6a6;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        parent_layout.addLayout(button_layout)
    
    def select_material(self):
        """选择物料（从BOM成品物料中选择）"""
        try:
            from demand.bom_product_search_dialog import BOMProductSearchDialog
            
            # 获取父对话框已选择的物料ID列表
            parent_dialog = self.parent()
            selected_materials = []
            
            if hasattr(parent_dialog, 'details_data'):
                selected_materials = [detail.get('material_id') for detail in parent_dialog.details_data]
                # 如果是编辑模式，排除当前编辑的物料
                if self.mode == 'edit' and self.detail_data:
                    current_material_id = self.detail_data.get('material_id')
                    if current_material_id in selected_materials:
                        selected_materials.remove(current_material_id)
            
            dialog = BOMProductSearchDialog(
                self,
                selected_materials=selected_materials
            )
            
            dialog.material_selected.connect(self.on_material_selected)
            dialog.exec()
            
        except ImportError:
            QMessageBox.information(self, "提示", "BOM成品物料选择对话框开发中...")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"选择物料失败: {str(e)}")
    
    def on_material_selected(self, material_data):
        """物料选择回调"""
        self.selected_material = material_data
        
        # 填充物料信息
        self.material_id_edit.setText(material_data.get('material_id', ''))
        self.material_name_edit.setText(material_data.get('material_name', ''))
        self.category_name_edit.setText(material_data.get('category_name', ''))
        self.specification_edit.setText(material_data.get('specification', ''))
        self.unit_edit.setText(material_data.get('unit', ''))
        
        # 从物料表获取成本价格并设置单价
        material_id = material_data.get('material_id', '')
        if material_id:
            try:
                # 从数据库获取物料成本价格
                cost_price = self.demand_plan_db.get_material_cost(material_id)
                if cost_price and cost_price > 0:
                    self.unit_price_edit.setValue(float(cost_price))
                else:
                    # 如果没有成本价格，尝试使用标准价格
                    standard_price = material_data.get('standard_price', 0)
                    if standard_price:
                        self.unit_price_edit.setValue(float(standard_price))
                    else:
                        # 都没有则设置为0
                        self.unit_price_edit.setValue(0.0)
            except Exception as e:
                print(f"获取物料成本价格失败: {str(e)}")
                # 获取失败时使用标准价格作为备选
                standard_price = material_data.get('standard_price', 0)
                if standard_price:
                    self.unit_price_edit.setValue(float(standard_price))
                else:
                    self.unit_price_edit.setValue(0.0)
        # 计算金额
        self.calculate_amounts()
    
    # def query_stock_info(self, material_id):
    #     """查询库存信息"""
    #     try:
    #         # TODO: 实现库存查询
    #         # stock_info = self.demand_plan_db.get_material_stock(material_id)
    #         # self.current_stock_edit.setValue(stock_info.get('current_qty', 0))
            
    #         # 临时设置为0
    #         # self.current_stock_edit.setValue(0)
            
    #     except Exception as e:
    #         print(f"查询库存信息失败: {str(e)}")
    #         # self.current_stock_edit.setValue(0)
    
    def calculate_shortage(self):
        """计算缺口数量"""
        # required_qty = self.required_qty_edit.value()
        # current_stock = self.current_stock_edit.value()
        # shortage_qty = required_qty - current_stock
        # self.shortage_qty_edit.setValue(shortage_qty)
        
        # # 重新计算金额
        # self.calculate_amounts()
    
    def calculate_amounts(self):
        """计算金额"""
        required_qty = self.required_qty_edit.value()
        unit_price = self.unit_price_edit.value()
        total_amount = required_qty * unit_price
        self.total_amount_edit.setValue(total_amount)
        
        # 如果需求数量变化，重新计算缺口
        if self.sender() == self.required_qty_edit:
            self.calculate_shortage()
    
    def fill_form_data(self):
        """填充表单数据"""
        if not self.detail_data:
            return
        
        # 物料信息
        material_id = self.detail_data.get('material_id', '')
        self.material_id_edit.setText(material_id)
        self.material_name_edit.setText(self.detail_data.get('material_name', ''))
        
        # 设置物料分类
        self.category_name_edit.setText(self.detail_data.get('category_name', ''))
        
        # 获取并设置规格型号
        if material_id:
            try:
                # 使用专门的方法获取物料规格型号
                specification = self.demand_plan_db.get_material_specification(material_id)
                if specification:
                    self.specification_edit.setText(specification)
                else:
                    # 如果数据库中没有，使用明细数据中保存的规格型号
                    self.specification_edit.setText(self.detail_data.get('specification', ''))
            except Exception as e:
                # 如果查询失败，使用明细数据中的规格型号
                self.specification_edit.setText(self.detail_data.get('specification', ''))
        else:
            # 如果没有物料ID，直接使用保存的数据
            self.specification_edit.setText(self.detail_data.get('specification', ''))
        
        # 设置单位
        self.unit_edit.setText(self.detail_data.get('required_unit', ''))
        
        # 构造selected_material对象，确保后续操作正常
        self.selected_material = {
            'material_id': material_id,
            'material_name': self.detail_data.get('material_name', ''),
            'category_name': self.detail_data.get('category_name', ''),
            'specification': self.specification_edit.text(),
            'unit': self.detail_data.get('required_unit', ''),
            'category_id': self.detail_data.get('material_category_id'),
            'standard_price': self.detail_data.get('unit_price', 0)
        }

        # 需求信息
        self.required_qty_edit.setValue(float(self.detail_data.get('required_qty', 0)))
        
        if self.detail_data.get('required_date'):
            # 编辑模式时使用保存的日期，不使用最小日期
            required_date = QDate.fromString(str(self.detail_data['required_date']), "yyyy-MM-dd")
            # 暂时断开信号连接，避免触发日期变化处理
            self.required_date_edit.dateChanged.disconnect()
            self.required_date_edit.setDate(required_date)
            # 重新连接信号
            self.required_date_edit.dateChanged.connect(self.on_required_date_changed)
        
        
        self.unit_price_edit.setValue(float(self.detail_data.get('unit_price', 0)))
        self.total_amount_edit.setValue(float(self.detail_data.get('total_amount', 0)))
        
        # 其他信息
        self.usage_purpose_edit.setText(self.detail_data.get('usage_purpose', ''))
        self.remark_edit.setPlainText(self.detail_data.get('remark', ''))
    
    def set_readonly_mode(self):
        """设置只读模式"""
        controls = [
            self.required_qty_edit, self.required_date_edit,
            self.unit_price_edit, self.usage_purpose_edit, self.remark_edit
        ]
        
        for control in controls:
            control.setEnabled(False)
            
        # 隐藏选择物料按钮
        self.select_material_btn.setVisible(False)
    
    def validate_data(self):
        """验证数据"""
        if not self.material_id_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "请选择物料！")
            return False
        
        if self.required_qty_edit.value() <= 0:
            QMessageBox.warning(self, "验证失败", "需求数量必须大于0！")
            self.required_qty_edit.setFocus()
            return False
        
        # 验证需求数量是否为BOM装箱数量的倍数
        material_id = self.material_id_edit.text().strip()
        required_qty = self.required_qty_edit.value()
        
        if material_id:
            try:
                # 从BOM表获取该物料的装箱数量
                box_qty = self.get_material_box_quantity(material_id)
                
                if box_qty and box_qty > 0:
                    # 检查需求数量是否为装箱数量的倍数
                    if required_qty % box_qty != 0:
                        QMessageBox.warning(
                            self, "验证失败", 
                            f"需求数量必须是装箱数量({box_qty})的倍数！\n"
                            f"当前需求数量: {required_qty}\n"
                            f"建议数量: {int(required_qty // box_qty + 1) * box_qty} 或 {int(required_qty // box_qty) * box_qty}"
                        )
                        self.required_qty_edit.setFocus()
                        return False
                        
            except Exception as e:
                print(f"验证装箱数量失败: {str(e)}")       
        
        return True
    
    def get_material_box_quantity(self, material_id):
        """获取物料的装箱数量"""
        try:
            # 查询BOM表中该物料作为成品的标准产出数量
            query = """
            SELECT package_qty_per_box, production_unit
            FROM bom_header 
            WHERE parent_material_id = %s 
            AND status = 1 
            AND effective_date <= CURDATE() 
            AND expiry_date > CURDATE()
            ORDER BY create_time DESC 
            LIMIT 1
            """
            
            result = self.bom_db.db_manager.execute_query(query, (material_id,))
            
            if result:
                box_qty = result[0].get('package_qty_per_box', 0)
                return float(box_qty) if box_qty else None
            else:
                # 如果没有找到BOM信息，尝试从物料基础信息中获取包装规格
                return self.get_material_package_spec(material_id)
                
        except Exception as e:
            Logger.log_error(f"获取物料装箱数量失败: {str(e)}")
            return None
        
    def get_material_package_spec(self, material_id):
        """从物料基础信息获取包装规格"""
        try:
            # 查询物料表中的包装规格信息
            query = """
            SELECT package_spec, package_unit
            FROM materials 
            WHERE material_id = %s
            """
            
            result = self.demand_plan_db.db_manager.execute_query(query, (material_id,))
            
            if result:
                package_spec = result[0].get('package_spec', 0)
                return float(package_spec) if package_spec else None
            
            return None
            
        except Exception as e:
            Logger.log_error(f"获取物料包装规格失败: {str(e)}")
            return None
    
    def get_detail_data(self):
        """获取明细数据"""
        # 确保所有必需字段都有值
        detail_data = {
            'material_id': self.material_id_edit.text().strip() or None,
            'material_name': self.material_name_edit.text().strip() or '',
            'material_category_id': self.selected_material.get('category_id') if self.selected_material else None,
            'category_name': self.category_name_edit.text().strip() or '',
            'specification': self.specification_edit.text().strip() or '',
            'required_qty': self.required_qty_edit.value() or 0,
            'required_unit': self.unit_edit.text().strip() or '',
            'required_date': self.required_date_edit.date().toString("yyyy-MM-dd"),
            'unit_price': self.unit_price_edit.value() or 0,
            'total_amount': self.total_amount_edit.value() or 0,
            'usage_purpose': self.usage_purpose_edit.text().strip() or '',
            'remark': self.remark_edit.toPlainText().strip() or '',
            'execution_status': 0,  # 默认未执行
            'executed_qty': 0,      # 默认已执行数量为0
            'supplier_id': None,    # 供应商ID，后续可扩展
            # 新增字段，初始值为空
            'production_plan_no': None,
            'production_plan_id': None,
            'purchase_request_no': None,
            'purchase_request_id': None,
            'bom_id': None,
            'bom_code': None,
            'is_bom_expanded': 0,
            'bom_expand_time': None
        }
        
        # 从选中的物料获取分类ID
        if self.selected_material and self.selected_material.get('category_id'):
            detail_data['material_category_id'] = self.selected_material['category_id']
        
        return detail_data
    
    def accept_data(self):
        """确认数据"""
        if self.validate_data():
            self.accept()
    
    def set_minimum_required_date(self):
        """设置最小需求日期"""
        try:
            # 获取父对话框的计划信息
            parent_dialog = self.parent()
            if not hasattr(parent_dialog, 'plan_date_edit') or not hasattr(parent_dialog, 'priority_combo'):
                # 如果无法获取父对话框信息，设置为当前日期+7天
                self.required_date_edit.setDate(QDate.currentDate().addDays(7))
                return
            
            # 获取计划日期
            plan_date = parent_dialog.plan_date_edit.date().toPyDate()
            
            # 获取紧急程度
            priority_level = parent_dialog.priority_combo.currentData()
            
            # 根据紧急程度确定最小间隔天数
            min_days_map = {
                1: 15,  # 普通：15天
                2: 12,  # 紧急：12天
                3: 8    # 特急：8天
            }
            
            min_days = min_days_map.get(priority_level, 15)  # 默认15天
            
            # 计算最小需求日期
            min_required_date = plan_date + timedelta(days=min_days)
            
            # 设置日期
            self.required_date_edit.setDate(QDate.fromString(min_required_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))
            
        except Exception as e:
            Logger.log_error(f"设置最小需求日期失败: {str(e)}")
            # 出错时设置为当前日期+7天
            self.required_date_edit.setDate(QDate.currentDate().addDays(7))
    
    def on_required_date_changed(self, date):
        """需求日期变化时的处理"""
        try:
            # 获取父对话框的计划信息
            parent_dialog = self.parent()
            if not hasattr(parent_dialog, 'plan_date_edit') or not hasattr(parent_dialog, 'priority_combo'):
                return
            
            # 获取计划日期
            plan_date = parent_dialog.plan_date_edit.date().toPyDate()
            
            # 获取紧急程度
            priority_level = parent_dialog.priority_combo.currentData()
            priority_name = parent_dialog.priority_combo.currentText()
            
            # 获取用户选择的需求日期
            required_date = date.toPyDate()
            
            # 根据紧急程度确定最小间隔天数
            min_days_map = {
                1: 15,  # 普通：15天
                2: 12,  # 紧急：12天
                3: 8    # 特急：8天
            }
            
            min_days = min_days_map.get(priority_level, 15)  # 默认15天
            
            # 计算实际间隔天数
            actual_days = (required_date - plan_date).days
            
            # 如果不满足要求，显示友好提示
            if actual_days < min_days:
                min_required_date = plan_date + timedelta(days=min_days)
                
                # 使用信息框而不是警告框，更友好
                reply = QMessageBox.information(
                    self, "日期提醒",
                    f"温馨提示：根据当前紧急程度({priority_name})，建议需求日期不早于 {min_required_date.strftime('%Y-%m-%d')}\n\n"
                    f"当前设置: {required_date.strftime('%Y-%m-%d')}\n"
                    f"计划日期: {plan_date.strftime('%Y-%m-%d')}\n"
                    f"实际间隔: {actual_days}天，建议间隔: {min_days}天\n\n"
                    f"是否自动调整为建议日期？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )
                
                # 如果用户选择自动调整
                if reply == QMessageBox.StandardButton.Yes:
                    # 暂时断开信号连接，避免递归调用
                    self.required_date_edit.dateChanged.disconnect()
                    self.required_date_edit.setDate(QDate.fromString(min_required_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))
                    # 重新连接信号
                    self.required_date_edit.dateChanged.connect(self.on_required_date_changed)
            
        except Exception as e:
            Logger.log_error(f"处理需求日期变化失败: {str(e)}")





