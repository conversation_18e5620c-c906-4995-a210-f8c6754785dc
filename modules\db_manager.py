from PyQt6.QtWidgets import QMessageBox
from core.database import DatabasePool
from core.logger import Logger
from core.exceptions import DatabaseError

class DatabaseManager:
    _instance = None
    _status_callback = None
    _logger = Logger.get_logger()
    
    @classmethod
    def set_status_callback(cls, callback):
        cls._status_callback = callback
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseManager, cls).__new__(cls)
            cls._instance.init_connection()
        return cls._instance
    
    def __init__(self):
        print("初始化数据库管理器...")  # 添加调试输出
        self.init_connection()
    
    def init_connection(self):
        """初始化数据库连接"""
        try:
            print("尝试初始化数据库连接...")
            self.connection = DatabasePool.get_instance().get_connection()
            print("数据库连接初始化成功")
            self._logger.info("数据库连接成功")
        except DatabaseError as e:
            error_msg = f"数据库连接错误: {str(e)}"
            print(error_msg)
            self._logger.error(error_msg)
            Logger.log_db_error("数据库连接初始化失败", error=e, extra={
                'error_location': 'DatabaseManager.init_connection',
                'error_type': 'DatabaseError',
                'connection_status': 'failed'
            })
            raise DatabaseError(error_msg)
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            if not self.connection or not self.connection.is_connected():
                self.init_connection()
            return self.connection
        except Exception as e:
            self._logger.error(f"获取数据库连接失败: {str(e)}")
            Logger.log_db_error("获取数据库连接失败", error=e, extra={
                'error_location': 'DatabaseManager.get_connection',
                'connection_status': 'disconnected' if not self.connection else 'invalid',
                'connection_type': 'get_connection'
            })
            self.init_connection()  # 尝试重新初始化连接
            return self.connection
    
    def execute_query(self, query, params=None):
        """执行查询并返回结果"""
        try:
            # 检查连接状态
            if not self.connection or not self.connection.is_connected():
                self.init_connection()
                
            cursor = self.connection.cursor(dictionary=True)
            
            print(f"执行查询: {query}")
            print(f"参数: {params}")
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            result = cursor.fetchall()
            cursor.close()
            
            print(f"查询结果数量: {len(result) if result else 0}")
            return result
            
        except Exception as e:
            self._logger.error(f"查询执行错误: {str(e)}")
            # 如果是连接错误，尝试重新连接
            if "MySQL Connection not available" in str(e):
                try:
                    self.init_connection()
                    return self.execute_query(query, params)  # 重试查询
                except Exception as retry_error:
                    self._logger.error(f"重试查询失败: {str(retry_error)}")
            raise
    
    def execute_update(self, query, params=None):
        """执行更新操作（插入、更新、删除）"""
        try:
            if not self.connection or not self.connection.is_connected():
                self.init_connection()
                
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            self.connection.commit()
            affected_rows = cursor.rowcount
            cursor.close()
            return affected_rows
        except Exception as e:
            self._logger.error(f"更新执行错误: {str(e)}")
            self.connection.rollback()
            QMessageBox.critical(None, "更新错误", f"执行更新操作时出错：\n{str(e)}")
            return -1
            
    def execute_many(self, query, values):
        """执行批量操作"""
        try:
            if not self.connection or not self.connection.is_connected():
                self.init_connection()
                
            cursor = self.connection.cursor()
            cursor.executemany(query, values)
            self.connection.commit()
            affected_rows = cursor.rowcount
            cursor.close()
            return affected_rows
        except Exception as e:
            self._logger.error(f"批量执行错误: {str(e)}")
            self.connection.rollback()
            QMessageBox.critical(None, "批量操作错误", f"执行批量操作时出错：\n{str(e)}")
            return -1
            
    def __del__(self):
        """析构函数"""
        try:
            if hasattr(self, 'connection') and self.connection:
                if not self.connection.is_connected():  # 只有在连接已断开时才关闭
                    self.connection.close()
                    self._logger.info("数据库连接已关闭")
        except:
            pass
    
    def begin_transaction(self):
        """开始事务"""
        try:
            if not self.connection or not self.connection.is_connected():
                self.init_connection()
            cursor = self.connection.cursor()
            cursor.execute("START TRANSACTION")
            return cursor
        except Exception as e:
            self._logger.error(f"开始事务失败: {str(e)}")
            raise e
    
    def commit_transaction(self):
        """提交事务"""
        if self.connection:
            self.connection.commit()
    
    def rollback_transaction(self):
        """回滚事务"""
        if self.connection:
            self.connection.rollback()
    
    def get_cursor(self):
        """获取数据库游标"""
        if not self.connection or not self.connection.is_connected():
            self.init_connection()
        return self.connection.cursor(dictionary=True)
    
    def commit(self):
        """提交事务"""
        if self.connection:
            self.connection.commit()
    
    def rollback(self):
        """回滚事务"""
        if self.connection:
            self.connection.rollback()
    
    def execute_insert(self, query, params=None):
        """执行插入操作"""
        try:
            if not self.connection or not self.connection.is_connected():
                self.init_connection()
                
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            self.connection.commit()
            affected_rows = cursor.rowcount
            cursor.close()
            return affected_rows
        except Exception as e:
            self._logger.error(f"插入执行错误: {str(e)}")
            self.connection.rollback()
            QMessageBox.critical(None, "插入错误", f"执行插入操作时出错：\n{str(e)}")
            return -1
    
    def executemany_insert(self, query, params_list):
        """批量执行插入操作"""
        try:
            if not self.connection or not self.connection.is_connected():
                self.init_connection()
                
            cursor = self.connection.cursor()
            cursor.executemany(query, params_list)
            affected_rows = cursor.rowcount
            self.connection.commit()
            cursor.close()
            return affected_rows
            
        except Exception as e:
            self._logger.error(f"批量插入执行错误: {str(e)}")
            self.connection.rollback()
            raise e

    def execute_batch(self, query, params_list):
        """批量执行SQL语句"""
        try:
            cursor = self.connection.cursor()
            cursor.executemany(query, params_list)
            return cursor.rowcount
        except Exception as e:
            Logger.log_error(f"批量执行SQL失败: {str(e)}")
            raise
