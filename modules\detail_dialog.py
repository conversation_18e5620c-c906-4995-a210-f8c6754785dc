from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QFormLayout, QWidget, QLineEdit, QGridLayout,
                            QFrame, QPushButton, QMessageBox, QComboBox)
from PyQt6.QtCore import Qt

class DeliveryDetailDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("出库单明细")
        self.setMinimumWidth(1200)
        self.setMinimumHeight(800)
        self.order_no = None  # 添加 order_no 属性
        self.return_db = None  # 添加 return_db 属性
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(0)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 出库单信息区域
        header_widget = QWidget()
        header_widget.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
            }
            QLabel {
                padding: 5px;
            }
            QLineEdit {
                background-color: white;
                border: 1px solid #cccccc;
                padding: 3px;
            }
            QComboBox {
                border: 1px solid #cccccc;
                selection-background-color: #1890ff;
                selection-color: #ffffff;
                background: #ffffff;
                padding: 0px;
            }
            QComboBox:hover {
                background: #f5f5f5;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #cccccc;
                selection-background-color: #1890ff;
                selection-color: #ffffff;
                background: #ffffff;
                padding: 0px;
            }
        """)
        
        grid_layout = QGridLayout(header_widget)
        grid_layout.setSpacing(10)
        
        # 第一行
        grid_layout.addWidget(QLabel("单据编号:"), 0, 0)
        self.delivery_id_edit = QLineEdit()
        self.delivery_id_edit.setReadOnly(True)
        grid_layout.addWidget(self.delivery_id_edit, 0, 1)
        
        grid_layout.addWidget(QLabel("原始单号:"), 0, 2)
        self.business_type_edit = QLineEdit()
        self.business_type_edit.setReadOnly(True)
        grid_layout.addWidget(self.business_type_edit, 0, 3)
        
        grid_layout.addWidget(QLabel("收货日期:"), 0, 4)
        self.received_date_edit = QLineEdit()
        self.received_date_edit.setReadOnly(True)
        grid_layout.addWidget(self.received_date_edit, 0, 5)
        
        # 第二行
        grid_layout.addWidget(QLabel("送货客户:"), 1, 0)
        self.customer_id_edit = QLineEdit()
        self.customer_id_edit.setReadOnly(True)
        grid_layout.addWidget(self.customer_id_edit, 1, 1)
        
        grid_layout.addWidget(QLabel("部门:"), 1, 2)
        self.department_id_edit = QLineEdit()
        self.department_id_edit.setReadOnly(True)
        grid_layout.addWidget(self.department_id_edit, 1, 3)
        
        grid_layout.addWidget(QLabel("物流公司:"), 1, 4)
        self.logistics_company_edit = QComboBox()
        self.logistics_company_edit.addItems([
            "上海如风道供应链有限公司",
            "上海得吉供应链管理有限公司",
            "快递承运商",
            "西安京东讯成物流有限公司",
            "客户自提",
            "上海市食品进出口国际货运有限公司",
            "港口直送虚拟承运商"
        ])
        grid_layout.addWidget(self.logistics_company_edit, 1, 5)
        
        # 第三行
        grid_layout.addWidget(QLabel("摘要:"), 2, 0)
        self.notes_edit = QLineEdit()
        self.notes_edit.setReadOnly(False)
        grid_layout.addWidget(self.notes_edit, 2, 1, 1, 5)
        
        layout.addWidget(header_widget)
        
        # 添加分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)
        
        # 明细表格
        self.detail_table = QTableWidget()
        self.detail_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d0d0d0;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 4px;
                border: 1px solid #d0d0d0;
            }
        """)
        
        # 设置明细表格列
        self.detail_table.setColumnCount(11)
        self.detail_table.setHorizontalHeaderLabels([
            "autoid", "物料编码", "物料名称", "单位", "批次", 
            "仓库", "拒收原因", "出库数量", "确认数量", 
            "剩余退货数量", "单据状态"
        ])
        
        # 设置列宽
        header = self.detail_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        # 设置行高
        vertical_header = self.detail_table.verticalHeader()
        vertical_header.setDefaultSectionSize(30)  # 设置默认行高为30像素
        
        # 设置表格样式
        self.detail_table.setShowGrid(True)
        self.detail_table.setGridStyle(Qt.PenStyle.SolidLine)
        self.detail_table.setAlternatingRowColors(True)
        
        # 设置表格部分列为可编辑
        self.detail_table.setEditTriggers(QTableWidget.EditTrigger.DoubleClicked)
        
        layout.addWidget(self.detail_table)
        
        # 修改按钮区域
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        
        # 添加生成交接单按钮
        self.create_handover_btn = QPushButton("生成交接单")
        self.create_handover_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;  /* 绿色按 */
                color: white;
                border: none;
                padding: 5px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
            QPushButton:pressed {
                background-color: #389e0d;
            }
        """)
        
        self.create_handover_btn.clicked.connect(lambda: self.create_handover_order())
        
        # 添加删除按钮
        self.delete_btn = QPushButton("删除")
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4d4f;
                color: white;
                border: none;
                padding: 5px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ff7875;
            }
            QPushButton:pressed {
                background-color: #d9363e;
            }
        """)
        self.delete_btn.clicked.connect(self.delete_delivery_order)
        
        # 添加提交按钮
        self.submit_btn = QPushButton("提交更新")
        self.submit_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 5px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
            QPushButton:pressed {
                background-color: #096dd9;
            }
        """)
        self.submit_btn.clicked.connect(self.submit_changes)
        
        button_layout.addStretch()
        button_layout.addWidget(self.create_handover_btn)  # 添加生成交接单按钮
        button_layout.addSpacing(10)
        button_layout.addWidget(self.delete_btn)  # 添加删除按钮
        button_layout.addSpacing(10)
        button_layout.addWidget(self.submit_btn)  # 添加提交更新按钮
        
        layout.addLayout(button_layout)
        
    def set_delivery_data(self,delivery_data,detail_data):
        """设置出库单数据和明细数据"""
        if not detail_data:
            return
        
        header_data = detail_data['header']
        details = detail_data['details']
        
        # 保存订单号
        self.order_no = header_data['order_no']
        
        # 计算原始单号
        from .return_module import DeliveryOrderTab
        original_order_no = DeliveryOrderTab.get_original_order_no(
            header_data.get('summary', ''),
            header_data.get('source_order_no', '')
        )
        
        # 设置表头信息
        self.delivery_id_edit.setText(str(header_data['order_no']))
        self.business_type_edit.setText(str(original_order_no))  # 设置计算得到的原始单号
        self.customer_id_edit.setText(str(header_data['customer']))
        self.department_id_edit.setText(str(header_data.get('department', '')))
        self.logistics_company_edit.setCurrentText(str(header_data['logistics_company']))
        self.notes_edit.setText(str(header_data.get('summary', '')))
        self.received_date_edit.setText(str(header_data.get('confirm_receipt_date', '')))
        
        # 设置明细表格
        self.detail_table.setRowCount(len(details))
        for row, detail in enumerate(details):
            items = [
                (0, detail['autoid'], True),  # 显示autoid
                (1, detail['material_code'], True),
                (2, detail['material_name'], True),
                (3, detail['unit'], True),
                (4, detail['batch_no'], True),
                (5, detail['warehouse'], True),
                (6, detail.get('reject_reason', ''), True),  # 拒收原因从数据中获取，设置为只读
                (7, detail['delivery_quantity'], True),
                (8, detail['confirmed_quantity'], True),
                (9, detail['remaining_return_quantity'], True),
                (10, '开启' if detail['order_status'] == 0 else '关闭', False)
            ]
            
            for col, value, readonly in items:
                if col == 10:  # 状态列使用下拉框
                    combo = QComboBox()
                    combo.addItems(['开启', '关闭'])
                    combo.setCurrentText('开启' if detail['order_status'] == 0 else '关闭')
                    self.detail_table.setCellWidget(row, col, combo)
                    continue
                    
                item = QTableWidgetItem(str(value) if value is not None else '')
                if readonly:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                if isinstance(value, (int, float)) and col in [7, 8, 9]:  # 数量列右对齐
                    item.setData(Qt.ItemDataRole.DisplayRole, value)
                    item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                else:
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.detail_table.setItem(row, col, item)
            
            # 保存autoid用于后续更新
            self.detail_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, detail['autoid'])
        
    def submit_changes(self):
        """提交更新到数据库"""
        try:
            # 添加确认对话框
            reply = QMessageBox.question(
                self,
                '确认更新',
                '是否确认更新当前出库单？',
                buttons=QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                defaultButton=QMessageBox.StandardButton.No  # 默认选中 No 按钮
            )
            
            # 如果用户点击了 No，直接返回
            if reply != QMessageBox.StandardButton.Yes:
                return
            
            updates = {
                'summary': self.notes_edit.text(),
                'logistics_company': self.logistics_company_edit.currentText(),
                'details': []
            }
            
            # 收集明细数据更新，以autoid为依据
            for row in range(self.detail_table.rowCount()):
                autoid = self.detail_table.item(row, 0).text()  # 获取autoid
                delivery_quantity = float(self.detail_table.item(row, 7).text())
                confirmed_quantity = float(self.detail_table.item(row, 8).text())
                status_combo = self.detail_table.cellWidget(row, 10)
                order_status = 0 if status_combo.currentText() == '开启' else 1
                
                # 添加数据验证
                if delivery_quantity < 0 or confirmed_quantity < 0:
                    raise ValueError(f"明细ID {autoid} 的数量不能为负数")
                    
                if confirmed_quantity > delivery_quantity:
                    raise ValueError(f"明细ID {autoid} 的确认数量不能大于出库数量")
                
                updates['details'].append({
                    'autoid': autoid,
                    'delivery_quantity': delivery_quantity,
                    'confirmed_quantity': confirmed_quantity,
                    'order_status': order_status
                })
            
            # 调用数据库更新方法
            from modules.return_db import ReturnDB
            db = ReturnDB()
            result = db.update_delivery_order(self.order_no, updates)
            
            if result:
                QMessageBox.information(self, "成功", "数据更新成功！")
                self.accept()
            else:
                QMessageBox.warning(self, "警告", "数据更新失败！")
                
        except ValueError as ve:
            QMessageBox.critical(self, "错误", str(ve))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"更新失败：{str(e)}") 

    def create_handover_order(self, show_dialog=True):
        """生成交接单"""
        
        try:
            # 检查是否有选中的行
            selected_details = []
            for row in range(self.detail_table.rowCount()):
                # 检查状态是否为"开启"
                status_combo = self.detail_table.cellWidget(row, 10)
                if status_combo.currentText() == '开启':
                    # 检查剩余退货数量是否大于0
                    remaining_qty = float(self.detail_table.item(row, 9).text())
                    if remaining_qty > 0:
                        detail = {
                            'autoid': self.detail_table.item(row, 0).text(),
                            'material_code': self.detail_table.item(row, 1).text(),
                            'material_name': self.detail_table.item(row, 2).text(),
                            'unit': self.detail_table.item(row, 3).text(),
                            'batch_no': self.detail_table.item(row, 4).text(),
                            'warehouse': self.detail_table.item(row, 5).text(),
                            'reject_reason': self.detail_table.item(row, 6).text(),
                            'remaining_return_quantity': remaining_qty
                        }
                        selected_details.append(detail)
        
            
        
            if not selected_details:
               
                if show_dialog:
                    QMessageBox.warning(
                        self, 
                        "警告", 
                        '没有可生成交接单的明细行！\n请确保有状态为"开启"且剩余退货数量大于0的明细。'
                    )
                return False
                
            # 确认是否生成交接单
            should_proceed = True
            if show_dialog:
                
                reply = QMessageBox.question(
                    self, 
                    '确认', 
                    '是否确认生成交接单？\n将为所有符合条件的明细生成交接单。',
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )
                should_proceed = (reply == QMessageBox.StandardButton.Yes)
                
                if not should_proceed:
                    return False
            
            # 准备出库单数据
            delivery_data = {
                'order_no': self.delivery_id_edit.text(),
                'customer': self.customer_id_edit.text(),
                'logistics_company': self.logistics_company_edit.currentText(),
                'department': self.department_id_edit.text(),
                'summary': self.notes_edit.text()
            }
            
            
            try:
                if not hasattr(self, 'return_db') or self.return_db is None:
                    
                    from modules.return_db import ReturnDB
                    self.return_db = ReturnDB()
                
                # 调用生成交接单方法
                result = self.return_db.generate_handover_order(delivery_data, selected_details)
                
                
                if result:
                    if show_dialog:
                        QMessageBox.information(self, "成功", "交接单生成成功！")
                    return True
                else:
                    if show_dialog:
                        QMessageBox.warning(self, "警告", "交接单生成失败！")
                    return False
                    
            except Exception as e:
                
                if "已存在未完成的交接单" in str(e):
                    if show_dialog:
                        QMessageBox.warning(self, "警告", str(e))
                else:
                    if show_dialog:
                        QMessageBox.critical(self, "错误", f"生成交接单失败：{str(e)}")
                return False
                
        except Exception as e:
            
            if show_dialog:
                QMessageBox.critical(self, "错误", f"生成交接单失败：{str(e)}")
            return False
        
    def delete_delivery_order(self):
        """删除出库单"""
        try:
            # 添加确认对话框
            reply = QMessageBox.question(
                self,
                '确认删除',
                '是否确认删除当前出库单？此操作不可恢复！',
                buttons=QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                defaultButton=QMessageBox.StandardButton.No
            )
            
            # 如果用户点击了 No，直接返回
            if reply != QMessageBox.StandardButton.Yes:
                return
            
            # 调用数据库删除方法
            from modules.return_db import ReturnDB
            db = ReturnDB()
            result = db.delete_delivery_order(self.order_no)
            
            if result:
                QMessageBox.information(self, "成功", "出库单删除成功！")
                self.accept()
            else:
                QMessageBox.warning(self, "警告", "出库单删除失败！")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除失败：{str(e)}")
        
