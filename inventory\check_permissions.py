"""
检查当前用户的库存管理权限
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.config import get_current_user
from inventory.inventory_permission_service import InventoryPermissionService


def check_user_permissions():
    """检查用户权限"""
    print("🔍 检查当前用户的库存管理权限")
    print("=" * 50)
    
    # 获取当前用户
    current_user = 'admin'
    print(f"当前用户: {current_user}")
    print()
    
    if not current_user:
        print("❌ 没有找到当前用户信息")
        print("💡 建议：请确保已正确登录系统")
        return
    
    # 初始化权限服务
    permission_service = InventoryPermissionService()
    
    # 检查库存管理相关权限
    permissions_to_check = [
        ('inventory:view', '库存查看'),
        ('inventory:manage', '库存管理'),
        ('inventory:initial', '期初库存'),
        ('inventory:transfer', '库存调拨'),
        ('inventory:count', '库存盘点'),
        ('inventory:report', '库存报表'),
        ('inventory:export', '数据导出'),
        ('system:admin', '系统管理员')
    ]
    
    print("📋 权限检查结果:")
    print("-" * 30)
    
    has_any_permission = False
    for perm_code, perm_name in permissions_to_check:
        has_perm = permission_service.has_permission(perm_code)
        status = "✅" if has_perm else "❌"
        print(f"{status} {perm_name} ({perm_code}): {has_perm}")
        if has_perm:
            has_any_permission = True
    
    print()
    
    # 检查部门权限
    print("🏢 部门权限检查:")
    print("-" * 20)
    
    departments_to_check = ['通路一', '通路二', '生产部', '采购部']
    accessible_departments = []
    
    for dept in departments_to_check:
        has_dept_perm = permission_service.check_department_permission(dept)
        status = "✅" if has_dept_perm else "❌"
        print(f"{status} {dept}: {has_dept_perm}")
        if has_dept_perm:
            accessible_departments.append(dept)
    
    print()
    
    # 总结和建议
    print("📊 权限总结:")
    print("-" * 15)
    
    if not has_any_permission:
        print("❌ 您没有任何库存管理权限")
        print("💡 建议：")
        print("   1. 联系系统管理员分配库存管理权限")
        print("   2. 确认您的角色是否包含库存管理权限")
        print("   3. 检查用户角色配置是否正确")
    else:
        print("✅ 您拥有部分库存管理权限")
        
        if not permission_service.has_permission('inventory:initial'):
            print("⚠️  缺少期初库存权限 (inventory:initial)")
        
        if not permission_service.has_permission('inventory:transfer'):
            print("⚠️  缺少库存调拨权限 (inventory:transfer)")
        
        if not permission_service.has_permission('inventory:count'):
            print("⚠️  缺少库存盘点权限 (inventory:count)")
    
    if not accessible_departments:
        print("❌ 您没有任何部门的访问权限")
        print("💡 建议：联系管理员分配部门访问权限")
    else:
        print(f"✅ 您可以访问的部门: {', '.join(accessible_departments)}")
    
    print()
    print("🔧 解决方案:")
    print("-" * 12)
    print("如果按钮无法点击，可能的原因和解决方案：")
    print("1. 权限不足 - 联系管理员分配相应权限")
    print("2. 按钮被禁用 - 检查权限配置")
    print("3. 对话框初始化失败 - 查看错误日志")
    print("4. 数据库连接问题 - 检查数据库连接")


if __name__ == "__main__":
    check_user_permissions()
