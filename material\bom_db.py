"""
BOM数据库操作类
处理BOM相关的数据库操作
"""
from modules.db_manager import DatabaseManager
from core.logger import Logger
from core.config import get_current_user
from datetime import datetime, date
import traceback

class BOMDB:
    """BOM数据库操作类"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.current_user = get_current_user()
        # 确保自动编号规则存在
        try:
            self.ensure_auto_number_rule()
        except Exception as e:
            Logger.log_error(f"初始化BOM自动编号规则失败: {str(e)}")
    
    def ensure_auto_number_rule(self):
        """确保BOM自动编号规则存在"""
        try:
            # 检查BOM规则是否存在
            check_query = "SELECT COUNT(*) as count FROM auto_number_rule WHERE rule_code = 'BOM'"
            result = self.db_manager.execute_query(check_query)
            
            if result and result[0].get('count', 0) == 0:
                # 创建BOM规则
                insert_query = """
                INSERT INTO auto_number_rule 
                (rule_code, rule_name, prefix, date_format, sequence_length, reset_type, status, description)
                VALUES ('BOM', 'BOM编码', 'BOM-', 'YYYYMMDD', 3, 1, 1, 'BOM物料清单编码规则')
                """
                self.db_manager.execute_update(insert_query)
                Logger.log_info("已创建BOM自动编号规则")
                
        except Exception as e:
            Logger.log_error(f"检查BOM自动编号规则失败: {str(e)}")
    
    def generate_auto_number(self):
        """使用自动编号服务生成BOM编号"""
        try:
            from system.auto_number_module import AutoNumberService
            
            auto_number_service = AutoNumberService()
            
            # 使用BOM规则生成编号
            bom_number = auto_number_service.generate_number(
                rule_code='BOM',
                business_date=date.today().strftime('%Y-%m-%d'),
                business_type='bom_header',
                business_id=None,
                user_id=self.current_user.get('user_id') if self.current_user else None
            )
            
            return bom_number
            
        except Exception as e:
            Logger.log_error(f"生成BOM自动编号失败: {str(e)}")
            # 如果自动编号失败，回退到手动编号
            
    
    def save_bom_header(self, bom_data):
        """保存BOM主表数据"""
        try:
            self.db_manager.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 保存主表
            insert_query = """
            INSERT INTO bom_header 
            (bom_code, parent_material_id, bom_name, bom_version, production_unit, 
             standard_output_qty, package_qty_per_box, effective_date, expiry_date, 
             status, remark, create_time, create_user, update_time, update_user)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            params = (
                bom_data['bom_code'],
                bom_data['parent_material_id'],
                bom_data.get('bom_name', ''),
                bom_data.get('bom_version', '1.0'),
                bom_data['production_unit'],
                bom_data['standard_output_qty'],
                bom_data.get('package_qty_per_box'),
                bom_data['effective_date'],
                bom_data.get('expiry_date'),
                bom_data.get('status', 1),
                bom_data.get('remark', ''),
                current_time,
                current_user,
                current_time,
                current_user
            )
            
            self.db_manager.execute_update(insert_query, params)
            
            # 获取插入的BOM ID
            bom_id_result = self.db_manager.execute_query("SELECT LAST_INSERT_ID() as id")
            if bom_id_result and len(bom_id_result) > 0:
                if isinstance(bom_id_result[0], dict):
                    bom_id = bom_id_result[0]['id']
                else:
                    bom_id = bom_id_result[0][0]
            else:
                raise Exception("无法获取插入的BOM记录ID")
            
            self.db_manager.commit_transaction()
            return bom_id
            
        except Exception as e:
            self.db_manager.rollback_transaction()
            Logger.log_error(f"保存BOM主表失败: {str(e)}")
            raise Exception(f"保存BOM主表失败: {str(e)}")
    
    def update_bom_header(self, bom_id, bom_data):
        """更新BOM主表数据"""
        try:
            self.db_manager.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 更新主表
            update_query = """
            UPDATE bom_header 
            SET parent_material_id=%s, bom_name=%s, bom_version=%s, production_unit=%s,
                standard_output_qty=%s, package_qty_per_box=%s, effective_date=%s, 
                expiry_date=%s, status=%s, remark=%s, update_time=%s, update_user=%s
            WHERE bom_id=%s
            """
            
            params = (
                bom_data['parent_material_id'],
                bom_data.get('bom_name', ''),
                bom_data.get('bom_version', '1.0'),
                bom_data['production_unit'],
                bom_data['standard_output_qty'],
                bom_data.get('package_qty_per_box'),
                bom_data['effective_date'],
                bom_data.get('expiry_date'),
                bom_data.get('status', 1),
                bom_data.get('remark', ''),
                current_time,
                current_user,
                bom_id
            )
            
            affected_rows = self.db_manager.execute_update(update_query, params)
            
            if affected_rows == 0:
                raise Exception("BOM记录不存在或未发生变更")
            
            self.db_manager.commit_transaction()
            
        except Exception as e:
            self.db_manager.rollback_transaction()
            Logger.log_error(f"更新BOM主表失败: {str(e)}")
            raise Exception(f"更新BOM主表失败: {str(e)}")
    
    def save_bom_package_details(self, bom_id, package_details):
        """保存BOM包材明细数据"""
        try:
            self.db_manager.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 先删除原有包材明细
            delete_query = "DELETE FROM bom_package_detail WHERE bom_id = %s"
            self.db_manager.execute_update(delete_query, (bom_id,))
            
            # 插入新包材明细
            if package_details:
                insert_query = """
                INSERT INTO bom_package_detail 
                (bom_id, sequence_no, package_material_id, material_category_id, 
                 required_qty, required_unit, original_box_qty, original_box_unit, 
                 scrap_rate, unit_cost, total_cost, supplier_id, remark, 
                 create_time, create_user, update_time, update_user)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                for detail in package_details:
                    # 计算总成本
                    required_qty = detail['required_qty']
                    unit_cost = detail.get('unit_cost', 0)
                    total_cost = round(required_qty * unit_cost if unit_cost else 0, 4)
                     
                    params = (
                        bom_id,
                        detail['sequence_no'],
                        detail['package_material_id'],
                        detail.get('material_category_id'),
                        detail['required_qty'],
                        detail['required_unit'],
                        detail.get('original_box_qty'),
                        detail.get('original_box_unit'),
                        detail.get('scrap_rate', 0),
                        detail.get('unit_cost',0),
                        total_cost,
                        detail.get('supplier_id'),
                        detail.get('remark', ''),
                        current_time,
                        current_user,
                        current_time,
                        current_user
                    )
                    self.db_manager.execute_update(insert_query, params)
            
            self.db_manager.commit_transaction()
            
        except Exception as e:
            self.db_manager.rollback_transaction()
            Logger.log_error(f"保存BOM包材明细失败: {str(e)}")
            raise Exception(f"保存BOM包材明细失败: {str(e)}")

    def save_bom_material_details(self, bom_id, material_details):
        """保存BOM原料明细数据"""
        try:
            self.db_manager.begin_transaction()
            
            current_user = self.current_user['real_name'] if self.current_user else '系统'
            current_time = datetime.now()
            
            # 先删除原有原料明细
            delete_query = "DELETE FROM bom_material_detail WHERE bom_id = %s"
            self.db_manager.execute_update(delete_query, (bom_id,))
            
            # 插入新原料明细
            if material_details:
                insert_query = """
                INSERT INTO bom_material_detail 
                (bom_id, sequence_no, raw_material_id, material_category_id, 
                 required_qty, required_unit, original_box_qty, original_box_unit, 
                 scrap_rate, unit_cost, total_cost, supplier_id, batch_required,
                 quality_standard, storage_condition, shelf_life_days, remark,
                 create_time, create_user, update_time, update_user)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                for detail in material_details:
                    # 计算总成本
                    required_qty = detail['required_qty']
                    #unit_cost 没有就返回0
                    unit_cost = detail.get('unit_cost', 0)
                    total_cost = round(required_qty * unit_cost if unit_cost else 0,4)
                   
                    params = (
                        bom_id,
                        detail['sequence_no'],
                        detail['raw_material_id'],
                        detail.get('material_category_id'),
                        detail['required_qty'],
                        detail['required_unit'],
                        detail.get('original_box_qty'),
                        detail.get('original_box_unit'),
                        detail.get('scrap_rate', 0),
                        detail.get('unit_cost',0),
                        total_cost,
                        detail.get('supplier_id'),
                        detail.get('batch_required', 0),
                        detail.get('quality_standard'),
                        detail.get('storage_condition'),
                        detail.get('shelf_life_days'),
                        detail.get('remark', ''),
                        current_time,
                        current_user,
                        current_time,
                        current_user
                    )
                    self.db_manager.execute_update(insert_query, params)
            
            self.db_manager.commit_transaction()
            
        except Exception as e:
            self.db_manager.rollback_transaction()
            Logger.log_error(f"保存BOM原料明细失败: {str(e)}")
            raise Exception(f"保存BOM原料明细失败: {str(e)}")

    def get_bom_package_details(self, bom_id):
        """获取BOM包材明细列表"""
        try:
            query = """
            SELECT 
                bpd.*,
                m.name as material_name,
                m.spec_model as material_spec,
                mc.category_name
            FROM bom_package_detail bpd
            LEFT JOIN materials m ON bpd.package_material_id = m.material_id
            LEFT JOIN material_categories mc ON bpd.material_category_id = mc.category_id
            WHERE bpd.bom_id = %s
            ORDER BY bpd.sequence_no
            """
            
            results = self.db_manager.execute_query(query, (bom_id,))
            return results if results else []
            
        except Exception as e:
            Logger.log_error(f"获取BOM包材明细失败: {str(e)}")
            return []

    def get_bom_material_details(self, bom_id):
        """获取BOM原料明细列表"""
        try:
            query = """
            SELECT 
                bmd.*,
                m.name as material_name,
                m.spec_model as material_spec,
                mc.category_name
            FROM bom_material_detail bmd
            LEFT JOIN materials m ON bmd.raw_material_id = m.material_id
            LEFT JOIN material_categories mc ON bmd.material_category_id = mc.category_id
            WHERE bmd.bom_id = %s
            ORDER BY bmd.sequence_no
            """
            
            results = self.db_manager.execute_query(query, (bom_id,))
            return results if results else []
            
        except Exception as e:
            Logger.log_error(f"获取BOM原料明细失败: {str(e)}")
            return []



    def delete_bom(self, bom_id):
        """删除BOM（包括明细）"""
        try:
            self.db_manager.begin_transaction()
            
            # 检查BOM是否存在
            check_query = "SELECT bom_code FROM bom_header WHERE bom_id = %s"
            result = self.db_manager.execute_query(check_query, (bom_id,))
            
            if not result:
                raise Exception("BOM记录不存在")
            
            # 删除明细（由于外键约束设置了CASCADE，主表删除时会自动删除明细）
            delete_header_query = "DELETE FROM bom_header WHERE bom_id = %s"
            affected_rows = self.db_manager.execute_update(delete_header_query, (bom_id,))
            
            if affected_rows == 0:
                raise Exception("删除BOM失败")
            
            self.db_manager.commit_transaction()
            
        except Exception as e:
            self.db_manager.rollback_transaction()
            Logger.log_error(f"删除BOM失败: {str(e)}")
            raise Exception(f"删除BOM失败: {str(e)}")
    
    def get_materials_for_bom(self, search_text=""):
        """获取可用于BOM的物料列表"""
        try:
            query = """
            SELECT material_id, name, spec_model, category_id
            FROM materials 
            WHERE 1=1
            """
            params = []
            
            if search_text:
                query += " AND (material_id LIKE %s OR name LIKE %s)"
                params.extend([f"%{search_text}%", f"%{search_text}%"])
            
            query += " ORDER BY material_id"
            
            return self.db_manager.execute_query(query, params if params else None)
            
        except Exception as e:
            Logger.log_error(f"获取物料列表失败: {str(e)}")
            return []
    
    def get_material_categories(self):
        """获取物料分类列表"""
        try:
            query = """
            SELECT category_id, category_name, parent_id, level
            FROM material_categories 
            ORDER BY sort_order, category_id
            """
            
            return self.db_manager.execute_query(query)
            
        except Exception as e:
            Logger.log_error(f"获取物料分类失败: {str(e)}")
            return []

    def get_bom_list(self, params=None):
        """
        获取BOM列表
        
        Args:
            params: 查询参数字典
                - bom_code: BOM编码
                - bom_name: BOM名称
                - parent_material_id: 成品物料编码
                - status: 状态
                - page: 页码
                - page_size: 每页记录数
        
        Returns:
            tuple: (总记录数, BOM列表)
        """
        try:
            if params is None:
                params = {}
            
            # 构建查询条件
            where_conditions = ["1=1"]
            query_params = []
            
            # BOM编码查询
            if params.get('bom_code'):
                where_conditions.append("bh.bom_code LIKE %s")
                query_params.append(f"%{params['bom_code']}%")
            
            # BOM名称查询
            if params.get('bom_name'):
                where_conditions.append("bh.bom_name LIKE %s")
                query_params.append(f"%{params['bom_name']}%")
            
            # 成品物料查询
            if params.get('parent_material_id'):
                where_conditions.append("(bh.parent_material_id LIKE %s OR m.name LIKE %s)")
                query_params.extend([f"%{params['parent_material_id']}%", f"%{params['parent_material_id']}%"])
            
            # 状态查询
            if params.get('status') is not None:
                where_conditions.append("bh.status = %s")
                query_params.append(params['status'])
            
            where_clause = " AND ".join(where_conditions)
            
            # 查询总记录数
            count_query = f"""
            SELECT COUNT(*) as total
            FROM bom_header bh
            LEFT JOIN materials m ON bh.parent_material_id = m.material_id
            WHERE {where_clause}
            """
            
            count_result = self.db_manager.execute_query(count_query, query_params)
            total = count_result[0]['total'] if count_result else 0
            
            # 分页参数
            page = params.get('page', 1)
            page_size = params.get('page_size', 20)
            offset = (page - 1) * page_size
            
            # 查询BOM列表
            list_query = f"""
            SELECT 
                bh.bom_id,
                bh.bom_code,
                bh.bom_name,
                bh.parent_material_id,
                m.name as parent_material_name,
                bh.bom_version,
                bh.production_unit,
                bh.standard_output_qty,
                bh.package_qty_per_box,
                bh.effective_date,
                bh.expiry_date,
                bh.status,
                bh.create_time,
                bh.create_user,
                bh.update_time,
                bh.update_user,
                -- 统计明细数量（包材明细 + 原料明细）
                COALESCE(package_count.package_num, 0) + COALESCE(material_count.material_num, 0) as detail_count
            FROM bom_header bh
            LEFT JOIN materials m ON bh.parent_material_id = m.material_id
            LEFT JOIN (
                SELECT bom_id, COUNT(*) as package_num
                FROM bom_package_detail
                GROUP BY bom_id
            ) package_count ON bh.bom_id = package_count.bom_id
            LEFT JOIN (
                SELECT bom_id, COUNT(*) as material_num
                FROM bom_material_detail
                GROUP BY bom_id
            ) material_count ON bh.bom_id = material_count.bom_id
            WHERE {where_clause}
            ORDER BY bh.create_time DESC
            LIMIT %s OFFSET %s
            """
            
            query_params.extend([page_size, offset])
            results = self.db_manager.execute_query(list_query, query_params)
            
            return total, results if results else []
            
        except Exception as e:
            Logger.log_error(f"获取BOM列表失败: {str(e)}")
            traceback.print_exc()
            return 0, []
    
    def get_bom_by_id(self, bom_id):
        """
        根据ID获取BOM详情
        
        Args:
            bom_id: BOM主键ID
            
        Returns:
            dict: BOM详情信息
        """
        try:
            query = """
            SELECT 
                bh.*,
                m.name as parent_material_name,
                m.spec_model as parent_material_spec
            FROM bom_header bh
            LEFT JOIN materials m ON bh.parent_material_id = m.material_id
            WHERE bh.bom_id = %s
            """
            
            results = self.db_manager.execute_query(query, (bom_id,))
            return results[0] if results else None
            
        except Exception as e:
            Logger.log_error(f"获取BOM详情失败: {str(e)}")
            traceback.print_exc()
            return None

    def get_bom_details(self, bom_id):
        """
        获取BOM所有明细列表（包材明细 + 原料明细）
        
        Args:
            bom_id: BOM主键ID
            
        Returns:
            dict: 包含包材明细和原料明细的字典
                - package_details: 包材明细列表
                - material_details: 原料明细列表
        """
        try:
            # 获取包材明细
            package_details = self.get_bom_package_details(bom_id)
            
            # 获取原料明细
            material_details = self.get_bom_material_details(bom_id)
            
            return {
                'package_details': package_details,
                'material_details': material_details
            }
            
        except Exception as e:
            Logger.log_error(f"获取BOM明细失败: {str(e)}")
            traceback.print_exc()
            return {
                'package_details': [],
                'material_details': []
            }

