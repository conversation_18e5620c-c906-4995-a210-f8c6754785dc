"""
库存数据访问类
提供库存管理相关的所有数据库操作
"""
from datetime import datetime, date
from typing import List, Dict, Optional, Any, Tuple
from core.logger import Logger
from modules.db_manager import DatabaseManager
from core.config import get_current_user


class InventoryDB:
    """库存数据访问类"""
    
    def __init__(self, current_user=None):
        """
        初始化库存数据访问类
        
        Args:
            current_user: 当前用户信息
        """
        self.db_manager = DatabaseManager()
        self.current_user = current_user or get_current_user() or {}
    
    # ==================== 部门库存查询 ====================
    
    def get_department_inventory_list(self, filters: Dict = None, page: int = 1, page_size: int = 50) -> Tuple[int, List[Dict]]:
        """
        获取部门库存列表（分页）
        
        Args:
            filters: 查询条件字典
            page: 页码
            page_size: 每页大小
            
        Returns:
            Tuple[int, List[Dict]]: (总记录数, 库存列表)
        """
        try:
            filters = filters or {}
            
            # 构建查询条件
            where_conditions = ["1=1"]
            params = []
            
            # 物料编码
            if filters.get('material_id'):
                where_conditions.append("di.material_id LIKE %s")
                params.append(f"%{filters['material_id']}%")
            
            # 物料名称
            if filters.get('material_name'):
                where_conditions.append("di.material_name LIKE %s")
                params.append(f"%{filters['material_name']}%")
            
            # 部门
            if filters.get('department'):
                where_conditions.append("di.department = %s")
                params.append(filters['department'])
            
            # 仓库
            if filters.get('warehouse_code'):
                where_conditions.append("di.warehouse_code = %s")
                params.append(filters['warehouse_code'])
            
            # 物料分类
            if filters.get('category_id'):
                where_conditions.append("di.material_category_id = %s")
                params.append(filters['category_id'])
            
            # 库存状态筛选
            if filters.get('stock_status'):
                status = filters['stock_status']
                if status == '缺货':
                    where_conditions.append("di.available_qty <= 0")
                elif status == '库存不足':
                    where_conditions.append("di.available_qty > 0 AND di.available_qty <= di.min_stock_qty")
                elif status == '库存过量':
                    where_conditions.append("di.available_qty >= di.max_stock_qty")
                elif status == '正常':
                    where_conditions.append("di.available_qty > di.min_stock_qty AND di.available_qty < di.max_stock_qty")
            
            # 应用部门数据权限过滤
            department_filter = self.apply_department_filter("di.department")
            if department_filter:
                where_conditions.append(department_filter)
            
            where_clause = " AND ".join(where_conditions)
            
            # 查询总记录数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM department_inventory di
                LEFT JOIN material_categories mc ON di.material_category_id = mc.category_id
                WHERE {where_clause}
            """
            
            count_result = self.db_manager.execute_query(count_sql, params)
            total = count_result[0]['total'] if count_result else 0
            
            # 查询分页数据
            if total > 0:
                offset = (page - 1) * page_size
                query_sql = f"""
                    SELECT 
                        di.dept_inventory_id,
                        di.material_id,
                        di.material_name,
                        di.material_category_id,
                        mc.category_name,
                        di.department,
                        di.warehouse_code,
                        di.warehouse_name,
                        di.total_qty,
                        di.available_qty,
                        di.reserved_qty,
                        di.frozen_qty,
                        di.unit,
                        di.avg_unit_cost,
                        di.total_cost,
                        di.min_stock_qty,
                        di.max_stock_qty,
                        di.last_in_date,
                        di.last_out_date,
                        di.update_time,
                        di.update_user,
                        -- 库存状态判断
                        CASE 
                            WHEN di.available_qty <= 0 THEN '缺货'
                            WHEN di.available_qty <= di.min_stock_qty THEN '库存不足'
                            WHEN di.available_qty >= di.max_stock_qty THEN '库存过量'
                            ELSE '正常'
                        END as stock_status
                    FROM department_inventory di
                    LEFT JOIN material_categories mc ON di.material_category_id = mc.category_id
                    WHERE {where_clause}
                    ORDER BY di.material_id, di.department
                    LIMIT %s OFFSET %s
                """
                
                query_params = params + [page_size, offset]
                inventory_list = self.db_manager.execute_query(query_sql, query_params)
            else:
                inventory_list = []
            
            return total, inventory_list
            
        except Exception as e:
            Logger.log_error(f"获取部门库存列表失败: {str(e)}")
            raise
    
    def get_inventory_by_id(self, dept_inventory_id: int) -> Optional[Dict]:
        """
        根据ID获取库存详情
        
        Args:
            dept_inventory_id: 部门库存ID
            
        Returns:
            Optional[Dict]: 库存详情
        """
        try:
            query = """
                SELECT 
                    di.*,
                    mc.category_name
                FROM department_inventory di
                LEFT JOIN material_categories mc ON di.material_category_id = mc.category_id
                WHERE di.dept_inventory_id = %s
            """
            
            result = self.db_manager.execute_query(query, (dept_inventory_id,))
            
            if result:
                inventory = result[0]
                # 检查部门权限
                if not self.check_department_permission(inventory['department']):
                    raise Exception("无权限访问该部门的库存数据")
                return inventory
            
            return None
            
        except Exception as e:
            Logger.log_error(f"获取库存详情失败: {str(e)}")
            raise
    
    # ==================== 库存流水查询 ====================
    
    def get_inventory_transactions(self, filters: Dict = None, page: int = 1, page_size: int = 50) -> Tuple[int, List[Dict]]:
        """
        获取库存流水列表（分页）
        
        Args:
            filters: 查询条件字典
            page: 页码
            page_size: 每页大小
            
        Returns:
            Tuple[int, List[Dict]]: (总记录数, 流水列表)
        """
        try:
            filters = filters or {}
            
            # 构建查询条件
            where_conditions = ["1=1"]
            params = []
            
            # 流水单号
            if filters.get('transaction_no'):
                where_conditions.append("it.transaction_no LIKE %s")
                params.append(f"%{filters['transaction_no']}%")
            
            # 物料编码
            if filters.get('material_id'):
                where_conditions.append("it.material_id LIKE %s")
                params.append(f"%{filters['material_id']}%")
            
            # 部门
            if filters.get('department'):
                where_conditions.append("it.department = %s")
                params.append(filters['department'])
            
            # 业务类型
            if filters.get('transaction_type'):
                where_conditions.append("it.transaction_type = %s")
                params.append(filters['transaction_type'])
            
            # 日期范围
            if filters.get('start_date'):
                where_conditions.append("it.transaction_date >= %s")
                params.append(filters['start_date'])
            
            if filters.get('end_date'):
                where_conditions.append("it.transaction_date <= %s")
                params.append(filters['end_date'])
            
            # 应用部门数据权限过滤
            department_filter = self.apply_department_filter("it.department")
            if department_filter:
                where_conditions.append(department_filter)
            
            where_clause = " AND ".join(where_conditions)
            
            # 查询总记录数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM inventory_transactions it
                WHERE {where_clause}
            """
            
            count_result = self.db_manager.execute_query(count_sql, params)
            total = count_result[0]['total'] if count_result else 0
            
            # 查询分页数据
            if total > 0:
                offset = (page - 1) * page_size
                query_sql = f"""
                    SELECT 
                        it.*,
                        CASE it.transaction_type
                            WHEN 1 THEN '期初'
                            WHEN 2 THEN '采购入库'
                            WHEN 3 THEN '生产领料'
                            WHEN 4 THEN '生产入库'
                            WHEN 5 THEN '销售出库'
                            WHEN 6 THEN '调拨'
                            WHEN 7 THEN '盘点'
                            WHEN 8 THEN '报废'
                            WHEN 9 THEN '其他'
                            ELSE '未知'
                        END as transaction_type_name
                    FROM inventory_transactions it
                    WHERE {where_clause}
                    ORDER BY it.transaction_date DESC, it.create_time DESC
                    LIMIT %s OFFSET %s
                """
                
                query_params = params + [page_size, offset]
                transactions = self.db_manager.execute_query(query_sql, query_params)
            else:
                transactions = []
            
            return total, transactions
            
        except Exception as e:
            Logger.log_error(f"获取库存流水列表失败: {str(e)}")
            raise
    
    # ==================== 库存预留查询 ====================
    
    def get_inventory_reservations(self, filters: Dict = None, page: int = 1, page_size: int = 50) -> Tuple[int, List[Dict]]:
        """
        获取库存预留记录列表（分页）
        
        Args:
            filters: 查询条件字典
            page: 页码
            page_size: 每页大小
            
        Returns:
            Tuple[int, List[Dict]]: (总记录数, 预留记录列表)
        """
        try:
            filters = filters or {}
            
            # 构建查询条件
            where_conditions = ["1=1"]
            params = []
            
            # 预留单号
            if filters.get('reservation_no'):
                where_conditions.append("ir.reservation_no LIKE %s")
                params.append(f"%{filters['reservation_no']}%")
            
            # 物料编码
            if filters.get('material_id'):
                where_conditions.append("ir.material_id LIKE %s")
                params.append(f"%{filters['material_id']}%")
            
            # 部门
            if filters.get('department'):
                where_conditions.append("ir.department = %s")
                params.append(filters['department'])
            
            # 预留状态
            if filters.get('status'):
                where_conditions.append("ir.status = %s")
                params.append(filters['status'])
            
            # 来源类型
            if filters.get('source_type'):
                where_conditions.append("ir.source_type = %s")
                params.append(filters['source_type'])
            
            # 应用部门数据权限过滤
            department_filter = self.apply_department_filter("ir.department")
            if department_filter:
                where_conditions.append(department_filter)
            
            where_clause = " AND ".join(where_conditions)
            
            # 查询总记录数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM inventory_reservations ir
                WHERE {where_clause}
            """
            
            count_result = self.db_manager.execute_query(count_sql, params)
            total = count_result[0]['total'] if count_result else 0
            
            # 查询分页数据
            if total > 0:
                offset = (page - 1) * page_size
                query_sql = f"""
                    SELECT 
                        ir.*,
                        m.name as material_name,
                        CASE ir.status
                            WHEN 1 THEN '已预留'
                            WHEN 2 THEN '部分消耗'
                            WHEN 3 THEN '已消耗'
                            WHEN 4 THEN '已取消'
                            ELSE '未知'
                        END as status_name
                    FROM inventory_reservations ir
                    LEFT JOIN materials m ON ir.material_id = m.material_id
                    WHERE {where_clause}
                    ORDER BY ir.reservation_date DESC, ir.create_time DESC
                    LIMIT %s OFFSET %s
                """
                
                query_params = params + [page_size, offset]
                reservations = self.db_manager.execute_query(query_sql, query_params)
            else:
                reservations = []
            
            return total, reservations
            
        except Exception as e:
            Logger.log_error(f"获取库存预留记录列表失败: {str(e)}")
            raise
    
    # ==================== 期初库存操作 ====================
    
    def create_initial_inventory(self, inventory_data: Dict) -> int:
        """
        创建期初库存记录
        
        Args:
            inventory_data: 期初库存数据
            
        Returns:
            int: 新创建的期初库存ID
        """
        try:
            self.db_manager.begin_transaction()
            
            # 检查部门权限
            if not self.check_department_permission(inventory_data['department']):
                raise Exception("无权限操作该部门的库存数据")
            
            # 插入期初库存记录
            insert_sql = """
                INSERT INTO inventory_initial (
                    material_id, material_name, material_category_id, department,
                    warehouse_code, warehouse_name, location_code, initial_qty,
                    unit, unit_cost, total_cost, batch_no, production_date,
                    expiry_date, status, remark, create_user
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            current_user = self.current_user.get('username', 'system')
            
            params = (
                inventory_data['material_id'],
                inventory_data.get('material_name'),
                inventory_data.get('material_category_id'),
                inventory_data['department'],
                inventory_data.get('warehouse_code'),
                inventory_data.get('warehouse_name'),
                inventory_data.get('location_code'),
                inventory_data['initial_qty'],
                inventory_data['unit'],
                inventory_data.get('unit_cost', 0),
                inventory_data.get('total_cost', 0),
                inventory_data.get('batch_no'),
                inventory_data.get('production_date'),
                inventory_data.get('expiry_date'),
                inventory_data.get('status', 1),
                inventory_data.get('remark'),
                current_user
            )
            
            initial_id = self.db_manager.execute_insert(insert_sql, params)
            
            # 更新或创建部门库存记录
            self._update_department_inventory_from_initial(inventory_data, initial_id)
            
            # 创建库存流水记录
            self._create_inventory_transaction(
                transaction_type=1,  # 期初
                material_id=inventory_data['material_id'],
                department=inventory_data['department'],
                in_qty=inventory_data['initial_qty'],
                balance_qty=inventory_data['initial_qty'],
                unit_cost=inventory_data.get('unit_cost', 0),
                source_type='initial_inventory',
                source_id=initial_id
            )
            
            self.db_manager.commit_transaction()
            return initial_id
            
        except Exception as e:
            self.db_manager.rollback_transaction()
            Logger.log_error(f"创建期初库存失败: {str(e)}")
            raise
    
    # ==================== 库存调拨操作 ====================
    
    def transfer_inventory(self, transfer_data: Dict) -> Dict:
        """
        库存调拨操作
        
        Args:
            transfer_data: 调拨数据
            
        Returns:
            Dict: 调拨结果
        """
        try:
            self.db_manager.begin_transaction()
            
            from_dept = transfer_data['from_department']
            to_dept = transfer_data['to_department']
            material_id = transfer_data['material_id']
            transfer_qty = transfer_data['transfer_qty']
            
            # 检查部门权限
            if not self.check_department_permission(from_dept):
                raise Exception(f"无权限操作源部门 {from_dept} 的库存")
            if not self.check_department_permission(to_dept):
                raise Exception(f"无权限操作目标部门 {to_dept} 的库存")
            
            # 检查源部门库存是否充足
            from_inventory = self._get_department_inventory(material_id, from_dept)
            if not from_inventory or from_inventory['available_qty'] < transfer_qty:
                raise Exception(f"源部门库存不足，可用数量: {from_inventory['available_qty'] if from_inventory else 0}")
            
            # 生成调拨单号
            from system.auto_number_module import AutoNumberService
            auto_service = AutoNumberService()
            transfer_no = auto_service.generate_number(
                rule_code='KCDB',
                business_date=date.today().strftime('%Y-%m-%d'),
                business_type='inventory_transfer',
                user_id=self.current_user.get('user_id')
            )
            
            # 扣减源部门库存
            self._update_department_inventory_qty(
                material_id, from_dept, 
                total_qty_change=-transfer_qty,
                available_qty_change=-transfer_qty
            )
            
            # 增加目标部门库存
            self._update_department_inventory_qty(
                material_id, to_dept,
                total_qty_change=transfer_qty,
                available_qty_change=transfer_qty
            )
            
            # 创建调拨出库流水
            self._create_inventory_transaction(
                transaction_type=6,  # 调拨
                material_id=material_id,
                department=from_dept,
                out_qty=transfer_qty,
                balance_qty=from_inventory['total_qty'] - transfer_qty,
                unit_cost=from_inventory['avg_unit_cost'],
                source_type='inventory_transfer',
                source_no=transfer_no,
                remark=f"调拨至{to_dept}"
            )
            
            # 创建调拨入库流水
            to_inventory = self._get_department_inventory(material_id, to_dept)
            new_balance = (to_inventory['total_qty'] if to_inventory else 0) + transfer_qty
            
            self._create_inventory_transaction(
                transaction_type=6,  # 调拨
                material_id=material_id,
                department=to_dept,
                in_qty=transfer_qty,
                balance_qty=new_balance,
                unit_cost=from_inventory['avg_unit_cost'],
                source_type='inventory_transfer',
                source_no=transfer_no,
                remark=f"从{from_dept}调入"
            )
            
            self.db_manager.commit_transaction()
            
            return {
                'success': True,
                'transfer_no': transfer_no,
                'message': f'成功调拨 {transfer_qty} {from_inventory["unit"]} 从 {from_dept} 到 {to_dept}'
            }
            
        except Exception as e:
            self.db_manager.rollback_transaction()
            Logger.log_error(f"库存调拨失败: {str(e)}")
            raise
    
    # ==================== 权限控制方法 ====================
    
    def apply_department_filter(self, department_field: str) -> Optional[str]:
        """
        应用部门数据权限过滤
        
        Args:
            department_field: 部门字段名
            
        Returns:
            Optional[str]: 过滤条件SQL
        """
        try:
            # 如果是超级管理员，不限制部门
            user_permissions = self.current_user.get('permissions', [])
            if 'system:admin' in user_permissions:
                return None
            
            # 获取用户可访问的部门列表
            user_departments = self.current_user.get('departments', [])
            if not user_departments:
                # 如果没有配置部门权限，默认只能访问自己创建的数据
                return f"{department_field} = ''"  # 返回空结果
            
            # 构建部门过滤条件
            if len(user_departments) == 1:
                return f"{department_field} = '{user_departments[0]}'"
            else:
                dept_list = "', '".join(user_departments)
                return f"{department_field} IN ('{dept_list}')"
                
        except Exception as e:
            Logger.log_error(f"应用部门权限过滤失败: {str(e)}")
            return f"{department_field} = ''"  # 出错时返回空结果
    
    def check_department_permission(self, department: str) -> bool:
        """
        检查部门权限
        
        Args:
            department: 部门名称
            
        Returns:
            bool: 是否有权限
        """
        try:
            # 超级管理员有所有权限
            user_permissions = self.current_user.get('permissions', [])
            if 'system:admin' in user_permissions:
                return True
            
            # 检查用户部门权限
            user_departments = self.current_user.get('departments', [])
            return department in user_departments
            
        except Exception as e:
            Logger.log_error(f"检查部门权限失败: {str(e)}")
            return False
    
    def check_operation_permission(self, operation: str, department: str = None) -> bool:
        """
        检查操作权限
        
        Args:
            operation: 操作类型
            department: 部门名称（可选）
            
        Returns:
            bool: 是否有权限
        """
        try:
            user_permissions = self.current_user.get('permissions', [])
            
            # 检查功能权限
            if operation not in user_permissions:
                return False
            
            # 检查部门权限
            if department and not self.check_department_permission(department):
                return False
            
            return True
            
        except Exception as e:
            Logger.log_error(f"检查操作权限失败: {str(e)}")
            return False
    
    # ==================== 私有辅助方法 ====================
    
    def _get_department_inventory(self, material_id: str, department: str) -> Optional[Dict]:
        """获取部门库存信息"""
        try:
            query = """
                SELECT * FROM department_inventory 
                WHERE material_id = %s AND department = %s
            """
            result = self.db_manager.execute_query(query, (material_id, department))
            return result[0] if result else None
        except Exception as e:
            Logger.log_error(f"获取部门库存信息失败: {str(e)}")
            return None
    
    def _update_department_inventory_qty(self, material_id: str, department: str, 
                                       total_qty_change: float = 0, 
                                       available_qty_change: float = 0,
                                       reserved_qty_change: float = 0):
        """更新部门库存数量"""
        try:
            current_time = datetime.now()
            current_user = self.current_user.get('username', 'system')
            
            update_sql = """
                UPDATE department_inventory 
                SET total_qty = total_qty + %s,
                    available_qty = available_qty + %s,
                    reserved_qty = reserved_qty + %s,
                    update_time = %s,
                    update_user = %s
                WHERE material_id = %s AND department = %s
            """
            
            self.db_manager.execute_update(update_sql, (
                total_qty_change, available_qty_change, reserved_qty_change,
                current_time, current_user, material_id, department
            ))
            
        except Exception as e:
            Logger.log_error(f"更新部门库存数量失败: {str(e)}")
            raise
    
    def _create_inventory_transaction(self, transaction_type: int, material_id: str, 
                                    department: str, in_qty: float = 0, out_qty: float = 0,
                                    balance_qty: float = 0, unit_cost: float = 0,
                                    source_type: str = None, source_no: str = None,
                                    source_id: int = None, remark: str = None):
        """创建库存流水记录"""
        try:
            from system.auto_number_module import AutoNumberService
            auto_service = AutoNumberService()
            transaction_no = auto_service.generate_number(
                rule_code='KCLS',
                business_date=date.today().strftime('%Y-%m-%d'),
                business_type='inventory_transaction',
                user_id=self.current_user.get('user_id')
            )
            
            # 获取物料信息
            material_query = "SELECT name, category_id, base_unit FROM materials WHERE material_id = %s"
            material_result = self.db_manager.execute_query(material_query, (material_id,))
            
            if material_result:
                material_info = material_result[0]
                material_name = material_info['name']
                category_id = material_info['category_id']
                unit = material_info['base_unit']
            else:
                material_name = material_id
                category_id = None
                unit = 'PCS'
            
            insert_sql = """
                INSERT INTO inventory_transactions (
                    transaction_no, transaction_date, transaction_type, material_id,
                    material_name, material_category_id, department, in_qty, out_qty,
                    balance_qty, unit, unit_cost, total_cost, source_type, source_no,
                    source_id, remark, create_user
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            current_user = self.current_user.get('username', 'system')
            total_cost = (in_qty + out_qty) * unit_cost
            
            params = (
                transaction_no, date.today(), transaction_type, material_id,
                material_name, category_id, department, in_qty, out_qty,
                balance_qty, unit, unit_cost, total_cost, source_type, source_no,
                source_id, remark, current_user
            )
            
            self.db_manager.execute_insert(insert_sql, params)
            
        except Exception as e:
            Logger.log_error(f"创建库存流水记录失败: {str(e)}")
            raise
    
    def _update_department_inventory_from_initial(self, inventory_data: Dict, initial_id: int):
        """从期初库存更新部门库存"""
        try:
            material_id = inventory_data['material_id']
            department = inventory_data['department']
            warehouse_code = inventory_data.get('warehouse_code')
            
            # 检查是否已存在部门库存记录
            existing = self._get_department_inventory(material_id, department)
            
            if existing:
                # 更新现有记录
                self._update_department_inventory_qty(
                    material_id, department,
                    total_qty_change=inventory_data['initial_qty'],
                    available_qty_change=inventory_data['initial_qty']
                )
            else:
                # 创建新的部门库存记录
                insert_sql = """
                    INSERT INTO department_inventory (
                        material_id, material_name, material_category_id, department,
                        warehouse_code, warehouse_name, total_qty, available_qty,
                        unit, avg_unit_cost, total_cost, update_user
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                current_user = self.current_user.get('username', 'system')
                
                params = (
                    material_id,
                    inventory_data.get('material_name'),
                    inventory_data.get('material_category_id'),
                    department,
                    warehouse_code,
                    inventory_data.get('warehouse_name'),
                    inventory_data['initial_qty'],
                    inventory_data['initial_qty'],
                    inventory_data['unit'],
                    inventory_data.get('unit_cost', 0),
                    inventory_data.get('total_cost', 0),
                    current_user
                )
                
                self.db_manager.execute_insert(insert_sql, params)
                
        except Exception as e:
            Logger.log_error(f"更新部门库存失败: {str(e)}")
            raise