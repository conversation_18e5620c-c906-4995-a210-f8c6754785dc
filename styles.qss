/* 主窗口和通用部件样式 */
QMainWindow, QDialog {
    background: #ffffff;
}

/* 按钮样式 */
QPushButton {
    background-color: #71c4ef;
    color: #ffffff;
    border: none;
    padding: 5px 15px;
    border-radius: 3px;
    min-width: 80px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #8fd0f2;
}

QPushButton:pressed {
    background-color: #71c4ef;
}

QPushButton:disabled {
    background-color: #cccccc;
    color: #444648;
}

/* 输入框样式 */
QLineEdit {
    border: 1px solid #cccccc;
    border-radius: 3px;
    padding: 5px;
    background: #ffffff;
    color: #1d1f21;
    selection-background-color: #71c4ef;
}

QLineEdit:focus {
    border: 1px solid #71c4ef;
}

QLineEdit:hover {
    border: 1px solid #8fd0f2;
}

/* 标签样式 */
QLabel {
    color: #444648;
}

/* 表格样式 */
QTableWidget {
    border: 1px solid #cccccc;
    background: #ffffff;
    gridline-color: #f5f5f5;
    selection-background-color: #71c4ef;
    selection-color: #ffffff;
}

QTableWidget::item {
    padding: 0px;
    /* 移除 color: #444648; */
    min-height: 20px;
    height: 20px;
    font-weight: normal;
}

QTableWidget::item:selected {
    background: #71c4ef;
    color: #ffffff;
}

/* 表格内的下拉框样式 */
/* QTableWidget QComboBox {
    border: none;
    border-radius: 0px;
    padding: 0px 15px 0px 5px;
    background: transparent;
    color: #1d1f21;
    min-width: 0px;
    width: 100%;
    height: 35px;
    margin: 0px;
}

QTableWidget QComboBox:hover {
    background: #f5f5f5;
}

QTableWidget QComboBox::drop-down {
    border: none;
    width: 15px;
    padding: 0px;
    background: transparent;
}

QTableWidget QComboBox::down-arrow {
    image: url(resources/icons/down-arrow.png);
    width: 10px;
    height: 10px;
}

QTableWidget QComboBox QAbstractItemView {
    border: 1px solid #cccccc;
    selection-background-color: #71c4ef;
    selection-color: #ffffff;
    background: #ffffff;
    padding: 0px;
}

QTableWidget QComboBox QAbstractItemView::item {
    min-height: 25px;
    padding: 5px;
}

QTableWidget QComboBox QAbstractItemView::item:hover {
    background-color: #f5f5f5;
} */

QHeaderView::section {
    background: #f5f5f5;
    color: #444648;
    padding: 4px;
    border: none;
    border-right: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc;
    font-weight: normal;
}


/* 标签页关闭按钮样式 */
QTabBar::close-button {
    margin: 2px;
}

/* 鼠标悬停在关闭按钮时的样式
   只保留圆角设置 */
/* QTabBar::close-button:hover {
    
    border-radius: 2px;
} */


/* 滚动条样式 */
QScrollBar:vertical {
    border: none;
    background: #f5f5f5;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background: #cccccc;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::handle:vertical:hover {
    background: #929292;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background: #f5f5f5;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background: #cccccc;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::handle:horizontal:hover {
    background: #929292;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* 分割器样式 */
QSplitter::handle {
    background: #cccccc;
    margin: 2px;
}

QSplitter::handle:hover {
    background: #8fd0f2;
}

/* 工具栏样式 */
QToolBar {
    background: #ffffff;
    border: none;
    spacing: 5px;
    padding: 5px;
}

QToolButton {
    background: transparent;
    border: none;
    padding: 5px;
    border-radius: 3px;
}

QToolButton:hover {
    background: #f5f5f5;
}

QToolButton:pressed {
    background: #cccccc;
}

/* 状态栏样式 */
QStatusBar {
    background: #ffffff;
    color: #444648;
}

/* 菜单样式 */
QMenuBar {
    background: #ffffff;
    border-bottom: 1px solid #cccccc;
}

QMenuBar::item {
    padding: 5px 10px;
    background: transparent;
}

QMenuBar::item:selected {
    background: #f5f5f5;
    color: #71c4ef;
}

QMenu {
    background: #ffffff;
    border: 1px solid #cccccc;
}

QMenu::item {
    padding: 5px 30px 5px 20px;
}

QMenu::item:selected {
    background: #f5f5f5;
    color: #71c4ef;
}

/* 分组框样式 */
QGroupBox {
    border: 1px solid #cccccc;
    border-radius: 3px;
    margin-top: 1em;
    padding-top: 10px;
}

QGroupBox::title {
    color: #444648;
  
  subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 3px;
    background: #ffffff;
} 
