"""
物料数据模型
用于物料相关的数据模型定义
"""
from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex, QVariant

class MaterialTableModel(QAbstractTableModel):
    """物料表格数据模型"""
    
    def __init__(self, data=None, headers=None, parent=None):
        super().__init__(parent)
        self._data = data if data is not None else []
        self._headers = headers if headers is not None else []
    
    def rowCount(self, parent=QModelIndex()):
        """返回行数"""
        return len(self._data)
    
    def columnCount(self, parent=QModelIndex()):
        """返回列数"""
        return len(self._headers)
    
    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        """获取数据"""
        if not index.isValid() or not (0 <= index.row() < len(self._data)):
            return QVariant()
        
        row = index.row()
        col = index.column()
        column_name = self._headers[col]
        
        if role == Qt.ItemDataRole.DisplayRole:
            return str(self._data[row].get(column_name, ""))
        
        if role == Qt.ItemDataRole.TextAlignmentRole:
            return Qt.AlignmentFlag.AlignCenter
        
        return QVariant()
    
    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        """获取表头数据"""
        if role != Qt.ItemDataRole.DisplayRole:
            return QVariant()
        
        if orientation == Qt.Orientation.Horizontal:
            if 0 <= section < len(self._headers):
                return self._headers[section]
        
        return QVariant()
    
    def update_data(self, data):
        """更新数据"""
        self.beginResetModel()
        self._data = data if data is not None else []
        self.endResetModel()
    
    def get_item(self, row):
        """获取指定行的数据"""
        if 0 <= row < len(self._data):
            return self._data[row]
        return None 