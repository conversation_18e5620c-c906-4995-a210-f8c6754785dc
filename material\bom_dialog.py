"""
BOM新增/编辑对话框
用于添加和编辑BOM信息
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QComboBox, QFormLayout, QDialogButtonBox, QTabWidget,
                           QWidget, QSpinBox, QDoubleSpinBox, QTextEdit, QCheckBox,
                           QTableWidget, QTableWidgetItem, QPushButton, QHeaderView,
                           QMessageBox, QDateEdit, QFrame, QGroupBox, QGridLayout)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QIcon
from datetime import datetime, date
from material.bom_db import BOMDB
from material.material_db import MaterialDB

class BOMDialog(QDialog):
    """BOM新增/编辑对话框"""
    
    # 定义信号
    data_saved = pyqtSignal()  # 数据保存成功信号
    
    def __init__(self, parent=None, bom_id=None, mode='add'):
        super().__init__(parent)
        self.bom_id = bom_id
        self.mode = mode  # add, edit, view
        self.bom_db = BOMDB()
        self.material_db = MaterialDB()
        
        # 分别存储包材和原料明细数据
        self.package_detail_data = []  # 包材明细数据
        self.material_detail_data = []  # 原料明细数据
        self.original_data = None
        
        self.setWindowTitle({
            'add': '新增BOM',
            'edit': '编辑BOM', 
            'view': '查看BOM'
        }.get(mode, 'BOM信息'))
        
        self.setModal(True)
        self.resize(1024, 732)
        self.setup_ui()
        
        # 自动生成BOM编码（仅在新增模式下）
        if mode == 'add':
            self.generate_bom_code()
        
        if bom_id:
            self.load_data()
    
    def setup_ui(self):
        """设置UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建主要内容区域
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(20, 20, 20, 10)
        content_layout.setSpacing(20)
        
        # 基本信息区域
        self.create_basic_info_section(content_layout)
        
        # 明细表格区域（标签页）
        self.create_detail_tabs(content_layout)
        
        main_layout.addWidget(content_widget)
        
        # 按钮区域
        self.create_button_area(main_layout)
        
        # 设置只读模式
        if self.mode == 'view':
            self.set_readonly_mode()
    
    def create_basic_info_section(self, parent_layout):
        """创建基本信息区域"""
        # 基本信息卡片
        basic_card = QFrame()
        basic_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        basic_layout = QVBoxLayout(basic_card)
        basic_layout.setContentsMargins(20, 15, 20, 20)
        basic_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📋 BOM基本信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        basic_layout.addWidget(title_label)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)
        
        # 第一行：BOM编码、BOM名称
        form_layout.addWidget(QLabel("BOM编码:"), 0, 0)
        
        self.bom_code_edit = QLineEdit()
        self.bom_code_edit.setPlaceholderText("系统自动生成")
        self.bom_code_edit.setReadOnly(True)  # 设置为只读
        form_layout.addWidget(self.bom_code_edit, 0, 1)
        
        form_layout.addWidget(QLabel("BOM名称:"), 0, 2)
        self.bom_name_edit = QLineEdit()
        self.bom_name_edit.setPlaceholderText("请输入BOM名称")
        form_layout.addWidget(self.bom_name_edit, 0, 3)
        
        # 第二行：成品物料、BOM版本
        form_layout.addWidget(QLabel("成品物料:"), 1, 0)
        
        material_layout = QHBoxLayout()
        self.parent_material_edit = QLineEdit()
        self.parent_material_edit.setPlaceholderText("请选择成品物料")
        self.parent_material_edit.setReadOnly(True)
        material_layout.addWidget(self.parent_material_edit)
        
        self.select_material_btn = QPushButton("选择物料")
        self.select_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                min-width: 70px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.select_material_btn.clicked.connect(self.select_parent_material)
        material_layout.addWidget(self.select_material_btn)
        
        form_layout.addLayout(material_layout, 1, 1)
        
        form_layout.addWidget(QLabel("BOM版本:"), 1, 2)
        self.bom_version_edit = QLineEdit()
        self.bom_version_edit.setText("1.0")
        self.bom_version_edit.setPlaceholderText("请输入版本号")
        form_layout.addWidget(self.bom_version_edit, 1, 3)
        
        # 第三行：物料名称（显示）、生产单位
        form_layout.addWidget(QLabel("物料名称:"), 2, 0)
        self.parent_material_name_label = QLabel()
        self.parent_material_name_label.setStyleSheet("color: #666; font-style: italic;")
        form_layout.addWidget(self.parent_material_name_label, 2, 1)
        
        form_layout.addWidget(QLabel("生产单位:"), 2, 2)
        self.production_unit_combo = QComboBox()
        self.production_unit_combo.addItems(["提", "个", "套", "箱", "包", "瓶", "PICs","组"])
        self.production_unit_combo.setEditable(True)
        form_layout.addWidget(self.production_unit_combo, 2, 3)
        
        # 第四行：标准产出、装箱数量
        form_layout.addWidget(QLabel("标准产出:"), 3, 0)
        self.standard_output_edit = QDoubleSpinBox()
        self.standard_output_edit.setRange(0.0001, 999999.9999)
        self.standard_output_edit.setDecimals(4)
        self.standard_output_edit.setValue(1.0000)
        form_layout.addWidget(self.standard_output_edit, 3, 1)
        
        form_layout.addWidget(QLabel("装箱数量:"), 3, 2)
        self.package_qty_edit = QDoubleSpinBox()
        self.package_qty_edit.setRange(0, 999999.9999)
        self.package_qty_edit.setDecimals(4)
        form_layout.addWidget(self.package_qty_edit, 3, 3)
        
        # 第五行：生效日期、失效日期
        form_layout.addWidget(QLabel("生效日期:"), 4, 0)
        self.effective_date_edit = QDateEdit()
        self.effective_date_edit.setDate(QDate.currentDate())
        self.effective_date_edit.setCalendarPopup(True)
        form_layout.addWidget(self.effective_date_edit, 4, 1)
        
        form_layout.addWidget(QLabel("失效日期:"), 4, 2)
        self.expiry_date_edit = QDateEdit()
        self.expiry_date_edit.setDate(QDate.currentDate().addYears(1))
        self.expiry_date_edit.setCalendarPopup(True)
        form_layout.addWidget(self.expiry_date_edit, 4, 3)
        
        # 第六行：状态、备注
        form_layout.addWidget(QLabel("状态:"), 5, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["启用", "停用"])
        form_layout.addWidget(self.status_combo, 5, 1)
        
        form_layout.addWidget(QLabel("备注:"), 5, 2)
        self.remark_edit = QLineEdit()
        self.remark_edit.setPlaceholderText("请输入备注信息...")
        form_layout.addWidget(self.remark_edit, 5, 3)
        
        # 设置列宽比例
        form_layout.setColumnStretch(1, 2)
        form_layout.setColumnStretch(3, 2)
        
        basic_layout.addWidget(form_widget)
        parent_layout.addWidget(basic_card)
    
    def create_detail_tabs(self, parent_layout):
        """创建明细标签页"""
        # 明细区域标题
        detail_title = QLabel("📦 BOM明细清单")
        detail_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                padding: 10px 0px 5px 0px;
            }
        """)
        parent_layout.addWidget(detail_title)
        
        # 创建标签页容器
        self.tab_widget = QTabWidget()
        
        # 包材明细标签页
        self.create_package_tab()
        
        # 原料明细标签页
        self.create_material_tab()
        
        parent_layout.addWidget(self.tab_widget)
    
    def create_package_tab(self):
        """创建包材明细标签页"""
        package_tab = QWidget()
        package_layout = QVBoxLayout(package_tab)
        package_layout.setContentsMargins(20, 20, 20, 20)
        package_layout.setSpacing(15)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.add_package_btn = QPushButton("➕ 添加包材")
        self.add_package_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
        """)
        self.add_package_btn.clicked.connect(self.add_package_detail)
        toolbar_layout.addWidget(self.add_package_btn)
        
        self.edit_package_btn = QPushButton("✏️ 编辑包材")
        self.edit_package_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.edit_package_btn.clicked.connect(self.edit_package_detail)
        toolbar_layout.addWidget(self.edit_package_btn)
        
        self.delete_package_btn = QPushButton("🗑️ 删除包材")
        self.delete_package_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4d4f;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #ff7875;
            }
        """)
        self.delete_package_btn.clicked.connect(self.delete_package_detail)
        toolbar_layout.addWidget(self.delete_package_btn)
        
        toolbar_layout.addStretch()
        
        # 统计信息
        self.package_stats_label = QLabel("包材明细: 0 项")
        self.package_stats_label.setStyleSheet("color: #666; font-size: 14px;")
        toolbar_layout.addWidget(self.package_stats_label)
        
        package_layout.addLayout(toolbar_layout)
        
        # 包材明细表格
        self.package_table = QTableWidget()
        self.package_table.setColumnCount(9)
        package_headers = ["序号", "物料编码", "物料名称", "需求数量", "需求单位", "原箱规格", "损耗率(%)", "单位成本", "备注"]
        self.package_table.setHorizontalHeaderLabels(package_headers)
        
        # 设置表格属性
        self.package_table.setAlternatingRowColors(True)
        self.package_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.package_table.horizontalHeader().setStretchLastSection(True)
        self.package_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 设置列宽
        self.package_table.setColumnWidth(0, 60)   # 序号
        self.package_table.setColumnWidth(1, 120)  # 物料编码
        self.package_table.setColumnWidth(3, 100)  # 需求数量
        self.package_table.setColumnWidth(4, 80)   # 需求单位
        self.package_table.setColumnWidth(5, 100)  # 原箱规格
        self.package_table.setColumnWidth(6, 80)   # 损耗率
        self.package_table.setColumnWidth(7, 80)   # 单位成本
        
        # 双击编辑/查看
        self.package_table.doubleClicked.connect(self.edit_package_detail)
        
        # 根据模式设置工具提示
        if self.mode == 'view':
            self.package_table.setToolTip("双击查看包材明细信息")
        else:
            self.package_table.setToolTip("双击编辑包材明细信息")
        
        # 添加右键菜单
        self.package_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.package_table.customContextMenuRequested.connect(self.show_package_context_menu)
        
        package_layout.addWidget(self.package_table)
        
        self.tab_widget.addTab(package_tab, "📦 包材明细")
    
    def create_material_tab(self):
        """创建原料明细标签页"""
        material_tab = QWidget()
        material_layout = QVBoxLayout(material_tab)
        material_layout.setContentsMargins(20, 20, 20, 20)
        material_layout.setSpacing(15)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.add_material_btn = QPushButton("➕ 添加原料")
        self.add_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
        """)
        self.add_material_btn.clicked.connect(self.add_material_detail)
        toolbar_layout.addWidget(self.add_material_btn)
        
        self.edit_material_btn = QPushButton("✏️ 编辑原料")
        self.edit_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.edit_material_btn.clicked.connect(self.edit_material_detail)
        toolbar_layout.addWidget(self.edit_material_btn)
        
        self.delete_material_btn = QPushButton("🗑️ 删除原料")
        self.delete_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4d4f;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #ff7875;
            }
        """)
        self.delete_material_btn.clicked.connect(self.delete_material_detail)
        toolbar_layout.addWidget(self.delete_material_btn)
        
        toolbar_layout.addStretch()
        
        # 统计信息
        self.material_stats_label = QLabel("原料明细: 0 项")
        self.material_stats_label.setStyleSheet("color: #666; font-size: 14px;")
        toolbar_layout.addWidget(self.material_stats_label)
        
        material_layout.addLayout(toolbar_layout)
        
        # 原料明细表格
        self.material_table = QTableWidget()
        self.material_table.setColumnCount(10)
        material_headers = ["序号", "物料编码", "物料名称", "需求数量", "需求单位", "原箱规格", "损耗率(%)", "质量标准", "存储条件", "备注"]
        self.material_table.setHorizontalHeaderLabels(material_headers)
        
        # 设置表格属性
        self.material_table.setAlternatingRowColors(True)
        self.material_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.material_table.horizontalHeader().setStretchLastSection(True)
        self.material_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 设置列宽
        self.material_table.setColumnWidth(0, 60)   # 序号
        self.material_table.setColumnWidth(1, 120)  # 物料编码
        self.material_table.setColumnWidth(3, 100)  # 需求数量
        self.material_table.setColumnWidth(4, 80)   # 需求单位
        self.material_table.setColumnWidth(5, 100)  # 原箱规格
        self.material_table.setColumnWidth(6, 80)   # 损耗率
        self.material_table.setColumnWidth(7, 120)  # 质量标准
        self.material_table.setColumnWidth(8, 100)  # 存储条件
        
        # 双击编辑/查看
        self.material_table.doubleClicked.connect(self.edit_material_detail)
        
        # 根据模式设置工具提示
        if self.mode == 'view':
            self.material_table.setToolTip("双击查看原料明细信息")
        else:
            self.material_table.setToolTip("双击编辑原料明细信息")
        
        # 添加右键菜单
        self.material_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.material_table.customContextMenuRequested.connect(self.show_material_context_menu)
        
        material_layout.addWidget(self.material_table)
        
        self.tab_widget.addTab(material_tab, "🧪 原料明细")
    
    def create_button_area(self, parent_layout):
        """创建按钮区域"""
        button_frame = QFrame()
        button_frame.setStyleSheet("""
            QFrame {
                background-color: #fafafa;
                border-top: 1px solid #e8e8e8;
            }
        """)
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(20, 15, 20, 15)
        
        button_layout.addStretch()
        
        # 保存按钮 - 改为实例属性
        self.save_btn = QPushButton("💾 保存")
        # self.save_btn.setStyleSheet("""
        #     QPushButton {
        #         background-color: #52c41a;
        #         color: white;
        #         border: none;
        #         padding: 12px 24px; 
        #         border-radius: 6px;
        #         font-weight: bold;
        #         font-size: 14px;
        #         min-width: 100px;
        #     }
        #     QPushButton:hover {
        #         background-color: #73d13d;
        #     }
        # """)
        self.save_btn.clicked.connect(self.save_bom)
        button_layout.addWidget(self.save_btn)
        
        # 取消按钮 - 改为实例属性
        self.cancel_btn = QPushButton("❌ 取消")
        # self.cancel_btn.setStyleSheet("""
        #     QPushButton {
        #         background-color: #d9d9d9;
        #         color: #666;
        #         border: none;
        #         padding: 12px 24px;
        #         border-radius: 6px;
        #         font-weight: bold;
        #         font-size: 14px;
        #         min-width: 100px;
        #     }
        #     QPushButton:hover {
        #         background-color: #bfbfbf;
        #     }
        # """)
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        parent_layout.addWidget(button_frame)
    
    def generate_bom_code(self):
        """生成BOM编码"""
        try:
            bom_code = self.bom_db.generate_auto_number()
            self.bom_code_edit.setText(bom_code)
            # QMessageBox.information(self, "成功", f"已生成BOM编码: {bom_code}")
        except Exception as e:
            QMessageBox.warning(self, "警告", f"生成BOM编码失败: {str(e)}")
    
    def select_parent_material(self):
        """选择成品物料"""
        try:
            from material.bom_material_search_dialog import BOMaterialSearchDialog
            
            # 获取已选择的明细物料ID列表
            all_selected_materials = []
            # 包材明细中的物料
            all_selected_materials.extend([detail.get('package_material_id') for detail in self.package_detail_data])
            # 原料明细中的物料
            all_selected_materials.extend([detail.get('raw_material_id') for detail in self.material_detail_data])
            
            dialog = BOMaterialSearchDialog(
                self, 
                exclude_material_id=None,  # 成品物料不需要排除
                selected_materials=all_selected_materials,
                material_type='product'  # 只显示商品类型的物料
            )
            
            dialog.material_selected.connect(self.on_parent_material_selected)
            dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"选择成品物料失败: {str(e)}")
    
    def on_parent_material_selected(self, material):
        """处理选中的成品物料"""
        if material:
            self.parent_material_edit.setText(material.get('material_id', ''))
            self.parent_material_name_label.setText(material.get('name', ''))
            
            # 如果BOM名称为空，自动填充
            if not self.bom_name_edit.text().strip():
                material_name = material.get('name', '')
                bom_version = self.bom_version_edit.text().strip() or '1.0'
                self.bom_name_edit.setText(f"{material_name} BOM V{bom_version}")
    
    def add_package_detail(self):
        """添加包材明细"""
        try:
            from material.bom_package_detail_dialog import BOMPackageDetailDialog
            dialog = BOMPackageDetailDialog(self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                detail_data = dialog.get_detail_data()
                print("包材明细数据：", detail_data)
                # 设置序号
                detail_data['sequence_no'] = len(self.package_detail_data) + 1
                self.package_detail_data.append(detail_data)
                self.refresh_package_table()
        except ImportError:
            QMessageBox.information(self, "提示", "包材明细对话框开发中...")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加包材明细失败: {str(e)}")
    
    def edit_package_detail(self):
        """编辑包材明细（仅编辑模式可用）"""
        # 查看模式下不允许双击弹出明细窗口
        if self.mode == 'view':
            return
        
        current_row = self.package_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要编辑的包材明细")
            return
        
        try:
            from material.bom_package_detail_dialog import BOMPackageDetailDialog
            detail_data = self.package_detail_data[current_row]
            
            dialog = BOMPackageDetailDialog(self, detail_data, mode='edit')
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                updated_data = dialog.get_detail_data()
                # 保持原序号
                updated_data['sequence_no'] = detail_data.get('sequence_no', current_row + 1)
                self.package_detail_data[current_row] = updated_data
                self.refresh_package_table()
                
        except ImportError:
            QMessageBox.information(self, "提示", "包材明细对话框开发中...")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑包材明细失败: {str(e)}")
    
    def delete_package_detail(self):
        """删除包材明细"""
        current_row = self.package_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要删除的包材明细")
            return
        
        reply = QMessageBox.question(
            self, "确认删除", 
            "确定要删除选中的包材明细吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            del self.package_detail_data[current_row]
            # 重新设置序号
            for i, detail in enumerate(self.package_detail_data):
                detail['sequence_no'] = i + 1
            self.refresh_package_table()
    
    def refresh_package_table(self):
        """刷新包材明细表格"""
        self.package_table.setRowCount(len(self.package_detail_data))
        
        for row, detail in enumerate(self.package_detail_data):
            # 序号
            self.package_table.setItem(row, 0, QTableWidgetItem(str(detail.get('sequence_no', row + 1))))
            # 物料编码
            self.package_table.setItem(row, 1, QTableWidgetItem(detail.get('package_material_id', '')))
            # 物料名称
            self.package_table.setItem(row, 2, QTableWidgetItem(detail.get('material_name', '')))
            # 需求数量
            self.package_table.setItem(row, 3, QTableWidgetItem(str(detail.get('required_qty', 0))))
            # 需求单位
            self.package_table.setItem(row, 4, QTableWidgetItem(detail.get('required_unit', '')))
            # 原箱规格
            original_box = detail.get('original_box_qty', 0)
            original_unit = detail.get('original_box_unit', '')
            box_spec = f"{original_box}{original_unit}" if original_box and original_unit else ""
            self.package_table.setItem(row, 5, QTableWidgetItem(box_spec))
            # 损耗率
            self.package_table.setItem(row, 6, QTableWidgetItem(str(detail.get('scrap_rate', 0))))
            # 单位成本
            unit_cost = detail.get('unit_cost', 0)
            cost_text = f"¥{unit_cost:.2f}" if unit_cost else ""
            self.package_table.setItem(row, 7, QTableWidgetItem(cost_text))
            # 备注
            self.package_table.setItem(row, 8, QTableWidgetItem(detail.get('remark', '')))
            
            # 设置所有单元格居中对齐
            for col in range(self.package_table.columnCount()):
                item = self.package_table.item(row, col)
                if item:
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 更新统计信息
        self.package_stats_label.setText(f"包材明细: {len(self.package_detail_data)} 项")
    
    def add_material_detail(self):
        """添加原料明细"""
        try:
            from material.bom_material_detail_dialog import BOMMaterialDetailDialog
            dialog = BOMMaterialDetailDialog(self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                detail_data = dialog.get_detail_data()
                # 打印验证是否包含category_id
                print("包材明细数据：", detail_data)  # 检查是否有'material_category_id'
                # 设置序号
                detail_data['sequence_no'] = len(self.material_detail_data) + 1
                self.material_detail_data.append(detail_data)
                self.refresh_material_table()
        except ImportError:
            QMessageBox.information(self, "提示", "原料明细对话框开发中...")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加原料明细失败: {str(e)}")
    
    def edit_material_detail(self):
        """编辑原料明细（仅编辑模式可用）"""
        # 查看模式下不允许双击弹出明细窗口
        if self.mode == 'view':
            return
        
        current_row = self.material_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要编辑的原料明细")
            return
        
        try:
            from material.bom_material_detail_dialog import BOMMaterialDetailDialog
            detail_data = self.material_detail_data[current_row]
            
            dialog = BOMMaterialDetailDialog(self, detail_data, mode='edit')
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                updated_data = dialog.get_detail_data()
                # 保持原序号
                updated_data['sequence_no'] = detail_data.get('sequence_no', current_row + 1)
                self.material_detail_data[current_row] = updated_data
                self.refresh_material_table()
                
        except ImportError:
            QMessageBox.information(self, "提示", "原料明细对话框开发中...")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑原料明细失败: {str(e)}")
    
    def delete_material_detail(self):
        """删除原料明细"""
        current_row = self.material_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要删除的原料明细")
            return
        
        reply = QMessageBox.question(
            self, "确认删除", 
            "确定要删除选中的原料明细吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            del self.material_detail_data[current_row]
            # 重新设置序号
            for i, detail in enumerate(self.material_detail_data):
                detail['sequence_no'] = i + 1
            self.refresh_material_table()
    
    def refresh_material_table(self):
        """刷新原料明细表格"""
        self.material_table.setRowCount(len(self.material_detail_data))
        
        for row, detail in enumerate(self.material_detail_data):
            # 序号
            self.material_table.setItem(row, 0, QTableWidgetItem(str(detail.get('sequence_no', row + 1))))
            # 物料编码
            self.material_table.setItem(row, 1, QTableWidgetItem(detail.get('raw_material_id', '')))
            # 物料名称
            self.material_table.setItem(row, 2, QTableWidgetItem(detail.get('material_name', '')))
            # 需求数量
            self.material_table.setItem(row, 3, QTableWidgetItem(str(detail.get('required_qty', 0))))
            # 需求单位
            self.material_table.setItem(row, 4, QTableWidgetItem(detail.get('required_unit', '')))
            # 原箱规格
            original_box = detail.get('original_box_qty', 0)
            original_unit = detail.get('original_box_unit', '')
            box_spec = f"{original_box}{original_unit}" if original_box and original_unit else ""
            self.material_table.setItem(row, 5, QTableWidgetItem(box_spec))
            # 损耗率
            self.material_table.setItem(row, 6, QTableWidgetItem(str(detail.get('scrap_rate', 0))))
            # 质量标准
            self.material_table.setItem(row, 7, QTableWidgetItem(detail.get('quality_standard', '')))
            # 存储条件
            self.material_table.setItem(row, 8, QTableWidgetItem(detail.get('storage_condition', '')))
            # 备注
            self.material_table.setItem(row, 9, QTableWidgetItem(detail.get('remark', '')))
            
            # 设置所有单元格居中对齐
            for col in range(self.material_table.columnCount()):
                item = self.material_table.item(row, col)
                if item:
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 更新统计信息
        self.material_stats_label.setText(f"原料明细: {len(self.material_detail_data)} 项")
    
    def set_readonly_mode(self):
        """设置只读模式"""
        # 禁用主表编辑控件
        controls = [
            self.bom_name_edit, self.parent_material_edit, self.select_material_btn,
            self.bom_version_edit, self.production_unit_combo, self.standard_output_edit,
            self.package_qty_edit, self.effective_date_edit, self.expiry_date_edit,
            self.remark_edit,self.status_combo
        ]
        
        for control in controls:
            if hasattr(control, 'setReadOnly'):
                control.setReadOnly(True)
            elif hasattr(control, 'setEnabled'):
                control.setEnabled(False)
        
        # 禁用明细操作按钮
        if hasattr(self, 'add_package_btn'):
            self.add_package_btn.setVisible(False)
            self.edit_package_btn.setVisible(False)
            self.delete_package_btn.setVisible(False)
        if hasattr(self, 'add_material_btn'):
            self.add_material_btn.setVisible(False)
            self.edit_material_btn.setVisible(False)
            self.delete_material_btn.setVisible(False)
        
        # 设置表格样式为只读状态
        readonly_style = """
            QTableWidget {
                background-color: #f5f5f5;
                color: #666;
            }
            QTableWidget::item:selected {
                background-color: #e0e0e0;
                color: #333;
            }
        """
        
        if hasattr(self, 'package_table'):
            self.package_table.setStyleSheet(readonly_style)
        if hasattr(self, 'material_table'):
            self.material_table.setStyleSheet(readonly_style)
        
        # 更新按钮
        self.save_btn.setText("关闭")
        self.cancel_btn.hide()
        
        # 修改确定按钮行为
        self.save_btn.clicked.disconnect()
        self.save_btn.clicked.connect(self.close)
    
    def load_data(self):
        """加载BOM数据"""
        try:
            # 获取BOM主表数据
            bom_data = self.bom_db.get_bom_by_id(self.bom_id)
            if not bom_data:
                QMessageBox.warning(self, "警告", "BOM数据不存在")
                return
            
            # 保存原始数据
            self.original_data = bom_data
            
            # 填充基本信息
            self.bom_code_edit.setText(bom_data.get('bom_code', ''))
            self.bom_name_edit.setText(bom_data.get('bom_name', ''))
            self.parent_material_edit.setText(bom_data.get('parent_material_id', ''))
            self.parent_material_name_label.setText(bom_data.get('parent_material_name', ''))
            self.bom_version_edit.setText(bom_data.get('bom_version', '1.0'))
            
            # 生产信息
            production_unit = bom_data.get('production_unit', '个')
            index = self.production_unit_combo.findText(production_unit)
            if index >= 0:
                self.production_unit_combo.setCurrentIndex(index)
            else:
                self.production_unit_combo.setCurrentText(production_unit)
            
            self.standard_output_edit.setValue(bom_data.get('standard_output_qty', 1.0))
            if bom_data.get('package_qty_per_box'):
                self.package_qty_edit.setValue(bom_data.get('package_qty_per_box', 0))
            
            # 日期信息
            if bom_data.get('effective_date'):
                self.effective_date_edit.setDate(QDate.fromString(str(bom_data['effective_date']), "yyyy-MM-dd"))
            if bom_data.get('expiry_date'):
                self.expiry_date_edit.setDate(QDate.fromString(str(bom_data['expiry_date']), "yyyy-MM-dd"))
            
            # 状态
            status = bom_data.get('status', 1)
            self.status_combo.setCurrentIndex(0 if status == 1 else 1)
            
            # 备注
            self.remark_edit.setText(bom_data.get('remark', ''))
            
            # 获取明细数据（使用新的字典结构）
            details_data = self.bom_db.get_bom_details(self.bom_id)
            self.package_detail_data = details_data.get('package_details', [])
            self.material_detail_data = details_data.get('material_details', [])
            
            # 刷新表格显示
            self.refresh_package_table()
            self.refresh_material_table()
            
            # 如果是查看模式，禁用编辑
            if self.mode == 'view':
                self.set_readonly_mode()
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载BOM数据失败：{str(e)}")
            print(f"加载BOM数据异常: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def validate_data(self):
        """验证数据"""
        if not self.bom_code_edit.text().strip():
            QMessageBox.warning(self, "警告", "BOM编码不能为空")
            self.tab_widget.setCurrentIndex(0)
            self.bom_code_edit.setFocus()
            return False
        
        if not self.parent_material_edit.text().strip():
            QMessageBox.warning(self, "警告", "请选择成品物料")
            self.tab_widget.setCurrentIndex(0)
            self.select_material_btn.setFocus()
            return False
        
        if self.standard_output_edit.value() <= 0:
            QMessageBox.warning(self, "警告", "标准产出数量必须大于0")
            self.tab_widget.setCurrentIndex(0)
            self.standard_output_edit.setFocus()
            return False
        
        # 验证日期
        effective_date = self.effective_date_edit.date().toPyDate()
        expiry_date = self.expiry_date_edit.date().toPyDate()
        if effective_date >= expiry_date:
            QMessageBox.warning(self, "警告", "生效日期必须早于失效日期")
            self.tab_widget.setCurrentIndex(0)
            self.effective_date_edit.setFocus()
            return False
        
        # 验证明细数据
        if not self.package_detail_data and not self.material_detail_data:
            QMessageBox.warning(self, "警告", "请至少添加一条BOM明细（包材或原料）")
            self.tab_widget.setCurrentIndex(1)
            return False
        
        return True
    
    def get_bom_data(self):
        """获取BOM数据"""
        return {
            'bom_code': self.bom_code_edit.text().strip(),
            'parent_material_id': self.parent_material_edit.text().strip(),
            'bom_name': self.bom_name_edit.text().strip(),
            'bom_version': self.bom_version_edit.text().strip() or '1.0',
            'production_unit': self.production_unit_combo.currentText(),
            'standard_output_qty': self.standard_output_edit.value(),
            'package_qty_per_box': self.package_qty_edit.value() if self.package_qty_edit.value() > 0 else None,
            'effective_date': self.effective_date_edit.date().toPyDate(),
            'expiry_date': self.expiry_date_edit.date().toPyDate(),
            'status': 1 if self.status_combo.currentIndex() == 0 else 0,
            'remark': self.remark_edit.text().strip()
        }
    
    def save_bom(self):
        """保存BOM"""
        if not self.validate_data():
            return
        
        try:
            bom_data = self.get_bom_data()
            
            if self.mode == 'add':
                # 新增BOM
                bom_id = self.bom_db.save_bom_header(bom_data)
                
                # 分别保存包材和原料明细
                if self.package_detail_data:
                    self.bom_db.save_bom_package_details(bom_id, self.package_detail_data)
                if self.material_detail_data:
                    self.bom_db.save_bom_material_details(bom_id, self.material_detail_data)
                    
                QMessageBox.information(self, "成功", "BOM保存成功！")
            else:
                # 更新BOM
                self.bom_db.update_bom_header(self.bom_id, bom_data)
                
                # 分别更新包材和原料明细
                self.bom_db.save_bom_package_details(self.bom_id, self.package_detail_data)
                self.bom_db.save_bom_material_details(self.bom_id, self.material_detail_data)
                
                QMessageBox.information(self, "成功", "BOM更新成功！")
            
            # 发送保存成功信号
            self.data_saved.emit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存BOM失败: {str(e)}")

    def show_package_context_menu(self, position):
        """显示包材明细右键菜单（仅编辑模式）"""
        # 查看模式下不显示右键菜单
        if self.mode == 'view':
            return
        
        if self.package_table.itemAt(position) is None:
            return
        
        from PyQt6.QtWidgets import QMenu
        menu = QMenu(self)
        
        edit_action = menu.addAction("✏️ 编辑明细")
        edit_action.triggered.connect(self.edit_package_detail)
        
        menu.addSeparator()
        
        delete_action = menu.addAction("🗑️ 删除明细")
        delete_action.triggered.connect(self.delete_package_detail)
        
        menu.exec(self.package_table.mapToGlobal(position))

    def show_material_context_menu(self, position):
        """显示原料明细右键菜单（仅编辑模式）"""
        # 查看模式下不显示右键菜单
        if self.mode == 'view':
            return
        
        if self.material_table.itemAt(position) is None:
            return
        
        from PyQt6.QtWidgets import QMenu
        menu = QMenu(self)
        
        edit_action = menu.addAction("✏️ 编辑明细")
        edit_action.triggered.connect(self.edit_material_detail)
        
        menu.addSeparator()
        
        delete_action = menu.addAction("🗑️ 删除明细")
        delete_action.triggered.connect(self.delete_material_detail)
        
        menu.exec(self.material_table.mapToGlobal(position))




