"""
BOM包材明细添加/编辑对话框
用于添加和编辑BOM包材明细信息
专门处理包装材料的用量、成本、供应商等信息
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QComboBox, QFormLayout, QDialogButtonBox, QDoubleSpinBox,
                           QSpinBox, QTextEdit, QMessageBox, QPushButton, QFrame,
                           QGroupBox)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon
from material.material_db import MaterialDB

class BOMPackageDetailDialog(QDialog):
    """BOM包材明细对话框"""
    
    def __init__(self, parent=None, detail_data=None, mode='edit'):
        super().__init__(parent)
        self.detail_data = detail_data or {}
        self.mode = mode  # 'add', 'edit', 'view'
        
        self.setWindowTitle({
            'add': '新增包材明细',
            'edit': '编辑包材明细',
            'view': '查看包材明细'
        }.get(mode, '包材明细'))
        
        self.setModal(True)
        self.resize(600, 500)
        
        self.setup_ui()
        self.load_data()  # 这里调用load_data方法
        
        # 如果是查看模式，设置为只读
        if self.mode == 'view':
            self.set_readonly_mode()
    
    def setup_ui(self):
        """设置UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 物料信息组
        material_group = QGroupBox("包材物料信息")
        material_form = QFormLayout(material_group)
        material_form.setSpacing(10)
        
        # 物料选择
        material_layout = QHBoxLayout()
        self.material_id_edit = QLineEdit()
        self.material_id_edit.setPlaceholderText("请选择包材物料")
        self.material_id_edit.setReadOnly(True)
        material_layout.addWidget(self.material_id_edit)
        
        self.select_material_btn = QPushButton("选择包材")
        self.select_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.select_material_btn.clicked.connect(self.select_material)
        material_layout.addWidget(self.select_material_btn)
        material_form.addRow("包材编码:", material_layout)
        
        # 物料名称（显示用）
        self.material_name_label = QLabel()
        self.material_name_label.setStyleSheet("color: #666; font-style: italic;")
        material_form.addRow("包材名称:", self.material_name_label)
        
        # 物料分类（显示用）
        self.material_category_label = QLabel()
        self.material_category_label.setStyleSheet("color: #666; font-style: italic;")
        material_form.addRow("物料分类:", self.material_category_label)
        
        main_layout.addWidget(material_group)
        
        # 用量信息组
        quantity_group = QGroupBox("用量信息")
        quantity_form = QFormLayout(quantity_group)
        quantity_form.setSpacing(10)
        
        # 序号
        self.sequence_edit = QSpinBox()
        self.sequence_edit.setRange(1, 9999)
        self.sequence_edit.setValue(1)
        quantity_form.addRow("序号:", self.sequence_edit)
        
        # 需求数量
        self.required_qty_edit = QDoubleSpinBox()
        self.required_qty_edit.setRange(0.0001, 999999.9999)
        self.required_qty_edit.setDecimals(4)
        self.required_qty_edit.setValue(1.0000)
        quantity_form.addRow("需求数量:", self.required_qty_edit)
        
        # 需求单位
        self.required_unit_combo = QComboBox()
        self.required_unit_combo.addItems(["提", "个", "箱", "包", "套", "组","PICs","瓶"])
        self.required_unit_combo.setEditable(True)
        quantity_form.addRow("需求单位:", self.required_unit_combo)
        
        # 原箱规格数量
        self.original_box_qty_edit = QDoubleSpinBox()
        self.original_box_qty_edit.setRange(0, 999999.9999)
        self.original_box_qty_edit.setDecimals(4)
        quantity_form.addRow("原箱规格数量:", self.original_box_qty_edit)
        
        # 原箱单位
        self.original_box_unit_combo = QComboBox()
        self.original_box_unit_combo.addItems(["提/箱", "个/箱", "包/箱", "组/箱" "PICs/箱", "套/箱","瓶/箱"])
        self.original_box_unit_combo.setEditable(True)
        quantity_form.addRow("原箱单位:", self.original_box_unit_combo)
        
        # 损耗率
        self.scrap_rate_edit = QDoubleSpinBox()
        self.scrap_rate_edit.setRange(0, 100)
        self.scrap_rate_edit.setDecimals(2)
        self.scrap_rate_edit.setSuffix("%")
        quantity_form.addRow("损耗率:", self.scrap_rate_edit)
        
        main_layout.addWidget(quantity_group)
        
        # 成本信息组
        cost_group = QGroupBox("成本信息")
        cost_form = QFormLayout(cost_group)
        cost_form.setSpacing(10)
        
        # 单位成本
        self.unit_cost_edit = QDoubleSpinBox()
        self.unit_cost_edit.setRange(0, 999999.9999)
        self.unit_cost_edit.setDecimals(4)
        self.unit_cost_edit.setPrefix("¥ ")
        cost_form.addRow("单位成本:", self.unit_cost_edit)
        
        # 供应商
        self.supplier_edit = QLineEdit()
        self.supplier_edit.setPlaceholderText("请输入供应商名称...")
        cost_form.addRow("供应商:", self.supplier_edit)
        
        main_layout.addWidget(cost_group)
        
        # 备注信息
        remark_group = QGroupBox("备注信息")
        remark_layout = QVBoxLayout(remark_group)
        self.remark_edit = QTextEdit()
        self.remark_edit.setMaximumHeight(60)
        self.remark_edit.setPlaceholderText("请输入备注信息...")
        remark_layout.addWidget(self.remark_edit)
        main_layout.addWidget(remark_group)
        
        # 按钮区域
        self.create_button_area(main_layout)
    
    def create_button_area(self, parent_layout):
        """创建按钮区域"""
        button_frame = QFrame()
        button_frame.setStyleSheet("QFrame { background-color: #f5f5f5; }")
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(20, 10, 20, 10)
        
        button_layout.addStretch()
        
        # 确定按钮
        self.ok_btn = QPushButton("✅ 确定")
        self.ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
        """)
        self.ok_btn.clicked.connect(self.accept_detail)
        button_layout.addWidget(self.ok_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #d9d9d9;
                color: #666;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #bfbfbf;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        parent_layout.addWidget(button_frame)
    
    def select_material(self):
        """选择包材物料"""
        try:
            from material.bom_material_search_dialog import BOMaterialSearchDialog
            
            # 获取父对话框的成品物料ID和已选择的明细物料
            parent_dialog = self.parent()
            exclude_material_id = None
            selected_materials = []
            
            if hasattr(parent_dialog, 'parent_material_edit'):
                exclude_material_id = parent_dialog.parent_material_edit.text().strip()
            
            if hasattr(parent_dialog, 'package_detail_data'):
                selected_materials = [detail.get('package_material_id') for detail in parent_dialog.package_detail_data]
            
            # 只显示包材类型的物料（parent_id=6的分类下的物料）
            dialog = BOMaterialSearchDialog(
                self,
                exclude_material_id=exclude_material_id,
                selected_materials=selected_materials,
                material_type='package'  # 指定为包材类型
            )
            
            dialog.material_selected.connect(self.on_material_selected)
            dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"选择包材失败: {str(e)}")
    
    def on_material_selected(self, material):
        """处理选中的物料"""
        if material:
            self.selected_material = material
            self.material_id_edit.setText(material.get('material_id', ''))
            self.material_name_label.setText(material.get('name', ''))
            
            # 获取物料分类信息
            category_name = material.get('category_name', '')
            self.material_category_label.setText(category_name)
            
            # 自动填充单位信息
            base_unit = material.get('base_unit', '')
            if base_unit:
                # 确保转换为字符串
                base_unit_str = str(base_unit)
                index = self.required_unit_combo.findText(base_unit_str)
                if index >= 0:
                    self.required_unit_combo.setCurrentIndex(index)
                else:
                    self.required_unit_combo.setCurrentText(base_unit_str)
            
            # 自动填充原箱信息
            original_box_unit = material.get('original_box_unit', '')
            if original_box_unit:
                # 确保转换为字符串
                original_box_unit_str = str(original_box_unit)
                index = self.original_box_unit_combo.findText(original_box_unit_str)
                if index >= 0:
                    self.original_box_unit_combo.setCurrentIndex(index)
                else:
                    self.original_box_unit_combo.setCurrentText(original_box_unit_str)
            
            # 自动填充成本信息
            cost = material.get('cost', 0)
            if cost:
                self.unit_cost_edit.setValue(float(cost))
    
    def load_data(self):
        """加载数据到界面"""
        if not self.detail_data:
            return
        
        try:
            # 加载物料信息
            material_id = self.detail_data.get('package_material_id', '') or self.detail_data.get('material_id', '')
            material_name = self.detail_data.get('material_name', '')
            if material_id:
                self.material_id_edit.setText(material_id)
                self.material_name_label.setText(material_name)
            #加载物流分类
            material_category = self.detail_data.get('category_name', '')
            if material_category and hasattr(self, 'material_category_label'):
                self.material_category_label.setText(material_category)
            
            # 获取物料详细信息
            if material_id:
                self.selected_material = {
                    'material_id': material_id,
                    'name': material_name,
                    'category_id': self.detail_data.get('material_category_id'),
                }
            
            # 序号
            sequence_no = self.detail_data.get('sequence_no', 1)
            if hasattr(self, 'sequence_edit'):
                self.sequence_edit.setValue(int(sequence_no))
            
            # 需求数量
            required_qty = self.detail_data.get('required_qty', 1.0)
            if hasattr(self, 'required_qty_edit'):
                self.required_qty_edit.setValue(float(required_qty))
            
            # 需求单位
            required_unit = self.detail_data.get('required_unit', '')
            if required_unit and hasattr(self, 'required_unit_combo'):
                index = self.required_unit_combo.findText(required_unit)
                if index >= 0:
                    self.required_unit_combo.setCurrentIndex(index)
                else:
                    self.required_unit_combo.setCurrentText(required_unit)
            
            # 原箱规格数量
            original_box_qty = self.detail_data.get('original_box_qty', 0)
            if original_box_qty and hasattr(self, 'original_box_qty_edit'):
                self.original_box_qty_edit.setValue(float(original_box_qty))
            
            # 原箱单位
            original_box_unit = self.detail_data.get('original_box_unit', '')
            if original_box_unit and hasattr(self, 'original_box_unit_combo'):
                index = self.original_box_unit_combo.findText(original_box_unit)
                if index >= 0:
                    self.original_box_unit_combo.setCurrentIndex(index)
                else:
                    self.original_box_unit_combo.setCurrentText(original_box_unit)
            
            # 损耗率
            scrap_rate = self.detail_data.get('scrap_rate', 0)
            if hasattr(self, 'scrap_rate_edit'):
                self.scrap_rate_edit.setValue(float(scrap_rate))
            
            # 单位成本
            unit_cost = self.detail_data.get('unit_cost', 0)
            if hasattr(self, 'unit_cost_edit'):
                self.unit_cost_edit.setValue(float(unit_cost))
            
            # 供应商
            supplier_id = self.detail_data.get('supplier_id', '')
            if hasattr(self, 'supplier_edit'):
                self.supplier_edit.setText(supplier_id)
            
            # 备注
            remark = self.detail_data.get('remark', '')
            if hasattr(self, 'remark_edit'):
                self.remark_edit.setPlainText(remark)
            
        except Exception as e:
            print(f"加载包材明细数据失败: {str(e)}")
    
    def get_detail_data(self):
        """获取明细数据"""
        try:
            # 获取物料信息
            material_id = self.material_id_edit.text().strip()
            material_name = self.material_name_label.text().strip()
            material_category_id = None

            #获取分类
            if hasattr(self, 'selected_material'):
                material_category_id = self.selected_material.get('category_id')
            # 获取序号
            sequence_no = 1
            if hasattr(self, 'sequence_edit'):
                sequence_no = self.sequence_edit.value()
            
            # 获取数量信息
            required_qty = 1.0
            if hasattr(self, 'required_qty_edit'):
                required_qty = self.required_qty_edit.value()
            
            required_unit = ''
            if hasattr(self, 'required_unit_combo'):
                required_unit = self.required_unit_combo.currentText().strip()
            
            # 获取原箱信息
            original_box_qty = 0
            if hasattr(self, 'original_box_qty_edit'):
                original_box_qty = self.original_box_qty_edit.value()
            
            original_box_unit = ''
            if hasattr(self, 'original_box_unit_combo'):
                original_box_unit = self.original_box_unit_combo.currentText().strip()
            
            # 获取损耗率
            scrap_rate = 0
            if hasattr(self, 'scrap_rate_edit'):
                scrap_rate = self.scrap_rate_edit.value()
            
            # 获取单位成本
            unit_cost = 0
            if hasattr(self, 'unit_cost_edit'):
                unit_cost = self.unit_cost_edit.value()
            
            # 获取供应商
            supplier_id = ''
            if hasattr(self, 'supplier_edit'):
                supplier_id = self.supplier_edit.text().strip()
            
            # 获取备注
            remark = ''
            if hasattr(self, 'remark_edit'):
                remark = self.remark_edit.toPlainText().strip()
            
            return {
                'material_id': material_id,
                'package_material_id': material_id,  # 兼容字段
                'material_name': material_name,
                'material_category_id': material_category_id,
                'sequence_no': sequence_no,
                'required_qty': required_qty,
                'required_unit': required_unit,
                'original_box_qty': original_box_qty,
                'original_box_unit': original_box_unit,
                'scrap_rate': scrap_rate,
                'unit_cost': unit_cost,
                'supplier_id': supplier_id,
                'remark': remark
            }
            
        except Exception as e:
            raise Exception(f"获取明细数据失败: {str(e)}")
    
    def accept(self):
        """确认保存"""
        if self.validate_data():
            super().accept()

    def accept_detail(self):
        """确认明细（兼容方法）"""
        if self.validate_data():
            self.accept()
    
    def set_readonly_mode(self):
        """设置只读模式"""
        # 禁用所有输入控件
        controls = [
            self.material_id_edit, 
        ]
        
        # 添加可能存在的控件
        if hasattr(self, 'select_material_btn'):
            controls.append(self.select_material_btn)
        if hasattr(self, 'required_qty_edit'):
            controls.append(self.required_qty_edit)
        if hasattr(self, 'required_unit_combo'):
            controls.append(self.required_unit_combo)
        if hasattr(self, 'original_box_qty_edit'):
            controls.append(self.original_box_qty_edit)
        if hasattr(self, 'original_box_unit_combo'):
            controls.append(self.original_box_unit_combo)
        if hasattr(self, 'scrap_rate_edit'):
            controls.append(self.scrap_rate_edit)
        if hasattr(self, 'unit_cost_edit'):
            controls.append(self.unit_cost_edit)
        if hasattr(self, 'supplier_edit'):
            controls.append(self.supplier_edit)
        if hasattr(self, 'remark_edit'):
            controls.append(self.remark_edit)
        if hasattr(self, 'sequence_edit'):
            controls.append(self.sequence_edit)
        
        for control in controls:
            if hasattr(control, 'setReadOnly'):
                control.setReadOnly(True)
            elif hasattr(control, 'setEnabled'):
                control.setEnabled(False)
        
        # 更新按钮文本和行为
        if hasattr(self, 'ok_btn'):
            self.ok_btn.setText("关闭")
            # 修改确定按钮行为：直接关闭而不保存
            try:
                self.ok_btn.clicked.disconnect()
            except:
                pass
            self.ok_btn.clicked.connect(self.close)
        
        if hasattr(self, 'cancel_btn'):
            self.cancel_btn.hide()

    def validate_data(self):
            """验证数据"""
            if not self.material_id_edit.text().strip():
                QMessageBox.warning(self, "警告", "请选择包材物料")
                self.select_material_btn.setFocus()
                return False
            
            if self.required_qty_edit.value() <= 0:
                QMessageBox.warning(self, "警告", "需求数量必须大于0")
                self.required_qty_edit.setFocus()
                return False
            
            if not self.required_unit_combo.currentText().strip():
                QMessageBox.warning(self, "警告", "请输入需求单位")
                self.required_unit_combo.setFocus()
                return False
            
            if self.sequence_edit.value() <= 0:
                QMessageBox.warning(self, "警告", "序号必须大于0")
                self.sequence_edit.setFocus()
                return False
            
            return True
    def fill_form_data(self):
        """填充表单数据（编辑模式）"""
        try:
            if not self.detail_data:
                return
            
            # 物料信息
            material_id = self.detail_data.get('package_material_id', '')
            self.material_id_edit.setText(material_id)
            self.material_name_label.setText(self.detail_data.get('material_name', ''))
            
            # 获取物料详细信息
            if material_id:
                # 这里可以通过material_id获取完整的物料信息
                self.selected_material = {
                    'material_id': material_id,
                    'name': self.detail_data.get('material_name', ''),
                    'category_id': self.detail_data.get('material_category_id'),
                }
            
            # 序号
            sequence_no = self.detail_data.get('sequence_no', 1)
            self.sequence_edit.setValue(int(sequence_no))
            
            # 需求数量
            required_qty = self.detail_data.get('required_qty', 1.0)
            self.required_qty_edit.setValue(float(required_qty))
            
            # 需求单位
            required_unit = self.detail_data.get('required_unit', '')
            if required_unit:
                index = self.required_unit_combo.findText(required_unit)
                if index >= 0:
                    self.required_unit_combo.setCurrentIndex(index)
                else:
                    self.required_unit_combo.setCurrentText(required_unit)
            
            # 原箱规格数量
            original_box_qty = self.detail_data.get('original_box_qty', 0)
            if original_box_qty:
                self.original_box_qty_edit.setValue(float(original_box_qty))
            
            # 原箱单位
            original_box_unit = self.detail_data.get('original_box_unit', '')
            if original_box_unit:
                index = self.original_box_unit_combo.findText(original_box_unit)
                if index >= 0:
                    self.original_box_unit_combo.setCurrentIndex(index)
                else:
                    self.original_box_unit_combo.setCurrentText(original_box_unit)
            
            # 损耗率
            scrap_rate = self.detail_data.get('scrap_rate', 0)
            if scrap_rate:
                self.scrap_rate_edit.setValue(float(scrap_rate))
            
            # 单位成本
            unit_cost = self.detail_data.get('unit_cost', 0)
            if unit_cost:
                self.unit_cost_edit.setValue(float(unit_cost))
            
            # 供应商
            supplier_id = self.detail_data.get('supplier_id', '')
            self.supplier_edit.setText(supplier_id)
            
            # 备注
            remark = self.detail_data.get('remark', '')
            self.remark_edit.setPlainText(remark)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"填充表单数据失败: {str(e)}")




