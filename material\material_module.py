"""
物料管理模块
包含物料基础信息管理等功能
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
                            QPushButton, QLineEdit, QLabel, QHeaderView, QComboBox, QMessageBox,
                            QSplitter, QTreeWidget, QTreeWidgetItem, QMenu, QFileDialog, QDialog,
                            QGridLayout, QListWidget, QListWidgetItem, QAbstractItemView, QProgressDialog,
                            QFrame, QStyle, QScrollArea, QApplication, QCheckBox)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt6.QtGui import QIcon, QCursor
from material.material_dialog import MaterialDialog
from material.material_db import MaterialDB
import pandas as pd
import os
from material.cj_goods_tab import CJGoodsTab

class MaterialBasicTab(QWidget):
    """物料基础信息管理标签页"""
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.material_db = MaterialDB()
        self.current_category_id = None  # 当前选中的分类ID
        self.status_combo = None
        self.setup_ui()
        # 初始化状态筛选框
        
    def setup_ui(self):
        """设置UI界面"""
        main_layout = QVBoxLayout(self)
        
        # 创建水平分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 创建左侧分类树
        self.category_tree = QTreeWidget()
        self.category_tree.setHeaderHidden(True)
        self.category_tree.setFixedWidth(180)
        self.category_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: #f8f8f8;
            }
            QTreeWidget::item {
                height: 25px;
            }
            QTreeWidget::item:selected {
                background-color: #e0e0e0;
            }
        """)
        
        # 加载分类树
        self.load_category_tree()
        
        # 连接分类树点击信号
        self.category_tree.itemClicked.connect(self.on_category_selected)
        
        # 创建右侧主界面
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 创建搜索区域
        search_layout = QHBoxLayout()
        
        # 搜索区域外框
        search_group = QFrame()
        search_group.setStyleSheet("QFrame { background-color: #f5f5f5; }")
        search_inner_layout = QHBoxLayout(search_group)
        search_inner_layout.setContentsMargins(10, 5, 10, 5)
        
        # 功能分组标签
        search_label = QLabel("搜索条件:")
        search_label.setStyleSheet("font-weight: bold;")
        search_inner_layout.addWidget(search_label)
        
        # 物料编码搜索
        search_inner_layout.addWidget(QLabel("物料编码:"))
        self.material_id_edit = QLineEdit()
        self.material_id_edit.setPlaceholderText("请输入物料编码")
        self.material_id_edit.setMinimumWidth(150)
        self.material_id_edit.returnPressed.connect(self.search_materials)
        search_inner_layout.addWidget(self.material_id_edit)
        
        # 物料名称搜索
        search_inner_layout.addWidget(QLabel("物料名称:"))
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入物料名称")
        self.name_edit.setMinimumWidth(150)
        self.name_edit.returnPressed.connect(self.search_materials)
        search_inner_layout.addWidget(self.name_edit)
        # 状态筛选
        search_inner_layout.addWidget(QLabel("状态:"))
        self.status_combo = QComboBox()
        self.status_combo.addItems(["全部", "启用", "停用"])
        self.status_combo.setMinimumWidth(80)
        self.status_combo.currentTextChanged.connect(self.search_materials)
        search_inner_layout.addWidget(self.status_combo)

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_BrowserReload))
        self.refresh_btn.clicked.connect(self.refresh_materials)
        search_inner_layout.addWidget(self.refresh_btn)
        
        # 添加按钮
        self.add_btn = QPushButton("添加物料")
        self.add_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogNewFolder))
        self.add_btn.clicked.connect(self.add_material)
        search_inner_layout.addWidget(self.add_btn)
        
        # 搜索按钮
        self.search_btn = QPushButton("搜索")
        self.search_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogContentsView))
        self.search_btn.clicked.connect(self.search_materials)
        search_inner_layout.addWidget(self.search_btn)
        
        # 添加搜索区域到主布局
        search_layout.addWidget(search_group)
        right_layout.addLayout(search_layout)
        
        # 创建功能按钮工具栏
        toolbar_layout = QHBoxLayout()
        
        # Excel操作组
        excel_group = QFrame()
        excel_group.setStyleSheet("QFrame { background-color: #f5f5f5; }")
        excel_layout = QHBoxLayout(excel_group)
        excel_layout.setContentsMargins(10, 5, 10, 5)
        
        # 功能分组标签
        excel_label = QLabel("批量操作:")
        excel_label.setStyleSheet("font-weight: bold;")
        excel_layout.addWidget(excel_label)
        
        # 导入按钮
        self.import_btn = QPushButton("导入Excel")
        self.import_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogOpenButton))
        self.import_btn.clicked.connect(self.import_excel)
        excel_layout.addWidget(self.import_btn)
        
        # 创建导入模板按钮
        self.create_template_btn = QPushButton("创建导入模板")
        self.create_template_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileIcon))
        self.create_template_btn.clicked.connect(self.create_import_template)
        excel_layout.addWidget(self.create_template_btn)
        
        # 批量更新按钮
        self.batch_update_btn = QPushButton("批量更新")
        self.batch_update_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_BrowserReload))
        self.batch_update_btn.clicked.connect(self.batch_update_materials)
        excel_layout.addWidget(self.batch_update_btn)
        
        # 批量维护价格按钮
        self.batch_price_btn = QPushButton("批量维护价格")
        self.batch_price_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogDetailedView))
        self.batch_price_btn.clicked.connect(self.batch_maintain_price)
        excel_layout.addWidget(self.batch_price_btn)
        
        # 导出按钮
        self.export_btn = QPushButton("导出Excel")
        self.export_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogSaveButton))
        self.export_btn.clicked.connect(self.export_excel)
        excel_layout.addWidget(self.export_btn)
        
        # 添加到工具栏
        toolbar_layout.addWidget(excel_group)
        
        # 添加弹性占位，使批量操作组靠左
        toolbar_layout.addStretch(1)
        
        # 添加工具栏到主布局
        right_layout.addLayout(toolbar_layout)
        
        # 确保搜索组和批量操作组宽度一致
        QTimer.singleShot(0, lambda: self.adjust_group_width(search_group, excel_group))
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(7)  # 增加一列
        self.table.setHorizontalHeaderLabels(["物料编码", "物料名称", "规格型号", "基本单位", "成本", "状态", "东方货物"])
        
        # 设置表格列宽自适应
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 物料编码
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 物料名称
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 规格型号
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 基本单位
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 成本
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 状态
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # 东方货物
        
        # # 表格加载完毕后优化列宽分配
        # QTimer.singleShot(100, self.optimize_column_widths)
        
        # 显示行号
        self.table.setVerticalHeaderLabels([str(i+1) for i in range(100)])  # 预设100行的行号
        
        # 设置表格不可编辑
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 设置表格行交替背景色
        self.table.setAlternatingRowColors(True)
        
        # 启用表格排序
        self.table.setSortingEnabled(True)
        
        # 连接双击信号
        self.table.doubleClicked.connect(self.on_table_double_clicked)
        
        # 设置右键菜单
        self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)
        #设置表格属性
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        
        right_layout.addWidget(self.table)
        
        # 分页控件
        page_layout = QHBoxLayout()
        page_layout.addWidget(QLabel("总记录数:"))
        self.total_label = QLabel("0")
        page_layout.addWidget(self.total_label)
        
        page_layout.addStretch()
        
        page_layout.addWidget(QLabel("当前页:"))
        self.current_page_label = QLabel("1")
        page_layout.addWidget(self.current_page_label)
        
        page_layout.addWidget(QLabel("每页记录:"))
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["20", "50", "100"])
        self.page_size_combo.currentTextChanged.connect(self.search_materials)
        page_layout.addWidget(self.page_size_combo)
        
        self.prev_btn = QPushButton("上一页")
        self.prev_btn.clicked.connect(self.prev_page)
        page_layout.addWidget(self.prev_btn)
        
        self.next_btn = QPushButton("下一页")
        self.next_btn.clicked.connect(self.next_page)
        page_layout.addWidget(self.next_btn)
        
        right_layout.addLayout(page_layout)
        
        # 将左侧分类树和右侧主界面添加到分割器
        splitter.addWidget(self.category_tree)
        splitter.addWidget(right_widget)
        
        # 设置分割器的初始尺寸
        splitter.setSizes([180, self.width() - 180])
        
        # 添加分割器到主布局
        main_layout.addWidget(splitter)
        
        # 设置各个区域之间的间距
        right_layout.setSpacing(10)
        search_inner_layout.setSpacing(5)
        excel_layout.setSpacing(8)
        
        # 初始化分页数据
        self.page = 1
        self.total = 0
        
        # 初始化数据
        self.search_materials()
    
    def load_category_tree(self):
        """加载分类树"""
        try:
            # 清空树
            self.category_tree.clear()
            
            # 添加"全部分类"根节点
            all_categories_item = QTreeWidgetItem(self.category_tree)
            all_categories_item.setText(0, "全部分类")
            all_categories_item.setData(0, Qt.ItemDataRole.UserRole, None)  # ID为None表示全部分类
            all_categories_item.setExpanded(True)
            
            # 获取所有分类
            categories = self.material_db.get_categories()
            
            # 检查分类表结构
            has_parent_id = False
            if categories and "parent_id" in categories[0]:
                has_parent_id = True
            
            if has_parent_id:
                # 构建一级分类节点映射 {category_id: tree_item}
                top_level_items = {}
                
                # 先添加一级分类节点
                for category in categories:
                    if category.get('parent_id') is None:  # 一级分类
                        item = QTreeWidgetItem(self.category_tree)
                        item.setText(0, category['category_name'])
                        item.setData(0, Qt.ItemDataRole.UserRole, category['category_id'])
                        item.setExpanded(True)
                        top_level_items[category['category_id']] = item
                
                # 再添加二级分类节点
                for category in categories:
                    if category.get('parent_id') is not None:  # 二级分类
                        parent_item = top_level_items.get(category['parent_id'])
                        if parent_item:
                            item = QTreeWidgetItem(parent_item)
                            item.setText(0, category['category_name'])
                            item.setData(0, Qt.ItemDataRole.UserRole, category['category_id'])
            else:
                # 表结构不包含parent_id，所有分类都作为一级分类显示
                for category in categories:
                    item = QTreeWidgetItem(self.category_tree)
                    item.setText(0, category['category_name'])
                    item.setData(0, Qt.ItemDataRole.UserRole, category['category_id'])
            
            # 默认选中"全部分类"
            self.category_tree.setCurrentItem(all_categories_item)
        except Exception as e:
            QMessageBox.warning(self, "警告", f"加载分类树失败：{str(e)}")
    
    def on_category_selected(self, item, column):
        """分类树选择处理"""
        # 获取分类ID
        self.current_category_id = item.data(0, Qt.ItemDataRole.UserRole)
        print(f"选中分类: {item.text(0)}, ID: {self.current_category_id}")
        # 更新搜索
        self.search_materials()
    
    def load_categories(self):
        """加载物料分类列表（搜索下拉框使用）"""
        try:
            categories = self.material_db.get_categories()
            for category in categories:
                self.category_combo.addItem(category["category_name"], category["category_id"])
        except Exception as e:
            print(f"加载物料分类失败: {str(e)}")
    
    def get_search_params(self):
        """获取搜索参数"""
        material_id = self.material_id_edit.text().strip()
        name = self.name_edit.text().strip()
        
        params = {
            "material_id": material_id if material_id else None,
            "name": name if name else None,
            "category_id": self.current_category_id,
            "page": self.page,
            "page_size": int(self.page_size_combo.currentText())
        }
        #状态筛选
        status_text = self.status_combo.currentText()
        if status_text == "启用":
            params["status"] = 1
        elif status_text == "停用":
            params["status"] = 0
        return params
       # "全部"时不添加status参数
    def search_materials(self):
        """搜索物料"""
        try:
            params = self.get_search_params()
            self.page = 1  # 重置到第一页
            params["page"] = self.page
            result = self.material_db.get_materials(params)
            
            if result:
                self.total, materials = result
                self.update_table(self.total, materials)
            else:
                self.update_table(0, [])
        except Exception as e:
            QMessageBox.warning(self, "警告", f"搜索物料失败：{str(e)}")
    
    def update_table(self, total, materials):
        """更新表格数据"""
        # 暂时禁用排序，避免插入数据时触发排序
        self.table.setSortingEnabled(False)
        
        self.total = total
        self.total_label.setText(str(total))
        self.current_page_label.setText(str(self.page))
        
        # 清空表格
        self.table.setRowCount(0)
        
        # 更新行号
        self.table.setVerticalHeaderLabels([str(i+1) for i in range(total)])
        
        # 填充数据
        for row, material in enumerate(materials):
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(material.get("material_id", "")))
            self.table.setItem(row, 1, QTableWidgetItem(material.get("name", "")))
            self.table.setItem(row, 2, QTableWidgetItem(material.get("spec_model", "")))
            self.table.setItem(row, 3, QTableWidgetItem(material.get("base_unit", "")))
            # self.table.setItem(row, 4, QTableWidgetItem(material.get("category_name", "")))
            
            # 成本
            cost = material.get("cost", 0)
            cost_item = QTableWidgetItem(f"{cost:.2f}" if cost is not None else "0.00")
            # 设置排序值为实际数值
            cost_item.setData(Qt.ItemDataRole.DisplayRole, float(cost) if cost is not None else 0.0)
            cost_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.table.setItem(row, 4, cost_item)
            
            # 状态
            status_text = "启用" if material.get("status") == 1 else "停用"
            status_item = QTableWidgetItem(status_text)
            # 设置排序值为状态数值
            status_item.setData(Qt.ItemDataRole.UserRole + 1, material.get("status", 0))
            status_item.setForeground(Qt.GlobalColor.green if material.get("status") == 1 else Qt.GlobalColor.red)
            self.table.setItem(row, 5, status_item)
            
            # 东方货物标识
            is_east = "是" if material.get("is_east_material") == 1 else "否"
            east_item = QTableWidgetItem(is_east)
            east_item.setForeground(Qt.GlobalColor.blue if material.get("is_east_material") == 1 else Qt.GlobalColor.black)
            self.table.setItem(row, 6, east_item)
            
            # 保存物料ID作为隐藏数据
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item:
                    item.setData(Qt.ItemDataRole.UserRole, material.get("material_id"))
        
        # 重新启用排序
        self.table.setSortingEnabled(True)
        
        # 更新分页按钮状态
        self.prev_btn.setEnabled(self.page > 1)
        page_size = int(self.page_size_combo.currentText())
        self.next_btn.setEnabled(self.page * page_size < self.total)
    
    def prev_page(self):
        """上一页"""
        if self.page > 1:
            self.page -= 1
            params = self.get_search_params()
            result = self.material_db.get_materials(params)
            if result:
                self.total, materials = result
                self.update_table(self.total, materials)
    
    def next_page(self):
        """下一页"""
        page_size = int(self.page_size_combo.currentText())
        if self.page * page_size < self.total:
            self.page += 1
            params = self.get_search_params()
            result = self.material_db.get_materials(params)
            if result:
                self.total, materials = result
                self.update_table(self.total, materials)
    
    def add_material(self):
        """添加物料"""
        dialog = MaterialDialog(self)
        if dialog.exec():
            material_data = dialog.get_material_data()
            try:
                success = self.material_db.add_material(material_data)
                if success:
                    QMessageBox.information(self, "提示", "添加物料成功")
                    self.search_materials()
                else:
                    QMessageBox.warning(self, "警告", "添加物料失败")
            except Exception as e:
                QMessageBox.warning(self, "警告", f"添加物料失败：{str(e)}")
    
    def edit_material(self, material_id):
        """编辑物料"""
        try:
            material = self.material_db.get_material_by_id(material_id)
            if material:
                dialog = MaterialDialog(self, material)
                if dialog.exec():
                    material_data = dialog.get_material_data()
                    success = self.material_db.update_material(material_id, material_data)
                    if success:
                        QMessageBox.information(self, "提示", "更新物料成功")
                        self.search_materials()
                    else:
                        QMessageBox.warning(self, "警告", "更新物料失败")
            else:
                QMessageBox.warning(self, "警告", "物料不存在")
        except Exception as e:
            QMessageBox.warning(self, "警告", f"编辑物料失败：{str(e)}")
    
    def delete_material(self, material_id):
        """删除物料"""
        reply = QMessageBox.question(self, "确认", f"确定要删除物料 {material_id} 吗？", 
                                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
                                    QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            try:
                success = self.material_db.delete_material(material_id)
                if success:
                    QMessageBox.information(self, "提示", "删除物料成功")
                    self.search_materials()
                else:
                    QMessageBox.warning(self, "警告", "删除物料失败")
            except Exception as e:
                QMessageBox.warning(self, "警告", f"删除物料失败：{str(e)}")
    
    def export_excel(self):
        """导出Excel"""
        try:
            # 获取所有物料数据（不分页）
            params = self.get_search_params()
            params["page"] = 1
            params["page_size"] = 10000  # 导出全部数据
            
            result = self.material_db.get_materials(params)
            if result and result[1]:
                total, materials = result
                
                # 创建DataFrame
                df = pd.DataFrame(materials)
                
                # 修改列名
                columns_map = {
                    "material_id": "物料编码",
                    "name": "物料名称",
                    "spec_model": "规格型号",
                    "base_unit": "基本单位",
                    "original_box_unit": "原箱规格",
                    "conversion_rate": "计量换算",
                    "barcode": "条形码",
                    "status": "状态",
                    "length_unit": "长度单位",
                    "length": "长度",
                    "width": "宽度",
                    "height": "高度",
                    "weight_unit": "重量单位",
                    "gross_weight": "毛重",
                    "volume_unit": "体积单位",
                    "volume": "体积",
                    "category_name": "物料分类",
                    "cost": "成本(元)",
                    "remark": "备注",
                    "is_east_material": "东方货物"
                }
                
                # 筛选需要导出的列
                export_columns = ["material_id", "name", "spec_model", "base_unit", 
                                "original_box_unit", "conversion_rate", "barcode", "status", "weight_unit",
                                "gross_weight","volume_unit","volume","category_name", "cost", "remark", "is_east_material"]
                
                df_export = df[export_columns].rename(columns=columns_map)
                
                # 转换状态列
                df_export["状态"] = df_export["状态"].map({1: "启用", 0: "停用"})
                
                # 打开文件对话框让用户选择保存位置
                from datetime import datetime
                default_filename = f"物料基础信息_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
                
                file_dialog = QFileDialog(self)
                file_dialog.setAcceptMode(QFileDialog.AcceptMode.AcceptSave)
                file_dialog.setNameFilter("Excel Files (*.xlsx)")
                file_dialog.setDefaultSuffix("xlsx")
                file_dialog.setWindowTitle("导出Excel")
                file_dialog.selectFile(default_filename)
                
                if file_dialog.exec():
                    filename = file_dialog.selectedFiles()[0]
                    if filename:
                        # 保存Excel
                        df_export.to_excel(filename, index=False)
                        QMessageBox.information(self, "提示", f"导出成功，文件保存在：\n{filename}")
                    else:
                        QMessageBox.warning(self, "警告", "未选择保存位置，导出取消")
                else:
                    QMessageBox.warning(self, "警告", "导出取消")
            else:
                QMessageBox.warning(self, "警告", "没有数据可导出")
        except Exception as e:
            QMessageBox.warning(self, "警告", f"导出Excel失败：{str(e)}")

    def on_table_double_clicked(self, index):
        """表格双击事件处理"""
        # 获取双击行的物料ID
        material_id = index.data(Qt.ItemDataRole.UserRole)
        if material_id:
            self.edit_material(material_id)
    
    def show_context_menu(self, pos):
        """显示右键菜单"""
        # 获取点击位置对应的行
        index = self.table.indexAt(pos)
        if not index.isValid():
            return
        
        # 获取物料ID
        material_id = index.data(Qt.ItemDataRole.UserRole)
        if not material_id:
            return
        
        # 创建右键菜单
        context_menu = QMenu(self)
        
        # 添加编辑和删除菜单项
        edit_action = context_menu.addAction("编辑物料")
        delete_action = context_menu.addAction("删除物料")
        
        # 显示菜单并获取用户选择的操作
        action = context_menu.exec(QCursor.pos())
        
        # 处理用户选择
        if action == edit_action:
            self.edit_material(material_id)
        elif action == delete_action:
            self.delete_material(material_id)

    def refresh_materials(self):
        """刷新物料列表"""
        self.search_materials()
    
    def import_excel(self):
        """导入Excel数据"""
        try:
            # 选择Excel文件
            file_dialog = QFileDialog(self)
            file_dialog.setNameFilter("Excel Files (*.xlsx *.xls)")
            file_dialog.setWindowTitle("选择导入的Excel文件")
            file_dialog.setFileMode(QFileDialog.FileMode.ExistingFile)
            
            if not file_dialog.exec():
                return
            
            file_path = file_dialog.selectedFiles()[0]
            if not file_path:
                return
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                QMessageBox.warning(self, "警告", "所选文件不存在！")
                return
                
            # 检查文件扩展名
            ext = os.path.splitext(file_path)[1].lower()
            if ext not in ['.xlsx', '.xls']:
                QMessageBox.warning(self, "警告", "请选择Excel格式文件(.xlsx或.xls)！")
                return
            
            try:
                # 尝试读取Excel文件
                print(f"正在读取Excel文件: {file_path}")
                df = pd.read_excel(file_path)
                print(f"Excel文件读取成功，行数: {len(df)}, 列数: {len(df.columns)}")
                print(f"列名: {df.columns.tolist()}")
            except Exception as excel_error:
                QMessageBox.critical(self, "错误", f"读取Excel文件失败：{str(excel_error)}")
                print(f"读取Excel错误: {str(excel_error)}")
                import traceback
                print(traceback.format_exc())
                return
            
            if df.empty:
                QMessageBox.warning(self, "警告", "Excel文件为空！")
                return
            
            # 显示字段映射对话框
            mapping_dialog = FieldMappingDialog(self, df.columns.tolist())
            if not mapping_dialog.exec():
                return
            
            # 获取字段映射
            field_mapping = mapping_dialog.get_field_mapping()
            if not field_mapping:
                QMessageBox.warning(self, "警告", "未进行字段映射！")
                return
                
            # 检查必填字段
            if not field_mapping.get("material_id"):
                QMessageBox.warning(self, "警告", "必须映射物料编码字段！")
                return
                
            if not field_mapping.get("name"):
                QMessageBox.warning(self, "警告", "必须映射物料名称字段！")
                return
            
            # 处理数据导入
            try:
                import_count = self.process_import_data(df, field_mapping)
                
                # 刷新表格
                self.search_materials()
            except Exception as import_error:
                QMessageBox.critical(self, "错误", f"处理导入数据失败：{str(import_error)}")
                print(f"导入处理错误: {str(import_error)}")
                import traceback
                print(traceback.format_exc())
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入Excel失败：{str(e)}")
            print(f"导入主函数错误: {str(e)}")
            import traceback
            print(traceback.format_exc())
    
    def process_import_data(self, df, field_mapping):
        """处理导入的数据"""
        # 反向映射，将Excel列名映射到数据库字段
        reverse_mapping = {v: k for k, v in field_mapping.items() if v}
        
        # 输出调试信息
        print(f"字段映射: {field_mapping}")
        print(f"反向映射: {reverse_mapping}")
        
        # 验证所有映射的列名在Excel文件中都存在
        excel_columns = df.columns.tolist()
        invalid_columns = [col for col in reverse_mapping.keys() if col not in excel_columns]
        if invalid_columns:
            QMessageBox.warning(self, "警告", f"Excel文件中不存在以下映射的列: {', '.join(invalid_columns)}")
            return 0
        
        # 创建进度对话框
        progress = QProgressDialog("正在导入数据...", "取消", 0, len(df), self)
        progress.setWindowTitle("导入进度")
        progress.setWindowModality(Qt.WindowModality.WindowModal)
        progress.show()
        
        # 统计导入成功的记录数
        import_count = 0
        # 统计跳过的记录数（已存在的物料）
        skip_count = 0
        # 统计错误记录数
        error_count = 0
        
        # 逐行处理数据
        for index, row in df.iterrows():
            # 更新进度
            progress.setValue(index)
            if progress.wasCanceled():
                break
            
            try:
                # 转换Excel数据到物料数据
                material_data = {}
                
                # 首先检查是否有物料编码字段的映射，这是必需的
                material_id_col = field_mapping.get("material_id")
                if not material_id_col or material_id_col not in excel_columns:
                    print(f"行 {index+1}: 缺少物料编码字段的正确映射，跳过该行")
                    error_count += 1
                    continue
                
                # 提取物料编码
                if pd.isna(row[material_id_col]):
                    print(f"行 {index+1}: 物料编码为空，跳过该行")
                    error_count += 1
                    continue
                
                material_data["material_id"] = row[material_id_col]
                
                # 检查物料名称字段，也是必需的
                name_col = field_mapping.get("name")
                if not name_col or name_col not in excel_columns:
                    print(f"行 {index+1}: 缺少物料名称字段的正确映射，跳过该行")
                    error_count += 1
                    continue
                
                if pd.isna(row[name_col]):
                    print(f"行 {index+1}: 物料名称为空，跳过该行")
                    error_count += 1
                    continue
                
                material_data["name"] = row[name_col]
                
                # 处理其他字段
                for db_field, excel_column in field_mapping.items():
                    # 跳过已处理的物料编码和名称字段
                    if db_field in ["material_id", "name"] or not excel_column or excel_column not in excel_columns:
                        continue
                    
                    # 跳过空值
                    if pd.isna(row[excel_column]):
                        print(f"行 {index+1} 中字段 {db_field} 对应的列 {excel_column} 为空，已跳过")
                        continue
                    
                    value = row[excel_column]
                    
                    # 特殊处理某些字段
                    if db_field == "status":
                        # 状态字段：将"启用"/"停用"转换为1/0
                        if isinstance(value, str):
                            if value == "启用":
                                value = 1
                            elif value == "停用":
                                value = 0
                    elif db_field == "original_box_unit":
                        # 原箱规格字段：确保是整数
                        try:
                            value = int(value)
                        except:
                            print(f"行 {index+1} 中字段 {db_field} 的值 {value} 无法转换为整数，已设为0")
                            value = 0
                    elif db_field == "conversion_rate":
                        # 计量换算字段：确保是整数
                        try:
                            value = int(value)
                        except:
                            print(f"行 {index+1} 中字段 {db_field} 的值 {value} 无法转换为整数，已设为0")
                            value = 0
                    elif db_field in ["length", "width", "height", "cost", "gross_weight", "volume"]:
                        # 数值字段：确保是浮点数
                        try:
                            value = float(value)
                        except:
                            print(f"行 {index+1} 中字段 {db_field} 的值 {value} 无法转换为浮点数，已设为0.0")
                            value = 0.0
                    
                    material_data[db_field] = value
                
                # 输出完整的物料数据
                print(f"行 {index+1} 的物料数据: {material_data}")
                
                # 检查物料数据是否足够完整
                required_fields = ["spec_model", "base_unit", "original_box_unit", "conversion_rate", "barcode", "status", 
                                "length_unit", "length", "width", "height", "weight_unit", 
                                "gross_weight", "volume_unit", "volume", "category_id", "cost", "remark"]
                
                # 填充缺失字段
                for field in required_fields:
                    if field not in material_data:
                        if field in ["length", "width", "height", "cost", "gross_weight", "volume"]:
                            material_data[field] = 0.0
                            print(f"行 {index+1} 缺少字段 {field}，已设为0.0")
                        elif field in ["status"]:
                            material_data[field] = 1
                            print(f"行 {index+1} 缺少字段 {field}，已设为1")
                        elif field in ["original_box_unit"]:
                            material_data[field] = 0
                            print(f"行 {index+1} 缺少字段 {field}，已设为0")
                        elif field in ["conversion_rate"]:
                            material_data[field] = 0
                            print(f"行 {index+1} 缺少字段 {field}，已设为0")
                        elif field in ["length_unit"]:
                            material_data[field] = "mm"
                            print(f"行 {index+1} 缺少字段 {field}，已设为mm")
                        elif field in ["weight_unit"]:
                            material_data[field] = "kg"
                            print(f"行 {index+1} 缺少字段 {field}，已设为kg")
                        elif field in ["volume_unit"]:
                            material_data[field] = "m³"
                            print(f"行 {index+1} 缺少字段 {field}，已设为m³")
                        elif field in ["category_id"]:
                            material_data[field] = 1
                            print(f"行 {index+1} 缺少字段 {field}，已设为1")
                        else:
                            material_data[field] = ""
                            print(f"行 {index+1} 缺少字段 {field}，已设为空字符串")
                
                # 再次输出完整的物料数据(补全后)
                print(f"行 {index+1} 补全后的物料数据: {material_data}")
                
                # 查询是否已存在此物料
                existing_material = self.material_db.get_material_by_id(material_data["material_id"])
                
                if existing_material:
                    # 如果物料已存在，跳过该记录（不更新）
                    print(f"物料 {material_data['material_id']} 已存在，跳过")
                    skip_count += 1
                else:
                    # 添加新物料
                    print(f"添加新物料: {material_data['material_id']}")
                    if self.material_db.add_material(material_data):
                        import_count += 1
                        print(f"物料 {material_data['material_id']} 添加成功")
                    else:
                        print(f"物料 {material_data['material_id']} 添加失败")
                        error_count += 1
                
            except Exception as e:
                print(f"导入第{index+1}行数据失败: {str(e)}")
                import traceback
                print(traceback.format_exc())
                error_count += 1
                continue
        
        # 关闭进度对话框
        progress.setValue(len(df))
        
        # 显示导入结果
        result_message = f"成功导入{import_count}条物料数据"
        if skip_count > 0:
            result_message += f"，跳过{skip_count}条已存在的物料数据"
        if error_count > 0:
            result_message += f"，{error_count}条数据导入失败"
        result_message += "。"
        
        QMessageBox.information(self, "导入结果", result_message)
        
        return import_count
    
    def batch_update_materials(self):
        """批量更新物料数据"""
        try:
            # 选择Excel文件
            file_dialog = QFileDialog(self)
            file_dialog.setNameFilter("Excel Files (*.xlsx *.xls)")
            file_dialog.setWindowTitle("选择批量更新的Excel文件")
            file_dialog.setFileMode(QFileDialog.FileMode.ExistingFile)
            
            if not file_dialog.exec():
                return
            
            file_path = file_dialog.selectedFiles()[0]
            if not file_path:
                return
            
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            if df.empty:
                QMessageBox.warning(self, "警告", "Excel文件为空！")
                return
            
            # 检查是否包含必要的物料编码列
            if "物料编码" not in df.columns and "material_id" not in df.columns:
                QMessageBox.warning(self, "警告", "Excel文件必须包含物料编码列！")
                return
            
            # 提取物料编码列
            id_column = "物料编码" if "物料编码" in df.columns else "material_id"
            
            # 显示字段映射对话框
            mapping_dialog = FieldMappingDialog(self, df.columns.tolist(), is_update=True)
            if not mapping_dialog.exec():
                return
            
            # 获取字段映射
            field_mapping = mapping_dialog.get_field_mapping()
            if not field_mapping or not field_mapping["material_id"]:
                QMessageBox.warning(self, "警告", "必须映射物料编码字段！")
                return
            
            # 创建进度对话框 - 使用百分比进度
            progress = QProgressDialog("准备批量更新...", "取消", 0, 100, self)
            progress.setWindowTitle("批量更新进度")
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.setMinimumDuration(0)  # 立即显示
            progress.setAutoClose(False)    # 完成后不自动关闭
            progress.setAutoReset(False)    # 完成后不自动重置
            # 确保对话框显示在前台
            progress.setWindowFlags(progress.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
            
            # 创建并启动后台线程前，先显示进度对话框
            progress.show()
            QApplication.processEvents()  # 强制处理事件，确保进度对话框显示
            
            # 创建并启动后台线程
            self.update_thread = BatchUpdateThread(df, field_mapping, self.material_db)
            
            # 连接信号
            self.update_thread.progress_signal.connect(
                lambda value, text: self.update_progress(progress, value, text))
            self.update_thread.finished_signal.connect(
                lambda count, message: self.handle_update_finished(progress, count, message))
            
            # 连接取消按钮
            progress.canceled.connect(self.update_thread.cancel)
            
            # 启动线程
            self.update_thread.start()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"批量更新失败：{str(e)}")
            import traceback
            print(traceback.format_exc())
    
    def update_progress(self, progress_dialog, value, text):
        """更新进度对话框"""
        if progress_dialog and progress_dialog.isVisible():
            progress_dialog.setValue(value)
            progress_dialog.setLabelText(text)
            # 确保UI更新
            QApplication.processEvents()
    
    def handle_update_finished(self, progress_dialog, count, message):
        """处理更新完成"""
        # 关闭进度对话框
        if progress_dialog and progress_dialog.isVisible():
            progress_dialog.close()
        
        # 显示结果消息
        QMessageBox.information(self, "批量更新结果", message)
        
        # 刷新表格
        self.search_materials()

    def create_import_template(self):
        """创建导入模板"""
        try:
            # 创建保存对话框
            file_dialog = QFileDialog(self)
            file_dialog.setAcceptMode(QFileDialog.AcceptMode.AcceptSave)
            file_dialog.setNameFilter("Excel Files (*.xlsx)")
            file_dialog.setDefaultSuffix("xlsx")
            file_dialog.setWindowTitle("保存导入模板")
            file_dialog.selectFile("物料导入模板.xlsx")
            
            if not file_dialog.exec():
                return
            
            file_path = file_dialog.selectedFiles()[0]
            if not file_path:
                return
            
            # 创建模板数据
            template_data = {
                "物料编码": ["M00001", "M00002", "M00003"],
                "物料名称": ["示例产品A", "示例产品B", "示例产品C"],
                "规格型号": ["规格1", "规格2", "规格3"],
                "基本单位": ["个", "箱", "件"],
                "原箱规格": [5, 10, 20],
                "计量换算": [12, 24, 36],
                "条形码": ["69012345", "69012346", "69012347"],
                "成本": [10.00, 20.00, 30.00],
                "状态": ["启用", "启用", "停用"],
                "长度单位": ["mm", "cm", "m"],
                "长度": [100, 200, 300],
                "宽度": [50, 60, 70],
                "高度": [30, 40, 50],
                "重量单位": ["kg", "g", "t"],
                "毛重": [1.5, 2.5, 3.5],
                "体积单位": ["m³", "cm³", "dm³"],
                "体积": [0.01, 0.02, 0.03],
                "分类ID": [1, 2, 3],
                "备注": ["示例备注1", "示例备注2", "示例备注3"],
                "东方货物": [0, 1, 0] # 新增东方货物字段
            }
            
            # 创建DataFrame
            df = pd.DataFrame(template_data)
            
            # 添加说明
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='导入数据', index=False)
                
                # 创建说明表
                instructions = pd.DataFrame({
                    "字段名": ["物料编码", "物料名称", "规格型号", "基本单位", "原箱规格", "计量换算", "条形码", "成本", "状态", 
                            "长度单位", "长度", "宽度", "高度", "重量单位", "毛重", 
                            "体积单位", "体积", "分类ID", "备注", "东方货物"],
                    "说明": [
                        "必填，唯一标识物料的编码", 
                        "必填，物料名称",
                        "选填，物料规格型号",
                        "选填，计量单位，如个、件、箱等",
                        "选填，原箱规格，整数类型",
                        "选填，计量换算，整数类型",
                        "选填，条形码",
                        "选填，物料成本，数值类型",
                        "选填，物料状态，可填'启用'或'停用'",
                        "选填，长度单位，如mm、cm、m等",
                        "选填，长度值，数值类型",
                        "选填，宽度值，数值类型",
                        "选填，高度值，数值类型",
                        "选填，重量单位，如g、kg、t等",
                        "选填，毛重值，数值类型",
                        "选填，体积单位，如cm³、m³等",
                        "选填，体积值，数值类型",
                        "选填，物料分类ID，数值类型",
                        "选填，备注信息",
                        "选填，是否东方货物，0-否，1-是"
                    ],
                    "示例": [
                        "M00001", 
                        "示例产品A",
                        "规格1",
                        "个",
                        "5",
                        "12",
                        "69012345",
                        "10.00",
                        "启用",
                        "mm",
                        "100",
                        "50",
                        "30",
                        "kg",
                        "1.5",
                        "m³",
                        "0.01",
                        "1",
                        "示例备注",
                        "0"
                    ],
                    "是否必填": [
                        "是", 
                        "是",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否",
                        "否"
                    ]
                })
                instructions.to_excel(writer, sheet_name='使用说明', index=False)
            
            QMessageBox.information(self, "提示", f"导入模板已创建并保存到：\n{file_path}")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建导入模板失败：{str(e)}")

    def adjust_group_width(self, search_group, excel_group):
        """调整组宽度"""
        search_width = search_group.sizeHint().width()
        excel_width = excel_group.sizeHint().width()
        self.table.setColumnWidth(0, search_width)
        self.table.setColumnWidth(1, search_width)
        self.table.setColumnWidth(2, search_width)
        self.table.setColumnWidth(3, search_width)
        self.table.setColumnWidth(4, search_width)
        self.table.setColumnWidth(5, search_width)
        self.table.setColumnWidth(6, search_width)

    def optimize_column_widths(self):
        """优化表格列宽分配"""
        # 确保列宽有合理的最小值
        header = self.table.horizontalHeader()
        min_widths = {
            0: 100,  # 物料编码最小宽度
            1: 150,  # 物料名称最小宽度
            2: 120,  # 规格型号最小宽度
            3: 80,   # 基本单位最小宽度
            4: 80,   # 成本最小宽度
            5: 60,   # 状态最小宽度
            6: 80    # 东方货物最小宽度
        }
        
        # 计算已使用的总宽度
        total_used_width = 0
        for col in range(self.table.columnCount()):
            current_width = header.sectionSize(col)
            min_width = min_widths.get(col, 80)
            
            # 如果当前宽度小于最小宽度，则设置为最小宽度
            if current_width < min_width:
                header.resizeSection(col, min_width)
                total_used_width += min_width
            else:
                total_used_width += current_width
        
        # 检查总宽度是否超过表格可用宽度
        available_width = self.table.width() - self.table.verticalHeader().width() - 20  # 减去垂直表头宽度和滚动条预留
        
        if total_used_width > available_width and available_width > 0:
            # 如果总宽度超过表格可用宽度，按比例缩小各列
            factor = available_width / total_used_width
            
            for col in range(self.table.columnCount()):
                current_width = header.sectionSize(col)
                # 保证最小宽度
                new_width = max(min_widths.get(col, 60), int(current_width * factor))
                header.resizeSection(col, new_width)

    def batch_maintain_price(self):
        """批量维护价格"""
        try:
            # 选择Excel文件
            file_dialog = QFileDialog(self)
            file_dialog.setNameFilter("Excel Files (*.xlsx *.xls)")
            file_dialog.setWindowTitle("选择价格维护Excel文件")
            file_dialog.setFileMode(QFileDialog.FileMode.ExistingFile)
            
            if not file_dialog.exec():
                return
            
            file_path = file_dialog.selectedFiles()[0]
            if not file_path:
                return
            
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            if df.empty:
                QMessageBox.warning(self, "警告", "Excel文件为空！")
                return
            
            # 检查必要列
            required_columns = ["物料编码", "成本"]
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                QMessageBox.warning(self, "警告", f"Excel文件缺少必要列：{', '.join(missing_columns)}")
                return
            
            # 创建进度对话框
            progress = QProgressDialog("正在处理价格维护...", "取消", 0, 100, self)
            progress.setWindowTitle("批量维护价格")
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.setMinimumDuration(0)
            progress.show()
            
            # 启动价格维护线程
            self.price_thread = PriceMaintenanceThread(df, self.material_db)
            self.price_thread.progress_signal.connect(
                lambda value, text: self.update_progress(progress, value, text))
            self.price_thread.finished_signal.connect(
                lambda result: self.handle_price_finished(progress, result))
            
            progress.canceled.connect(self.price_thread.cancel)
            self.price_thread.start()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"批量维护价格失败：{str(e)}")

    def handle_price_finished(self, progress_dialog, result):
        """处理价格维护完成"""
        progress_dialog.close()
        
        success_count = result['success_count']
        not_found_materials = result['not_found_materials']
        
        # 显示结果
        message = f"价格维护完成！\n成功更新：{success_count} 条记录"
        
        if not_found_materials:
            message += f"\n未找到物料：{len(not_found_materials)} 条"
            
            # 询问是否导出未找到的物料
            reply = QMessageBox.question(
                self, "导出未找到物料", 
                f"{message}\n\n是否导出未找到的物料清单？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.export_not_found_materials(not_found_materials)
        else:
            QMessageBox.information(self, "完成", message)
        
        # 刷新表格
        self.search_materials()

    def export_not_found_materials(self, not_found_materials):
        """导出未找到的物料清单"""
        try:
            from datetime import datetime
            
            # 创建DataFrame
            df = pd.DataFrame(not_found_materials)
            
            # 选择保存位置
            default_filename = f"未找到物料清单_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存未找到物料清单", default_filename, "Excel Files (*.xlsx)"
            )
            
            if file_path:
                df.to_excel(file_path, index=False)
                QMessageBox.information(self, "成功", f"未找到物料清单已导出到：\n{file_path}")
                
        except Exception as e:
            QMessageBox.warning(self, "警告", f"导出失败：{str(e)}")

class FieldMappingDialog(QDialog):
    """字段映射对话框"""
    
    def __init__(self, parent=None, excel_columns=None, is_update=False):
        super().__init__(parent)
        self.excel_columns = excel_columns or []
        self.is_update = is_update
        self.setWindowTitle("字段映射")
        self.resize(800, 600)
        self.setup_ui()
        self.auto_map_fields()
    
    def setup_ui(self):
        """设置UI界面"""
        main_layout = QVBoxLayout(self)
        
        # 说明标签
        instruction = QLabel("请将Excel表格中的列映射到系统字段。拖拽左侧列到右侧对应字段处进行映射。")
        instruction.setWordWrap(True)
        main_layout.addWidget(instruction)
        
        # 整体布局容器，包含标题和内容
        mapping_layout = QHBoxLayout()
        
        # 左侧布局（Excel列表）
        left_layout = QVBoxLayout()
        left_layout.setContentsMargins(0, 0, 10, 0)
        
        # 左侧标题
        excel_label = QLabel("Excel表格列:")
        excel_label.setStyleSheet("font-weight: bold;")
        left_layout.addWidget(excel_label)
        
        # 左侧内容框
        excel_group = QFrame()
        excel_group.setFrameShape(QFrame.Shape.StyledPanel)
        excel_layout = QVBoxLayout(excel_group)
        excel_layout.setContentsMargins(10, 10, 10, 10)
        
        self.excel_list = QListWidget()
        self.excel_list.addItems(self.excel_columns)
        self.excel_list.setDragEnabled(True)
        self.excel_list.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        excel_layout.addWidget(self.excel_list)
        
        left_layout.addWidget(excel_group)
        
        # 右侧布局（系统字段）
        right_layout = QVBoxLayout()
        right_layout.setContentsMargins(10, 0, 0, 0)
        
        # 右侧标题
        fields_label = QLabel("系统字段:")
        fields_label.setStyleSheet("font-weight: bold;")
        right_layout.addWidget(fields_label)
        
        # 右侧内容框
        fields_group = QFrame()
        fields_group.setFrameShape(QFrame.Shape.StyledPanel)
        fields_layout = QVBoxLayout(fields_group)
        fields_layout.setContentsMargins(10, 10, 10, 10)
        fields_layout.setSpacing(8)
        
        # 设置必填字段提示
        if self.is_update:
            required_fields = ["material_id"]
            update_note = QLabel("注意: 批量更新时只需要映射物料编码和需要更新的字段")
            update_note.setStyleSheet("color: blue;")
            fields_layout.addWidget(update_note)
        else:
            required_fields = ["material_id", "name"]
        
        # 创建字段映射控件
        self.field_combos = {}
        
        # 定义系统字段列表
        db_fields = {
            "material_id": "物料编码*",
            "name": "物料名称*",
            "spec_model": "规格型号",
            "base_unit": "基本单位",
            "original_box_unit": "原箱规格",
            "conversion_rate": "计量换算",
            "barcode": "条形码",
            "cost": "成本",
            "status": "状态(1启用/0停用)",
            "length_unit": "长度单位",
            "length": "长度",
            "width": "宽度",
            "height": "高度",
            "weight_unit": "重量单位",
            "gross_weight": "毛重",
            "volume_unit": "体积单位",
            "volume": "体积",
            "category_id": "分类ID",
            "remark": "备注",
            "is_east_material": "东方货物" # 新增东方货物字段
        }
        
        # 创建滚动区域
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(8)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        
        # 生成字段映射UI - 竖向排列
        for field_key, field_label in db_fields.items():
            # 创建每个字段的行布局
            field_layout = QHBoxLayout()
            
            # 添加星号标记必填字段
            if field_key in required_fields:
                label_text = f"<b>{field_label}</b>"
            else:
                label_text = field_label
            
            label = QLabel(label_text)
            label.setFixedWidth(150)  # 固定标签宽度
            
            combo = QComboBox()
            combo.setAcceptDrops(True)
            combo.addItem("--选择对应的Excel列--")
            combo.addItems(self.excel_columns)
            combo.setFixedWidth(200)  # 设置下拉框固定宽度
            
            self.field_combos[field_key] = combo
            
            field_layout.addWidget(label)
            field_layout.addWidget(combo)  # 移除拉伸因子
            field_layout.addStretch(1)  # 添加弹性空间，使下拉框靠左
            
            scroll_layout.addLayout(field_layout)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(scroll_widget)
        
        fields_layout.addWidget(scroll_area)
        right_layout.addWidget(fields_group)
        
        # 添加左右布局到整体布局
        mapping_layout.addLayout(left_layout)
        mapping_layout.addLayout(right_layout, 1)  # 右侧比例更大
        
        main_layout.addLayout(mapping_layout, 1)  # 内容区域占据主要空间
        
        # 按钮
        button_layout = QHBoxLayout()
        self.reset_btn = QPushButton("重置")
        self.reset_btn.clicked.connect(self.reset_mapping)
        self.auto_map_btn = QPushButton("自动映射")
        self.auto_map_btn.clicked.connect(self.auto_map_fields)
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.reset_btn)
        button_layout.addWidget(self.auto_map_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(button_layout)
    
    def reset_mapping(self):
        """重置映射"""
        for combo in self.field_combos.values():
            combo.setCurrentIndex(0)
    
    def auto_map_fields(self):
        """自动映射字段"""
        mapping_rules = {
            "物料编码": "material_id",
            "物料名称": "name",
            "名称": "name",
            "规格型号": "spec_model",
            "规格": "spec_model",
            "型号": "spec_model",
            "基本单位": "base_unit",
            "单位": "base_unit",
            "原箱规格": "original_box_unit",
            "原箱单位": "original_box_unit",
            "条形码": "barcode",
            "条码": "barcode",
            "成本": "cost",
            "物料成本": "cost",
            "状态": "status",
            "物料状态": "status",
            "长度单位": "length_unit",
            "长度": "length",
            "宽度": "width",
            "高度": "height",
            "重量单位": "weight_unit",
            "重量": "gross_weight", 
            "毛重": "gross_weight",
            "体积单位": "volume_unit",
            "体积": "volume",
            "分类ID": "category_id",
            "分类": "category_id",
            "备注": "remark",
            "描述": "remark",
            "计量换算": "conversion_rate",
            "换算比例": "conversion_rate",
            "东方货物": "is_east_material" # 新增东方货物字段
        }
        
        # 重置所有映射
        self.reset_mapping()
        
        # 自动匹配
        for col_idx, excel_col in enumerate(self.excel_columns):
            # 尝试精确匹配
            mapped = False
            for excel_name, db_field in mapping_rules.items():
                if excel_col == excel_name:
                    combo = self.field_combos.get(db_field)
                    if combo:
                        combo.setCurrentText(excel_col)
                        mapped = True
                        break
            
            # 如果没有精确匹配，尝试部分匹配
            if not mapped:
                for excel_name, db_field in mapping_rules.items():
                    if excel_name in excel_col or excel_col in excel_name:
                        combo = self.field_combos.get(db_field)
                        if combo and combo.currentIndex() == 0:  # 只有尚未映射的才自动映射
                            combo.setCurrentText(excel_col)
                            break
    
    def get_field_mapping(self):
        """获取字段映射结果"""
        mapping = {}
        for field, combo in self.field_combos.items():
            if combo.currentIndex() > 0:
                mapping[field] = combo.currentText()
            else:
                mapping[field] = None
        return mapping
        
    def accept(self):
        """确认按钮处理"""
        # 检查必填字段是否已映射
        if self.is_update:
            required_fields = ["material_id"]
        else:
            required_fields = ["material_id", "name"]
        
        for field in required_fields:
            combo = self.field_combos.get(field)
            if combo and combo.currentIndex() == 0:
                QMessageBox.warning(self, "警告", f"必须映射{field}字段！")
                return
        
        super().accept()
        
class BatchUpdateThread(QThread):
    """优化版批量更新数据的后台线程类 - 使用pandas向量化操作和批量SQL更新"""
    progress_signal = pyqtSignal(int, str)  # 进度信号，参数：进度值、状态信息
    finished_signal = pyqtSignal(int, str)  # 完成信号，参数：更新成功的数量、结果信息
    
    def __init__(self, df, field_mapping, material_db):
        super().__init__()
        self.df = df
        self.field_mapping = field_mapping
        self.material_db = material_db
        self.canceled = False
    
    def run(self):
        try:
            # 输出调试信息
            print(f"批量更新 - 字段映射: {self.field_mapping}")
            print(f"批量更新 - 数据行数: {len(self.df)}")
            
            # 发送进度信号
            self.progress_signal.emit(10, f"正在预处理数据...")
            
            # 1. 预处理：过滤有效数据
            # 首先获取物料编码列
            material_id_column = self.field_mapping.get("material_id")
            if not material_id_column:
                self.finished_signal.emit(0, "错误：未指定物料编码列")
                return
                
            # 创建一个新的DataFrame，仅包含有物料编码的行
            valid_df = self.df.dropna(subset=[material_id_column])
            
            # 如果没有有效数据，直接返回
            if valid_df.empty:
                self.finished_signal.emit(0, "没有有效的物料数据")
                return
                
            # 检查是否取消
            if self.canceled:
                self.finished_signal.emit(0, "批量更新已取消")
                return
                
            # 发送进度信号
            self.progress_signal.emit(20, f"正在转换数据格式...")
            
            # 2. 创建批量更新数据列表
            update_data = []
            
            # 处理成本字段
            cost_column = self.field_mapping.get("cost")
            original_box_unit_column = self.field_mapping.get("original_box_unit")
            conversion_rate_column = self.field_mapping.get("conversion_rate")
            
            # 检查是否取消
            if self.canceled:
                self.finished_signal.emit(0, "批量更新已取消")
                return
                
            # 3. 向量化处理数据 - 使用pandas而非循环
            # 为每一列创建一个处理后的Series
            processed_data = {'material_id': valid_df[material_id_column].astype(str)}
            
            # 处理成本列
            if cost_column and cost_column in valid_df.columns:
                try:
                    # 将成本列转换为浮点数
                    cost_series = pd.to_numeric(valid_df[cost_column], errors='coerce')
                    # 限制范围
                    cost_series = cost_series.clip(lower=0, upper=999999.99)
                    # 保留两位小数
                    cost_series = cost_series.round(2)
                    # 将NaN替换为None
                    processed_data['cost'] = cost_series.where(pd.notnull(cost_series), None)
                except Exception as e:
                    print(f"处理成本列出错: {str(e)}")
            
            # 处理原箱规格列
            if original_box_unit_column and original_box_unit_column in valid_df.columns:
                try:
                    # 将原箱规格列转换为整数
                    box_series = pd.to_numeric(valid_df[original_box_unit_column], errors='coerce')
                    # 限制范围
                    box_series = box_series.clip(lower=0, upper=9999999)
                    # 转为整数
                    box_series = box_series.fillna(0).astype(int)
                    # 将0替换为None（如果原始值为NaN）
                    processed_data['original_box_unit'] = box_series.where(pd.notnull(valid_df[original_box_unit_column]), None)
                except Exception as e:
                    print(f"处理原箱规格列出错: {str(e)}")
            
            # 处理计量换算列
            if conversion_rate_column and conversion_rate_column in valid_df.columns:
                try:
                    # 将计量换算列转换为整数
                    rate_series = pd.to_numeric(valid_df[conversion_rate_column], errors='coerce')
                    # 限制范围
                    rate_series = rate_series.clip(lower=0, upper=9999999)
                    # 转为整数
                    rate_series = rate_series.fillna(0).astype(int)
                    # 将0替换为None（如果原始值为NaN）
                    processed_data['conversion_rate'] = rate_series.where(pd.notnull(valid_df[conversion_rate_column]), None)
                except Exception as e:
                    print(f"处理计量换算列出错: {str(e)}")
            
            # 发送进度信号
            self.progress_signal.emit(60, f"正在准备更新数据...")
            
            # 将处理后的数据转换为字典列表
            df_processed = pd.DataFrame(processed_data)
            
            # 检查是否至少有一个需要更新的字段
            update_fields = [f for f in ['cost', 'original_box_unit', 'conversion_rate'] 
                            if f in processed_data]
            
            if not update_fields:
                self.finished_signal.emit(0, "没有有效的更新字段")
                return
            
            # 转换为字典列表，每个字典代表一行数据
            update_data = df_processed.to_dict(orient='records')
            
            # 过滤掉只有material_id的记录
            update_data = [item for item in update_data if len(item) > 1]
            
            # 检查是否取消
            if self.canceled:
                self.finished_signal.emit(0, "批量更新已取消")
                return
            
            # 发送进度信号
            self.progress_signal.emit(80, f"正在更新数据库，共 {len(update_data)} 条记录...")
            
            # 4. 批量更新数据库
            update_count = 0
            if update_data:
                try:
                    # 调用批量更新方法
                    update_count = self.material_db.bulk_update_materials(update_data)
                except Exception as e:
                    print(f"批量更新数据库失败: {str(e)}")
                    import traceback
                    print(traceback.format_exc())
            
            # 构建结果消息
            total_rows = len(self.df)
            valid_rows = len(valid_df)
            
            result_message = f"批量更新完成:\n"
            result_message += f"- 总处理行数: {total_rows}\n"
            result_message += f"- 有效数据行: {valid_rows}\n"
            result_message += f"- 实际更新成功: {update_count}\n"
            
            if self.canceled:
                result_message = "批量更新已取消"
            
            # 发送完成信号
            self.progress_signal.emit(100, "更新完成")
            self.finished_signal.emit(update_count, result_message)
            
        except Exception as e:
            print(f"批量更新线程异常: {str(e)}")
            import traceback
            print(traceback.format_exc())
            self.finished_signal.emit(0, f"更新失败: {str(e)}")
    
    def cancel(self):
        """取消线程处理"""
        self.canceled = True

class PriceMaintenanceThread(QThread):
    """价格维护线程"""
    progress_signal = pyqtSignal(int, str)
    finished_signal = pyqtSignal(dict)
    
    def __init__(self, df, material_db):
        super().__init__()
        self.df = df
        self.material_db = material_db
        self.canceled = False
    
    def run(self):
        try:
            total_rows = len(self.df)
            success_count = 0
            not_found_materials = []
            
            self.progress_signal.emit(10, "开始处理价格数据...")
            
            # 处理每一行数据
            for index, row in self.df.iterrows():
                if self.canceled:
                    break
                
                material_id = str(row['物料编码']).strip()
                cost = row['成本']
                
                # 跳过空值
                if pd.isna(material_id) or pd.isna(cost):
                    continue
                
                try:
                    # 验证成本值
                    cost_value = float(cost)
                    if cost_value < 0:
                        cost_value = 0
                    elif cost_value > 999999.99:
                        cost_value = 999999.99
                    cost_value = round(cost_value, 2)
                    
                    # 检查物料是否存在
                    existing_material = self.material_db.get_material_by_id(material_id)
                    
                    if existing_material:
                        # 更新成本
                        update_data = [{'material_id': material_id, 'cost': cost_value}]
                        if self.material_db.batch_update_cost(update_data) > 0:
                            success_count += 1
                    else:
                        # 记录未找到的物料
                        not_found_materials.append({
                            '物料编码': material_id,
                            '成本': cost_value
                        })
                
                except (ValueError, TypeError) as e:
                    print(f"处理物料 {material_id} 价格失败: {str(e)}")
                    continue
                
                # 更新进度
                progress = int((index + 1) / total_rows * 90) + 10
                self.progress_signal.emit(progress, f"处理中... {index + 1}/{total_rows}")
            
            # 完成
            result = {
                'success_count': success_count,
                'not_found_materials': not_found_materials
            }
            
            self.progress_signal.emit(100, "处理完成")
            self.finished_signal.emit(result)
            
        except Exception as e:
            print(f"价格维护线程异常: {str(e)}")
            self.finished_signal.emit({
                'success_count': 0,
                'not_found_materials': [],
                'error': str(e)
            })
    
    def cancel(self):
        """取消处理"""
        self.canceled = True

class MaterialModule:
    """物料管理模块"""
    def __init__(self, db):
        self.db = db
        self.material_basic_tab = MaterialBasicTab(db)
        self.cj_goods_tab = CJGoodsTab(db)  # 添加东方货品标签页


