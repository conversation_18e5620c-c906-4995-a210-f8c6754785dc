"""
需求计划对话框
用于新增、编辑、查看需求计划
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                           QTextEdit, QComboBox, QDateEdit, QPushButton, QTableWidget,
                           QTableWidgetItem, QHeaderView, QMessageBox, QFrame, 
                           QFormLayout, QDoubleSpinBox, QSpinBox, QCheckBox,
                           QGroupBox, QSplitter, QTabWidget, QWidget, QGridLayout)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QFont
from datetime import datetime, date
from demand.demand_plan_db import DemandPlanDB
from core.logger import Logger
from core.config import get_current_user

class DemandPlanDialog(QDialog):
    """需求计划对话框"""
    
    # 定义信号
    data_saved = pyqtSignal()
    
    def __init__(self, parent=None, plan_id=None, mode='add'):
        super().__init__(parent)
        self.plan_id = plan_id
        self.mode = mode  # 'add', 'edit', 'view'
        self.demand_plan_db = DemandPlanDB()
        self.plan_data = {}
        self.details_data = []
        self.original_data = None
        self.current_user = get_current_user()
        
        self.setWindowTitle({
            'add': '新增需求计划',
            'edit': '编辑需求计划',
            'view': '查看需求计划'
        }.get(mode, '需求计划管理'))
        
        self.setModal(True)
        self.resize(1024, 732)
        self.setup_ui()
        
        # 自动生成计划编号（仅在新增模式下）
        if mode == 'add':
            self.generate_plan_no()
            self.applicant_edit.setText(self.generate_current_user())
            self.generate_plan_name()
        if plan_id:
            self.load_data()
            
    def setup_ui(self):
        """设置界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建基本信息区域
        self.create_basic_info_section(main_layout)
        
        # 创建明细区域
        self.create_detail_section(main_layout)
        
        # 创建按钮区域
        self.create_button_section(main_layout)
        
    def create_basic_info_section(self, parent_layout):
        """创建基本信息区域"""
        # 基本信息卡片
        basic_card = QFrame()
        basic_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        basic_layout = QVBoxLayout(basic_card)
        basic_layout.setContentsMargins(20, 15, 20, 20)
        basic_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📋 需求计划基本信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        basic_layout.addWidget(title_label)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)
        
        # 第一行：计划编号、计划名称
        form_layout.addWidget(QLabel("计划编号:"), 0, 0)
        
        self.plan_no_edit = QLineEdit()
        self.plan_no_edit.setPlaceholderText("系统自动生成")
        self.plan_no_edit.setReadOnly(True)
        form_layout.addWidget(self.plan_no_edit, 0, 1)
        
        form_layout.addWidget(QLabel("计划名称:"), 0, 2)
        self.plan_name_edit = QLineEdit()
        self.plan_name_edit.setPlaceholderText("请输入计划名称")
        self.plan_name_edit.setReadOnly(True)
        form_layout.addWidget(self.plan_name_edit, 0, 3)
        
        # 第二行：申请部门、申请人
        form_layout.addWidget(QLabel("申请部门:"), 1, 0)
        self.department_edit = QComboBox()
        self.department_edit.addItem("通路一")
        self.department_edit.addItem("通路二")
        self.department_edit.addItem("电商-京东")
        self.department_edit.addItem("电商-猫超")
        self.department_edit.addItem("市场部-东方电视")
        self.department_edit.addItem("市场达博")
        self.department_edit.currentTextChanged.connect(self.update_plan_name)
        form_layout.addWidget(self.department_edit, 1, 1)
        
        form_layout.addWidget(QLabel("申请人:"), 1, 2)
        self.applicant_edit = QLineEdit()
        self.applicant_edit.setPlaceholderText("请输入申请人")
        self.applicant_edit.setReadOnly(True)
        form_layout.addWidget(self.applicant_edit, 1, 3)
        
        # 第三行：需求类型、优先级
        form_layout.addWidget(QLabel("需求类型:"), 2, 0)
        self.demand_type_combo = QComboBox()
        self.demand_type_combo.addItem("生产需求", 1)
        self.demand_type_combo.addItem("销售需求", 2)
        self.demand_type_combo.addItem("库存补充", 3)
        self.demand_type_combo.addItem("其他需求", 4)
        form_layout.addWidget(self.demand_type_combo, 2, 1)
        
        form_layout.addWidget(QLabel("优先级:"), 2, 2)
        self.priority_combo = QComboBox()
        self.priority_combo.addItem("普通", 1)
        self.priority_combo.addItem("紧急", 2)
        self.priority_combo.addItem("特急", 3)
        form_layout.addWidget(self.priority_combo, 2, 3)
        
        # 第四行：计划日期、需求日期
        form_layout.addWidget(QLabel("计划日期:"), 3, 0)
        self.plan_date_edit = QDateEdit()
        self.plan_date_edit.setCalendarPopup(True)
        self.plan_date_edit.setDate(QDate.currentDate())
        self.plan_date_edit.setDisplayFormat("yyyy-MM-dd")
        form_layout.addWidget(self.plan_date_edit, 3, 1)
        
        
        
        # 第五行：预估金额、生产计划号
        form_layout.addWidget(QLabel("预估金额:"), 4, 0)
        self.total_amount_edit = QDoubleSpinBox()
        self.total_amount_edit.setRange(0, 999999999.99)
        self.total_amount_edit.setDecimals(2)
        self.total_amount_edit.setSuffix(" 元")
        form_layout.addWidget(self.total_amount_edit, 4, 1)
        
        
        # 第六行：备注
        form_layout.addWidget(QLabel("备注:"), 5, 0)
        self.remark_edit = QLineEdit()
        self.remark_edit.setPlaceholderText("请输入备注信息...")
        form_layout.addWidget(self.remark_edit, 5, 1, 1, 3)  # 跨3列
        
        # 设置列宽比例
        form_layout.setColumnStretch(1, 2)
        form_layout.setColumnStretch(3, 2)
        
        basic_layout.addWidget(form_widget)
        parent_layout.addWidget(basic_card)
        
    def create_detail_section(self, parent_layout):
        """创建明细区域"""
        # 明细区域标题
        detail_title = QLabel("📦 需求明细清单")
        detail_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                padding: 10px 0px 5px 0px;
            }
        """)
        parent_layout.addWidget(detail_title)
        
        # 明细卡片
        detail_card = QFrame()
        detail_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        detail_layout = QVBoxLayout(detail_card)
        detail_layout.setContentsMargins(20, 15, 20, 20)
        detail_layout.setSpacing(15)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        # 添加明细按钮
        self.add_detail_btn = QPushButton("添加明细")
        self.add_detail_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
            QPushButton:pressed {
                background-color: #096dd9;
            }
        """)
        self.add_detail_btn.clicked.connect(self.add_detail)
        toolbar_layout.addWidget(self.add_detail_btn)
        
        # 编辑明细按钮
        self.edit_detail_btn = QPushButton("编辑明细")
        self.edit_detail_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
            QPushButton:pressed {
                background-color: #389e0d;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #bfbfbf;
            }
        """)
        self.edit_detail_btn.clicked.connect(self.edit_detail)
        self.edit_detail_btn.setEnabled(False)
        toolbar_layout.addWidget(self.edit_detail_btn)
        
        # 删除明细按钮
        self.delete_detail_btn = QPushButton("删除明细")
        self.delete_detail_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4d4f;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ff7875;
            }
            QPushButton:pressed {
                background-color: #d9363e;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #bfbfbf;
            }
        """)
        self.delete_detail_btn.clicked.connect(self.delete_detail)
        self.delete_detail_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_detail_btn)
        
        toolbar_layout.addStretch()
        
        # 统计信息
        self.detail_stats_label = QLabel("需求明细: 0 项")
        self.detail_stats_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 14px;
                padding: 8px 12px;
                background-color: #f5f5f5;
                border-radius: 4px;
            }
        """)
        toolbar_layout.addWidget(self.detail_stats_label)
        
        detail_layout.addLayout(toolbar_layout)
        
        # 明细表格
        self.details_table = QTableWidget()
        self.details_table.setColumnCount(14)
        detail_headers = ["序号", "物料编码", "物料名称", "物料分类", "需求数量", "单位", 
                         "单价", "金额", "需求日期", "用途", "备注","执行状态", "生产计划号", "采购申请号"]
        self.details_table.setHorizontalHeaderLabels(detail_headers)
        
        # 设置表格属性
        self.details_table.setAlternatingRowColors(True)
        self.details_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.details_table.horizontalHeader().setStretchLastSection(True)
        self.details_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 设置列宽
        self.details_table.setColumnWidth(0, 60)   # 序号
        self.details_table.setColumnWidth(1, 120)  # 物料编码
        self.details_table.setColumnWidth(3, 100)  # 物料分类
        self.details_table.setColumnWidth(4, 80)   # 需求数量
        self.details_table.setColumnWidth(5, 60)   # 单位
        self.details_table.setColumnWidth(6, 80)   # 单价
        self.details_table.setColumnWidth(7, 80)   # 金额
        self.details_table.setColumnWidth(8, 100) # 需求日期
        self.details_table.setColumnWidth(9, 100) # 用途
    
        
        # 双击编辑
        self.details_table.doubleClicked.connect(self.edit_detail)
        
        # 根据模式设置工具提示
        if self.mode == 'view':
            self.details_table.setToolTip("双击查看明细信息")
        else:
            self.details_table.setToolTip("双击编辑明细信息")
            
        # 选择变化事件
        self.details_table.itemSelectionChanged.connect(self.on_detail_selection_changed)
        
        detail_layout.addWidget(self.details_table)
        parent_layout.addWidget(detail_card)
        
    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 左侧：生成操作按钮
        if self.mode != 'view' and self.plan_id:  # 编辑模式且有计划ID时显示
            generate_layout = QHBoxLayout()
            
            # 生成加工任务按钮
            self.generate_task_btn = QPushButton("🏭 生成加工任务")
            self.generate_task_btn.setStyleSheet("""
                QPushButton {
                    background-color: #722ed1;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #9254de;
                }
                QPushButton:pressed {
                    background-color: #531dab;
                }
            """)
            self.generate_task_btn.clicked.connect(self.generate_processing_task)
            generate_layout.addWidget(self.generate_task_btn)
            
            # 生成采购申请按钮
            self.generate_purchase_btn = QPushButton("🛒 生成采购申请")
            self.generate_purchase_btn.setStyleSheet("""
                QPushButton {
                    background-color: #eb2f96;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #f759ab;
                }
                QPushButton:pressed {
                    background-color: #c41d7f;
                }
            """)
            self.generate_purchase_btn.clicked.connect(self.generate_purchase_request)
            generate_layout.addWidget(self.generate_purchase_btn)
            
            button_layout.addLayout(generate_layout)
        
        button_layout.addStretch()
        
        if self.mode != 'view':
            # 保存按钮
            self.save_btn = QPushButton("保存")
            self.save_btn.setStyleSheet("""
                QPushButton {
                    background-color: #52c41a;
                    color: white;
                    border: none;
                    padding: 8px 20px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #73d13d;
                }
                QPushButton:pressed {
                    background-color: #389e0d;
                }
            """)
            self.save_btn.clicked.connect(self.save_data)
            button_layout.addWidget(self.save_btn)
        
        # 关闭按钮
        self.close_btn = QPushButton("关闭")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #595959;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #8c8c8c;
            }
            QPushButton:pressed {
                background-color: #434343;
            }
        """)
        self.close_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.close_btn)
        
        parent_layout.addLayout(button_layout)
    
    def generate_processing_task(self):
        """生成加工任务"""
        if not self.plan_id:
            QMessageBox.warning(self, "警告", "请先保存需求计划")
            return
        
        if not self.details_data:
            QMessageBox.warning(self, "警告", "需求计划没有明细数据，无法生成加工任务")
            return
        
        # 检查计划状态
        if self.plan_data.get('plan_status', 0) == 0:
            reply = QMessageBox.question(
                self, "确认操作",
                "当前需求计划还是草稿状态，建议先确认计划后再生成加工任务。\n是否继续生成？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if reply != QMessageBox.StandardButton.Yes:
                return
        
        try:
            # TODO: 实现生成加工任务的具体逻辑
            # 1. 分析需求明细中需要加工的物料
            # 2. 创建加工任务记录
            # 3. 关联需求计划
            
            QMessageBox.information(
                self, "功能开发中",
                f"生成加工任务功能开发中...\n"
                f"需求计划: {self.plan_data.get('plan_no', '')}\n"
                f"明细数量: {len(self.details_data)} 项"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成加工任务失败: {str(e)}")
    
    def generate_purchase_request(self):
        """生成采购申请"""
        if not self.plan_id:
            QMessageBox.warning(self, "警告", "请先保存需求计划")
            return
        
        if not self.details_data:
            QMessageBox.warning(self, "警告", "需求计划没有明细数据，无法生成采购申请")
            return
        
        try:
            # TODO: 实现生成采购申请的具体逻辑
            # 1. 分析需求明细中需要采购的物料
            # 2. 创建采购申请记录
            # 3. 关联需求计划
            
            QMessageBox.information(
                self, "功能开发中",
                f"生成采购申请功能开发中...\n"
                f"需求计划: {self.plan_data.get('plan_no', '')}\n"
                f"明细数量: {len(self.details_data)} 项"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成采购申请失败: {str(e)}")
            
    def generate_plan_no(self):
        """生成计划编号"""
        try:
            plan_no = self.demand_plan_db.generate_auto_number()
            self.plan_no_edit.setText(plan_no)
        except Exception as e:
            Logger.log_error(f"生成计划编号失败: {str(e)}")

    def generate_current_user(self):
        """生成当前用户"""
        return self.current_user['real_name']
    
    def generate_plan_name(self):
        """生成计划名称（初始化时调用）"""
        self.update_plan_name()
            
    def load_data(self):
        """加载数据"""
        if not self.plan_id:
            return
            
        try:
            # 加载主表数据
            self.plan_data = self.demand_plan_db.get_demand_plan_by_id(self.plan_id)
            if not self.plan_data:
                QMessageBox.warning(self, "警告", "未找到需求计划数据")
                return
                
            # 保存原始数据
            self.original_data = self.plan_data.copy()
            
            # 填充基本信息
            self.plan_no_edit.setText(self.plan_data.get('plan_no', ''))
            self.plan_name_edit.setText(self.plan_data.get('plan_name', ''))
            self.department_edit.setCurrentText(self.plan_data.get('department', ''))
            self.applicant_edit.setText(self.plan_data.get('applicant', ''))
            
            # 设置日期
            if self.plan_data.get('plan_date'):
                plan_date = QDate.fromString(str(self.plan_data['plan_date']), "yyyy-MM-dd")
                self.plan_date_edit.setDate(plan_date)
                
            
            
            # 设置下拉框
            demand_type = self.plan_data.get('demand_type', 1)
            for i in range(self.demand_type_combo.count()):
                if self.demand_type_combo.itemData(i) == demand_type:
                    self.demand_type_combo.setCurrentIndex(i)
                    break
                    
            priority_level = self.plan_data.get('priority_level', 1)
            for i in range(self.priority_combo.count()):
                if self.priority_combo.itemData(i) == priority_level:
                    self.priority_combo.setCurrentIndex(i)
                    break
            
            # 其他信息
            self.total_amount_edit.setValue(float(self.plan_data.get('total_amount', 0)))
            self.remark_edit.setText(self.plan_data.get('remark', ''))
            
            # 加载明细数据
            self.details_data = self.demand_plan_db.get_demand_plan_details(self.plan_id)
            self.refresh_details_table()
            
            # 如果是查看模式，禁用编辑
            if self.mode == 'view':
                self.set_readonly_mode()
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载需求计划数据失败：{str(e)}")
            Logger.log_error(f"加载需求计划数据异常: {str(e)}")
            
    def set_readonly_mode(self):
        """设置只读模式"""
        # 禁用所有输入控件
        controls = [
            self.plan_name_edit, self.department_edit, self.applicant_edit,
            self.demand_type_combo, self.priority_combo, self.plan_date_edit,
            self.total_amount_edit, self.remark_edit
        ]
        
        for control in controls:
            control.setEnabled(False)
            
        # 隐藏明细操作按钮
        self.add_detail_btn.setVisible(False)
        self.edit_detail_btn.setVisible(False)
        self.delete_detail_btn.setVisible(False)
        
    def refresh_details_table(self):
        """刷新明细表格"""
        self.details_table.setRowCount(len(self.details_data))
        
        for row, detail in enumerate(self.details_data):
            # 序号
            self.details_table.setItem(row, 0, QTableWidgetItem(str(detail.get('sequence_no', row + 1))))
            # 物料编码
            self.details_table.setItem(row, 1, QTableWidgetItem(detail.get('material_id', '')))
            # 物料名称
            self.details_table.setItem(row, 2, QTableWidgetItem(detail.get('material_name', '')))
            # 物料分类
            self.details_table.setItem(row, 3, QTableWidgetItem(detail.get('category_name', '')))
            # 需求数量
            self.details_table.setItem(row, 4, QTableWidgetItem(str(detail.get('required_qty', 0))))
            # 单位
            self.details_table.setItem(row, 5, QTableWidgetItem(detail.get('required_unit', '')))
            # 单价 - 第6列
            unit_price = detail.get('unit_price', 0)
            price_text = f"¥{unit_price:.2f}" if unit_price else "¥0.00"
            self.details_table.setItem(row, 6, QTableWidgetItem(price_text))
            # 金额 - 第7列
            total_amount = detail.get('total_amount', 0)
            amount_text = f"¥{total_amount:.2f}" if total_amount else "¥0.00"
            self.details_table.setItem(row, 7, QTableWidgetItem(amount_text))
            # 需求日期 - 第8列
            required_date = detail.get('required_date', '')
            self.details_table.setItem(row, 8, QTableWidgetItem(str(required_date)))
            # 用途 - 第9列
            self.details_table.setItem(row, 9, QTableWidgetItem(detail.get('usage_purpose', '')))
            # 备注 - 第10列
            self.details_table.setItem(row, 10, QTableWidgetItem(detail.get('remark', '')))
            # 执行状态 - 第11列
            execution_status_name = detail.get('execution_status_name', '未执行')
            self.details_table.setItem(row, 11, QTableWidgetItem(execution_status_name))
            # 采购申请号 - 第13列
            purchase_request_no = detail.get('purchase_request_no', '')
            self.details_table.setItem(row, 12, QTableWidgetItem(str(purchase_request_no)))
            
            # 设置所有单元格居中对齐
            for col in range(self.details_table.columnCount()):
                item = self.details_table.item(row, col)
                if item:
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 更新统计信息
        self.detail_stats_label.setText(f"需求明细: {len(self.details_data)} 项")
        
        # 重新计算预估总金额
        self.calculate_total_amount()
        
    def on_detail_selection_changed(self):
        """明细选择变化"""
        has_selection = len(self.details_table.selectedItems()) > 0
        self.edit_detail_btn.setEnabled(has_selection and self.mode != 'view')
        self.delete_detail_btn.setEnabled(has_selection and self.mode != 'view')
        
    def add_detail(self):
        """添加明细"""
        try:
            from demand.demand_detail_dialog import DemandDetailDialog
            dialog = DemandDetailDialog(self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                detail_data = dialog.get_detail_data()
                # 设置序号
                detail_data['sequence_no'] = len(self.details_data) + 1
                self.details_data.append(detail_data)
                self.refresh_details_table()
        except ImportError:
            QMessageBox.information(self, "提示", "需求明细对话框开发中...")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加需求明细失败: {str(e)}")
            
    def edit_detail(self):
        """编辑明细"""
        # 查看模式下不允许编辑
        if self.mode == 'view':
            return
            
        current_row = self.details_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要编辑的需求明细")
            return
            
        try:
            from demand.demand_detail_dialog import DemandDetailDialog
            detail_data = self.details_data[current_row]
            
            dialog = DemandDetailDialog(self, detail_data, mode='edit')
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                updated_data = dialog.get_detail_data()
                # 保持原序号
                updated_data['sequence_no'] = detail_data.get('sequence_no', current_row + 1)
                self.details_data[current_row] = updated_data
                self.refresh_details_table()
        except ImportError:
            QMessageBox.information(self, "提示", "需求明细对话框开发中...")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑需求明细失败: {str(e)}")
            
    def delete_detail(self):
        """删除明细"""
        current_row = self.details_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要删除的需求明细")
            return
            
        reply = QMessageBox.question(
            self, "确认删除",
            "确定要删除选中的需求明细吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 从数据中删除
            del self.details_data[current_row]
            # 重新设置序号
            for i, detail in enumerate(self.details_data):
                detail['sequence_no'] = i + 1
            # 刷新表格
            self.refresh_details_table()
            
    def validate_data(self):
        """验证数据"""
      
       
        return True
        
    def get_plan_data(self):
        """获取计划数据"""
        return {
            'plan_no': self.plan_no_edit.text().strip(),
            'plan_date': self.plan_date_edit.date().toPyDate(),
            'plan_name': self.plan_name_edit.text().strip(),
            'department': self.department_edit.currentText().strip(),
            'applicant': self.applicant_edit.text().strip(),
            'demand_type': self.demand_type_combo.currentData(),
            'priority_level': self.priority_combo.currentData(),
            'plan_status': 0,  # 草稿状态
            'total_amount': self.total_amount_edit.value(),
            'remark': self.remark_edit.text().strip()
        }
        
    def save_data(self):
        """保存数据"""
        if not self.validate_data():
            return False
            
        try:
            plan_data = self.get_plan_data()
            
            if self.mode == 'add':
                # 新增需求计划
                plan_id = self.demand_plan_db.create_demand_plan(plan_data, self.details_data)
                self.plan_id = plan_id
                QMessageBox.information(self, "成功", "需求计划保存成功！")
            elif self.mode == 'edit':
                # 更新需求计划
                self.demand_plan_db.update_demand_plan(self.plan_id, plan_data, self.details_data)
                QMessageBox.information(self, "成功", "需求计划更新成功！")
            elif self.mode == 'copy':
                # 复制模式 - 创建新的需求计划
                # 重新生成计划编号
                plan_data['plan_no'] = self.demand_plan_db.generate_auto_number()
                plan_id = self.demand_plan_db.create_demand_plan(plan_data, self.details_data)
                self.plan_id = plan_id
                QMessageBox.information(self, "成功", "需求计划复制成功！")
            
            # 发送保存成功信号
            self.data_saved.emit()
            self.accept()  # 关闭对话框
            return True
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")
            Logger.log_error(f"保存需求计划失败: {str(e)}")
            return False
     
    
    def calculate_total_amount(self):
        """计算预估总金额"""
        try:
            total_amount = 0.0
            
            # 遍历所有明细数据，累加金额
            for detail in self.details_data:
                detail_amount = detail.get('total_amount', 0)
                if detail_amount:
                    total_amount += float(detail_amount)
            
            # 更新预估金额显示
            self.total_amount_edit.setValue(total_amount)
            
        except Exception as e:
            Logger.log_error(f"计算预估总金额失败: {str(e)}")
     
    def update_plan_name(self):
        """更新计划名称"""
        try:
            department = self.department_edit.currentText()
            demand_type = self.demand_type_combo.currentText()
            applicant = self.applicant_edit.text().strip()
            plan_no = self.plan_no_edit.text().strip()
            
            # 生成计划名称：申请部门-需求类型-申请人-计划编号
            if department and demand_type and applicant and plan_no:
                plan_name = f"{department}-{demand_type}-{applicant}-{plan_no}"
            elif department and demand_type and plan_no:
                plan_name = f"{department}-{demand_type}-{plan_no}"
            else:
                plan_name = ""
            
            self.plan_name_edit.setText(plan_name)
            
        except Exception as e:
            Logger.log_error(f"更新计划名称失败: {str(e)}")



