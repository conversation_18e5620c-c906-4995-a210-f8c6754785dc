"""
测试库存管理按钮功能
验证期初库存、库存调拨、库存盘点按钮是否能正常弹出对话框
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt6.QtCore import Qt
import traceback

from core.logger import Logger
from modules.db_manager import DatabaseManager
from inventory.inventory_module import InventoryModule


class TestButtonWindow(QMainWindow):
    """测试按钮功能的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("库存管理按钮功能测试")
        self.resize(1200, 800)
        
        try:
            # 初始化数据库管理器
            self.db_manager = DatabaseManager()
            
            # 创建库存管理模块
            self.inventory_module = InventoryModule(self.db_manager, self)
            self.setCentralWidget(self.inventory_module)
            
            print("✅ 库存管理模块初始化成功")
            print("📝 请点击以下按钮测试功能：")
            print("   - 📝 期初库存")
            print("   - 🔄 库存调拨") 
            print("   - 📊 库存盘点")
            print()
            print("💡 如果按钮被禁用，说明权限控制生效")
            print("💡 如果按钮可点击但无反应，可能是对话框初始化失败")
            print("💡 如果弹出开发模式提示，说明修复成功")
            
        except Exception as e:
            print(f"❌ 库存管理模块初始化失败: {str(e)}")
            traceback.print_exc()


def test_dialog_imports():
    """测试对话框导入"""
    print("\n🧪 测试对话框导入...")
    print("-" * 30)
    
    try:
        from inventory.inventory_initial_dialog import InventoryInitialDialog
        print("✅ 期初库存对话框导入成功")
    except Exception as e:
        print(f"❌ 期初库存对话框导入失败: {str(e)}")
    
    try:
        from inventory.inventory_transfer_dialog import InventoryTransferDialog
        print("✅ 库存调拨对话框导入成功")
    except Exception as e:
        print(f"❌ 库存调拨对话框导入失败: {str(e)}")
    
    try:
        from inventory.inventory_count_dialog import InventoryCountDialog
        print("✅ 库存盘点对话框导入成功")
    except Exception as e:
        print(f"❌ 库存盘点对话框导入失败: {str(e)}")


def test_direct_dialog_creation():
    """直接测试对话框创建"""
    print("\n🔧 测试对话框直接创建...")
    print("-" * 35)
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    try:
        from inventory.inventory_initial_dialog import InventoryInitialDialog
        dialog = InventoryInitialDialog()
        print("✅ 期初库存对话框创建成功")
        dialog.close()
    except Exception as e:
        print(f"❌ 期初库存对话框创建失败: {str(e)}")
        traceback.print_exc()
    
    try:
        from inventory.inventory_transfer_dialog import InventoryTransferDialog
        dialog = InventoryTransferDialog()
        print("✅ 库存调拨对话框创建成功")
        dialog.close()
    except Exception as e:
        print(f"❌ 库存调拨对话框创建失败: {str(e)}")
        traceback.print_exc()
    
    try:
        from inventory.inventory_count_dialog import InventoryCountDialog
        dialog = InventoryCountDialog()
        print("✅ 库存盘点对话框创建成功")
        dialog.close()
    except Exception as e:
        print(f"❌ 库存盘点对话框创建失败: {str(e)}")
        traceback.print_exc()


def main():
    """主测试函数"""
    print("🚀 库存管理按钮功能测试")
    print("=" * 40)
    
    # 测试对话框导入
    test_dialog_imports()
    
    # 测试对话框直接创建
    test_direct_dialog_creation()
    
    # 测试完整的GUI界面
    print("\n🖥️  测试完整GUI界面...")
    print("-" * 25)
    
    app = QApplication(sys.argv)
    
    try:
        window = TestButtonWindow()
        window.show()
        
        print("\n🎯 测试指南:")
        print("-" * 15)
        print("1. 界面已打开，请手动点击按钮测试")
        print("2. 如果按钮被禁用（灰色），说明权限控制生效")
        print("3. 如果按钮可点击，应该会弹出对话框或开发模式提示")
        print("4. 关闭窗口结束测试")
        print()
        print("📋 预期结果:")
        print("- 无用户信息时：弹出开发模式确认对话框")
        print("- 有用户信息但无权限时：显示权限不足提示")
        print("- 有权限时：正常打开对应的功能对话框")
        
        # 运行应用
        app.exec()
        
    except Exception as e:
        print(f"❌ GUI测试失败: {str(e)}")
        traceback.print_exc()
    
    print("\n🎉 测试完成")


if __name__ == "__main__":
    main()
