"""
物料管理数据库操作模块
负责物料管理相关的数据库操作
"""

from modules.db_manager import DatabaseManager
from core.logger import Logger
import traceback

class MaterialDB:
    """物料数据库操作类"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def get_materials(self, params):
        """
        获取物料列表
        params: {
            material_id: 物料编码,
            name: 物料名称,
            category_id: 分类ID,
            status: 状态 (0=停用, 1=启用),
            page: 页码,
            page_size: 每页记录数
        }
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            values = []
            
            if params.get("material_id"):
                conditions.append("m.material_id LIKE %s")
                values.append(f"%{params['material_id']}%")
            
            if params.get("name"):
                conditions.append("m.name LIKE %s")
                values.append(f"%{params['name']}%")
            
            if params.get("category_id"):
                conditions.append("m.category_id = %s")
                values.append(params["category_id"])
            
            if params.get("status") is not None:
                conditions.append("m.status = %s")
                values.append(params["status"])
            
            # 构建WHERE子句
            where_clause = " AND ".join(conditions)
            
            # 构建查询语句
            count_sql = f"""
                SELECT COUNT(*) as total 
                FROM materials m
                LEFT JOIN material_categories c ON m.category_id = c.category_id
                WHERE {where_clause}
            """
            
            query_sql = f"""
                SELECT m.*, c.category_name
                FROM materials m
                LEFT JOIN material_categories c ON m.category_id = c.category_id
                WHERE {where_clause}
                ORDER BY m.material_id
                LIMIT %s OFFSET %s
            """
            
            # 分页参数
            page = params.get("page", 1)
            page_size = params.get("page_size", 10)
            offset = (page - 1) * page_size
            
            # 查询总记录数
            count_result = self.db_manager.execute_query(count_sql, values)
            total = count_result[0]["total"] if count_result else 0
            
            # 查询分页数据
            if total > 0:
                query_values = values + [page_size, offset]
                materials = self.db_manager.execute_query(query_sql, query_values)
            else:
                materials = []
            
            return total, materials
            
        except Exception as e:
            Logger.log_error("获取物料列表失败", error=e, extra={
                "params": params,
                "traceback": traceback.format_exc()
            })
            return 0, []
    
    def get_material_by_id(self, material_id):
        """根据ID获取物料信息"""
        try:
            sql = """
                SELECT m.*, c.category_name
                FROM materials m
                LEFT JOIN material_categories c ON m.category_id = c.category_id
                WHERE m.material_id = %s
            """
            
            result = self.db_manager.execute_query(sql, (material_id,))
            return result[0] if result else None
            
        except Exception as e:
            Logger.log_error("获取物料信息失败", error=e, extra={
                "material_id": material_id,
                "traceback": traceback.format_exc()
            })
            return None
    
    def add_material(self, material_data):
        """添加物料"""
        try:
            # 构建SQL语句和参数
            fields = []
            placeholders = []
            values = []
            
            # 可插入字段
            insert_fields = [
                "material_id", "name", "spec_model", "base_unit", "original_box_unit", 
                "conversion_rate", "barcode", "status", "length_unit", "length", "width", "height",
                "weight_unit", "gross_weight", "volume_unit", "volume", 
                "category_id", "cost", "remark", "is_east_material"
            ]
            
            for field in insert_fields:
                if field in material_data:
                    fields.append(field)
                    placeholders.append("%s")
                    values.append(material_data[field])
            
            # 构建SQL
            sql = f"""
                INSERT INTO materials (
                    {', '.join(fields)}
                ) VALUES (
                    {', '.join(placeholders)}
                )
            """
            
            affected_rows = self.db_manager.execute_update(sql, values)
            return affected_rows > 0
            
        except Exception as e:
            Logger.log_error("添加物料失败", error=e, extra={
                "material_data": material_data,
                "traceback": traceback.format_exc()
            })
            return False
    
    def update_material(self, material_id, material_data):
        """更新物料"""
        try:
            # 构建SQL语句和参数
            fields = []
            values = []
            
            # 可更新字段
            update_fields = [
                "name", "spec_model", "base_unit", "original_box_unit", "conversion_rate", 
                "barcode", "status", "length_unit", "length", "width", "height",
                "weight_unit", "gross_weight", "volume_unit", "volume", 
                "category_id", "cost", "remark", "is_east_material"
            ]
            
            for field in update_fields:
                if field in material_data:
                    fields.append(f"{field} = %s")
                    values.append(material_data[field])
            
            # 添加WHERE条件参数
            values.append(material_id)
            
            # 构建SQL
            sql = f"""
                UPDATE materials SET
                    {', '.join(fields)}
                WHERE material_id = %s
            """
            
            affected_rows = self.db_manager.execute_update(sql, values)
            return affected_rows > 0
            
        except Exception as e:
            Logger.log_error("更新物料失败", error=e, extra={
                "material_id": material_id,
                "material_data": material_data,
                "traceback": traceback.format_exc()
            })
            return False
    
    def delete_material(self, material_id):
        """删除物料"""
        try:
            sql = "DELETE FROM materials WHERE material_id = %s"
            affected_rows = self.db_manager.execute_update(sql, (material_id,))
            return affected_rows > 0
            
        except Exception as e:
            Logger.log_error("删除物料失败", error=e, extra={
                "material_id": material_id,
                "traceback": traceback.format_exc()
            })
            return False
    
    def get_material_categories(self):
        """获取物料分类列表"""
        try:
            # 检查表结构
            check_sql = "SHOW COLUMNS FROM material_categories LIKE 'parent_id'"
            check_result = self.db_manager.execute_query(check_sql)
            has_parent_id = len(check_result) > 0
            
            check_sql = "SHOW COLUMNS FROM material_categories LIKE 'level'"
            check_result = self.db_manager.execute_query(check_sql)
            has_level = len(check_result) > 0
            
            # 根据字段存在情况构建SQL
            if has_parent_id and has_level:
                sql = """
                    SELECT category_id, category_name, parent_id, level
                    FROM material_categories
                    ORDER BY level, sort_order
                """
            else:
                sql = """
                    SELECT category_id, category_name, sort_order
                    FROM material_categories
                    ORDER BY sort_order
                """
            
            return self.db_manager.execute_query(sql)
            
        except Exception as e:
            Logger.log_error("获取物料分类列表失败", error=e, extra={
                "traceback": traceback.format_exc()
            })
            return []
    
    def get_category_by_id(self, category_id):
        """根据ID获取物料分类"""
        try:
            sql = """
                SELECT category_id, category_name, parent_id, level
                FROM material_categories
                WHERE category_id = %s
            """
            result = self.db_manager.execute_query(sql, (category_id,))
            return result[0] if result else None
            
        except Exception as e:
            Logger.log_error("获取物料分类失败", error=e, extra={
                "category_id": category_id,
                "traceback": traceback.format_exc()
            })
            return None
    
    def batch_update_material_fields(self, update_data):
        """批量更新物料多个字段"""
        try:
            if not update_data:
                return 0
            
            success_count = 0
            
            for item in update_data:
                material_id = item.get('material_id')
                if not material_id:
                    continue
                
                # 构建更新字段
                update_fields = []
                values = []
                
                for field in ['cost', 'original_box_unit', 'conversion_rate']:
                    if field in item:
                        update_fields.append(f"{field} = %s")
                        values.append(item[field])
                
                if not update_fields:
                    continue
                
                values.append(material_id)
                
                sql = f"""
                    UPDATE materials 
                    SET {', '.join(update_fields)}
                    WHERE material_id = %s
                """
                
                affected_rows = self.db_manager.execute_update(sql, values)
                if affected_rows > 0:
                    success_count += 1
            
            return success_count
            
        except Exception as e:
            Logger.log_error("批量更新物料字段失败", error=e, extra={
                "update_data": update_data,
                "traceback": traceback.format_exc()
            })
            return 0
    
    def get_materials_for_cj_goods(self, params):
        """获取东方货品物料列表（is_east_material = 1）"""
        try:
            # 构建查询条件
            conditions = ["m.is_east_material = 1"]
            values = []
            
            if params.get("material_id"):
                conditions.append("m.material_id LIKE %s")
                values.append(f"%{params['material_id']}%")
            
            if params.get("name"):
                conditions.append("m.name LIKE %s")
                values.append(f"%{params['name']}%")
            
            # 构建WHERE子句
            where_clause = " AND ".join(conditions)
            
            # 构建查询语句
            count_sql = f"""
                SELECT COUNT(*) as total 
                FROM materials m
                WHERE {where_clause}
            """
            
            query_sql = f"""
                SELECT m.*, c.category_name
                FROM materials m
                LEFT JOIN material_categories c ON m.category_id = c.category_id
                WHERE {where_clause}
                ORDER BY m.material_id
                LIMIT %s OFFSET %s
            """
            
            # 分页参数
            page = params.get("page", 1)
            page_size = params.get("page_size", 10)
            offset = (page - 1) * page_size
            
            # 查询总记录数
            count_result = self.db_manager.execute_query(count_sql, values)
            total = count_result[0]["total"] if count_result else 0
            
            # 查询分页数据
            if total > 0:
                query_values = values + [page_size, offset]
                materials = self.db_manager.execute_query(query_sql, query_values)
            else:
                materials = []
            
            return total, materials
            
        except Exception as e:
            Logger.log_error("获取东方货品物料列表失败", error=e, extra={
                "params": params,
                "traceback": traceback.format_exc()
            })
            return 0, []
    
    def get_materials_for_bom(self, params=None, material_type=None):
        """获取可用于BOM的物料列表"""
        try:
            if params is None:
                params = {}
            
            # 构建查询条件
            conditions = ["m.status = 1"]
            values = []
            
            # 大类筛选
            if material_type == 'product':
                conditions.append("m.category_id IN (SELECT category_id FROM material_categories WHERE parent_id = 1)")
            elif material_type == 'package':
                conditions.append("m.category_id IN (SELECT category_id FROM material_categories WHERE parent_id = 6)")
            
            # 小分类筛选
            if params.get('category_id'):
                conditions.append("m.category_id = %s")
                values.append(params['category_id'])
            
            # 物料编码查询
            if params.get('material_id'):
                conditions.append("m.material_id LIKE %s")
                values.append(f"%{params['material_id']}%")
            
            # 物料名称查询
            if params.get('name'):
                conditions.append("m.name LIKE %s")
                values.append(f"%{params['name']}%")
            
            # 规格型号查询
            if params.get('spec_model'):
                conditions.append("m.spec_model LIKE %s")
                values.append(f"%{params['spec_model']}%")
            
            # 构建WHERE子句
            where_clause = " AND ".join(conditions)
            
            # 构建查询语句
            count_sql = f"""
                SELECT COUNT(*) as total 
                FROM materials m
                LEFT JOIN material_categories c ON m.category_id = c.category_id
                WHERE {where_clause}
            """
            
            query_sql = f"""
                SELECT 
                    m.material_id,
                    m.name,
                    m.spec_model,
                    m.category_id,
                    c.category_name,
                    m.base_unit,
                    m.original_box_unit,
                    m.conversion_rate,
                    m.barcode,
                    m.status,
                    m.cost,
                    m.remark
                FROM materials m
                LEFT JOIN material_categories c ON m.category_id = c.category_id
                WHERE {where_clause}
                ORDER BY m.material_id
                LIMIT %s OFFSET %s
            """
            
            # 分页参数
            page = params.get('page', 1)
            page_size = params.get('page_size', 20)
            offset = (page - 1) * page_size
            
            # 查询总记录数
            count_result = self.db_manager.execute_query(count_sql, values)
            total = count_result[0]["total"] if count_result else 0
            
            # 查询分页数据
            if total > 0:
                query_values = values + [page_size, offset]
                materials = self.db_manager.execute_query(query_sql, query_values)
            else:
                materials = []
            
            return total, materials
            
        except Exception as e:
            Logger.log_error("获取BOM物料列表失败", error=e, extra={
                "params": params,
                "material_type": material_type,
                "traceback": traceback.format_exc()
            })
            return 0, []
    
    def get_materials_by_category_type(self, category_type, params=None):
        """根据分类类型获取物料列表"""
        try:
            if params is None:
                params = {}
            
            # 根据分类类型确定parent_id
            if category_type == 'product':
                parent_id = 1
            elif category_type == 'package':
                parent_id = 6
            else:
                raise ValueError(f"不支持的分类类型: {category_type}")
            
            # 构建查询条件
            conditions = [
                "m.category_id IN (SELECT category_id FROM material_categories WHERE parent_id = %s)",
                "m.status = 1"
            ]
            values = [parent_id]
            
            # 物料编码查询
            if params.get('material_id'):
                conditions.append("m.material_id LIKE %s")
                values.append(f"%{params['material_id']}%")
            
            # 物料名称查询
            if params.get('name'):
                conditions.append("m.name LIKE %s")
                values.append(f"%{params['name']}%")
            
            # 规格型号查询
            if params.get('spec_model'):
                conditions.append("m.spec_model LIKE %s")
                values.append(f"%{params['spec_model']}%")
            
            # 构建WHERE子句
            where_clause = " AND ".join(conditions)
            
            # 构建查询语句
            count_sql = f"""
                SELECT COUNT(*) as total 
                FROM materials m
                LEFT JOIN material_categories c ON m.category_id = c.category_id
                WHERE {where_clause}
            """
            
            query_sql = f"""
                SELECT 
                    m.material_id,
                    m.name,
                    m.spec_model,
                    m.category_id,
                    c.category_name,
                    m.base_unit,
                    m.original_box_unit,
                    m.conversion_rate,
                    m.barcode,
                    m.status,
                    m.cost,
                    m.remark
                FROM materials m
                LEFT JOIN material_categories c ON m.category_id = c.category_id
                WHERE {where_clause}
                ORDER BY m.material_id
                LIMIT %s OFFSET %s
            """
            
            # 分页参数
            page = params.get('page', 1)
            page_size = params.get('page_size', 20)
            offset = (page - 1) * page_size
            
            # 查询总记录数
            count_result = self.db_manager.execute_query(count_sql, values)
            total = count_result[0]["total"] if count_result else 0
            
            # 查询分页数据
            if total > 0:
                query_values = values + [page_size, offset]
                materials = self.db_manager.execute_query(query_sql, query_values)
            else:
                materials = []
            
            return total, materials
            
        except Exception as e:
            Logger.log_error("根据分类类型获取物料列表失败", error=e, extra={
                "category_type": category_type,
                "params": params,
                "traceback": traceback.format_exc()
            })
            return 0, []
    
    def get_material_stock(self, material_id):
        """获取物料库存数量"""
        try:
            query = """
            SELECT stock_quantity 
            FROM tmp_material_stock 
            WHERE material_id = %s
            """
            result = self.db_manager.execute_query(query, (material_id,))
            
            if result and len(result) > 0:
                return float(result[0]['stock_quantity'])
            return 0.0
            
        except Exception as e:
            Logger.log_error(f"获取物料库存失败: {str(e)}")
            return 0.0

    def get_categories(self):
        """获取物料分类列表（简化版本，用于下拉框等）"""
        try:
            # 检查表结构
            check_sql = "SHOW COLUMNS FROM material_categories LIKE 'parent_id'"
            check_result = self.db_manager.execute_query(check_sql)
            has_parent_id = len(check_result) > 0
            
            check_sql = "SHOW COLUMNS FROM material_categories LIKE 'level'"
            check_result = self.db_manager.execute_query(check_sql)
            has_level = len(check_result) > 0
            
            # 根据字段存在情况构建SQL
            if has_parent_id and has_level:
                sql = """
                    SELECT category_id, category_name, parent_id, level, sort_order
                    FROM material_categories
                    ORDER BY level, sort_order, category_id
                """
            else:
                sql = """
                    SELECT category_id, category_name, sort_order
                    FROM material_categories
                    ORDER BY sort_order, category_id
                """
            
            return self.db_manager.execute_query(sql)
            
        except Exception as e:
            Logger.log_error("获取物料分类列表失败", error=e, extra={
                "traceback": traceback.format_exc()
            })
            return []






