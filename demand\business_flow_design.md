# 需求计划业务流程设计

## 1. 需求计划创建流程
1. 用户创建需求计划，添加成品物料需求明细
2. 系统保存到 `demand_plans` 和 `demand_plan_details` 表
3. 用户可选择是否立即进行BOM展开

## 2. BOM展开流程
1. 系统读取成品物料的有效BOM
2. 根据成品需求数量计算原材料和包材需求
3. 考虑损耗率计算实际需求数量
4. 将展开结果保存到 `demand_bom_materials` 表
5. 更新明细表的 `is_bom_expanded` 状态

## 3. 执行计划生成流程
1. **生产计划生成**：
   - 针对成品物料生成生产计划
   - 更新 `demand_plan_details.production_plan_id`

2. **采购申请生成**：
   - 针对原材料和包材生成采购申请
   - 更新 `demand_bom_materials.purchase_request_id`

## 4. 执行跟踪流程
1. 生产执行时记录到 `demand_executions` 表
2. 采购执行时记录到 `demand_executions` 表
3. 系统自动更新各表的执行状态和已执行数量

## 5. 数据查询优势
- 可以按成品物料查询需求
- 可以按原材料查询所有相关需求
- 可以跟踪每个物料的执行状态
- 支持成本分析和供应商分析
