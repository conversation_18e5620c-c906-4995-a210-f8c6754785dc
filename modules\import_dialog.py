from tkinter import messagebox
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QTableWidget, 
                            QTableWidgetItem, QFileDialog, QHeaderView,
                            QWidget, QCheckBox, QMessageBox, QLineEdit)
from PyQt6.QtCore import Qt
import pandas as pd

class ImportDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("导入Excel数据")
        self.resize(600, 400)
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        
        # 顶部区域
        top_layout = QHBoxLayout()
        
        # 文件选择
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("文件名:"))
        self.file_combo = QLineEdit()
        self.file_combo.setMinimumWidth(300)
        self.file_combo.setReadOnly(True)
        file_layout.addWidget(self.file_combo)
        
        # 浏览按钮
        self.browse_btn = QPushButton("浏览")
        self.browse_btn.clicked.connect(self.browse_file)
        file_layout.addWidget(self.browse_btn)
        
        top_layout.addLayout(file_layout)
        layout.addLayout(top_layout)
        
        # 工作表选择
        sheet_layout = QHBoxLayout()
        sheet_layout.addWidget(QLabel("工作表:"))
        self.sheet_combo = QComboBox()
        self.sheet_combo.currentIndexChanged.connect(self.sheet_changed)
        sheet_layout.addWidget(self.sheet_combo)
        sheet_layout.addStretch()
        layout.addLayout(sheet_layout)
        
        # 字段映射表格
        self.mapping_table = QTableWidget()
        self.mapping_table.setColumnCount(4)
        self.mapping_table.setHorizontalHeaderLabels(["#", "列名", "导入到字段", "不导入"])
        self.mapping_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.mapping_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        layout.addWidget(self.mapping_table)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.import_btn = QPushButton("开始导入")
        self.import_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.import_btn)
        
        self.cancel_btn = QPushButton("关闭")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
    def browse_file(self):
        """选择Excel文件"""
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "选择Excel文件",
            "",
            "Excel Files (*.xlsx *.xls)"
        )
        if file_name:
            try:
                self.file_combo.setText(file_name)
                self.load_excel_info(file_name)
            except Exception as e:
                self.file_combo.clear()
                self.sheet_combo.clear()
                self.mapping_table.setRowCount(0)
                self.current_file = None
                QMessageBox.critical(self, "错误", f"读取Excel文件失败：{str(e)}")
    
    def load_excel_info(self, file_name):
        """加载Excel文件信息"""
        try:
            # 清除之前的数据
            self.sheet_combo.clear()
            self.mapping_table.setRowCount(0)
            
            # 保存当前文件名，供sheet_changed使用
            self.current_file = file_name
            
            # 读取所有工作表名称
            excel_file = pd.ExcelFile(file_name)
            self.sheet_combo.addItems(excel_file.sheet_names)
            
            # 读取第一个工作表的列名
            df = pd.read_excel(file_name, sheet_name=excel_file.sheet_names[0])
            self.update_mapping_table(df.columns)
            
        except Exception as e:
            self.current_file = None
            raise Exception(f"读取Excel文件失败：{str(e)}")
    
    def sheet_changed(self, index):
        """工作表切换处理"""
        try:
            if not hasattr(self, 'current_file') or not self.current_file:
                return
            
            sheet_name = self.sheet_combo.currentText()
            if not sheet_name:
                return
            
            # 读取新工作表的数据
            df = pd.read_excel(self.current_file, sheet_name=sheet_name)
            self.update_mapping_table(df.columns)
            
        except Exception as e:
            self.mapping_table.setRowCount(0)
            QMessageBox.critical(self, "错误", f"读取工作表失败：{str(e)}")
    
    def update_mapping_table(self, columns):
        """更新字段映射表格"""
        self.mapping_table.setRowCount(len(columns))
        
        # 数据库字段列表 - 与 delivery_order 表保持一致
        db_fields = [
            "autoid",  # 主键
            "business_date",  # 业务日期
            "order_no",  # 单据编号
            "source_order_no",  # 源单据编号
            "customer",  # 送货客户
            "department",  # 部门
            "logistics_company",  # 物流公司
            "summary",  # 摘要
            "confirm_receipt_date",  # 确认收货日期
            "material_code",  # 物料编码
            "material_name",  # 物料名称
            "unit",  # 计量单位
            "batch_no",  # 批次
            "warehouse",  # 仓库
            "reject_reason",  # 客户拒收原因
            "delivery_quantity",  # 出库数量
            "confirmed_quantity",  # 确认收货数量
            "return_quantity",  # 退货数量
            "remaining_return_quantity",  # 剩余退货数量
            "order_status",  # 单据状态
            "unit_price_with_tax"  # 含税单价
        ]
        
        # 字段显示名称映射
        field_display_names = {
            "autoid": "autoid",
            "business_date": "业务日期",
            "order_no": "单据编号",
            "source_order_no": "源单据编号",
            "customer": "送货客户",
            "department": "部门",
            "logistics_company": "物流公司",
            "summary": "摘要",
            "confirm_receipt_date": "确认收货日期",
            "material_code": "物料编码",
            "material_name": "物料名称",
            "unit": "计量单位",
            "batch_no": "批次",
            "warehouse": "仓库",
            "reject_reason": "客户拒收原因",
            "delivery_quantity": "出库数量(求和)",
            "confirmed_quantity": "确认收货数量(求和)",
            "return_quantity": "退货数量",
            "remaining_return_quantity": "剩余退货数量",
            "order_status": "单据状态",
            "unit_price_with_tax": "含税单价"
        }
        
        for i, col in enumerate(columns):
            # 序号
            item_no = QTableWidgetItem(str(i + 1))
            item_no.setFlags(item_no.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.mapping_table.setItem(i, 0, item_no)
            
            # Excel列名
            item_col = QTableWidgetItem(str(col))
            item_col.setFlags(item_col.flags() & ~Qt.ItemFlag.ItemIsEditable)
            item_col.setBackground(Qt.GlobalColor.lightGray)
            self.mapping_table.setItem(i, 1, item_col)
            
            # 对应字段（下拉框）
            field_combo = QComboBox()
            # 添加空选项和所有字段（显示名称）
            field_combo.addItem("")
            for field in db_fields:
                field_combo.addItem(field_display_names[field], field)  # 使用显示名称作为显示文本，数据库字段名作为数据
                
            # 尝试自动匹配字段
            excel_col_lower = str(col).lower().replace(" ", "_")
            for field in db_fields:
                if excel_col_lower == field.lower() or excel_col_lower == field_display_names[field].lower():
                    field_combo.setCurrentText(field_display_names[field])
                    field_combo.setEnabled(False)
                    field_combo.setStyleSheet("""
                        QComboBox {
                            background-color: lightgray;
                        }
                        QComboBox::drop-down {
                            border: none;
                        }
                        QComboBox::down-arrow {
                            image: none;
                        }
                    """)
                    break
                
            self.mapping_table.setCellWidget(i, 2, field_combo)
            
            # 不导入复选框
            checkbox = QCheckBox()
            if excel_col_lower in [f.lower() for f in db_fields] or \
               excel_col_lower in [v.lower() for v in field_display_names.values()]:
                checkbox.setEnabled(False)
            checkbox_widget = QWidget()
            checkbox_layout = QHBoxLayout(checkbox_widget)
            checkbox_layout.addWidget(checkbox)
            checkbox_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            checkbox_layout.setContentsMargins(0, 0, 0, 0)
            self.mapping_table.setCellWidget(i, 3, checkbox_widget) 
        
    def accept(self):
        """确认导入"""
        try:
            print("\n开始导入数据处理...")
            file_name = self.file_combo.text()
            sheet_name = self.sheet_combo.currentText()
            
            print(f"文件名: {file_name}")
            print(f"工作表: {sheet_name}")
            
            if not file_name or not sheet_name:
                raise Exception("请选择要导入的Excel文件和工作表")
            
            # 收集字段映射信息
            field_mapping = {}
            skip_columns = set()
            
            print("收集字段映射信息...")
            for row in range(self.mapping_table.rowCount()):
                excel_col = self.mapping_table.item(row, 1).text()
                field_combo = self.mapping_table.cellWidget(row, 2)
                skip_checkbox = self.mapping_table.cellWidget(row, 3).findChild(QCheckBox)
                
                print(f"行 {row}:")
                print(f"  Excel列: {excel_col}")
                print(f"  映射字段: {field_combo.currentText() if field_combo else 'None'}")
                print(f"  是否跳过: {skip_checkbox.isChecked() if skip_checkbox else 'None'}")
                
                if skip_checkbox and skip_checkbox.isChecked():
                    skip_columns.add(excel_col)
                elif field_combo and field_combo.currentText():
                    field_mapping[excel_col] = field_combo.currentData()
                    
            print("字段映射结果:")
            print(f"  映射关系: {field_mapping}")
            print(f"  跳过列: {skip_columns}")
            
            # 读取Excel数据
            print("读取Excel数据...")
            df = pd.read_excel(file_name, sheet_name=sheet_name)
            print(f"Excel数据行数: {len(df)}")
            print("Excel列名:", df.columns.tolist())
            
            # 检查必填字段
            required_fields = [
                "order_no",
                "material_code",
                "material_name",
                "delivery_quantity"
            ]
            
            print("检查必填字段...")
            missing_fields = []
            for field in required_fields:
                mapped = False
                for excel_col, db_field in field_mapping.items():
                    if db_field == field:
                        mapped = True
                        break
                if not mapped:
                    missing_fields.append(field)
                    
            print(f"缺失必填字段: {missing_fields}")
            if missing_fields:
                raise Exception(f"以下必填字段未映射：{', '.join(missing_fields)}")
            
            # 准备导入数据
            import_data = []
            print("准备导入数据...")
            
            for _, row in df.iterrows():
                record = {}
                for excel_col, db_field in field_mapping.items():
                    if excel_col not in skip_columns:
                        value = row[excel_col]
                        # 处理 nan 值
                        if pd.isna(value):
                            value = None if db_field in ['logistics_company', 'batch_no', 'reject_reason'] else 0
                        record[db_field] = value
                import_data.append(record)
                
            print(f"准备导入 {len(import_data)} 条记录")
            if import_data:
                print("第一条记录示例:", import_data[0])
                
            # 在导入数据前检查autoid是否存在
            from .return_db import ReturnDB
            return_db = ReturnDB()
            
            # 获取所有要导入数据的autoid
            autoids = [record.get('autoid') for record in import_data if record.get('autoid')]
            
            if autoids:
                print(f"\n检查数据重复性...")
                print(f"待检查的autoid数量: {len(autoids)}")
                
                # 检查重复的autoid
                duplicates = return_db.check_duplicate_autoids(autoids)
                
                if duplicates:
                    print(f"\n发现重复记录:")
                    print(f"重复的autoid数量: {len(duplicates)}")
                    print(f"重复的autoid: {duplicates[:5]}")
                    
                    # 过滤掉重复的记录
                    import_data = [record for record in import_data 
                                 if record.get('autoid') not in duplicates]
                    
                    if not import_data:
                        message = f"导入失败！\n所有记录都是重复数据：\n"
                        message += f"重复记录数：{len(duplicates)} 条\n"
                        # message += f"重复的ID：{', '.join(str(d) for d in duplicates[:5])}"
                        # if len(duplicates) > 5:
                        #     message += f" 等{len(duplicates)}条记录"
                        QMessageBox.warning(self, "导入提示", message)
                        return  # 直接返回，不关闭对话框
                    
                    print(f"\n过滤后待导入记录数: {len(import_data)}")
                else:
                    print("未发现重复记录")
            
            if not import_data:
                QMessageBox.warning(self, "导入提示", "没有可导入的数据")
                return  # 直接返回，不关闭对话框
            
            # 执行数据导入
            try:
                success, _ = return_db.import_delivery_orders(import_data)
                if success:
                    self.import_data = import_data
                    self.duplicates = duplicates if 'duplicates' in locals() else []
                    super().accept()
                return
            except Exception as e:
                print(f"导入失败: {str(e)}")
                return
            
        except Exception as e:
            print(f"导入失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导入失败：{str(e)}")