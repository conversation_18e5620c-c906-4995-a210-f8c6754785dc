"""
库存管理模块测试脚本
用于测试库存管理功能的基本操作
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt6.QtWidgets import QApplication, QMainWindow
from PyQt6.QtCore import Qt
import traceback

from core.logger import Logger
from modules.db_manager import DatabaseManager
from inventory.inventory_module import InventoryModule
from inventory.inventory_db import InventoryDB
from inventory.inventory_permission_service import InventoryPermissionService


class TestInventoryWindow(QMainWindow):
    """测试库存管理窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("库存管理模块测试")
        self.resize(1200, 800)
        
        try:
            # 初始化数据库管理器
            self.db_manager = DatabaseManager()
            
            # 创建库存管理模块
            self.inventory_module = InventoryModule(self.db_manager, self)
            self.setCentralWidget(self.inventory_module)
            
            print("✅ 库存管理模块初始化成功")
            
        except Exception as e:
            print(f"❌ 库存管理模块初始化失败: {str(e)}")
            traceback.print_exc()


def test_inventory_db():
    """测试库存数据访问类"""
    print("\n=== 测试库存数据访问类 ===")
    
    try:
        # 创建测试用户
        test_user = {
            'user_id': 1,
            'username': 'test_user',
            'real_name': '测试用户',
            'department': '通路一',
            'departments': ['通路一', '通路二'],
            'permissions': ['inventory:view', 'inventory:manage', 'inventory:initial']
        }
        
        # 初始化库存DB
        inventory_db = InventoryDB(test_user)
        print("✅ InventoryDB 初始化成功")
        
        # 测试获取部门库存列表
        try:
            total, inventory_list = inventory_db.get_department_inventory_list(
                filters={'department': '通路一'}, 
                page=1, 
                page_size=10
            )
            print(f"✅ 获取部门库存列表成功: 总记录数 {total}, 返回 {len(inventory_list)} 条记录")
        except Exception as e:
            print(f"⚠️  获取部门库存列表失败: {str(e)}")
        
        # 测试获取库存流水
        try:
            total, transaction_list = inventory_db.get_inventory_transactions(
                filters={}, 
                page=1, 
                page_size=10
            )
            print(f"✅ 获取库存流水成功: 总记录数 {total}, 返回 {len(transaction_list)} 条记录")
        except Exception as e:
            print(f"⚠️  获取库存流水失败: {str(e)}")
        
        # 测试获取库存预留
        try:
            total, reservation_list = inventory_db.get_inventory_reservations(
                filters={}, 
                page=1, 
                page_size=10
            )
            print(f"✅ 获取库存预留成功: 总记录数 {total}, 返回 {len(reservation_list)} 条记录")
        except Exception as e:
            print(f"⚠️  获取库存预留失败: {str(e)}")
        
    except Exception as e:
        print(f"❌ 库存数据访问类测试失败: {str(e)}")
        traceback.print_exc()


def test_inventory_permission_service():
    """测试库存权限服务类"""
    print("\n=== 测试库存权限服务类 ===")
    
    try:
        # 创建测试用户
        test_user = {
            'user_id': 1,
            'username': 'test_user',
            'real_name': '测试用户',
            'department': '通路一',
            'departments': ['通路一', '通路二'],
            'permissions': ['inventory:view', 'inventory:manage', 'inventory:initial']
        }
        
        # 初始化权限服务
        permission_service = InventoryPermissionService(test_user)
        print("✅ InventoryPermissionService 初始化成功")
        
        # 测试权限检查
        permissions_to_test = [
            'inventory:view',
            'inventory:manage', 
            'inventory:initial',
            'inventory:transfer',
            'inventory:count',
            'system:admin'
        ]
        
        for perm in permissions_to_test:
            has_perm = permission_service.has_permission(perm)
            status = "✅" if has_perm else "❌"
            print(f"{status} 权限 {perm}: {has_perm}")
        
        # 测试部门权限
        departments_to_test = ['通路一', '通路二', '生产部']
        for dept in departments_to_test:
            has_dept_perm = permission_service.check_department_permission(dept)
            status = "✅" if has_dept_perm else "❌"
            print(f"{status} 部门权限 {dept}: {has_dept_perm}")
        
        # 测试权限信息获取
        user_info = permission_service.get_user_permissions_info()
        print(f"✅ 用户权限信息: {user_info}")
        
    except Exception as e:
        print(f"❌ 库存权限服务类测试失败: {str(e)}")
        traceback.print_exc()


def test_inventory_dialogs():
    """测试库存对话框"""
    print("\n=== 测试库存对话框 ===")
    
    try:
        # 测试期初库存对话框
        try:
            from inventory.inventory_initial_dialog import InventoryInitialDialog
            print("✅ 期初库存对话框导入成功")
        except Exception as e:
            print(f"❌ 期初库存对话框导入失败: {str(e)}")
        
        # 测试库存调拨对话框
        try:
            from inventory.inventory_transfer_dialog import InventoryTransferDialog
            print("✅ 库存调拨对话框导入成功")
        except Exception as e:
            print(f"❌ 库存调拨对话框导入失败: {str(e)}")
        
        # 测试库存盘点对话框
        try:
            from inventory.inventory_count_dialog import InventoryCountDialog
            print("✅ 库存盘点对话框导入成功")
        except Exception as e:
            print(f"❌ 库存盘点对话框导入失败: {str(e)}")
            
    except Exception as e:
        print(f"❌ 库存对话框测试失败: {str(e)}")
        traceback.print_exc()


def main():
    """主测试函数"""
    print("🚀 开始库存管理模块测试")
    
    # 测试数据访问类
    test_inventory_db()
    
    # 测试权限服务类
    test_inventory_permission_service()
    
    # 测试对话框导入
    test_inventory_dialogs()
    
    # 测试GUI界面
    print("\n=== 测试GUI界面 ===")
    app = QApplication(sys.argv)
    
    try:
        window = TestInventoryWindow()
        window.show()
        print("✅ 库存管理界面显示成功")
        print("📝 请手动测试界面功能...")
        
        # 运行应用
        # app.exec()  # 注释掉自动运行，避免阻塞测试
        
    except Exception as e:
        print(f"❌ 库存管理界面测试失败: {str(e)}")
        traceback.print_exc()
    
    print("\n🎉 库存管理模块测试完成")


if __name__ == "__main__":
    main()
