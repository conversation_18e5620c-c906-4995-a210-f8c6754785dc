from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
from .dongfang_return_db import DongfangReturnDB

class DongfangDetailDialog(QDialog):
    """东方退货明细对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.dongfang_db = DongfangReturnDB()
        self.parent_dialog = parent  # 保存父对话框引用
        self.document_status = 0  # 默认为保存状态
        self.setWindowTitle("添加明细")
        self.setModal(True)
        self.resize(500, 300)
        self.material_code = ""
        self.material_name = ""
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 表单区域
        form_layout = QGridLayout()
        
        # 运单号码
        form_layout.addWidget(QLabel("运单号码:"), 0, 0)
        self.waybill_no_input = QLineEdit()
        form_layout.addWidget(self.waybill_no_input, 0, 1, 1, 2)
        
        # 东方货号
        form_layout.addWidget(QLabel("东方货号:"), 1, 0)
        self.dongfang_code_input = QLineEdit()
        form_layout.addWidget(self.dongfang_code_input, 1, 1)
        
        # 选择货品按钮
        self.select_goods_btn = QPushButton("选择货品")
        self.select_goods_btn.clicked.connect(self.select_goods)
        form_layout.addWidget(self.select_goods_btn, 1, 2)
        
        # 货品名称（只读）
        form_layout.addWidget(QLabel("货品名称:"), 2, 0)
        self.goods_name_input = QLineEdit()
        self.goods_name_input.setReadOnly(True)
        self.goods_name_input.setStyleSheet("QLineEdit { background-color: #f0f0f0; }")
        form_layout.addWidget(self.goods_name_input, 2, 1, 1, 2)
        
        # # 规格（只读）
        # form_layout.addWidget(QLabel("规格:"), 3, 0)
        # self.specification_input = QLineEdit()
        # self.specification_input.setReadOnly(True)
        # self.specification_input.setStyleSheet("QLineEdit { background-color: #f0f0f0; }")
        # form_layout.addWidget(self.specification_input, 3, 1, 1, 2)
        
        # # 单位（只读）
        # form_layout.addWidget(QLabel("单位:"), 4, 0)
        # self.unit_input = QLineEdit()
        # self.unit_input.setReadOnly(True)
        # self.unit_input.setStyleSheet("QLineEdit { background-color: #f0f0f0; }")
        # form_layout.addWidget(self.unit_input, 4, 1, 1, 2)
        
        # 数量
        form_layout.addWidget(QLabel("数量:"), 5, 0)
        self.quantity_input = QSpinBox()
        self.quantity_input.setMinimum(1)
        self.quantity_input.setMaximum(999999)
        self.quantity_input.setValue(1)
        form_layout.addWidget(self.quantity_input, 5, 1, 1, 2)
        
        layout.addLayout(form_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.validate_and_accept)
        button_layout.addWidget(self.ok_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def select_goods(self):
        """选择货品"""
        from .goods_select_dialog import GoodsSelectDialog
        dialog = GoodsSelectDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            goods_data = dialog.get_selected_goods()
            if goods_data:
                self.dongfang_code_input.setText(goods_data['cj_code'])
                self.goods_name_input.setText(goods_data['cj_name'])
                # 保存物料信息
                self.material_code = goods_data.get('material_code', '')
                self.material_name = goods_data.get('material_name', '')
    
    def validate_and_accept(self):
        """验证数据并接受"""
        # 验证必填字段
        if not self.waybill_no_input.text().strip():
            QMessageBox.warning(self, "警告", "请输入运单号码！")
            self.waybill_no_input.setFocus()
            return
        
        if not self.dongfang_code_input.text().strip():
            QMessageBox.warning(self, "警告", "请输入或选择东方货号！")
            self.dongfang_code_input.setFocus()
            return
        
        if self.quantity_input.value() <= 0:
            QMessageBox.warning(self, "警告", "数量必须大于0！")
            self.quantity_input.setFocus()
            return
        
        self.accept()
    
    def get_data(self):
        """获取数据"""
        return {
            'waybill_no': self.waybill_no_input.text().strip(),
            'dongfang_code': self.dongfang_code_input.text().strip(),
            'quantity': self.quantity_input.value(),
            'goods_name': self.goods_name_input.text().strip(),
            'material_code': self.material_code,
            'material_name': self.material_name
        }
    
    def set_document_status(self, document_status):
        """设置单据状态并更新UI状态"""
        self.document_status = document_status
        self.update_ui_state()
    
    def update_ui_state(self):
        """根据单据状态更新UI状态"""
        is_readonly = self.document_status in [1, 2]  # 审核或关闭状态为只读
        
        # 设置表单控件状态
        self.waybill_no_input.setReadOnly(is_readonly)
        self.dongfang_code_input.setReadOnly(is_readonly)
        self.goods_name_input.setReadOnly(is_readonly)
        self.quantity_input.setReadOnly(is_readonly)
        
        
        # 设置按钮状态
        if hasattr(self, 'select_goods_btn'):
            self.select_goods_btn.setEnabled(not is_readonly)
        
        # 设置样式
        if is_readonly:
            readonly_style = "background-color: #f5f5f5; color: #666666;"
            self.waybill_no_input.setStyleSheet(readonly_style)
            self.dongfang_code_input.setStyleSheet(readonly_style)
            self.goods_name_input.setStyleSheet(readonly_style)
            self.quantity_input.setStyleSheet(readonly_style)
        else:
            normal_style = "background-color: white; color: black;"
            self.waybill_no_input.setStyleSheet(normal_style)
            self.dongfang_code_input.setStyleSheet(normal_style)
            self.goods_name_input.setStyleSheet(normal_style)
            self.quantity_input.setStyleSheet(normal_style)




