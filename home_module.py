from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class HomeModule(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        """初始化首页界面"""
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 添加欢迎标题
        title = QLabel("欢迎使用退货管理系统")
        title.setFont(QFont("Microsoft YaHei", 24, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 添加系统信息
        info = QLabel("系统版本: 3.8.9\n\n"
                     "主要功能:\n"
                     "- 出库单管理\n"
                     "- 交接单管理\n"
                     "- 退货入库管理\n"
                     "- 操作日志\n"
                     "- 报表统计")
        info.setFont(QFont("Microsoft YaHei", 12))
        info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(info)
        
        # 添加底部信息
        footer = QLabel("© 2024 退货管理系统")
        footer.setFont(QFont("Microsoft YaHei", 10))
        footer.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(footer)
        
        self.setLayout(layout) 