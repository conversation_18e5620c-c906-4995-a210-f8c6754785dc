from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
from datetime import datetime, date
import traceback
from .dongfang_return_db import DongfangReturnDB
from .dongfang_return_dialog import DongfangReturnDialog

class DongfangReturnTab(QWidget):
    """东方退货申请标签页"""
    
    def __init__(self, return_db=None):
        super().__init__()
        self.return_db = return_db
        self.dongfang_db = DongfangReturnDB()
        self.current_page = 1
        self.page_size = 20
        self.total_records = 0
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 搜索区域
        search_frame = QFrame()
        search_frame.setStyleSheet("QFrame { background-color: #f5f5f5; }")
        search_layout = QHBoxLayout(search_frame)
        
        # 搜索条件组
        search_group = QFrame()
        search_group.setStyleSheet("QFrame { background-color: #f5f5f5; }")
        search_inner_layout = QHBoxLayout(search_group)
        search_inner_layout.setContentsMargins(10, 5, 10, 5)
        
        # 功能分组标签
        search_label = QLabel("搜索条件:")
        search_label.setStyleSheet("font-weight: bold;")
        search_inner_layout.addWidget(search_label)
        
        # 业务编号搜索
        search_inner_layout.addWidget(QLabel("业务编号:"))
        self.yewu_no_input = QLineEdit()
        self.yewu_no_input.setPlaceholderText("请输入业务编号")
        self.yewu_no_input.returnPressed.connect(self.search_data)  # 添加回车搜索
        search_inner_layout.addWidget(self.yewu_no_input)
        
        # # 客户名称搜索
        # search_inner_layout.addWidget(QLabel("客户名称:"))
        # self.customer_input = QLineEdit()
        # self.customer_input.setPlaceholderText("请输入客户名称")
        # self.customer_input.returnPressed.connect(self.search_data)
        # search_inner_layout.addWidget(self.customer_input)
        
        # 退货日期范围
        search_inner_layout.addWidget(QLabel("退货日期:"))
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        search_inner_layout.addWidget(self.start_date)
        
        search_inner_layout.addWidget(QLabel("至"))
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        search_inner_layout.addWidget(self.end_date)
        
        # 单据状态搜索
        search_inner_layout.addWidget(QLabel("单据状态:"))
        self.status_combo = QComboBox()
        self.status_combo.addItems(["全部状态", "保存", "审核", "关闭"])
        # self.status_combo.setStyleSheet("""
        #     QComboBox {
        #         border: 1px solid #d9d9d9;
        #         border-radius: 4px;
        #         padding: 6px 8px;
        #         background-color: white;
        #         font-size: 12px;
        #         color: #333333;
        #         selection-background-color: #1890ff;
        #     }
        #     QComboBox:focus {
        #         border-color: #1890ff;
        #     }
        #     QComboBox:hover {
        #         border-color: #40a9ff;
        #     }
        #     QComboBox::drop-down {
        #         border: none;
        #         width: 20px;
        #         background-color: transparent;
        #     }
        #     QComboBox::down-arrow {
        #         width: 12px;
        #         height: 12px;
        #         image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMgNC41TDYgNy41TDkgNC41IiBzdHJva2U9IiM2NjY2NjYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
        #     }
        #     QComboBox QAbstractItemView {
        #         border: 1px solid #d9d9d9;
        #         border-radius: 4px;
        #         background-color: white;
        #         selection-background-color: #e6f7ff;
        #         selection-color: #1890ff;
        #         outline: none;
        #         font-size: 12px;
        #         padding: 2px;
        #     }
        #     QComboBox QAbstractItemView::item {
        #         height: 28px;
        #         padding: 4px 8px;
        #         border: none;
        #         color: #333333;
        #     }
        #     QComboBox QAbstractItemView::item:selected {
        #         background-color: #1890ff;
        #         color: white;
        #         font-weight: 500;
        #     }
        #     QComboBox QAbstractItemView::item:hover {
        #         background-color: #e6f7ff;
        #         color: #1890ff;
        #     }
        # """)
        search_inner_layout.addWidget(self.status_combo)
        #单据状态更改，自动刷新搜索数据
        self.status_combo.currentIndexChanged.connect(self.search_data)

        # 搜索按钮
        self.search_btn = QPushButton("搜索")
        self.search_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogContentsView))
        self.search_btn.clicked.connect(self.search_data)
        search_inner_layout.addWidget(self.search_btn)
        
        # 重置按钮
        self.reset_btn = QPushButton("重置")
        self.reset_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_BrowserReload))
        self.reset_btn.clicked.connect(self.reset_search)
        search_inner_layout.addWidget(self.reset_btn)
        
        # 新增按钮
        self.add_btn = QPushButton("新增")
        self.add_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogNewFolder))
        self.add_btn.clicked.connect(self.add_return_application)
        search_inner_layout.addWidget(self.add_btn)
        
        # 添加搜索组到布局
        search_layout.addWidget(search_group)
        layout.addWidget(search_frame)
        
        # 批量操作工具栏
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("QFrame { background-color: #f5f5f5; }")
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(10, 5, 10, 5)
        
        # 功能分组标签
        toolbar_label = QLabel("批量操作:")
        toolbar_label.setStyleSheet("font-weight: bold;")
        toolbar_layout.addWidget(toolbar_label)
        
        # 导出按钮
        self.export_btn = QPushButton("导出Excel")
        self.export_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogSaveButton))
        self.export_btn.clicked.connect(self.export_excel)
        toolbar_layout.addWidget(self.export_btn)
        
        # 批量删除按钮
        self.batch_delete_btn = QPushButton("批量删除")
        self.batch_delete_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_TrashIcon))
        self.batch_delete_btn.clicked.connect(self.batch_delete_applications)
        toolbar_layout.addWidget(self.batch_delete_btn)
        
        # 添加弹性占位符
        toolbar_layout.addStretch()
        
        # 添加工具栏到主布局
        layout.addWidget(toolbar_frame)
        
        # 数据表格
        self.table = QTableWidget()
        self.table.setColumnCount(8)  # 增加状态列
        self.table.setHorizontalHeaderLabels([
            "业务编号", "退货日期", "客户名称", "物流公司",
            "备注", "单据状态", "创建时间", "创建人"
        ])
        
        # 设置表格列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        
        # 设置表格属性
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        
        # 添加双击事件
        self.table.doubleClicked.connect(self.on_table_double_clicked)
        
        # 添加右键菜单
        self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)
        
        layout.addWidget(self.table)
        
        # 分页控件 - 移动到表格底部
        page_frame = QFrame()
        page_layout = QHBoxLayout(page_frame)
        page_layout.setContentsMargins(10, 8, 10, 8)
        
        # 左侧：总条数显示
        page_layout.addWidget(QLabel("总记录数:"))
        self.total_label = QLabel("0")
        page_layout.addWidget(self.total_label)
        
        # 添加弹性占位符
        page_layout.addStretch()
        
        # 当前页
        page_layout.addWidget(QLabel("当前页:"))
        self.current_page_label = QLabel("1")
        page_layout.addWidget(self.current_page_label)
        
        # 每页记录数
        page_layout.addWidget(QLabel("每页记录:"))
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["20", "50", "100"])
        self.page_size_combo.setCurrentText(str(self.page_size))
        self.page_size_combo.currentTextChanged.connect(self.on_page_size_changed)
        page_layout.addWidget(self.page_size_combo)
        
        # 翻页按钮
        self.prev_btn = QPushButton("上一页")
        self.prev_btn.clicked.connect(self.prev_page)
        page_layout.addWidget(self.prev_btn)
        
        self.next_btn = QPushButton("下一页")
        self.next_btn.clicked.connect(self.next_page)
        page_layout.addWidget(self.next_btn)
        
        layout.addWidget(page_frame)
    
    def get_search_params(self):
        """获取搜索参数"""
        params = {
            'yewu_no': self.yewu_no_input.text().strip(),
            'start_date': self.start_date.date().toPyDate(),
            'end_date': self.end_date.date().toPyDate(),
            'page': self.current_page,
            'page_size': int(self.page_size_combo.currentText())
        }
        
        # 添加单据状态过滤
        status_text = self.status_combo.currentText()
        if status_text != "全部状态":
            status_map = {"保存": 0, "审核": 1, "关闭": 2}
            params['document_status'] = status_map.get(status_text)
        
        return params
    
    def search_data(self):
        """搜索数据"""
        self.current_page = 1
        self.load_data()
    
    def reset_search(self):
        """重置搜索条件"""
        self.yewu_no_input.clear()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.end_date.setDate(QDate.currentDate())
        self.status_combo.setCurrentText("全部状态")  # 重置状态选择
        self.current_page = 1
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        try:
            filters = self.get_search_params()
            total, results = self.dongfang_db.get_return_applications(filters)
            
            self.total_records = total
            self.update_table(results)
            self.update_pagination()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载数据失败：{str(e)}")
    
    def update_table(self, results):
        """更新表格数据"""
        self.table.setRowCount(len(results))
        
        for row, data in enumerate(results):
            try:
                if isinstance(data, dict):
                    # 字典格式数据
                    self.table.setItem(row, 0, QTableWidgetItem(str(data.get('yewu_no', ''))))
                    self.table.setItem(row, 1, QTableWidgetItem(str(data.get('return_date', ''))))
                    self.table.setItem(row, 2, QTableWidgetItem(str(data.get('customer', ''))))
                    self.table.setItem(row, 3, QTableWidgetItem(str(data.get('logistics', ''))))
                    self.table.setItem(row, 4, QTableWidgetItem(str(data.get('remark', ''))))
                    
                    # 单据状态列
                    status_text = data.get('status_text', '保存')
                    status_item = QTableWidgetItem(status_text)
                    
                    # 根据状态设置颜色
                    document_status = data.get('document_status', 0)
                    if document_status == 0:  # 保存
                        status_item.setForeground(QColor("#000000"))  # 黑色
                    elif document_status == 1:  # 审核
                        status_item.setForeground(QColor("#52c41a"))  # 绿色
                    elif document_status == 2:  # 关闭
                        status_item.setForeground(QColor("#ff4d4f"))  # 红色
                    
                    status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.table.setItem(row, 5, status_item)
                    
                    self.table.setItem(row, 6, QTableWidgetItem(str(data.get('create_time', ''))))
                    self.table.setItem(row, 7, QTableWidgetItem(str(data.get('create_user', ''))))
                    
                    # 存储ID到第一列的UserRole中
                    first_item = self.table.item(row, 0)
                    if first_item:
                        first_item.setData(Qt.ItemDataRole.UserRole, data.get('id'))
                else:
                    # 元组格式数据
                    self.table.setItem(row, 0, QTableWidgetItem(str(data[1] or '')))
                    self.table.setItem(row, 1, QTableWidgetItem(str(data[2] or '')))
                    self.table.setItem(row, 2, QTableWidgetItem(str(data[3] or '')))
                    self.table.setItem(row, 3, QTableWidgetItem(str(data[4] or '')))
                    self.table.setItem(row, 4, QTableWidgetItem(str(data[5] or '')))
                    
                    # 单据状态列
                    document_status = data[6] if len(data) > 6 else 0
                    status_text = data[7] if len(data) > 7 else '保存'
                    status_item = QTableWidgetItem(status_text)
                    
                    # 根据状态设置颜色
                    if document_status == 0:  # 保存
                        status_item.setForeground(QColor("#000000"))  # 黑色
                    elif document_status == 1:  # 审核
                        status_item.setForeground(QColor("#52c41a"))  # 绿色
                    elif document_status == 2:  # 关闭
                        status_item.setForeground(QColor("#ff4d4f"))  # 红色
                    
                    status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.table.setItem(row, 5, status_item)
                    
                    self.table.setItem(row, 6, QTableWidgetItem(str(data[8] or '')))
                    self.table.setItem(row, 7, QTableWidgetItem(str(data[9] or '')))
                    
                    # 存储ID到第一列的UserRole中
                    first_item = self.table.item(row, 0)
                    if first_item:
                        first_item.setData(Qt.ItemDataRole.UserRole, data[0])
                
                # 设置所有单元格居中对齐
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    if item:
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                        
            except Exception as e:
                print(f"更新表格第{row}行数据失败: {str(e)}")
                continue
    
    def update_pagination(self):
        """更新分页控件状态"""
        # 更新总条数
        self.total_label.setText(str(self.total_records))

        # 计算总页数
        page_size = int(self.page_size_combo.currentText())
        total_pages = max((self.total_records + page_size - 1) // page_size, 1)

        # 更新当前页显示
        self.current_page_label.setText(str(self.current_page))

        # 更新按钮状态
        self.prev_btn.setEnabled(self.current_page > 1)
        self.next_btn.setEnabled(self.current_page < total_pages)
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_data()
    
    def next_page(self):
        """下一页"""
        page_size = int(self.page_size_combo.currentText())
        total_pages = (self.total_records + page_size - 1) // page_size
        if self.current_page < total_pages:
            self.current_page += 1
            self.load_data()

    def on_page_size_changed(self, value):
        """每页记录数改变"""
        self.current_page = 1  # 重置到第一页
        self.load_data()

    def on_table_double_clicked(self, index):
        """处理表格双击事件"""
        row = index.row()
        yewu_no = self.table.item(row, 0).text()  # 获取业务编号
        
        # 获取当前退货申请数据
        params = {"yewu_no": yewu_no, "page": 1, "page_size": 1}
        _, applications = self.dongfang_db.get_return_applications(params)
        if not applications:
            QMessageBox.warning(self, "警告", "退货申请不存在")
            return
        
        # 根据单据状态决定打开模式
        document_status = applications[0].get('document_status', 0)
        if document_status in [1, 2]:  # 审核或关闭状态
            mode = 'view'
        else:  # 保存状态
            mode = 'edit'
            
        # 弹出对话框
        dialog = DongfangReturnDialog(self, applications[0].get('id'), mode=mode)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_data()  # 刷新数据
    
    def add_return_application(self):
        """新增退货申请"""
        try:
            dialog = DongfangReturnDialog(self, mode='add')
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_data()  # 刷新数据
                QMessageBox.information(self, "成功", "退货申请添加成功！")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加退货申请失败：{str(e)}")
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.table.itemAt(position) is None:
            return
            
        row = self.table.rowAt(position.y())
        if row < 0:
            return
        
        # 获取单据状态
        yewu_no = self.table.item(row, 0).text()
        params = {"yewu_no": yewu_no, "page": 1, "page_size": 1}
        _, applications = self.dongfang_db.get_return_applications(params)
        
        if not applications:
            return
            
        document_status = applications[0].get('document_status', 0)
        
        menu = QMenu(self)
        
        # 查看操作（所有状态都可以查看）
        view_action = menu.addAction("查看")
        view_action.triggered.connect(lambda: self.view_return_application(row))
        
        # 编辑操作（只有保存状态可以编辑）
        if document_status == 0:
            edit_action = menu.addAction("编辑")
            edit_action.triggered.connect(lambda: self.edit_return_application(row))
            
            delete_action = menu.addAction("删除")
            delete_action.triggered.connect(lambda: self.delete_return_application(row))
        
        # 状态操作
        menu.addSeparator()
        if document_status == 0:  # 保存状态可以审核
            audit_action = menu.addAction("审核")
            audit_action.triggered.connect(lambda: self.audit_return_application(row))
        
        if document_status in [0, 1]:  # 保存和审核状态可以关闭
            close_action = menu.addAction("关闭")
            close_action.triggered.connect(lambda: self.close_return_application(row))
        
        # 显示菜单
        menu.exec(self.table.mapToGlobal(position))

    def view_return_application(self, row):
        """查看退货申请"""
        return_id = self.get_return_id_from_row(row)
        if return_id is None:
            return
            
        try:
            dialog = DongfangReturnDialog(self, return_id, mode='view')
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_data()  # 刷新数据
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开查看对话框失败：{str(e)}")

    def edit_return_application(self, row):
        """编辑退货申请"""
        return_id = self.get_return_id_from_row(row)
        if return_id is None:
            return
            
        try:
            dialog = DongfangReturnDialog(self, return_id, mode='edit')
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_data()  # 刷新数据
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开编辑对话框失败：{str(e)}")

    def delete_return_application(self, row):
        """删除退货申请"""
        return_id = self.get_return_id_from_row(row)
        if return_id is None:
            return
            
        yewu_no = self.table.item(row, 0).text()
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除业务编号为 {yewu_no} 的退货申请吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.dongfang_db.delete_return_application(return_id)
                QMessageBox.information(self, "成功", "删除成功！")
                self.load_data()
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除失败：{str(e)}")

    def get_return_id_from_row(self, row):
        """从表格行获取退货申请ID"""
        try:
            if row < 0 or row >= self.table.rowCount():
                return None
                
            # 从第一列的用户数据中获取ID
            item = self.table.item(row, 0)
            if item:
                return_id = item.data(Qt.ItemDataRole.UserRole)
                if return_id:
                    return return_id
            
            # 如果没有存储ID，则通过业务编号查询
            yewu_no = self.table.item(row, 0).text()
            if not yewu_no:
                return None
                
            query = f"SELECT id FROM return_application WHERE yewu_no = %s"
            result = self.dongfang_db.db.execute_query(query, (yewu_no,))
            
            if result and len(result) > 0:
                if isinstance(result[0], dict):
                    return result[0]['id']
                else:
                    return result[0][0]
            else:
                QMessageBox.warning(self, "警告", f"未找到业务编号为 {yewu_no} 的记录")
                return None
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取记录ID失败：{str(e)}")
            return None

    def export_excel(self):
        """导出Excel"""
        try:
            # 获取当前显示的数据（导出全部数据，不分页）
            filters = self.get_search_params()
            filters['page_size'] = 10000  # 设置大的页面大小以获取全部数据
            total, results = self.dongfang_db.get_return_applications(filters)
            
            if not results:
                QMessageBox.information(self, "提示", "没有数据可导出！")
                return
                
            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出Excel", 
                f"东方退货申请_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel files (*.xlsx)"
            )
            
            if not file_path:
                return
                
            # 创建进度对话框
            progress_dialog = QProgressDialog("正在导出数据...", "取消", 0, len(results), self)
            progress_dialog.setWindowTitle("导出进度")
            progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
            progress_dialog.setMinimumDuration(0)
            progress_dialog.show()
            
            # 导入pandas和openpyxl
            import pandas as pd
            from openpyxl import Workbook
            from openpyxl.styles import Font
            
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "东方退货申请"
            
            # 设置表头
            headers = [
                "业务编号", "退货日期", "客户名称", "物流公司", "备注", "单据状态",
                "运单号码", "东方货号", "货品名称", "数量", "物料编码", "物料名称",
                "创建时间", "创建人", "更新时间", "更新人"
            ]
            ws.append(headers)
            
            # 写入数据
            for i, main_data in enumerate(results):
                # 更新进度
                progress_dialog.setValue(i)
                progress_dialog.setLabelText(f"正在处理第 {i+1}/{len(results)} 条数据")
                
                # 检查是否取消
                if progress_dialog.wasCanceled():
                    wb.close()
                    return
                
                # 获取明细数据
                details = self.dongfang_db.get_return_application_details(main_data['id'])
                
                if details:
                    # 如果有明细，每个明细一行
                    for detail in details:
                        # 处理明细数据格式
                        if isinstance(detail, dict):
                            waybill_no = detail.get('waybill_no', '')
                            dongfang_code = detail.get('dongfang_code', '')
                            goods_name = detail.get('goods_name', '')
                            quantity = detail.get('quantity', 0)
                            material_code = detail.get('material_code', '')
                            material_name = detail.get('material_name', '')
                        else:
                            # 如果是元组格式
                            waybill_no = detail[1] if len(detail) > 1 else ''
                            dongfang_code = detail[2] if len(detail) > 2 else ''
                            quantity = detail[3] if len(detail) > 3 else 0
                            goods_name = detail[8] if len(detail) > 8 else ''
                            material_code = detail[9] if len(detail) > 9 else ''
                            material_name = detail[10] if len(detail) > 10 else ''
                        
                        # 状态文本转换
                        status_text = {0: '保存', 1: '审核', 2: '关闭'}.get(main_data.get('document_status', 0), '未知')
                        
                        # 合并主表和明细数据
                        ws.append([
                            main_data.get('yewu_no', ''),                    # 业务编号
                            str(main_data.get('return_date', '')),          # 退货日期
                            main_data.get('customer', ''),                  # 客户名称
                            main_data.get('logistics', ''),                 # 物流公司
                            main_data.get('remark', ''),                    # 备注
                            status_text,                                     # 单据状态
                            waybill_no,                                      # 运单号码
                            dongfang_code,                                   # 东方货号
                            goods_name,                                      # 货品名称
                            quantity,                                        # 数量
                            material_code,                                   # 物料编码
                            material_name,                                   # 物料名称
                            str(main_data.get('create_time', '')),          # 创建时间
                            main_data.get('create_user', ''),               # 创建人
                            str(main_data.get('update_time', '')),          # 更新时间
                            main_data.get('update_user', '')                # 更新人
                        ])
                else:
                    # 如果没有明细，只导出主表数据
                    status_text = {0: '保存', 1: '审核', 2: '关闭'}.get(main_data.get('document_status', 0), '未知')
                    
                    ws.append([
                        main_data.get('yewu_no', ''),                    # 业务编号
                        str(main_data.get('return_date', '')),          # 退货日期
                        main_data.get('customer', ''),                  # 客户名称
                        main_data.get('logistics', ''),                 # 物流公司
                        main_data.get('remark', ''),                    # 备注
                        status_text,                                     # 单据状态
                        '',                                              # 运单号码
                        '',                                              # 东方货号
                        '',                                              # 货品名称
                        '',                                              # 数量
                        '',                                              # 物料编码
                        '',                                              # 物料名称
                        str(main_data.get('create_time', '')),          # 创建时间
                        main_data.get('create_user', ''),               # 创建人
                        str(main_data.get('update_time', '')),          # 更新时间
                        main_data.get('update_user', '')                # 更新人
                    ])
                
                QApplication.processEvents()  # 处理事件，保持UI响应
            
            # 调整列宽
            for column in ws.columns:
                max_length = 0
                column = list(column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
                ws.column_dimensions[column[0].column_letter].width = adjusted_width
            
            # 冻结首行
            ws.freeze_panes = "A2"
            
            # 设置首行样式
            for cell in ws[1]:
                cell.font = Font(name='Calibri', size=9, bold=True)
            
            # 设置其他单元格字体
            for row in ws.iter_rows(min_row=2):
                for cell in row:
                    cell.font = Font(name='Calibri', size=9)
            
            # 完成进度
            progress_dialog.setValue(len(results))
            
            # 保存文件
            wb.save(file_path)
            QMessageBox.information(self, "成功", f"数据导出成功！\n共导出 {len(results)} 条记录")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败：{str(e)}")
        finally:
            if 'progress_dialog' in locals():
                progress_dialog.close()

    def batch_delete_applications(self):
        """批量删除退货申请"""
        selected_rows = []
        for i in range(self.table.rowCount()):
            if self.table.item(i, 0).isSelected():
                selected_rows.append(i)
        
        if not selected_rows:
            QMessageBox.information(self, "提示", "请先选择要删除的记录！")
            return
            
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除选中的 {len(selected_rows)} 条记录吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                success_count = 0
                for row in reversed(selected_rows):  # 从后往前删除
                    return_id = self.get_return_id_from_row(row)
                    if return_id:
                        self.dongfang_db.delete_return_application(return_id)
                        success_count += 1
                
                QMessageBox.information(self, "成功", f"成功删除 {success_count} 条记录！")
                self.load_data()
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"批量删除失败：{str(e)}")

    def audit_return_application(self, row):
        """审核退货申请"""
        yewu_no = self.table.item(row, 0).text()
        
        reply = QMessageBox.question(
            self, "确认审核", 
            f"确定要审核业务编号为 {yewu_no} 的退货申请吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 获取ID
                params = {"yewu_no": yewu_no, "page": 1, "page_size": 1}
                _, applications = self.dongfang_db.get_return_applications(params)
                if applications:
                    self.dongfang_db.audit_return_application(applications[0].get('id'))
                    QMessageBox.information(self, "成功", "审核成功！")
                    self.load_data()
            except Exception as e:
                QMessageBox.critical(self, "错误", f"审核失败：{str(e)}")

    def close_return_application(self, row):
        """关闭退货申请"""
        yewu_no = self.table.item(row, 0).text()
        
        reply = QMessageBox.question(
            self, "确认关闭", 
            f"确定要关闭业务编号为 {yewu_no} 的退货申请吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 获取ID
                params = {"yewu_no": yewu_no, "page": 1, "page_size": 1}
                _, applications = self.dongfang_db.get_return_applications(params)
                if applications:
                    self.dongfang_db.close_return_application(applications[0].get('id'))
                    QMessageBox.information(self, "成功", "关闭成功！")
                    self.load_data()
            except Exception as e:
                QMessageBox.critical(self, "错误", f"关闭失败：{str(e)}")




















