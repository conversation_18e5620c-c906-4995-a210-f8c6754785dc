"""
权限管理器 - 新权限系统核心
完全兼容现有权限系统，提供扩展功能
"""
from typing import List, Dict, Optional, Any
from core.config import get_current_user

class PermissionManager:
    """权限管理器 - 支持菜单、按钮、数据权限"""
    
    def __init__(self, user_permissions: List[str] = None, legacy_mode: bool = True):
        """
        初始化权限管理器
        
        Args:
            user_permissions: 用户权限列表
            legacy_mode: 兼容模式，True时不限制按钮权限
        """
        self.user_permissions = user_permissions or self._get_current_user_permissions()
        self.legacy_mode = legacy_mode
        
    def _get_current_user_permissions(self) -> List[str]:
        """获取当前用户权限"""
        try:
            user = get_current_user()
            if user and hasattr(user, 'permissions'):
                return user.permissions
            return []
        except Exception as e:
            print(f"获取用户权限失败: {e}")
            return []
    
    def has_permission(self, permission_code: str) -> bool:
        """
        检查是否有权限 - 完全兼容现有逻辑
        
        Args:
            permission_code: 权限代码
            
        Returns:
            bool: 是否有权限
        """
        # 超级管理员拥有所有权限
        if 'system:admin' in self.user_permissions:
            return True
        return permission_code in self.user_permissions
    
    def check_button_permission(self, button, permission_code: str) -> bool:
        """
        检查按钮权限并设置状态
        
        Args:
            button: 按钮对象
            permission_code: 权限代码
            
        Returns:
            bool: 是否有权限
        """
        # 兼容模式下不限制按钮
        if self.legacy_mode:
            return True
            
        has_perm = self.has_permission(permission_code)
        
        # 设置按钮状态
        if hasattr(button, 'setEnabled'):
            button.setEnabled(has_perm)
        if hasattr(button, 'setVisible'):
            # 可选：隐藏无权限的按钮
            # button.setVisible(has_perm)
            pass
            
        return has_perm
    
    def apply_button_permissions(self, widget, button_permissions: Dict[str, str], enable: bool = False):
        """
        批量应用按钮权限
        
        Args:
            widget: 包含按钮的窗口对象
            button_permissions: 按钮权限映射 {'button_name': 'permission_code'}
            enable: 是否启用按钮权限控制
        """
        if not enable or self.legacy_mode:
            return
            
        for button_name, permission_code in button_permissions.items():
            button = getattr(widget, button_name, None)
            if button:
                self.check_button_permission(button, permission_code)
    
    def get_data_filter_condition(self, table_name: str, user_field: str = 'create_user') -> str:
        """
        获取数据权限过滤条件
        
        Args:
            table_name: 表名
            user_field: 用户字段名
            
        Returns:
            str: SQL过滤条件
        """
        # 超级管理员查看所有数据
        if 'system:admin' in self.user_permissions:
            return "1=1"
        
        # 默认只能查看自己创建的数据
        current_user = get_current_user()
        if current_user:
            user_name = current_user.get('real_name', current_user.get('username', ''))
            return f"{user_field} = '{user_name}'"
        
        return "1=0"  # 无用户信息时不显示任何数据
    
    def enable_button_permissions(self):
        """启用按钮权限控制"""
        self.legacy_mode = False
    
    def disable_button_permissions(self):
        """禁用按钮权限控制（兼容模式）"""
        self.legacy_mode = True


class PermissionDecorator:
    """权限装饰器"""
    
    @staticmethod
    def require_permission(permission_code: str, show_message: bool = True):
        """
        权限检查装饰器
        
        Args:
            permission_code: 需要的权限代码
            show_message: 是否显示权限不足消息
        """
        def decorator(func):
            def wrapper(self, *args, **kwargs):
                # 获取权限管理器
                permission_manager = getattr(self, 'permission_manager', None)
                if not permission_manager:
                    # 创建临时权限管理器
                    permission_manager = PermissionManager()
                
                # 检查权限
                if not permission_manager.has_permission(permission_code):
                    if show_message:
                        from PyQt6.QtWidgets import QMessageBox
                        QMessageBox.warning(
                            self, 
                            "权限不足", 
                            f"您没有执行此操作的权限\n需要权限：{permission_code}"
                        )
                    return None
                
                return func(self, *args, **kwargs)
            return wrapper
        return decorator


# 全局权限管理器实例（可选使用）
_global_permission_manager = None

def get_permission_manager() -> PermissionManager:
    """获取全局权限管理器"""
    global _global_permission_manager
    if _global_permission_manager is None:
        _global_permission_manager = PermissionManager()
    return _global_permission_manager

def init_permission_manager(user_permissions: List[str] = None, legacy_mode: bool = True):
    """初始化全局权限管理器"""
    global _global_permission_manager
    _global_permission_manager = PermissionManager(user_permissions, legacy_mode)