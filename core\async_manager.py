"""
异步操作管理模块 - 处理异步数据库操作
"""
from PyQt6.QtCore import QObject, QTimer
from core.database import DatabasePool
from core.logger import Logger
import mysql.connector
import time
from core.config import Config

class AsyncManager(QObject):
    def __init__(self):
        super().__init__()
        self.pending_operations = {}
    
    def fetch_data(self, key, callback, db_function, params=None, priority=False):
        """获取数据的异步方法"""
        try:
            if priority:
                # 立即执行数据获取
                result = self._try_execute_operation(db_function, params)
                self._handle_success(callback, result)
                return
            else:
                self._cancel_pending_operation(key)
                self._schedule_operation(key, callback, db_function, params)
        except Exception as e:
            self._handle_error(callback, e)  # 修改为使用 _handle_error
    
    def _handle_error(self, callback, error):
        """统一的错误处理方法"""
        Logger.log_async_operation(
            f"异步操作失败: {str(error)}", 
            'error',
            extra={'error_type': type(error).__name__}
        )
        if callback:
            callback(None)
    
    def _cancel_pending_operation(self, key):
        """取消待处理的操作"""
        if key in self.pending_operations:
            self.pending_operations[key].stop()
            self.pending_operations[key].deleteLater()
    
    def _schedule_operation(self, key, callback, db_function, params):
        """调度异步操作"""
        timer = QTimer()
        timer.setSingleShot(True)
        timer.timeout.connect(
            lambda: self._execute_operation(key, callback, db_function, params)
        )
        self.pending_operations[key] = timer
        timer.start(0)
    
    def _execute_operation(self, key, callback, db_function, params):
        """执行数据库操作"""
        retry_count = 0
        max_retries = Config.DB_CONNECTION_CONFIG['max_reconnect_attempts']
        
        while retry_count < max_retries:
            try:
                result = self._try_execute_operation(db_function, params)
                self._cleanup_operation(key)
                self._handle_success(callback, result)
                return
                
            except mysql.connector.Error as e:
                retry_count += 1
                if not self._handle_database_error(e, retry_count, max_retries, callback):
                    break
                    
            except Exception as e:
                self._handle_general_error(e, callback)
                break
    
    def _try_execute_operation(self, db_function, params):
        """尝试执行数据库操作"""
        db_pool = DatabasePool.get_instance()
        connection = db_pool.get_connection()
        return db_function(params)
    
    def _cleanup_operation(self, key):
        """清理操作"""
        if key in self.pending_operations:
            self.pending_operations[key].deleteLater()
            del self.pending_operations[key]
    
    def _handle_success(self, callback, result):
        """处理成功结果"""
        if callback:
            callback(result)
    
    def _handle_database_error(self, error, retry_count, max_retries, callback):
        """处理数据库错误"""
        Logger.log_async_operation(
            f"数据库操作失败(尝试 {retry_count}/{max_retries}): {str(error)}", 
            'warning',
            extra={
                'error_type': type(error).__name__,
                'retry_count': retry_count
            }
        )
        
        if retry_count < max_retries:
            time.sleep(Config.DB_CONNECTION_CONFIG['reconnect_delay'])
            return True
            
        Logger.log_async_operation(
            f"达到最大重试次数，操作失败: {str(error)}", 
            'error'
        )
        if callback:
            callback(None)
        return False
    
    def _handle_general_error(self, error, callback):
        """处理一般错误"""
        Logger.log_async_operation(
            f"执行异步操作失败: {str(error)}", 
            'error',
            extra={'error_type': type(error).__name__}
        )
        if callback:
            callback(None)