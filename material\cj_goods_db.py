from core.database import Database
from core.logger import Logger
import traceback
from core.config import get_current_user

class CJGoodsDB:
    """东方货品数据库操作类"""
    
    def __init__(self):
        self.db = Database()
        self.current_user = get_current_user()
        
    def get_cj_goods(self, params):
        """获取东方货品列表"""
        try:
            conditions = []
            values = []
            
            if params.get("cj_code"):
                conditions.append("g.cj_code LIKE %s")
                values.append(f"%{params['cj_code']}%")
            
            if params.get("cj_name"):
                conditions.append("g.cj_name LIKE %s")
                values.append(f"%{params['cj_name']}%")
            
            if params.get("material_id"):
                conditions.append("g.material_id LIKE %s")
                values.append(f"%{params['material_id']}%")
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            # 构建查询语句
            count_sql = f"""
                SELECT COUNT(*) as total 
                FROM CJ_GOODS g 
                WHERE {where_clause}
            """
            
            query_sql = f"""
                SELECT 
                    g.*,
                    m.name as material_name
                FROM CJ_GOODS g
                LEFT JOIN materials m ON g.material_id = m.material_id
                WHERE {where_clause}
                ORDER BY g.create_time DESC
                LIMIT %s OFFSET %s
            """
            
            # 分页参数
            page = int(params.get('page', 1))
            page_size = int(params.get('page_size', 20))
            offset = (page - 1) * page_size
            
            # 查询总记录数
            conn = self.db.get_connection()
            with conn.cursor(dictionary=True) as cursor:
                cursor.execute(count_sql, values)
                result = cursor.fetchone()
                total = result["total"] if result else 0
                
                # 查询分页数据
                if total > 0:
                    # 添加分页参数
                    query_values = values + [page_size, offset]
                    cursor.execute(query_sql, query_values)
                    results = cursor.fetchall()
                else:
                    results = []
            
            conn.close()
            return total, results
            
        except Exception as e:
            Logger.log_error("获取东方货品列表失败", error=e, extra={
                "params": params,
                "traceback": traceback.format_exc()
            })
            return 0, []
    
    def add_cj_goods(self, goods_data):
        """添加东方货品"""
        try:
            # 检查必填字段
            required_fields = ['cj_code', 'cj_name', 'material_id']
            for field in required_fields:
                if not goods_data.get(field):
                    return False, f"{field} 不能为空"
            
            conn = self.db.get_connection()
            with conn.cursor(dictionary=True) as cursor:
                # 检查东方货号是否已存在
                check_sql = "SELECT COUNT(*) as count FROM CJ_GOODS WHERE cj_code = %s"
                cursor.execute(check_sql, [goods_data['cj_code']])
                result = cursor.fetchone()
                if result and result['count'] > 0:
                    return False, "东方货号已存在"
                
                # 检查物料是否存在
                check_sql = "SELECT COUNT(*) as count FROM materials WHERE material_id = %s"
                cursor.execute(check_sql, [goods_data['material_id']])
                result = cursor.fetchone()
                if result and result['count'] == 0:
                    return False, "物料编号不存在"
                
                # 构建插入SQL
                sql = """
                    INSERT INTO CJ_GOODS (
                        cj_code, cj_name, material_id, box_spec, split_code, create_user
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s
                    )
                """
                
                values = [
                    goods_data['cj_code'],
                    goods_data['cj_name'],
                    goods_data['material_id'],
                    goods_data.get('box_spec'),
                    goods_data.get('split_code'),
                    self.current_user  # 使用当前用户
                ]
                
                cursor.execute(sql, values)
                conn.commit()
            
            conn.close()
            return True, "添加成功"
            
        except Exception as e:
            Logger.log_error("添加东方货品失败", error=e, extra={
                "goods_data": goods_data,
                "traceback": traceback.format_exc()
            })
            return False, str(e)
    
    def update_cj_goods(self, cj_code, goods_data):
        """更新东方货品"""
        try:
            conn = self.db.get_connection()
            with conn.cursor(dictionary=True) as cursor:
                # 检查东方货号是否存在
                check_sql = "SELECT COUNT(*) as count FROM CJ_GOODS WHERE cj_code = %s"
                cursor.execute(check_sql, [cj_code])
                result = cursor.fetchone()
                if result and result['count'] == 0:
                    return False, "东方货号不存在"
                
                # 如果更新物料编号，检查新物料是否存在
                if goods_data.get('material_id'):
                    check_sql = "SELECT COUNT(*) as count FROM materials WHERE material_id = %s"
                    cursor.execute(check_sql, [goods_data['material_id']])
                    result = cursor.fetchone()
                    if result and result['count'] == 0:
                        return False, "物料编号不存在"
                
                # 构建更新SQL
                update_fields = []
                values = []
                
                for field in ['cj_name', 'material_id', 'box_spec', 'split_code']:
                    if field in goods_data:
                        update_fields.append(f"{field} = %s")
                        values.append(goods_data[field])
                
                if not update_fields:
                    return False, "没有需要更新的字段"
                
                # 添加更新人
                update_fields.append("update_user = %s")
                values.append(self.current_user)  # 使用当前用户
                
                # 添加WHERE条件参数
                values.append(cj_code)
                
                sql = f"""
                    UPDATE CJ_GOODS SET
                        {', '.join(update_fields)}
                    WHERE cj_code = %s
                """
                
                cursor.execute(sql, values)
                conn.commit()
            
            conn.close()
            return True, "更新成功"
            
        except Exception as e:
            Logger.log_error("更新东方货品失败", error=e, extra={
                "cj_code": cj_code,
                "goods_data": goods_data,
                "traceback": traceback.format_exc()
            })
            return False, str(e)
    
    def delete_cj_goods(self, cj_code):
        """删除东方货品"""
        try:
            conn = self.db.get_connection()
            with conn.cursor() as cursor:
                sql = "DELETE FROM CJ_GOODS WHERE cj_code = %s"
                cursor.execute(sql, [cj_code])
                conn.commit()
            
            conn.close()
            return True, "删除成功"
            
        except Exception as e:
            Logger.log_error("删除东方货品失败", error=e, extra={
                "cj_code": cj_code,
                "traceback": traceback.format_exc()
            })
            return False, str(e)

    def batch_add_cj_goods(self, goods_list):
        """批量添加东方货品（使用事务）"""
        try:
            if not goods_list:
                return True, "没有数据需要添加"
            
            conn = self.db.get_connection()
            success_count = 0
            error_records = []
            
            try:
                with conn.cursor(dictionary=True) as cursor:
                    # 开始事务
                    conn.start_transaction()
                    
                    for index, goods_data in enumerate(goods_list):
                        try:
                            # 检查必填字段
                            required_fields = ['cj_code', 'cj_name', 'material_id']
                            for field in required_fields:
                                if not goods_data.get(field):
                                    error_records.append(f"第{index+1}条: {field} 不能为空")
                                    continue
                            
                            # 检查东方货号是否已存在
                            check_sql = "SELECT COUNT(*) as count FROM CJ_GOODS WHERE cj_code = %s"
                            cursor.execute(check_sql, [goods_data['cj_code']])
                            result = cursor.fetchone()
                            if result and result['count'] > 0:
                                error_records.append(f"第{index+1}条: 东方货号 {goods_data['cj_code']} 已存在")
                                continue
                            
                            # 检查物料是否存在且是东方货物
                            check_sql = """
                                SELECT COUNT(*) as count 
                                FROM materials 
                                WHERE material_id = %s AND is_east_material = 1
                            """
                            cursor.execute(check_sql, [goods_data['material_id']])
                            result = cursor.fetchone()
                            if result and result['count'] == 0:
                                error_records.append(f"第{index+1}条: 物料编号 {goods_data['material_id']} 不存在或不是东方货物")
                                continue
                            
                            # 构建插入SQL
                            sql = """
                                INSERT INTO CJ_GOODS (
                                    cj_code, cj_name, material_id, box_spec, split_code, create_user
                                ) VALUES (
                                    %s, %s, %s, %s, %s, %s
                                )
                            """
                            
                            values = [
                                goods_data['cj_code'],
                                goods_data['cj_name'],
                                goods_data['material_id'],
                                goods_data.get('box_spec'),
                                goods_data.get('split_code'),
                                self.current_user
                            ]
                            
                            cursor.execute(sql, values)
                            success_count += 1
                            
                        except Exception as e:
                            error_records.append(f"第{index+1}条: {str(e)}")
                    
                    # 提交事务
                    conn.commit()
                    
            except Exception as e:
                # 回滚事务
                conn.rollback()
                raise e
            
            finally:
                conn.close()
            
            # 返回结果
            if error_records:
                return False, f"批量添加完成。成功：{success_count}条，失败：{len(error_records)}条。错误信息：{' | '.join(error_records)}"
            else:
                return True, f"批量添加成功，共添加 {success_count} 条记录"
                
        except Exception as e:
            Logger.log_error("批量添加东方货品失败", error=e, extra={
                "data_count": len(goods_list) if goods_list else 0,
                "traceback": traceback.format_exc()
            })
            return False, f"批量添加失败：{str(e)}"

    def batch_add_cj_goods_with_validation(self, goods_list):
        """批量添加东方货品（带详细验证和错误分类）"""
        try:
            if not goods_list:
                return {
                    'success': True,
                    'message': '没有数据需要添加',
                    'success_count': 0,
                    'error_count': 0,
                    'errors': []
                }
            
            conn = self.db.get_connection()
            success_count = 0
            error_records = []
            invalid_materials = set()
            duplicate_codes = set()
            validation_errors = []
            
            try:
                with conn.cursor(dictionary=True) as cursor:
                    # 开始事务
                    conn.start_transaction()
                    
                    for index, goods_data in enumerate(goods_list):
                        try:
                            # 验证必填字段
                            required_fields = ['cj_code', 'cj_name', 'material_id']
                            missing_fields = [field for field in required_fields if not goods_data.get(field)]
                            if missing_fields:
                                validation_errors.append({
                                    'index': index + 1,
                                    'type': 'missing_fields',
                                    'message': f"缺少必填字段：{', '.join(missing_fields)}",
                                    'data': goods_data
                                })
                                continue
                            
                            # 检查东方货号是否已存在
                            check_sql = "SELECT COUNT(*) as count FROM CJ_GOODS WHERE cj_code = %s"
                            cursor.execute(check_sql, [goods_data['cj_code']])
                            result = cursor.fetchone()
                            if result and result['count'] > 0:
                                duplicate_codes.add(goods_data['cj_code'])
                                validation_errors.append({
                                    'index': index + 1,
                                    'type': 'duplicate_code',
                                    'message': f"东方货号 {goods_data['cj_code']} 已存在",
                                    'data': goods_data
                                })
                                continue
                            
                            # 检查物料是否存在且是东方货物
                            check_sql = """
                                SELECT COUNT(*) as count 
                                FROM materials 
                                WHERE material_id = %s AND is_east_material = 1
                            """
                            cursor.execute(check_sql, [goods_data['material_id']])
                            result = cursor.fetchone()
                            if result and result['count'] == 0:
                                invalid_materials.add(goods_data['material_id'])
                                validation_errors.append({
                                    'index': index + 1,
                                    'type': 'invalid_material',
                                    'message': f"物料编号 {goods_data['material_id']} 不存在或不是东方货物",
                                    'data': goods_data
                                })
                                continue
                            
                            # 构建插入SQL
                            sql = """
                                INSERT INTO CJ_GOODS (
                                    cj_code, cj_name, material_id, box_spec, split_code, create_user
                                ) VALUES (
                                    %s, %s, %s, %s, %s, %s
                                )
                            """
                            
                            values = [
                                goods_data['cj_code'],
                                goods_data['cj_name'],
                                goods_data['material_id'],
                                goods_data.get('box_spec'),
                                goods_data.get('split_code'),
                                self.current_user
                            ]
                            
                            cursor.execute(sql, values)
                            success_count += 1
                            
                        except Exception as e:
                            validation_errors.append({
                                'index': index + 1,
                                'type': 'database_error',
                                'message': str(e),
                                'data': goods_data
                            })
                    
                    # 提交事务
                    conn.commit()
                    
            except Exception as e:
                # 回滚事务
                conn.rollback()
                raise e
            
            finally:
                conn.close()
            
            # 返回详细结果
            return {
                'success': success_count > 0,
                'message': f"批量添加完成。成功：{success_count}条，失败：{len(validation_errors)}条",
                'success_count': success_count,
                'error_count': len(validation_errors),
                'errors': validation_errors,
                'invalid_materials': list(invalid_materials),
                'duplicate_codes': list(duplicate_codes)
            }
                
        except Exception as e:
            Logger.log_error("批量添加东方货品失败", error=e, extra={
                "data_count": len(goods_list) if goods_list else 0,
                "traceback": traceback.format_exc()
            })
            return {
                'success': False,
                'message': f"批量添加失败：{str(e)}",
                'success_count': 0,
                'error_count': len(goods_list) if goods_list else 0,
                'errors': []
            }