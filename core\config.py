"""
配置管理模块 - 统一管理系统配置
"""
from datetime import datetime
import os


# 全局变量，保存当前登录用户信息
CURRENT_USER = None

def set_current_user(user):
    """
    设置当前登录用户
    user: dict 或对象，包含用户信息
    """
    global CURRENT_USER
    CURRENT_USER = user

def get_current_user():
    """
    获取当前登录用户
    返回: dict 或对象，包含用户信息
    """
    return CURRENT_USER


class Config:
    # 项目基础配置
    PROJECT_NAME = "退货管理系统"
    VERSION = "1.0.0"
    
    # 数据库配置
    DB_CONFIG = {
        'host': '*************',
        'port': 3306,
        'user': 'root',
        'password': 'nsl00123',
        'database': 'migosys',
        'connect_timeout': 300,
        'use_pure': True,
        'get_warnings': True,
        'raise_on_warnings': True,
        'autocommit': True,
        'ssl_disabled': True
    }
    
    # 数据库连接管理配置
    DB_CONNECTION_CONFIG = {
        'ping_interval': 300,        # 每5分钟ping一次
        'connection_timeout': 28800,  # 8小时超时
        'max_reconnect_attempts': 3,  # 最大重试次数
        'reconnect_delay': 5,        # 重试延迟(秒)
        'monitor_idle_time': True,   # 监控空闲时间
        'idle_time_threshold': 60    # 空闲时间阈值(秒)
    }
    
    # 日志配置
    LOG_DIR = "logs"
    LOG_CONFIG = {
        'system_log': os.path.join(LOG_DIR, f"system_{datetime.now().strftime('%Y%m%d')}.log"),
        'db_log': os.path.join(LOG_DIR, f"database_{datetime.now().strftime('%Y%m%d')}.log"),
        'async_log': os.path.join(LOG_DIR, f"async_{datetime.now().strftime('%Y%m%d')}.log"),
        'error_log': os.path.join(LOG_DIR, f"error_{datetime.now().strftime('%Y%m%d')}.log"),
        'level': "INFO",
        'format': "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        'date_format': "%Y-%m-%d %H:%M:%S",
        'max_bytes': 10485760,  # 10MB
        'backup_count': 30,     # 保留30天的日志
        'encoding': 'utf-8',    # 添加默认编码设置
    }
    
    @classmethod
    def init(cls):
        """初始化配置"""
        # 创建日志目录
        if not os.path.exists(cls.LOG_DIR):
            os.makedirs(cls.LOG_DIR)
            
        # 设置默认编码
        import sys
        if sys.getdefaultencoding() != 'utf-8':
            import locale
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
