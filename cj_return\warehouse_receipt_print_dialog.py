from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QPushButton, QLabel, 
                            QScrollArea, QWidget, QHBoxLayout, QMessageBox,
                            QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt6.QtCore import Qt, QRectF, QMarginsF, QPoint
from PyQt6.QtGui import QPainter, QFont, QPageSize, QPageLayout, QRegion, QPixmap
from PyQt6.QtPrintSupport import QPrinter, QPrintDialog, QPrinterInfo

class WarehouseReceiptPrintDialog(QDialog):
    def __init__(self, receipt_data, parent=None):
        super().__init__(parent)
        self.receipt_data = receipt_data
        self.is_ui_setup = False
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI界面"""
        if self.is_ui_setup:
            return
        
        self.setWindowTitle("入库收货单打印预览")
        self.setMinimumSize(1024, 768)
        
        layout = QVBoxLayout(self)
        
        # 预览区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        self.preview_widget = QWidget()
        self.preview_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: none;
            }
        """)
        
        preview_layout = QVBoxLayout(self.preview_widget)
        preview_layout.setSpacing(10)
        preview_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("入库收货单")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                font-family: SimHei;
                background-color: transparent;
                border: none;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        preview_layout.addWidget(title_label)
        
        # 基本信息区域
        info_widget = QWidget()
        info_widget.setStyleSheet("border:none;")
        info_layout = QVBoxLayout(info_widget)
        
        # 第一行
        row1_layout = QHBoxLayout()
        row1_layout.addWidget(self._create_info_pair("收货单号:", self.receipt_data['main']['receipt_no']))
        row1_layout.addWidget(self._create_info_pair("退货申请号:", self.receipt_data['main']['return_yewu_no']))
        info_layout.addLayout(row1_layout)
        info_layout.addSpacing(20)
        
        # 第二行
        row2_layout = QHBoxLayout()
        row2_layout.addWidget(self._create_info_pair("收货日期:", self.receipt_data['main']['receipt_date']))
        row2_layout.addWidget(self._create_info_pair("收货人:", self.receipt_data['main']['receiver']))
        info_layout.addLayout(row2_layout)
        info_layout.addSpacing(20)
        
        # 第三行
        row3_layout = QHBoxLayout()
        row3_layout.addWidget(self._create_info_pair("收货仓库:", self.receipt_data['main']['warehouse']))
        row3_layout.addWidget(self._create_info_pair("收货状态:", self.receipt_data['main']['status']))
        info_layout.addLayout(row3_layout)
        info_layout.addSpacing(20)
        
        # 数量汇总行 - 使用格式化方法
        row4_layout = QHBoxLayout()
        row4_layout.addWidget(self._create_info_pair("总数量:", self._format_quantity(self.receipt_data['main']['total_quantity'])))
        row4_layout.addWidget(self._create_info_pair("原料数量:", self._format_quantity(self.receipt_data['main']['total_raw_quantity'])))
        info_layout.addLayout(row4_layout)
        info_layout.addSpacing(20)
        
        row5_layout = QHBoxLayout()
        row5_layout.addWidget(self._create_info_pair("变形数量:", self._format_quantity(self.receipt_data['main']['total_deformed_quantity'])))
        row5_layout.addWidget(self._create_info_pair("待报废数量:", self._format_quantity(self.receipt_data['main']['total_to_scrap_quantity'])))
        info_layout.addLayout(row5_layout)
        info_layout.addSpacing(20)
        
        row6_layout = QHBoxLayout()
        row6_layout.addWidget(self._create_info_pair("报废数量:", self._format_quantity(self.receipt_data['main']['total_scrapped_quantity'])))
        if self.receipt_data['main']['remark']:
            row6_layout.addWidget(self._create_info_pair("备注:", self.receipt_data['main']['remark']))
        info_layout.addLayout(row6_layout)

        preview_layout.addWidget(info_widget)
        
        # 明细表格
        table_widget = self._create_detail_table(self.receipt_data['details'])
        preview_layout.addWidget(table_widget)
        
        # 签名区域
        signature_widget = QWidget()
        signature_layout = QHBoxLayout(signature_widget)
        signature_layout.addWidget(QLabel("收货人签名: _________________"))
        signature_layout.addStretch()
        signature_layout.addWidget(QLabel("仓库管理员签名: _________________")) 
        preview_layout.addWidget(signature_widget)
        
        scroll_area.setWidget(self.preview_widget)
        layout.addWidget(scroll_area)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        print_btn = QPushButton("打印")
        print_btn.clicked.connect(self.print_document)
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        
        button_layout.addStretch()
        button_layout.addWidget(print_btn)
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)
        
        self.is_ui_setup = True
        
    def _create_info_pair(self, label, value):
        """创建标签值对"""
        widget = QWidget()
        widget.setStyleSheet("border:none;")
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("font-weight: bold;")
        value_widget = QLabel(str(value))
        
        layout.addWidget(label_widget)
        layout.addWidget(value_widget)
        layout.addStretch()
        
        return widget
        
    def _create_detail_table(self, details):
        """创建明细表格"""
        table = QTableWidget()
        table.setColumnCount(13)  # 从12列增加到13列
        table.setHorizontalHeaderLabels([
            "序号", "运单号", "物料编码", "物料名称", "退货数量", "实物", "批次号",
            "拆分数量", "收货数量", "原料数量", "变形数量", "待报废数量", "报废数量"
        ])
        
        # 设置表格数据
        table.setRowCount(len(details))
        for row, detail in enumerate(details):
            table.setItem(row, 0, QTableWidgetItem(str(row + 1)))
            table.setItem(row, 1, QTableWidgetItem(detail['waybill_no']))
            table.setItem(row, 2, QTableWidgetItem(detail['update_material_code'] or detail['material_code']))
            table.setItem(row, 3, QTableWidgetItem(detail['material_name']))
            table.setItem(row, 4, QTableWidgetItem(self._format_quantity(detail['return_quantity'])))  # 退货数量
            table.setItem(row, 5, QTableWidgetItem(''))  # 实物字段 - 空白显示
            table.setItem(row, 6, QTableWidgetItem(detail['batch_no']))  # 批次号
            table.setItem(row, 7, QTableWidgetItem(self._format_quantity(detail['split_quantity'])))  # 拆分数量
            table.setItem(row, 8, QTableWidgetItem(self._format_quantity(detail['received_quantity'])))  # 收货数量
            table.setItem(row, 9, QTableWidgetItem(self._format_quantity(detail['raw_quantity'])))
            table.setItem(row, 10, QTableWidgetItem(self._format_quantity(detail['deformed_quantity'])))
            table.setItem(row, 11, QTableWidgetItem(self._format_quantity(detail['to_scrap_quantity'])))
            table.setItem(row, 12, QTableWidgetItem(self._format_quantity(detail['scrapped_quantity'])))
        
        # 设置表格样式
        header = table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        
        table.setStyleSheet("""
            QTableWidget {
                background-color: transparent;
                border: none;
                gridline-color: transparent;
            }
            QHeaderView::section {
                background-color: transparent;
                padding: 4px;
                border: none;
                border-bottom: 1px solid black;
                font-weight: bold;
            }
            QTableWidget::item {
                background-color: transparent;
                border: none;
                border-bottom: 1px solid black;
                padding: 4px;
            }
        """)
        
        table.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        table.verticalHeader().setVisible(False)
        
        return table

    def _format_quantity(self, value):
        """格式化数量显示，0显示为空白"""
        try:
            if value is None or value == '' or str(value).strip() == '':
                return ''
            
            # 转换为数字
            num_value = float(str(value))
            
            # 如果是0，返回空字符串
            if num_value == 0:
                return ''
            
            # 如果是整数，显示为整数
            if num_value == int(num_value):
                return str(int(num_value))
            else:
                return str(num_value)
                
        except (ValueError, TypeError):
            return str(value) if value else ''

    def _do_print(self, show_success_message=True):
        """内部共用的打印实现方法"""
        try:
            # 获取默认打印机
            printer_info = QPrinterInfo.defaultPrinter()
            if not printer_info:
                QMessageBox.warning(self, "警告", "未找到默认打印机")
                return False
            
            # 创建高分辨率打印机对象
            printer = QPrinter(printer_info, QPrinter.PrinterMode.HighResolution)
            
            # 设置打印机参数
            printer.setPageOrientation(QPageLayout.Orientation.Landscape)
            printer.setPageSize(QPageSize(QPageSize.PageSizeId.A4))
            
            # 设置页面边距
            margins = QMarginsF(0, 0, 0, 0)
            layout = QPageLayout(QPageSize(QPageSize.PageSizeId.A4),
                               QPageLayout.Orientation.Landscape,
                               margins,
                               QPageLayout.Unit.Millimeter)
            printer.setPageLayout(layout)
            
            printer.setFullPage(True)
            printer.setColorMode(QPrinter.ColorMode.GrayScale)
            
            # 直接开始打印
            painter = QPainter()
            if painter.begin(printer):
                # 获取页面大小
                page_rect = printer.pageRect(QPrinter.Unit.DevicePixel)
                
                # 调整缩放比例
                scale_factor = (page_rect.width() * 0.95) / self.preview_widget.width()
                painter.scale(scale_factor, scale_factor)
                
                # 移动绘制位置，确保内容居中
                x_offset = (page_rect.width() / scale_factor - self.preview_widget.width()) / 2
                y_offset = (page_rect.height() / scale_factor - self.preview_widget.height()) / 2
                painter.translate(x_offset, y_offset)
                
                # 打印预览窗口的内容
                self.preview_widget.render(painter)
                painter.end()
                
                if show_success_message:
                    QMessageBox.information(self, "成功", "打印成功！")
                return True
            return False
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打印失败：{str(e)}")
            return False

    def print_document(self):
        """交互式打印方法"""
        try:
            if self._do_print():
                self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打印失败：{str(e)}")

    def direct_print(self, show_success_message=True):
        """直接打印方法"""
        try:
            if self._do_print(show_success_message):
                self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"直接打印失败：{str(e)}")



