"""
数据库权限测试脚本
测试权限数据的读取和验证
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.db_manager import DatabaseManager

def test_permission_data():
    """测试权限数据读取"""
    print("=== 测试数据库权限数据 ===")
    
    try:
        db = DatabaseManager()
        
        # 1. 查询所有权限类型
        query = """
        SELECT 
            permission_name,
            permission_code,
            permission_type,
            CASE permission_type 
                WHEN 1 THEN '菜单权限'
                WHEN 2 THEN '按钮权限' 
                WHEN 3 THEN '数据权限'
                ELSE '未知类型'
            END as type_name,
            status
        FROM permission 
        WHERE status = 1
        ORDER BY permission_type, permission_code
        """
        
        permissions = db.execute_query(query)
        
        print(f"数据库中共有 {len(permissions)} 个权限:")
        
        menu_count = 0
        button_count = 0
        data_count = 0
        
        for perm in permissions:
            print(f"  {perm['permission_name']} ({perm['permission_code']}) - {perm['type_name']}")
            
            if perm['permission_type'] == 1:
                menu_count += 1
            elif perm['permission_type'] == 2:
                button_count += 1
            elif perm['permission_type'] == 3:
                data_count += 1
        
        print(f"\n权限统计:")
        print(f"  菜单权限: {menu_count} 个")
        print(f"  按钮权限: {button_count} 个")
        print(f"  数据权限: {data_count} 个")
        
        # 2. 测试用户权限查询
        user_permission_query = """
        SELECT DISTINCT p.permission_code
        FROM user u
        JOIN user_role ur ON u.user_id = ur.user_id
        JOIN role_permission rp ON ur.role_id = rp.role_id
        JOIN permission p ON rp.permission_id = p.permission_id
        WHERE u.username = 'admin' AND p.status = 1
        """
        
        user_permissions = db.execute_query(user_permission_query)
        
        if user_permissions:
            print(f"\n管理员用户权限:")
            for perm in user_permissions:
                print(f"  {perm['permission_code']}")
        else:
            print(f"\n⚠️  未找到管理员用户权限，可能需要配置角色权限关联")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库权限测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_permission_manager_with_db():
    """测试权限管理器与数据库集成"""
    print("\n=== 测试权限管理器数据库集成 ===")
    
    try:
        from system.permission_manager import PermissionManager
        
        # 模拟从数据库获取的用户权限
        mock_permissions = [
            'material:view', 'material:add', 'material:edit',
            'bom:view', 'bom:add',
            'user:view'
        ]
        
        pm = PermissionManager(mock_permissions, legacy_mode=True)
        
        # 测试各种权限
        test_cases = [
            ('material:add', True),
            ('material:delete', False),
            ('bom:view', True),
            ('bom:delete', False),
            ('user:add', False)
        ]
        
        print("权限测试结果:")
        all_passed = True
        
        for permission_code, expected in test_cases:
            result = pm.has_permission(permission_code)
            status = "✅" if result == expected else "❌"
            print(f"  {status} {permission_code}: {result} (期望: {expected})")
            
            if result != expected:
                all_passed = False
        
        if all_passed:
            print("\n✅ 权限管理器数据库集成测试通过！")
        else:
            print("\n❌ 权限管理器数据库集成测试失败！")
            
        return all_passed
        
    except Exception as e:
        print(f"❌ 权限管理器数据库集成测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("数据库权限测试开始...")
    
    success1 = test_permission_data()
    success2 = test_permission_manager_with_db()
    
    if success1 and success2:
        print("\n🎉 所有数据库权限测试通过！")
    else:
        print("\n⚠️  部分测试失败，请检查数据库配置")