"""
异常管理模块
定义系统所有自定义异常
"""

class ReturnSystemError(Exception):
    """系统基础异常类"""
    def __init__(self, message, code=None):
        super().__init__(message)
        self.code = code
        self.message = message

class DatabaseError(ReturnSystemError):
    """数据库相关异常"""
    pass

class CacheError(ReturnSystemError):
    """缓存相关异常"""
    pass

class UIError(ReturnSystemError):
    """UI相关异常"""
    pass

class BusinessError(ReturnSystemError):
    """业务逻辑相关异常"""
    pass

class ConfigError(ReturnSystemError):
    """配置相关异常"""
    pass

def handle_exception(e):
    """统一异常处理"""
    from .logger import Logger
    logger = Logger.get_logger()
    
    if isinstance(e, ReturnSystemError):
        logger.error(f"{e.__class__.__name__}: {e.message}")
        return e.message
    else:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return "系统错误，请联系管理员" 