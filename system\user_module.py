import hashlib
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                           QTableWidgetItem, QPushButton, QHeaderView, QLabel, 
                           QLineEdit, QComboBox, QDialog, QFormLayout, QMessageBox,
                           QSpinBox, QCheckBox, QDateEdit, QDialogButtonBox)
from PyQt6.QtCore import Qt, QDateTime
from PyQt6.QtGui import QIcon, QColor

class UserManagementModule(QWidget):
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 搜索区域
        search_layout = QHBoxLayout()
        
        self.username_search = QLineEdit()
        self.username_search.setPlaceholderText("用户名")
        self.username_search.setMaximumWidth(150)
        
        self.realname_search = QLineEdit()
        self.realname_search.setPlaceholderText("姓名")
        self.realname_search.setMaximumWidth(150)
        
        self.status_filter = QComboBox()
        self.status_filter.addItem("全部状态", -1)
        self.status_filter.addItem("启用", 1)
        self.status_filter.addItem("禁用", 0)
        self.status_filter.setMaximumWidth(100)
        
        search_button = QPushButton("搜索")
        search_button.clicked.connect(self.search_users)
        
        reset_button = QPushButton("重置")
        reset_button.clicked.connect(self.reset_search)
        
        search_layout.addWidget(QLabel("用户名:"))
        search_layout.addWidget(self.username_search)
        search_layout.addWidget(QLabel("姓名:"))
        search_layout.addWidget(self.realname_search)
        search_layout.addWidget(QLabel("状态:"))
        search_layout.addWidget(self.status_filter)
        search_layout.addWidget(search_button)
        search_layout.addWidget(reset_button)
        search_layout.addStretch()
        
        # 操作按钮区域
        button_layout = QHBoxLayout()
        
        add_button = QPushButton("添加用户")
        add_button.setIcon(QIcon("resources/icons/add.png"))
        add_button.clicked.connect(self.add_user)
        
        edit_button = QPushButton("编辑用户")
        edit_button.setIcon(QIcon("resources/icons/edit.png"))
        edit_button.clicked.connect(self.edit_user)
        
        delete_button = QPushButton("删除用户")
        delete_button.setIcon(QIcon("resources/icons/delete.png"))
        delete_button.clicked.connect(self.delete_user)
        
        reset_pwd_button = QPushButton("重置密码")
        reset_pwd_button.setIcon(QIcon("resources/icons/reset.png"))
        reset_pwd_button.clicked.connect(self.reset_password)
        
        refresh_button = QPushButton("刷新")
        refresh_button.setIcon(QIcon("resources/icons/refresh.png"))
        refresh_button.clicked.connect(self.search_users)
        
        button_layout.addWidget(add_button)
        button_layout.addWidget(edit_button)
        button_layout.addWidget(delete_button)
        button_layout.addWidget(reset_pwd_button)
        button_layout.addStretch()
        button_layout.addWidget(refresh_button)
        
        # 用户表格
        self.user_table = QTableWidget()
        self.user_table.setColumnCount(7)
        self.user_table.setHorizontalHeaderLabels([
            "ID", "用户名", "姓名", "电子邮件", "电话", "状态", "最后登录时间"
        ])
        
        # 设置表格样式
        self.user_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.user_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.user_table.setAlternatingRowColors(True)
        
        # 禁用表格编辑，只允许双击弹出编辑窗口
        self.user_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 连接双击事件
        self.user_table.cellDoubleClicked.connect(self.on_cell_double_clicked)
        
        self.user_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: #fff;
            }
            QTableWidget::item:selected {
                background-color: #e0f2fe;
                color: #000;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 5px;
                border: none;
                border-right: 1px solid #ddd;
                border-bottom: 1px solid #ddd;
                font-weight: bold;
            }
        """)
        
        # 自动调整表格
        header = self.user_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Stretch)
        
        # 布局
        layout.addLayout(search_layout)
        layout.addLayout(button_layout)
        layout.addWidget(self.user_table)
        
        self.setLayout(layout)
        
        # 初始加载数据
        self.search_users()
    
    def search_users(self):
        """搜索用户"""
        try:
            username = self.username_search.text().strip()
            realname = self.realname_search.text().strip()
            status = self.status_filter.currentData()
            
            # 构建查询条件
            query = """
            SELECT 
                user_id, username, real_name, email, phone, status, 
                DATE_FORMAT(last_login_time, '%Y-%m-%d %H:%i:%s') as last_login_time,
                last_login_ip
            FROM user
            WHERE 1=1
            """
            params = []
            
            if username:
                query += " AND username LIKE %s"
                params.append(f"%{username}%")
            
            if realname:
                query += " AND real_name LIKE %s"
                params.append(f"%{realname}%")
            
            if status != -1:
                query += " AND status = %s"
                params.append(status)
            
            query += " ORDER BY user_id ASC"
            
            # 执行查询
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            results = db_manager.execute_query(query, tuple(params) if params else None)
            
            # 清空表格
            self.user_table.setRowCount(0)
            
            # 填充数据
            if results:
                self.user_table.setRowCount(len(results))
                for row, user in enumerate(results):
                    self.user_table.setItem(row, 0, QTableWidgetItem(str(user['user_id'])))
                    self.user_table.setItem(row, 1, QTableWidgetItem(user['username']))
                    self.user_table.setItem(row, 2, QTableWidgetItem(user['real_name']))
                    self.user_table.setItem(row, 3, QTableWidgetItem(user['email'] or ""))
                    self.user_table.setItem(row, 4, QTableWidgetItem(user['phone'] or ""))
                    
                    status_item = QTableWidgetItem("启用" if user['status'] == 1 else "禁用")
                    status_item.setForeground(QColor("#2ecc71" if user['status'] == 1 else "#e74c3c"))
                    self.user_table.setItem(row, 5, status_item)
                    
                    self.user_table.setItem(row, 6, QTableWidgetItem(user['last_login_time'] or ""))
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询用户失败：{str(e)}")
    
    def reset_search(self):
        """重置搜索条件"""
        self.username_search.clear()
        self.realname_search.clear()
        self.status_filter.setCurrentIndex(0)
        self.search_users()
    
    def on_cell_double_clicked(self, row, column):
        """处理表格双击事件"""
        if row >= 0:
            self.edit_user()
    
    def add_user(self):
        """添加用户"""
        dialog = UserDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 添加用户
            try:
                user_data = dialog.get_user_data()
                
                # 检查用户名是否已存在
                from modules.db_manager import DatabaseManager
                db_manager = DatabaseManager()
                check_query = "SELECT COUNT(*) as count FROM user WHERE username = %s"
                result = db_manager.execute_query(check_query, (user_data['username'],))
                
                if result and result[0]['count'] > 0:
                    QMessageBox.warning(self, "警告", "用户名已存在，请使用其他用户名")
                    return
                
                # 密码加密
                password_md5 = hashlib.md5(user_data['password'].encode()).hexdigest()
                
                # 插入用户
                insert_query = """
                INSERT INTO user (username, password, real_name, email, phone, status)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                params = (
                    user_data['username'],
                    password_md5,
                    user_data['real_name'],
                    user_data['email'],
                    user_data['phone'],
                    user_data['status']
                )
                
                db_manager.execute_update(insert_query, params)
                
                # 如果有角色选择，添加用户角色关联
                if 'roles' in user_data and user_data['roles']:
                    # 获取新增用户的ID
                    user_query = "SELECT user_id FROM user WHERE username = %s"
                    user_result = db_manager.execute_query(user_query, (user_data['username'],))
                    
                    if user_result:
                        user_id = user_result[0]['user_id']
                        
                        # 批量插入用户角色关联
                        role_query = "INSERT INTO user_role (user_id, role_id) VALUES (%s, %s)"
                        role_params = [(user_id, role_id) for role_id in user_data['roles']]
                        
                        db_manager.executemany_insert(role_query, role_params)
                
                QMessageBox.information(self, "成功", "用户添加成功")
                self.search_users()
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"添加用户失败：{str(e)}")
    
    def edit_user(self):
        """编辑用户"""
        selected_rows = self.user_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要编辑的用户")
            return
        
        row = selected_rows[0].row()
        user_id = int(self.user_table.item(row, 0).text())
        
        try:
            # 获取用户信息
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            query = """
            SELECT u.*, GROUP_CONCAT(ur.role_id) as role_ids
            FROM user u
            LEFT JOIN user_role ur ON u.user_id = ur.user_id
            WHERE u.user_id = %s
            GROUP BY u.user_id
            """
            result = db_manager.execute_query(query, (user_id,))
            
            if not result:
                QMessageBox.warning(self, "警告", "未找到用户信息")
                return
            
            user = result[0]
            
            # 获取用户的角色ID列表
            role_ids = []
            if user['role_ids']:
                role_ids = [int(id) for id in user['role_ids'].split(',')]
            
            # 打开编辑对话框
            dialog = UserDialog(self, user, role_ids)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # 更新用户
                user_data = dialog.get_user_data()
                
                # 更新用户基本信息
                update_query = """
                UPDATE user
                SET real_name = %s, email = %s, phone = %s, status = %s
                WHERE user_id = %s
                """
                params = (
                    user_data['real_name'],
                    user_data['email'],
                    user_data['phone'],
                    user_data['status'],
                    user_id
                )
                
                db_manager.execute_update(update_query, params)
                
                # 更新用户角色关联
                if 'roles' in user_data:
                    # 先删除原有角色关联
                    delete_roles_query = "DELETE FROM user_role WHERE user_id = %s"
                    db_manager.execute_update(delete_roles_query, (user_id,))
                    
                    # 添加新角色关联
                    if user_data['roles']:
                        role_query = "INSERT INTO user_role (user_id, role_id) VALUES (%s, %s)"
                        role_params = [(user_id, role_id) for role_id in user_data['roles']]
                        db_manager.executemany_insert(role_query, role_params)
                
                QMessageBox.information(self, "成功", "用户信息更新成功")
                self.search_users()
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑用户失败：{str(e)}")
    
    def delete_user(self):
        """删除用户"""
        selected_rows = self.user_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要删除的用户")
            return
        
        row = selected_rows[0].row()
        user_id = int(self.user_table.item(row, 0).text())
        username = self.user_table.item(row, 1).text()
        
        # 确认删除
        reply = QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要删除用户{username}吗？\n此操作不可恢复！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                from modules.db_manager import DatabaseManager
                db_manager = DatabaseManager()
                
                # 删除用户角色关联
                db_manager.execute_update("DELETE FROM user_role WHERE user_id = %s", (user_id,))
                
                # 删除用户
                db_manager.execute_update("DELETE FROM user WHERE user_id = %s", (user_id,))
                
                QMessageBox.information(self, "成功", "用户删除成功")
                self.search_users()
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除用户失败：{str(e)}")
    
    def reset_password(self):
        """重置用户密码"""
        selected_rows = self.user_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要重置密码的用户")
            return
        
        row = selected_rows[0].row()
        user_id = int(self.user_table.item(row, 0).text())
        username = self.user_table.item(row, 1).text()
        # 确认重置
        reply = QMessageBox.question(
            self, 
            "确认重置密码", 
            f"确定要重置用户{username}的密码吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 默认密码123456
                default_password = "123456"
                password_md5 = hashlib.md5(default_password.encode()).hexdigest()
                
                from modules.db_manager import DatabaseManager
                db_manager = DatabaseManager()
                
                # 更新密码
                db_manager.execute_update(
                    "UPDATE user SET password = %s WHERE user_id = %s",
                    (password_md5, user_id)
                )
                
                QMessageBox.information(
                    self, 
                    "成功", 
                    f"密码重置成功，新密码为：{default_password}\n请提醒用户尽快修改密码"
                )
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"重置密码失败：{str(e)}")

class UserDialog(QDialog):
    def __init__(self, parent=None, user=None, role_ids=None):
        super().__init__(parent)
        self.user = user
        self.role_ids = role_ids or []
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        if self.user:
            self.setWindowTitle("编辑用户")
        else:
            self.setWindowTitle("添加用户")
            
        self.setMinimumWidth(400)
        layout = QVBoxLayout(self)
        form_layout = QFormLayout()
        
        # 用户名
        self.username_input = QLineEdit()
        if self.user:
            self.username_input.setText(self.user['username'])
            self.username_input.setReadOnly(True)
        form_layout.addRow("用户名:", self.username_input)
        
        # 密码（仅在新增时显示）
        if not self.user:
            self.password_input = QLineEdit()
            self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
            form_layout.addRow("密码:", self.password_input)
            
            self.confirm_password_input = QLineEdit()
            self.confirm_password_input.setEchoMode(QLineEdit.EchoMode.Password)
            form_layout.addRow("确认密码:", self.confirm_password_input)
        
        # 姓名
        self.realname_input = QLineEdit()
        if self.user:
            self.realname_input.setText(self.user['real_name'])
        form_layout.addRow("姓名:", self.realname_input)
        
        # 电子邮件
        self.email_input = QLineEdit()
        if self.user and self.user['email']:
            self.email_input.setText(self.user['email'])
        form_layout.addRow("电子邮件:", self.email_input)
        
        # 电话
        self.phone_input = QLineEdit()
        if self.user and self.user['phone']:
            self.phone_input.setText(self.user['phone'])
        form_layout.addRow("电话:", self.phone_input)
        
        # 状态
        self.status_input = QComboBox()
        self.status_input.addItem("启用", 1)
        self.status_input.addItem("禁用", 0)
        if self.user:
            index = 0 if self.user['status'] == 1 else 1
            self.status_input.setCurrentIndex(index)
        form_layout.addRow("状态:", self.status_input)
        
        # 角色选择
        self.role_layout = QVBoxLayout()
        self.role_checkboxes = []
        
        role_label = QLabel("分配角色:")
        form_layout.addRow(role_label)
        
        # 获取所有角色
        try:
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            query = "SELECT role_id, role_name FROM role ORDER BY role_id"
            roles = db_manager.execute_query(query)
            
            if roles:
                for role in roles:
                    checkbox = QCheckBox(role['role_name'])
                    checkbox.setProperty("role_id", role['role_id'])
                    
                    # 如果是编辑模式，设置选中状态
                    if self.user and role['role_id'] in self.role_ids:
                        checkbox.setChecked(True)
                        
                    self.role_checkboxes.append(checkbox)
                    self.role_layout.addWidget(checkbox)
            
        except Exception as e:
            print(f"获取角色列表失败: {str(e)}")
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        # 布局
        layout.addLayout(form_layout)
        layout.addLayout(self.role_layout)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def accept(self):
        """验证并接受对话框"""
        if not self.validate():
            return
        super().accept()
    
    def validate(self):
        """验证表单"""
        username = self.username_input.text().strip()
        if not username:
            QMessageBox.warning(self, "警告", "用户名不能为空")
            return False
        
        if not self.user:  # 新增用户时验证密码
            password = self.password_input.text()
            confirm_password = self.confirm_password_input.text()
            
            if not password:
                QMessageBox.warning(self, "警告", "密码不能为空")
                return False
                
            if password != confirm_password:
                QMessageBox.warning(self, "警告", "两次输入的密码不一致")
                return False
        
        realname = self.realname_input.text().strip()
        if not realname:
            QMessageBox.warning(self, "警告", "姓名不能为空")
            return False
        
        return True
    
    def get_user_data(self):
        """获取用户数据"""
        data = {
            'username': self.username_input.text().strip(),
            'real_name': self.realname_input.text().strip(),
            'email': self.email_input.text().strip(),
            'phone': self.phone_input.text().strip(),
            'status': self.status_input.currentData(),
            'roles': [cb.property("role_id") for cb in self.role_checkboxes if cb.isChecked()]
        }
        
        if not self.user:  # 新增用户时添加密码
            data['password'] = self.password_input.text()
        
        return data 