from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                           QTextEdit, QLabel, QMessageBox, QCheckBox)
from PyQt6.QtCore import Qt

class BatchSearchDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.source_order_nos = []
        self.is_exact_match = True  # 默认精确匹配
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("批量搜索原始单号")
        self.setMinimumWidth(400)
        self.setMinimumHeight(300)
        
        layout = QVBoxLayout(self)
        
        # 添加说明标签
        tip_label = QLabel(
            "请输入要搜索的原始单号，每行一个单号\n"
            "支持直接粘贴Excel列数据"
        )
        tip_label.setStyleSheet("color: #666666;")
        layout.addWidget(tip_label)
        
        # 添加文本输入框
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText("在此粘贴单号...")
        layout.addWidget(self.text_edit)
        
        # 添加搜索模式选择
        self.exact_match_checkbox = QCheckBox("精确匹配模式")
        self.exact_match_checkbox.setChecked(True)
        self.exact_match_checkbox.setToolTip("选中为精确匹配，取消选中为模糊匹配")
        layout.addWidget(self.exact_match_checkbox)
        
        # 按钮区域
        btn_layout = QHBoxLayout()
        
        # 清空按钮
        clear_btn = QPushButton("清空")
        clear_btn.clicked.connect(self.text_edit.clear)
        
        # 搜索按钮
        search_btn = QPushButton("搜索")
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 5px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
            QPushButton:pressed {
                background-color: #096dd9;
            }
        """)
        search_btn.clicked.connect(self.accept)
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        
        btn_layout.addWidget(clear_btn)
        btn_layout.addStretch()
        btn_layout.addWidget(search_btn)
        btn_layout.addWidget(cancel_btn)
        
        layout.addLayout(btn_layout)
        
    def accept(self):
        """确认按钮点击事件"""
        # 获取文本并处理
        text = self.text_edit.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "警告", "请输入要搜索的单号！")
            return
            
        # 分割文本并去重
        self.source_order_nos = list(set(
            line.strip() 
            for line in text.split('\n') 
            if line.strip()
        ))
        
        if not self.source_order_nos:
            QMessageBox.warning(self, "警告", "未找到有效的单号！")
            return
            
        super().accept()
    
    def get_source_order_nos(self):
        """获取处理后的单号列表"""
        return self.source_order_nos 