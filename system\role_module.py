from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                           QTableWidgetItem, QPushButton, QHeaderView, QLabel, 
                           QLineEdit, QComboBox, QDialog, QFormLayout, QMessageBox,
                           QCheckBox, QDialogButtonBox, QTreeWidget, QTreeWidgetItem)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon, QColor

class RoleManagementModule(QWidget):
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 搜索区域
        search_layout = QHBoxLayout()
        
        self.role_name_search = QLineEdit()
        self.role_name_search.setPlaceholderText("角色名称")
        self.role_name_search.setMaximumWidth(200)
        
        self.status_filter = QComboBox()
        self.status_filter.addItem("全部状态", -1)
        self.status_filter.addItem("启用", 1)
        self.status_filter.addItem("禁用", 0)
        self.status_filter.setMaximumWidth(100)
        
        search_button = QPushButton("搜索")
        search_button.clicked.connect(self.search_roles)
        
        reset_button = QPushButton("重置")
        reset_button.clicked.connect(self.reset_search)
        
        search_layout.addWidget(QLabel("角色名称:"))
        search_layout.addWidget(self.role_name_search)
        search_layout.addWidget(QLabel("状态:"))
        search_layout.addWidget(self.status_filter)
        search_layout.addWidget(search_button)
        search_layout.addWidget(reset_button)
        search_layout.addStretch()
        
        # 操作按钮区域
        button_layout = QHBoxLayout()
        
        add_button = QPushButton("添加角色")
        add_button.setIcon(QIcon("resources/icons/add.png"))
        add_button.clicked.connect(self.add_role)
        
        edit_button = QPushButton("编辑角色")
        edit_button.setIcon(QIcon("resources/icons/edit.png"))
        edit_button.clicked.connect(self.edit_role)
        
        delete_button = QPushButton("删除角色")
        delete_button.setIcon(QIcon("resources/icons/delete.png"))
        delete_button.clicked.connect(self.delete_role)
        
        assign_perm_button = QPushButton("分配权限")
        assign_perm_button.setIcon(QIcon("resources/icons/permission.png"))
        assign_perm_button.clicked.connect(self.assign_permissions)
        
        refresh_button = QPushButton("刷新")
        refresh_button.setIcon(QIcon("resources/icons/refresh.png"))
        refresh_button.clicked.connect(self.search_roles)
        
        button_layout.addWidget(add_button)
        button_layout.addWidget(edit_button)
        button_layout.addWidget(delete_button)
        button_layout.addWidget(assign_perm_button)
        button_layout.addStretch()
        button_layout.addWidget(refresh_button)
        
        # 角色表格
        self.role_table = QTableWidget()
        self.role_table.setColumnCount(4)
        self.role_table.setHorizontalHeaderLabels([
            "ID", "角色名称", "描述", "状态"
        ])
        
        # 设置表格样式
        self.role_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.role_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.role_table.setAlternatingRowColors(True)
        
        # 禁用表格编辑，只允许双击弹出编辑窗口
        self.role_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 连接双击事件
        self.role_table.cellDoubleClicked.connect(self.on_cell_double_clicked)
        
        self.role_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: #fff;
            }
            QTableWidget::item:selected {
                background-color: #e0f2fe;
                color: #000;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 5px;
                border: none;
                border-right: 1px solid #ddd;
                border-bottom: 1px solid #ddd;
                font-weight: bold;
            }
        """)
        
        # 自动调整表格
        header = self.role_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        
        # 布局
        layout.addLayout(search_layout)
        layout.addLayout(button_layout)
        layout.addWidget(self.role_table)
        
        self.setLayout(layout)
        
        # 初始加载数据
        self.search_roles()
    
    def search_roles(self):
        """搜索角色"""
        try:
            role_name = self.role_name_search.text().strip()
            status = self.status_filter.currentData()
            
            # 构建查询条件
            query = """
            SELECT role_id, role_name, role_desc, status
            FROM role
            WHERE 1=1
            """
            params = []
            
            if role_name:
                query += " AND role_name LIKE %s"
                params.append(f"%{role_name}%")
            
            if status != -1:
                query += " AND status = %s"
                params.append(status)
            
            query += " ORDER BY role_id ASC"
            
            # 执行查询
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            results = db_manager.execute_query(query, tuple(params) if params else None)
            
            # 清空表格
            self.role_table.setRowCount(0)
            
            # 填充数据
            if results:
                self.role_table.setRowCount(len(results))
                for row, role in enumerate(results):
                    self.role_table.setItem(row, 0, QTableWidgetItem(str(role['role_id'])))
                    self.role_table.setItem(row, 1, QTableWidgetItem(role['role_name']))
                    self.role_table.setItem(row, 2, QTableWidgetItem(role['role_desc'] or ""))
                    
                    status_item = QTableWidgetItem("启用" if role['status'] == 1 else "禁用")
                    status_item.setForeground(QColor("#2ecc71" if role['status'] == 1 else "#e74c3c"))
                    self.role_table.setItem(row, 3, status_item)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询角色失败：{str(e)}")
    
    def reset_search(self):
        """重置搜索条件"""
        self.role_name_search.clear()
        self.status_filter.setCurrentIndex(0)
        self.search_roles()
    
    def on_cell_double_clicked(self, row, column):
        """处理表格双击事件"""
        if row >= 0:
            self.edit_role()
    
    def add_role(self):
        """添加角色"""
        dialog = RoleDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 添加角色
            try:
                role_data = dialog.get_role_data()
                
                # 检查角色名是否已存在
                from modules.db_manager import DatabaseManager
                db_manager = DatabaseManager()
                check_query = "SELECT COUNT(*) as count FROM role WHERE role_name = %s"
                result = db_manager.execute_query(check_query, (role_data['role_name'],))
                
                if result and result[0]['count'] > 0:
                    QMessageBox.warning(self, "警告", "角色名已存在，请使用其他角色名")
                    return
                
                # 插入角色
                insert_query = """
                INSERT INTO role (role_name, role_desc, status)
                VALUES (%s, %s, %s)
                """
                params = (
                    role_data['role_name'],
                    role_data['role_desc'],
                    role_data['status']
                )
                
                db_manager.execute_update(insert_query, params)
                
                QMessageBox.information(self, "成功", "角色添加成功")
                self.search_roles()
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"添加角色失败：{str(e)}")
    
    def edit_role(self):
        """编辑角色"""
        selected_rows = self.role_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要编辑的角色")
            return
        
        row = selected_rows[0].row()
        role_id = int(self.role_table.item(row, 0).text())
        
        try:
            # 获取角色信息
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            query = "SELECT * FROM role WHERE role_id = %s"
            result = db_manager.execute_query(query, (role_id,))
            
            if not result:
                QMessageBox.warning(self, "警告", "未找到角色信息")
                return
            
            role = result[0]
            
            # 打开编辑对话框
            dialog = RoleDialog(self, role)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # 更新角色
                role_data = dialog.get_role_data()
                
                # 检查角色名是否已存在
                check_query = """
                SELECT COUNT(*) as count 
                FROM role 
                WHERE role_name = %s AND role_id != %s
                """
                check_result = db_manager.execute_query(check_query, (role_data['role_name'], role_id))
                
                if check_result and check_result[0]['count'] > 0:
                    QMessageBox.warning(self, "警告", "角色名已存在，请使用其他角色名")
                    return
                
                # 更新角色基本信息
                update_query = """
                UPDATE role
                SET role_name = %s, role_desc = %s, status = %s
                WHERE role_id = %s
                """
                params = (
                    role_data['role_name'],
                    role_data['role_desc'],
                    role_data['status'],
                    role_id
                )
                
                db_manager.execute_update(update_query, params)
                
                QMessageBox.information(self, "成功", "角色信息更新成功")
                self.search_roles()
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑角色失败：{str(e)}")
    
    def delete_role(self):
        """删除角色"""
        selected_rows = self.role_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要删除的角色")
            return
        
        row = selected_rows[0].row()
        role_id = int(self.role_table.item(row, 0).text())
        role_name = self.role_table.item(row, 1).text()
        
        # 确认删除
        reply = QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要删除角色{role_name}吗？\n此操作不可恢复！\n\n注意：删除角色将同时删除与该角色关联的所有权限分配。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                from modules.db_manager import DatabaseManager
                db_manager = DatabaseManager()
                
                # 检查角色是否已分配给用户
                check_query = "SELECT COUNT(*) as count FROM user_role WHERE role_id = %s"
                check_result = db_manager.execute_query(check_query, (role_id,))
                
                if check_result and check_result[0]['count'] > 0:
                    QMessageBox.warning(
                        self, 
                        "警告", 
                        f"该角色已分配给{check_result[0]['count']}个用户，请先取消用户的角色分配"
                    )
                    return
                
                # 删除角色权限关联
                db_manager.execute_update("DELETE FROM role_permission WHERE role_id = %s", (role_id,))
                
                # 删除角色
                db_manager.execute_update("DELETE FROM role WHERE role_id = %s", (role_id,))
                
                QMessageBox.information(self, "成功", "角色删除成功")
                self.search_roles()
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除角色失败：{str(e)}")
    
    def assign_permissions(self):
        """分配权限"""
        selected_rows = self.role_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要分配权限的角色")
            return
        
        row = selected_rows[0].row()
        role_id = int(self.role_table.item(row, 0).text())
        role_name = self.role_table.item(row, 1).text()
        
        try:
            # 获取角色当前的权限
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            query = """
            SELECT permission_id FROM role_permission WHERE role_id = %s
            """
            result = db_manager.execute_query(query, (role_id,))
            
            current_permissions = [r['permission_id'] for r in result] if result else []
            
            # 打开权限分配对话框
            dialog = PermissionDialog(self, role_id, role_name, current_permissions)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # 获取选择的权限
                selected_permissions = dialog.get_selected_permissions()
                
                # 先删除原有权限关联
                db_manager.execute_update(
                    "DELETE FROM role_permission WHERE role_id = %s", 
                    (role_id,)
                )
                
                # 添加新权限关联
                if selected_permissions:
                    role_perm_query = "INSERT INTO role_permission (role_id, permission_id) VALUES (%s, %s)"
                    role_perm_params = [(role_id, perm_id) for perm_id in selected_permissions]
                    db_manager.executemany_insert(role_perm_query, role_perm_params)
                
                QMessageBox.information(self, "成功", "权限分配成功")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"分配权限失败：{str(e)}")

class RoleDialog(QDialog):
    def __init__(self, parent=None, role=None):
        super().__init__(parent)
        self.role = role
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        if self.role:
            self.setWindowTitle("编辑角色")
        else:
            self.setWindowTitle("添加角色")
            
        self.setMinimumWidth(400)
        layout = QVBoxLayout(self)
        form_layout = QFormLayout()
        
        # 角色名称
        self.role_name_input = QLineEdit()
        if self.role:
            self.role_name_input.setText(self.role['role_name'])
        form_layout.addRow("角色名称:", self.role_name_input)
        
        # 角色描述
        self.role_desc_input = QLineEdit()
        if self.role and self.role['role_desc']:
            self.role_desc_input.setText(self.role['role_desc'])
        form_layout.addRow("角色描述:", self.role_desc_input)
        
        # 状态
        self.status_input = QComboBox()
        self.status_input.addItem("启用", 1)
        self.status_input.addItem("禁用", 0)
        if self.role:
            index = 0 if self.role['status'] == 1 else 1
            self.status_input.setCurrentIndex(index)
        form_layout.addRow("状态:", self.status_input)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        # 布局
        layout.addLayout(form_layout)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def accept(self):
        """验证并接受对话框"""
        if not self.validate():
            return
        super().accept()
    
    def validate(self):
        """验证表单"""
        role_name = self.role_name_input.text().strip()
        if not role_name:
            QMessageBox.warning(self, "警告", "角色名称不能为空")
            return False
        
        return True
    
    def get_role_data(self):
        """获取角色数据"""
        data = {
            'role_name': self.role_name_input.text().strip(),
            'role_desc': self.role_desc_input.text().strip(),
            'status': self.status_input.currentData()
        }
        
        return data

class PermissionDialog(QDialog):
    def __init__(self, parent=None, role_id=None, role_name=None, current_permissions=None):
        super().__init__(parent)
        self.role_id = role_id
        self.role_name = role_name
        self.current_permissions = current_permissions or []
        self.init_ui()
        self.load_permissions()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle(f"分配权限 - {self.role_name}")
        self.setMinimumSize(500, 400)
        layout = QVBoxLayout(self)
        
        # 创建权限树
        self.permission_tree = QTreeWidget()
        self.permission_tree.setHeaderLabels(["权限名称", "权限代码"])
        self.permission_tree.setColumnWidth(0, 250)
        self.permission_tree.setAlternatingRowColors(True)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        # 全选/取消全选按钮
        select_layout = QHBoxLayout()
        select_all_button = QPushButton("全选")
        select_all_button.clicked.connect(self.select_all_permissions)
        
        deselect_all_button = QPushButton("取消全选")
        deselect_all_button.clicked.connect(self.deselect_all_permissions)
        
        expand_all_button = QPushButton("展开所有")
        expand_all_button.clicked.connect(self.permission_tree.expandAll)
        
        collapse_all_button = QPushButton("折叠所有")
        collapse_all_button.clicked.connect(self.permission_tree.collapseAll)
        
        select_layout.addWidget(select_all_button)
        select_layout.addWidget(deselect_all_button)
        select_layout.addStretch()
        select_layout.addWidget(expand_all_button)
        select_layout.addWidget(collapse_all_button)
        
        # 布局
        layout.addWidget(QLabel(f"为角色 <b>{self.role_name}</b> 分配权限："))
        layout.addLayout(select_layout)
        layout.addWidget(self.permission_tree)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def load_permissions(self):
        """加载权限列表"""
        try:
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            
            # 获取所有权限，按照父子关系排序
            query = """
            SELECT 
                permission_id, permission_name, permission_code, 
                permission_type, parent_id, status
            FROM permission
            ORDER BY parent_id, permission_type, permission_id
            """
            permissions = db_manager.execute_query(query)
            
            if not permissions:
                return
            
            # 构建权限树
            permission_map = {}
            root_items = []
            
            # 首先构建ID到项目的映射
            for perm in permissions:
                if perm['parent_id'] == 0:  # 顶级权限
                    item = QTreeWidgetItem([perm['permission_name'], perm['permission_code']])
                    item.setData(0, Qt.ItemDataRole.UserRole, perm['permission_id'])
                    
                    # 设置禁用状态的权限为灰色
                    if perm['status'] == 0:
                        item.setForeground(0, QColor("#999999"))
                        item.setForeground(1, QColor("#999999"))
                    
                    # 设置图标
                    if perm['permission_type'] == 1:  # 菜单
                        item.setIcon(0, QIcon("resources/icons/menu.png"))
                    elif perm['permission_type'] == 2:  # 按钮
                        item.setIcon(0, QIcon("resources/icons/button.png"))
                    elif perm['permission_type'] == 3:  # 数据
                        item.setIcon(0, QIcon("resources/icons/data.png"))
                    
                    # 设置复选框
                    item.setCheckState(0, Qt.CheckState.Checked if perm['permission_id'] in self.current_permissions else Qt.CheckState.Unchecked)
                    
                    root_items.append(item)
                    permission_map[perm['permission_id']] = item
            
            # 然后添加子权限
            for perm in permissions:
                if perm['parent_id'] > 0 and perm['parent_id'] in permission_map:
                    parent_item = permission_map[perm['parent_id']]
                    
                    item = QTreeWidgetItem(parent_item, [perm['permission_name'], perm['permission_code']])
                    item.setData(0, Qt.ItemDataRole.UserRole, perm['permission_id'])
                    
                    # 设置禁用状态的权限为灰色
                    if perm['status'] == 0:
                        item.setForeground(0, QColor("#999999"))
                        item.setForeground(1, QColor("#999999"))
                    
                    # 设置图标
                    if perm['permission_type'] == 1:  # 菜单
                        item.setIcon(0, QIcon("resources/icons/menu.png"))
                    elif perm['permission_type'] == 2:  # 按钮
                        item.setIcon(0, QIcon("resources/icons/button.png"))
                    elif perm['permission_type'] == 3:  # 数据
                        item.setIcon(0, QIcon("resources/icons/data.png"))
                    
                    # 设置复选框
                    item.setCheckState(0, Qt.CheckState.Checked if perm['permission_id'] in self.current_permissions else Qt.CheckState.Unchecked)
                    
                    permission_map[perm['permission_id']] = item
            
            # 添加到树控件
            self.permission_tree.addTopLevelItems(root_items)
            self.permission_tree.expandAll()
            
            # 连接信号，处理父子节点复选框联动
            self.permission_tree.itemChanged.connect(self.on_item_changed)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载权限失败：{str(e)}")
    
    def on_item_changed(self, item, column):
        """处理权限项状态变化"""
        if column == 0:
            check_state = item.checkState(0)
            
            # 如果是选中父节点，不强制选中所有子节点
            # 只在取消选中父节点时，取消选中所有子节点
            if check_state == Qt.CheckState.Unchecked:
                # 取消选中父节点时，取消选中所有子节点
                for i in range(item.childCount()):
                    item.child(i).setCheckState(0, Qt.CheckState.Unchecked)
            
            # 向上更新父节点状态（允许部分选中状态）
            self.update_parent_check_state(item)
    
    def update_parent_check_state(self, item):
        """更新父节点复选框状态（支持部分选中）"""
        parent = item.parent()
        if parent is None:
            return
        
        # 检查所有子节点状态
        checked_count = 0
        unchecked_count = 0
        total_count = parent.childCount()
        
        for i in range(total_count):
            child_state = parent.child(i).checkState(0)
            if child_state == Qt.CheckState.Checked:
                checked_count += 1
            elif child_state == Qt.CheckState.Unchecked:
                unchecked_count += 1
        
        # 设置父节点状态
        if checked_count == total_count:
            # 所有子节点都选中
            parent.setCheckState(0, Qt.CheckState.Checked)
        elif unchecked_count == total_count:
            # 所有子节点都未选中
            parent.setCheckState(0, Qt.CheckState.Unchecked)
        else:
            # 部分子节点选中
            parent.setCheckState(0, Qt.CheckState.PartiallyChecked)
        
        # 递归更新上层父节点
        self.update_parent_check_state(parent)
    
    def select_all_permissions(self):
        """全选权限"""
        for i in range(self.permission_tree.topLevelItemCount()):
            self.permission_tree.topLevelItem(i).setCheckState(0, Qt.CheckState.Checked)
    
    def deselect_all_permissions(self):
        """取消全选权限"""
        for i in range(self.permission_tree.topLevelItemCount()):
            self.permission_tree.topLevelItem(i).setCheckState(0, Qt.CheckState.Unchecked)
    
    def get_selected_permissions(self):
        """获取选中的权限ID列表"""
        selected_permissions = []
        
        # 递归获取所有选中的权限ID
        def collect_checked_items(item):
            if item.checkState(0) == Qt.CheckState.Checked:
                permission_id = item.data(0, Qt.ItemDataRole.UserRole)
                selected_permissions.append(permission_id)
            
            for i in range(item.childCount()):
                collect_checked_items(item.child(i))
        
        # 遍历顶级项目
        for i in range(self.permission_tree.topLevelItemCount()):
            collect_checked_items(self.permission_tree.topLevelItem(i))
        
        return selected_permissions
