from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QFormLayout, QWidget, QLineEdit, QGridLayout,
                            QFrame, QPushButton, QMessageBox, QComboBox,
                            QToolBar, QMainWindow)
from PyQt6.QtCore import Qt, QMarginsF
from PyQt6.QtGui import QIcon, QAction, QPageSize, QPageLayout, QPainter, QColor
from datetime import datetime
from .print_preview_dialog import PrintPreviewDialog
from PyQt6.QtPrintSupport import QPrinter, QPrinterInfo
from PyQt6.QtWidgets import QApplication

class HandoverDetailDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("交接单明细")
        # 设置一个较小的最小宽度，实际宽度将根据内容自适应
        self.setMinimumWidth(800)
        self.setMinimumHeight(800)
        self.return_db = None
        self.original_data = None  # 用于存储原始数据，用于比较是否有修改
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(0)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建工具栏
        toolbar = QToolBar()
        toolbar.setStyleSheet("""
            QToolBar {
                background: #f0f0f0;
                border: none;
                padding: 5px;
                spacing: 10px;
            }
            QToolButton {
                background-color: transparent;
                border: none;
                padding: 5px 15px;
                color: white;
                border-radius: 3px;
            }
            QToolButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
        """)
        
        # 创建操作按钮
        self.update_action = QAction("更新", self)
        self.update_action.triggered.connect(self.update_handover)
        toolbar.addAction(self.update_action)
        
        self.delete_action = QAction("删除", self)
        self.delete_action.triggered.connect(self.delete_handover)
        toolbar.addAction(self.delete_action)
        
        self.audit_action = QAction("审核", self)
        self.audit_action.triggered.connect(self.audit_handover)
        toolbar.addAction(self.audit_action)
        
        self.preview_action = QAction("打印预览", self)
        self.preview_action.triggered.connect(self.print_preview)
        toolbar.addAction(self.preview_action)
        
        self.print_action = QAction("打印", self)
        self.print_action.triggered.connect(self.print_document_handover)
        toolbar.addAction(self.print_action)
        
        self.unaudit_action = QAction("反审核", self)
        self.unaudit_action.triggered.connect(self.unaudit_handover)
        toolbar.addAction(self.unaudit_action)
        
        self.split_action = QAction("拆分明细", self)
        self.split_action.triggered.connect(self.split_detail)
        toolbar.addAction(self.split_action)
        
        # 添加作废按钮到工具栏
        self.void_action = QAction("作废", self)
        self.void_action.triggered.connect(self.void_handover)
        toolbar.addAction(self.void_action)
        
        # 设置按钮样式
        for action in toolbar.actions():
            widget = toolbar.widgetForAction(action)
            if widget:
                if action.text() == "更新":
                    widget.setStyleSheet("background-color: #1890ff;")
                elif action.text() == "删除":
                    widget.setStyleSheet("background-color: #ff4d4f;")
                elif action.text() == "审核":
                    widget.setStyleSheet("background-color: #52c41a;")
                elif action.text() == "打印预览":
                    widget.setStyleSheet("background-color: #722ed1;")
                elif action.text() == "打印":
                    widget.setStyleSheet("background-color: #13c2c2;")
                elif action.text() == "反审核":
                    widget.setStyleSheet("background-color: #faad14;")
                elif action.text() == "拆分明细":
                    widget.setStyleSheet("background-color: #eb2f96;")
                elif action.text() == "作废":
                    widget.setStyleSheet("background-color: #434343;")  # 深灰色
        
        layout.addWidget(toolbar)
        
        # 交接单信息区域
        header_widget = QWidget()
        header_widget.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
            }
            QLabel {
                padding: 5px;
            }
            QLineEdit {
                background-color: white;
                border: 1px solid #cccccc;
                padding: 3px;
            }
            QComboBox {
                border: 1px solid #cccccc;
                selection-background-color: #1890ff;
                selection-color: #ffffff;
                background: #ffffff;
                padding: 0px;
            }
            QComboBox:hover {
                background: #f5f5f5;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #cccccc;
                selection-background-color: #1890ff;
                selection-color: #ffffff;
                background: #ffffff;
                padding: 0px;
            }
        """)
        
        grid_layout = QGridLayout(header_widget)
        grid_layout.setSpacing(10)
        
        # 第一行
        grid_layout.addWidget(QLabel("交接单号:"), 0, 0)
        self.handover_no_edit = QLineEdit()
        self.handover_no_edit.setReadOnly(True)
        grid_layout.addWidget(self.handover_no_edit, 0, 1)
        
        grid_layout.addWidget(QLabel("交接日期:"), 0, 2)
        self.handover_date_edit = QLineEdit()
        self.handover_date_edit.setReadOnly(True)
        grid_layout.addWidget(self.handover_date_edit, 0, 3)
        
        grid_layout.addWidget(QLabel("出库单号:"), 0, 4)
        self.delivery_no_edit = QLineEdit()
        self.delivery_no_edit.setReadOnly(True)
        grid_layout.addWidget(self.delivery_no_edit, 0, 5)
        
        # 第一行增加原始单号
        grid_layout.addWidget(QLabel("原始单号:"), 0, 6)
        self.source_order_edit = QLineEdit()
        self.source_order_edit.setReadOnly(True)
        grid_layout.addWidget(self.source_order_edit, 0, 7)
        
        # 第二行
        grid_layout.addWidget(QLabel("客户:"), 1, 0)
        self.customer_edit = QLineEdit()
        self.customer_edit.setReadOnly(True)
        grid_layout.addWidget(self.customer_edit, 1, 1)
        
        grid_layout.addWidget(QLabel("物流公司:"), 1, 2)
        self.logistics_edit = QLineEdit()
        self.logistics_edit.setReadOnly(True)
        grid_layout.addWidget(self.logistics_edit, 1, 3)
        
        grid_layout.addWidget(QLabel("部门:"), 1, 4)
        self.department_edit = QLineEdit()
        self.department_edit.setReadOnly(True)
        grid_layout.addWidget(self.department_edit, 1, 5)
        
        # 第二行增加退货类型和单据状态
        grid_layout.addWidget(QLabel("退货类型:"), 1, 6)
        self.return_type_combo = QComboBox()
        self.return_type_combo.addItems(["拒收退货", "仓退", "代销退货"])
        grid_layout.addWidget(self.return_type_combo, 1, 7)
        
        grid_layout.addWidget(QLabel("单据状态:"), 2, 6)
        self.status_edit = QLineEdit()
        self.status_edit.setReadOnly(True)
        grid_layout.addWidget(self.status_edit, 2, 7)
        
        # 第三行
        grid_layout.addWidget(QLabel("摘要:"), 2, 0)
        self.summary_edit = QLineEdit()
        self.summary_edit.setReadOnly(True)
        grid_layout.addWidget(self.summary_edit, 2, 1, 1, 5)
        
        layout.addWidget(header_widget)
        
        # 分割
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)
        
        # 明细表格
        self.detail_table = QTableWidget()
        self.detail_table.setEditTriggers(QTableWidget.EditTrigger.DoubleClicked)
        self.detail_table.setColumnCount(12)
        self.detail_table.setHorizontalHeaderLabels([
            "操作", "明细ID", "物料编码", "物料名称", "计量单位", 
            "批次", "仓库", "客户拒收原因", "退货数量", "收货数量",
            "退货入库单号", "退货状态"
        ])
        
        # 设置表格属性
        header = self.detail_table.horizontalHeader()
        header.setSortIndicatorShown(True)  # 显示排序指示器
        header.sectionClicked.connect(self.on_header_clicked)  # 添加表头点击事件
        
        for i in range(self.detail_table.columnCount()):
            if i == 0:  # 操作列
                header.setSectionResizeMode(i, QHeaderView.ResizeMode.Fixed)
                self.detail_table.setColumnWidth(i, 40)
            else:
                header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
        
        # 添加表格编辑完成事件处理
        self.detail_table.itemChanged.connect(self.on_item_changed)
        
        layout.addWidget(self.detail_table)
        
    def set_handover_data(self, data):
        """设置交接单数据"""
        self.original_data = data  # 保存原始数据
        try:
            main_data = data['main']
            # 对明细按 delivery_autoid 排序，使用字符串比较
            details = sorted(data['details'], key=lambda x: str(x['delivery_autoid']))
            
            # 设置主表数据
            self.handover_no_edit.setText(main_data['handover_no'])
            self.handover_date_edit.setText(str(main_data['handover_date']))
            self.delivery_no_edit.setText(str(main_data['delivery_order_no']))
            self.source_order_edit.setText(str(main_data['source_order_no'] or ''))
            self.customer_edit.setText(str(main_data['customer'] or ''))
            self.logistics_edit.setText(str(main_data['logistics_company'] or ''))
            self.department_edit.setText(str(main_data['department'] or ''))
            self.summary_edit.setText(str(main_data['summary'] or ''))
            
            # 设置退货类型
            return_type_map = {0: "拒收退货", 1: "仓退", 2: "代销退货"}
            self.return_type_combo.setCurrentText(return_type_map.get(main_data['return_type'], "拒收退货"))
            
            # 设置单据状态
            status_map = {0: "保存", 1: "审核", 2: "作废"}
            self.status_edit.setText(status_map.get(main_data['handover_status'], "保存"))
            
            # 根据单据状态设置界面状态
            is_audited = main_data['handover_status'] == 1
            is_voided = main_data['handover_status'] == 2
            self.set_ui_state(is_audited, is_voided)
            
            # 设置明细数据
            self.detail_table.setRowCount(len(details))
            
            # 计算内容宽度所需的变量
            max_content_widths = [0] * self.detail_table.columnCount()
            
            for row, detail in enumerate(details):
                # 创建删除按钮
                delete_btn = QPushButton("×")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: transparent;
                        color: #ff4d4f;
                        border: none;
                        padding: 2px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 25px;
                        max-width: 25px;
                        min-height: 25px;
                        max-height: 25px;
                    }
                    QPushButton:hover {
                        background-color: #fff1f0;
                        color: #ff7875;
                    }
                    QPushButton:pressed {
                        background-color: #ffccc7;
                    }
                    QPushButton:disabled {
                        color: #d9d9d9;
                    }
                """)
                delete_btn.clicked.connect(lambda checked, r=row: self.delete_detail_row(r))
                
                # 设置删除按钮状态
                if is_audited or is_voided:
                    delete_btn.setEnabled(False)
                    delete_btn.setToolTip("审核或作废状态下不能删除明细")
                elif len(details) <= 1:
                    delete_btn.setEnabled(False)
                    delete_btn.setToolTip("至少需要保留一行明细")
                else:
                    delete_btn.setEnabled(True)
                    delete_btn.setToolTip("删除该明细")
                
                self.detail_table.setCellWidget(row, 0, delete_btn)
                max_content_widths[0] = max(max_content_widths[0], 40)  # 固定操作列宽度
                
                # 设置明细ID (delivery_autoid)
                delivery_autoid_item = QTableWidgetItem(str(detail['delivery_autoid']))
                delivery_autoid_item.setData(Qt.ItemDataRole.UserRole, detail['detail_no'])  # 在明细ID单元格中也存储detail_no
                delivery_autoid_item.setFlags(delivery_autoid_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # 设置为只读
                self.detail_table.setItem(row, 1, delivery_autoid_item)
                max_content_widths[1] = max(max_content_widths[1], len(str(detail['delivery_autoid'])) * 10)
                
                # 设置物料编码，同时保存 detail_no
                material_code_item = QTableWidgetItem(detail['material_code'])
                material_code_item.setData(Qt.ItemDataRole.UserRole, detail['detail_no'])  # 保持原有的detail_no存储
                material_code_item.setFlags(material_code_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # 设置为只读
                self.detail_table.setItem(row, 2, material_code_item)
                max_content_widths[2] = max(max_content_widths[2], len(detail['material_code']) * 8 + 20)
                
                # 设置其他列的数据
                material_name_item = QTableWidgetItem(detail['material_name'])
                material_name_item.setFlags(material_name_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                self.detail_table.setItem(row, 3, material_name_item)
                max_content_widths[3] = max(max_content_widths[3], len(detail['material_name']) * 10 + 20)

                unit_item = QTableWidgetItem(str(detail['unit']))
                unit_item.setFlags(unit_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                self.detail_table.setItem(row, 4, unit_item)
                max_content_widths[4] = max(max_content_widths[4], len(str(detail['unit'])) * 10 + 10)

                batch_no_item = QTableWidgetItem(str(detail['batch_no']))
                self.detail_table.setItem(row, 5, batch_no_item)
                max_content_widths[5] = max(max_content_widths[5], len(str(detail['batch_no'])) * 8 + 20)

                warehouse_item = QTableWidgetItem(str(detail['warehouse']))
                warehouse_item.setFlags(warehouse_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                self.detail_table.setItem(row, 6, warehouse_item)
                max_content_widths[6] = max(max_content_widths[6], len(str(detail['warehouse'])) * 10 + 20)

                reject_reason_item = QTableWidgetItem(str(detail['reject_reason'] or ''))
                reject_reason_item.setFlags(reject_reason_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                self.detail_table.setItem(row, 7, reject_reason_item)
                max_content_widths[7] = max(max_content_widths[7], len(str(detail['reject_reason'] or '')) * 10 + 20)

                return_quantity_item = QTableWidgetItem(str(detail['return_quantity']))
                self.detail_table.setItem(row, 8, return_quantity_item)
                max_content_widths[8] = max(max_content_widths[8], len(str(detail['return_quantity'])) * 10 + 20)

                received_quantity_item = QTableWidgetItem()
                if detail.get('received_quantity'):
                    # 格式化为2位小数
                    formatted_qty = "{:.2f}".format(float(detail['received_quantity']))
                    received_quantity_item.setText(formatted_qty)
                else:
                    received_quantity_item.setText('')
                received_quantity_item.setForeground(QColor("#ff4d4f"))  # 设置红色字体
                received_quantity_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.detail_table.setItem(row, 9, received_quantity_item)
                max_content_widths[9] = max(max_content_widths[9], 90)  # 收货数量列固定宽度

                return_warehouse_item = QTableWidgetItem(str(detail['return_warehouse_no'] or ''))
                self.detail_table.setItem(row, 10, return_warehouse_item)
                max_content_widths[10] = max(max_content_widths[10], len(str(detail['return_warehouse_no'] or '')) * 10 + 20)

                stock_status_item = QTableWidgetItem(str(detail['stock_status_text'] or ''))
                self.detail_table.setItem(row, 11, stock_status_item)
                max_content_widths[11] = max(max_content_widths[11], 100)  # 固定宽度，因为使用下拉框
                
                # 添加退货状态下拉框
                status_combo = QComboBox()
                status_combo.addItems(["", "原料", "变形", "待报废", "报废"])  # 添加空值选项作为第一个选项
                current_status = detail.get('stock_status_text', '')
                if current_status:
                    status_combo.setCurrentText(current_status)
                else:
                    status_combo.setCurrentIndex(0)  # 如果没有状态值，默认选择空值
                
                # 如果是审核状态，禁用下拉框
                if main_data['handover_status'] == 1:
                    status_combo.setEnabled(False)
                
                self.detail_table.setCellWidget(row, 11, status_combo)
                
                # 设置单元格居中对齐
                for col in range(1, self.detail_table.columnCount()):  # 从第二列开始（跳过删除按钮列）
                    item = self.detail_table.item(row, col)
                    if item:
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # 设置表头宽度
            header = self.detail_table.horizontalHeader()
            for col in range(self.detail_table.columnCount()):
                header_text = self.detail_table.horizontalHeaderItem(col).text()
                max_content_widths[col] = max(max_content_widths[col], len(header_text) * 10 + 20)
                self.detail_table.setColumnWidth(col, max_content_widths[col])
            
            # 计算表格总宽度
            total_width = sum(max_content_widths) + 70# 添加一些额外空间
            
            # 设置窗口宽度
            screen_width = QApplication.primaryScreen().availableGeometry().width()
            window_width = min(total_width, screen_width * 0.9)  # 不超过屏幕宽度的90%
            window_width = max(window_width, 800)  # 不小于最小宽度
            self.resize(window_width, self.height())
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"设置交接单数据失败：{str(e)}")
    
    def delete_detail_row(self, row):
        """删除明细行"""
        try:
            # 检查是否只剩一行
            if self.detail_table.rowCount() <= 1:
                QMessageBox.warning(self, "警告", "至少需要保留一行明细数据！")
                return
            
            # 获取要删除的明细信息
            material_code_item = self.detail_table.item(row, 2)  # 获取物料编码列
            if not material_code_item:
                raise Exception("无法获取明细信息")
            
            # 从物料编码单元格的 UserRole 中获取 detail_no
            detail_no = material_code_item.data(Qt.ItemDataRole.UserRole)
            if not detail_no:
                raise Exception("无法获取明细编号")
            
            handover_no = self.handover_no_edit.text()
            
            reply = QMessageBox.question(
                self,
                "确认删除",
                "确定要删除这条明细记录吗？\n删除后不可恢复！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 调用数据库删除方法
                if self.return_db.delete_handover_detail(handover_no, detail_no):
                    self.detail_table.removeRow(row)
                    
                    # 更新所有删除按钮的事件处理函数
                    self.update_delete_buttons()
                    
                    QMessageBox.information(self, "成功", "明细删除成功！")
                else:
                    QMessageBox.warning(self, "失败", "明细删除失败！")
                    
        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除明细失败：{str(e)}")

    def update_delete_buttons(self):
        """更新所有删除按钮的事件处理函数"""
        row_count = self.detail_table.rowCount()
        for row in range(row_count):
            delete_btn = self.detail_table.cellWidget(row, 0)
            if delete_btn:
                # 断开旧的连接
                try:
                    delete_btn.clicked.disconnect()
                except:
                    pass
                
                # 重新连接到新的行号
                delete_btn.clicked.connect(lambda checked, r=row: self.delete_detail_row(r))
                
                # 更新按钮状态
                if row_count <= 1:
                    delete_btn.setEnabled(False)
                    delete_btn.setToolTip("至少需要保留一行明细")
                else:
                    delete_btn.setEnabled(True)
                    delete_btn.setToolTip("删除该明细")
            
    def is_data_modified(self):
        """检查数据是否被修改"""
        try:
            # 检查退货类型是否修改
            current_return_type = self.return_type_combo.currentIndex()
            if current_return_type != self.original_data['main']['return_type']:
                return True
            
            # 可以添加其他需检查的字段
            
            return False
        except Exception:
            return False

    def closeEvent(self, event):
        """关闭窗口事件"""
        if self.is_data_modified():
            reply = QMessageBox.question(
                self,
                "确认",
                "数据已修改，是否保存？",
                QMessageBox.StandardButton.Yes | 
                QMessageBox.StandardButton.No | 
                QMessageBox.StandardButton.Cancel
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                if self.update_handover():
                    event.accept()
                else:
                    event.ignore()
            elif reply == QMessageBox.StandardButton.No:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

    def update_handover(self):
        """更新交接单"""
        try:
            handover_no = self.handover_no_edit.text()
            
            # 收集更新数据
            updates = {
                'logistics_company': self.logistics_edit.text(),
                'return_type': self.return_type_combo.currentIndex(),
                'details': []
            }
            
            # 收集明细数据
            for row in range(self.detail_table.rowCount()):
                # 从物料编码单元格的 data 角色获取 detail_no
                material_code_item = self.detail_table.item(row, 2)
                detail_no = material_code_item.data(Qt.ItemDataRole.UserRole)
                # 获取批次号
                batch_no_item = self.detail_table.item(row, 5)
                batch_no = batch_no_item.text().strip() if batch_no_item else None
                
                # 获取退货数量
                return_qty_item = self.detail_table.item(row, 8)
                return_qty = float(return_qty_item.text()) if return_qty_item and return_qty_item.text().strip() else 0
                
                # 获取收货数量
                received_qty_item = self.detail_table.item(row, 9)
                received_qty = None
                if received_qty_item and received_qty_item.text().strip():
                    try:
                        received_qty = float(received_qty_item.text().strip())
                    except ValueError:
                        received_qty = None
                
                # 获取退货入库单号
                return_warehouse_no_item = self.detail_table.item(row, 10)
                return_warehouse_no = return_warehouse_no_item.text().strip() if return_warehouse_no_item else None
                
                # 获取状态值
                status_combo = self.detail_table.cellWidget(row, 11)
                status_text = status_combo.currentText()
                status_map = {
                    "原料": "raw",
                    "变形": "deformed",
                    "待报废": "to_scrap",
                    "报废": "scrapped"
                }
                stock_status = status_map.get(status_text)
                
                detail = {
                    'detail_no': detail_no,
                    'batch_no': batch_no,
                    'return_quantity': return_qty,  # 添加退货数量
                    'received_quantity': received_qty,
                    'return_warehouse_no': return_warehouse_no,
                    'stock_status': stock_status  # 添加状态值
                }
                updates['details'].append(detail)
                print(updates)

            # 调用数据库更新方法
            if self.return_db.update_handover_order(handover_no, updates):
                QMessageBox.information(self, "成功", "交接单更新成功！")
                return True
            return False
        except Exception as e:
            QMessageBox.critical(self, "错误", f"更新失败：{str(e)}")
            return False

    def delete_handover(self):
        """删除交接单"""
        try:
            reply = QMessageBox.question(
                self,
                "确认删除",
                "确定要删除该交接单吗？删除后不可恢复！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                handover_no = self.handover_no_edit.text()
                if self.return_db.delete_handover_order(handover_no):
                    QMessageBox.information(self, "成功", "交接单删除成功！")
                    self.accept()  # 关闭窗口
                    return True
            return False
        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除失败：{str(e)}")
            return False

    def print_preview(self):
        """打印预览"""
        try:
            # 获取当前数据
            data = {
                'main': {
                    'handover_no': self.handover_no_edit.text(),
                    'handover_date': self.handover_date_edit.text(),
                    'delivery_order_no': self.delivery_no_edit.text(),
                    'source_order_no': self.source_order_edit.text(),
                    'customer': self.customer_edit.text(),
                    'logistics_company': self.logistics_edit.text(),
                    'return_type': self.return_type_combo.currentIndex(),
                    'summary': self.summary_edit.text(),
                    'department': self.department_edit.text()
                },
                'details': []
            }
            
            # 获取明细数据
            for row in range(self.detail_table.rowCount()):
                material_code_item = self.detail_table.item(row, 2)
                detail_no = material_code_item.data(Qt.ItemDataRole.UserRole)
                
                detail = {
                    'detail_no': detail_no,
                    'material_code': self.detail_table.item(row, 2).text(),
                    'material_name': self.detail_table.item(row, 3).text(),
                    'unit': self.detail_table.item(row, 4).text(),
                    'batch_no': self.detail_table.item(row, 5).text(),
                    'warehouse': self.detail_table.item(row, 6).text(),
                    'reject_reason': self.detail_table.item(row, 7).text(),
                    'return_quantity': self.detail_table.item(row, 8).text(),
                    'received_quantity': self.detail_table.item(row, 9).text() if self.detail_table.item(row, 9) else '',
                    'return_warehouse_no': self.detail_table.item(row, 10).text() if self.detail_table.item(row, 10) else ''
                }
                data['details'].append(detail)
            
            # 显示打印预览窗口
            preview_dialog = PrintPreviewDialog(data, self)
            preview_dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打印预览失败：{str(e)}")
    
    def audit_handover(self):
        """审核交接单"""
        try:
            handover_no = self.handover_no_edit.text()
            
            reply = QMessageBox.question(
                self,
                "确认审核",
                "确定要审核该交接单吗？\n审核后数据将不能修改！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 先收集更新数据
                updates = {
                    'logistics_company': self.logistics_edit.text(),
                    'return_type': self.return_type_combo.currentIndex(),
                    'details': []
                }
                
                # 收集明细数据
                for row in range(self.detail_table.rowCount()):
                    # 从物料编码单元格的 data 角色获取 detail_no
                    material_code_item = self.detail_table.item(row, 2)
                    detail_no = material_code_item.data(Qt.ItemDataRole.UserRole)
                    received_qty_item = self.detail_table.item(row, 9)
                    return_warehouse_no_item = self.detail_table.item(row, 10)
                    # 获取状态值
                    status_combo = self.detail_table.cellWidget(row, 11)
                    status_text = status_combo.currentText()
                    status_map = {
                        "原料": "raw",
                        "变形": "deformed",
                        "待报废": "to_scrap",
                        "报废": "scrapped"
                    }
                    stock_status = status_map.get(status_text)
                    detail = {
                        'detail_no': detail_no,
                        'received_quantity': float(received_qty_item.text()) if received_qty_item and received_qty_item.text() else 0,
                        'return_warehouse_no': return_warehouse_no_item.text() if return_warehouse_no_item else '',
                        'stock_status': stock_status,
                        'return_quantity': float(self.detail_table.item(row, 8).text()),
                        'batch_no': self.detail_table.item(row, 5).text()
                    }
                    updates['details'].append(detail)
                
                # 先执行更新
                if not self.return_db.update_handover_order(handover_no, updates):
                    return False
                    
                # 更新成功后执行审核
                if self.return_db.audit_handover_order(handover_no):
                    # 更新界面状态为审核状态
                    self.set_ui_state(True)
                    QMessageBox.information(self, "成功", "交接单审核成功！")
                    return True
                    
            return False
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"审核失败：{str(e)}")
            return False

    def unaudit_handover(self):
        """反审核交接单"""
        try:
            handover_no = self.handover_no_edit.text()
            
            reply = QMessageBox.question(
                self,
                "确认反审核",
                "确定要反审核该交接单吗？\n反审核后将恢复到保存状态！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                if self.return_db.unaudit_handover_order(handover_no):
                    # 更新界面状态为保存状态
                    self.set_ui_state(False)
                    QMessageBox.information(self, "成功", "交接单反审核成功！")
                    return True
            return False
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"反审核失败：{str(e)}")
            return False

    def split_detail(self):
        """拆分明细行"""
        try:
            # 获取当前选中的行
            selected_rows = set(item.row() for item in self.detail_table.selectedItems())
            if not selected_rows:
                QMessageBox.warning(self, "提示", "请先选择要拆分的明细行！")
                return
            
            # 检查单据状态
            if self.status_edit.text() == "审核":
                QMessageBox.warning(self, "提示", "审核状态下不能拆分明细！")
                return
            
            # 获取交接单号
            handover_no = self.handover_no_edit.text()
            
            # 收集选中行的数据
            split_details = []
            original_rows = []  # 记录原始行的索引
            for row in sorted(selected_rows):
                # 获取收货数量和退货入库单号，确保正确处理 None 值
                received_qty_item = self.detail_table.item(row, 9)
                received_qty = None
                if received_qty_item and received_qty_item.text().strip():
                    try:
                        received_qty = float(received_qty_item.text().strip())
                    except ValueError:
                        received_qty = None
                
                return_warehouse_no_item = self.detail_table.item(row, 10)
                return_warehouse_no = return_warehouse_no_item.text().strip() if return_warehouse_no_item else None
                
                detail = {
                    'delivery_autoid': self.detail_table.item(row, 1).text(),
                    'material_code': self.detail_table.item(row, 2).text(),
                    'material_name': self.detail_table.item(row, 3).text(),
                    'unit': self.detail_table.item(row, 4).text(),
                    'batch_no': self.detail_table.item(row, 5).text(),
                    'warehouse': self.detail_table.item(row, 6).text(),
                    'reject_reason': self.detail_table.item(row, 7).text(),
                    'return_quantity': float(self.detail_table.item(row, 8).text()),
                    'received_quantity': received_qty,
                    'return_warehouse_no': return_warehouse_no
                }
                split_details.append(detail)
                original_rows.append(row)
            
            # 调用数据库方法进行拆分
            result = self.return_db.split_handover_details(handover_no, split_details)
            if result:
                # 获取拆分后的新明细数据
                split_result = self.return_db.get_split_details(handover_no, [d['delivery_autoid'] for d in split_details])
                if split_result:
                    # 删除原始行
                    for row in reversed(original_rows):  # 从后向前删除，避免索引变化
                        self.detail_table.removeRow(row)
                    
                    # 在原位置插入拆分后的新行
                    insert_row = original_rows[0]  # 使用第一个原始行的位置作为插入位置
                    for new_detail in split_result:
                        self.detail_table.insertRow(insert_row)
                        
                        # 创建删除按钮
                        delete_btn = QPushButton("×")
                        delete_btn.setStyleSheet("""
                            QPushButton {
                                background-color: transparent;
                                color: #ff4d4f;
                                border: none;
                                padding: 2px;
                                font-size: 16px;
                                font-weight: bold;
                                min-width: 25px;
                                max-width: 25px;
                                min-height: 25px;
                                max-height: 25px;
                            }
                            QPushButton:hover {
                                background-color: #fff1f0;
                                color: #ff7875;
                            }
                            QPushButton:pressed {
                                background-color: #ffccc7;
                            }
                            QPushButton:disabled {
                                color: #d9d9d9;
                            }
                        """)
                        delete_btn.clicked.connect(lambda checked, r=insert_row: self.delete_detail_row(r))
                        self.detail_table.setCellWidget(insert_row, 0, delete_btn)
                        
                        # 设置明细ID (delivery_autoid)
                        delivery_autoid_item = QTableWidgetItem(str(new_detail.get('delivery_autoid', '')))
                        delivery_autoid_item.setFlags(delivery_autoid_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                        self.detail_table.setItem(insert_row, 1, delivery_autoid_item)
                        
                        # 设置物料编码，同时保存 detail_no
                        material_code_item = QTableWidgetItem(str(new_detail.get('material_code', '')))
                        material_code_item.setData(Qt.ItemDataRole.UserRole, new_detail.get('detail_no'))  # 关键修改：保存 detail_no
                        material_code_item.setFlags(material_code_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                        self.detail_table.setItem(insert_row, 2, material_code_item)
                        
                        # 设置其他列的数据
                        columns = [
                            ('material_name', 3), ('unit', 4), ('batch_no', 5),
                            ('warehouse', 6), ('reject_reason', 7),
                            ('return_quantity', 8), ('received_quantity', 9),
                            ('return_warehouse_no', 10)
                        ]
                        
                        for field, col in columns:
                            value = str(new_detail.get(field, ''))
                            item = QTableWidgetItem(value)
                            if col == 9:  # 收货数量列
                                item.setForeground(QColor("#ff4d4f"))
                            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                            if col in [3, 4, 6, 7]:  # 设置只读列
                                item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                            self.detail_table.setItem(insert_row, col, item)
                        
                        # 添加退货状态下拉框
                        status_combo = QComboBox()
                        status_combo.addItems(["", "原料", "变形", "待报废", "报废"])
                        current_status = new_detail.get('stock_status_text', '')
                        if current_status:
                            status_combo.setCurrentText(current_status)
                        else:
                            status_combo.setCurrentIndex(0)
                        self.detail_table.setCellWidget(insert_row, 11, status_combo)
                        
                        insert_row += 1
                    
                    # 更新所有删除按钮的状态
                    self.update_delete_buttons()
                    
                    # 重新加载交接单数据
                    handover_no = self.handover_no_edit.text()
                    new_data = self.return_db.get_handover_details(handover_no)
                    if new_data:
                        self.set_handover_data(new_data)
                    
                    QMessageBox.information(self, "成功", "明细拆分成功！")
                   
        except Exception as e:
            QMessageBox.critical(self, "错误", f"拆分明细失败：{str(e)}")

    def set_ui_state(self, is_audited, is_voided=False):
        """根据单据状态设置界面控件状态"""
        # 设置表格编辑状态
        if is_audited or is_voided:
            self.detail_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
            # 禁用所有退货状态下拉框
            for row in range(self.detail_table.rowCount()):
                status_combo = self.detail_table.cellWidget(row, 11)  # 第12列是退货状态
                if status_combo:
                    status_combo.setEnabled(False)
        else:
            self.detail_table.setEditTriggers(
                QTableWidget.EditTrigger.DoubleClicked | 
                QTableWidget.EditTrigger.EditKeyPressed |
                QTableWidget.EditTrigger.AnyKeyPressed
            )
            # 启用所有退货状态下拉框
            for row in range(self.detail_table.rowCount()):
                status_combo = self.detail_table.cellWidget(row, 11)  # 第12列是退货状态
                if status_combo:
                    status_combo.setEnabled(True)
        
        # 设置按钮状态和样式
        button_states = {
            self.update_action: {
                'enabled': not (is_audited or is_voided),
                'style': "background-color: #1890ff;" if not (is_audited or is_voided) else "background-color: #d9d9d9;"
            },
            self.delete_action: {
                'enabled': not (is_audited or is_voided),
                'style': "background-color: #ff4d4f;" if not (is_audited or is_voided) else "background-color: #d9d9d9;"
            },
            self.audit_action: {
                'enabled': not (is_audited or is_voided),
                'style': "background-color: #52c41a;" if not (is_audited or is_voided) else "background-color: #d9d9d9;"
            },
            self.unaudit_action: {
                'enabled': is_audited and not is_voided,
                'style': "background-color: #faad14;" if (is_audited and not is_voided) else "background-color: #d9d9d9;"
            },
            self.split_action: {
                'enabled': not (is_audited or is_voided),
                'style': "background-color: #eb2f96;" if not (is_audited or is_voided) else "background-color: #d9d9d9;"
            },
            self.void_action: {
                'enabled': not (is_audited or is_voided),
                'style': "background-color: #434343;" if not (is_audited or is_voided) else "background-color: #d9d9d9;"
            }
        }
        
        # 应用按钮状态和样式
        for action, settings in button_states.items():
            action.setEnabled(settings['enabled'])
            # 遍历所有工具栏找到对应的 widget
            for toolbar in self.findChildren(QToolBar):
                widget = toolbar.widgetForAction(action)
                if widget:
                    widget.setStyleSheet(settings['style'])
        
        # 设置其他固定样式的按钮
        static_styles = {
            "打印预览": "background-color: #722ed1;",
            "打印": "background-color: #13c2c2;"
        }
        
        # 遍历所有工具栏设置固定样式按钮
        for toolbar in self.findChildren(QToolBar):
            for action in toolbar.actions():
                if action.text() in static_styles:
                    widget = toolbar.widgetForAction(action)
                    if widget:
                        widget.setStyleSheet(static_styles[action.text()])
        
        # 设置可编辑字段状态
        self.return_type_combo.setEnabled(not (is_audited or is_voided))
        if is_voided:
            self.status_edit.setText("作废")
        else:
            self.status_edit.setText("审核" if is_audited else "保存")

    def print_document_handover(self):
        """直接打印文档"""
        try:
            print("\n=== HandoverDetailDialog 打印开始 ===")
            
            # 获取当前数据
            data = {
                'main': {
                    'handover_no': self.handover_no_edit.text(),
                    'handover_date': self.handover_date_edit.text(),
                    'delivery_order_no': self.delivery_no_edit.text(),
                    'source_order_no': self.source_order_edit.text(),
                    'customer': self.customer_edit.text(),
                    'logistics_company': self.logistics_edit.text(),
                    'return_type': self.return_type_combo.currentIndex(),
                    'summary': self.summary_edit.text(),
                    'department': self.department_edit.text()
                },
                'details': []
            }
            
            # 获取明细数据
            for row in range(self.detail_table.rowCount()):
                material_code_item = self.detail_table.item(row, 2)
                detail_no = material_code_item.data(Qt.ItemDataRole.UserRole)
                
                detail = {
                    'detail_no': detail_no,
                    'material_code': self.detail_table.item(row, 2).text(),
                    'material_name': self.detail_table.item(row, 3).text(),
                    'unit': self.detail_table.item(row, 4).text(),
                    'batch_no': self.detail_table.item(row, 5).text(),
                    'warehouse': self.detail_table.item(row, 6).text(),
                    'reject_reason': self.detail_table.item(row, 7).text(),
                    'return_quantity': self.detail_table.item(row, 8).text(),
                    'received_quantity': self.detail_table.item(row, 9).text() if self.detail_table.item(row, 9) else '',
                    'return_warehouse_no': self.detail_table.item(row, 10).text() if self.detail_table.item(row, 10) else ''
                }
                data['details'].append(detail)

            # 创建打印预览对话框
            preview_dialog = PrintPreviewDialog(data, self)
            
            # 设置固定大小，与预览窗口一致
            preview_dialog.setFixedSize(1024, 768)
            preview_dialog.setup_ui()
            
            # 强制处理事件，确保组件完全渲染
            from PyQt6.QtCore import QCoreApplication
            QCoreApplication.processEvents()
            
            # 确保预览组件大小一致
            preview_dialog.preview_widget.setFixedSize(1000, 714)  # 与预览窗口打印时的尺寸一致
            
            print(f"UI初始化完成: {preview_dialog.preview_widget.width()}x{preview_dialog.preview_widget.height()}")
            
            if preview_dialog._do_print(show_success_message=True):
                preview_dialog.accept()
                print("=== HandoverDetailDialog 打印完成 ===\n")
            
        except Exception as e:
            print(f"HandoverDetailDialog 打印错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"打印失败：{str(e)}")
            
    def on_item_changed(self, item):
        """处理表格单元格编辑完成事件"""
        try:
            # print("\n=== 开始处理单元格变化 ===")
            
            # 保存行列信息，因为item可能会被删除
            row = item.row()
            column = item.column()
            current_text = item.text()
            
            # print(f"列号: {column}")
            # print(f"行号: {row}")
            # print(f"当前文本: {current_text}")
            
            # 只处理收货数量列（第9列）
            if column == 9:
                text = current_text.strip()
                print(f"处理的文本: {text}")
                
                if text:
                    try:
                        value = float(text)
                        formatted_text = "{:.2f}".format(value)
                        # print(f"格式化后的文本: {formatted_text}")
                        
                        # 断开信号连接
                        self.detail_table.itemChanged.disconnect(self.on_item_changed)
                        
                        # 创建新的单元格项并立即设置所有属性
                        new_item = QTableWidgetItem(formatted_text)
                        # 使用 RGB 值设置颜色
                        new_item.setForeground(QColor(255, 77, 79))  # 使用RGB值设置红色
                        new_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                        new_item.setFlags(new_item.flags() | Qt.ItemFlag.ItemIsEditable)
                        
                        # 设置到表格中
                        self.detail_table.setItem(row, column, new_item)
                        
                        # 强制更新显示
                        self.detail_table.viewport().update()
                        
                        # 重新连接信号
                        self.detail_table.itemChanged.connect(self.on_item_changed)
                        
                        # 验证颜色设置
                        # print(f"颜色设置验证: {new_item.foreground().color().name()}")
                        
                    except ValueError as ve:
                        # print(f"数值转换错误: {str(ve)}")
                        self.detail_table.setItem(row, column, QTableWidgetItem(""))
                
        except Exception as e:
            # print(f"处理单元格变化失败: {str(e)}")
            import traceback
            traceback.print_exc()
            
    def on_header_clicked(self, logical_index):
        """处理表头点击事件"""
        if logical_index == 0:  # 跳过操作列
            return
        
        # 获取当前排序顺序
        order = Qt.SortOrder.AscendingOrder
        if self.detail_table.horizontalHeader().sortIndicatorSection() == logical_index:
            if self.detail_table.horizontalHeader().sortIndicatorOrder() == Qt.SortOrder.AscendingOrder:
                order = Qt.SortOrder.DescendingOrder
        
        # 执行排序
        self.detail_table.sortItems(logical_index, order)
        
        # 更新删除按钮
        for row in range(self.detail_table.rowCount()):
            delete_btn = self.detail_table.cellWidget(row, 0)
            if delete_btn:
                # 断开旧的连接
                try:
                    delete_btn.clicked.disconnect()
                except:
                    pass
                # 重新连接到新的行号
                delete_btn.clicked.connect(lambda checked, r=row: self.delete_detail_row(r))
        
    def void_handover(self):
        """作废交接单"""
        try:
            handover_no = self.handover_no_edit.text()
            
            reply = QMessageBox.question(
                self,
                "确认作废",
                "确定要作废该交接单吗？\n作废后数据将不能修改！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                if self.return_db.void_handover_order(handover_no):
                    # 更新界面状态为作废状态
                    self.set_ui_state(False, True)
                    QMessageBox.information(self, "成功", "交接单作废成功！")
                    return True
            return False
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"作废失败：{str(e)}")
            return False
