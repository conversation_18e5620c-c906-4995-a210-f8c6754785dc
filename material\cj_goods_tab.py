from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                           QTableWidgetItem, QPushButton, QLineEdit, QLabel, 
                           QHeaderView, QMessageBox, QFrame, QStyle, QFileDialog,
                           QProgressDialog, QComboBox)
from PyQt6.QtCore import Qt
import pandas as pd
import os
from material.cj_goods_dialog import CJGoodsDialog
from material.cj_goods_db import CJGoodsDB

class CJGoodsTab(QWidget):
    """东方货品管理标签页"""
    
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.cj_goods_db = CJGoodsDB()
        # 设置当前用户
        if hasattr(db, 'current_user'):
            self.cj_goods_db.current_user = db.current_user
        self.current_page = 1  # 当前页码
        self.total_count = 0   # 总记录数
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        
        # 搜索区域
        search_frame = QFrame()
        search_frame.setStyleSheet("QFrame { background-color: #f5f5f5; }")
        search_layout = QHBoxLayout(search_frame)
        
        # 搜索条件组
        search_group = QFrame()
        search_group.setStyleSheet("QFrame { background-color: #f5f5f5; }")
        search_inner_layout = QHBoxLayout(search_group)
        search_inner_layout.setContentsMargins(10, 5, 10, 5)
        
        # 功能分组标签
        search_label = QLabel("搜索条件:")
        search_label.setStyleSheet("font-weight: bold;")
        search_inner_layout.addWidget(search_label)
        
        # 东方货号搜索
        search_inner_layout.addWidget(QLabel("东方货号:"))
        self.cj_code_edit = QLineEdit()
        self.cj_code_edit.setPlaceholderText("请输入东方货号")
        self.cj_code_edit.returnPressed.connect(self.search_goods)
        search_inner_layout.addWidget(self.cj_code_edit)
        
        # 东方名称搜索
        search_inner_layout.addWidget(QLabel("东方名称:"))
        self.cj_name_edit = QLineEdit()
        self.cj_name_edit.setPlaceholderText("请输入东方名称")
        self.cj_name_edit.returnPressed.connect(self.search_goods)
        search_inner_layout.addWidget(self.cj_name_edit)
        
        # 物料编号搜索
        search_inner_layout.addWidget(QLabel("物料编号:"))
        self.material_id_edit = QLineEdit()
        self.material_id_edit.setPlaceholderText("请输入物料编号")
        self.material_id_edit.returnPressed.connect(self.search_goods)
        search_inner_layout.addWidget(self.material_id_edit)
        
        # 搜索按钮
        self.search_btn = QPushButton("搜索")
        self.search_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogContentsView))
        self.search_btn.clicked.connect(self.search_goods)
        search_inner_layout.addWidget(self.search_btn)
        
        # 添加按钮
        self.add_btn = QPushButton("添加")
        self.add_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogNewFolder))
        self.add_btn.clicked.connect(self.add_goods)
        search_inner_layout.addWidget(self.add_btn)
        
        # 添加搜索组到布局
        search_layout.addWidget(search_group)
        layout.addWidget(search_frame)
        
        # 批量操作工具栏
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("QFrame { background-color: #f5f5f5; }")
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(10, 5, 10, 5)
        
        # 功能分组标签
        toolbar_label = QLabel("批量操作:")
        toolbar_label.setStyleSheet("font-weight: bold;")
        toolbar_layout.addWidget(toolbar_label)
        
        # 创建导入模板按钮
        self.create_template_btn = QPushButton("创建导入模板")
        self.create_template_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileIcon))
        self.create_template_btn.clicked.connect(self.create_import_template)
        toolbar_layout.addWidget(self.create_template_btn)
        
        # 导入按钮
        self.import_btn = QPushButton("导入Excel")
        self.import_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogOpenButton))
        self.import_btn.clicked.connect(self.import_excel)
        toolbar_layout.addWidget(self.import_btn)
        
        # 导出按钮
        self.export_btn = QPushButton("导出Excel")
        self.export_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogSaveButton))
        self.export_btn.clicked.connect(self.export_excel)
        toolbar_layout.addWidget(self.export_btn)
        
        # 批量删除按钮
        self.batch_delete_btn = QPushButton("批量删除")
        self.batch_delete_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_TrashIcon))
        self.batch_delete_btn.clicked.connect(self.batch_delete_goods)
        toolbar_layout.addWidget(self.batch_delete_btn)
        
        # 添加弹性占位符
        toolbar_layout.addStretch()
        
        # 添加工具栏到主布局
        layout.addWidget(toolbar_frame)
        
        # 表格
        self.table = QTableWidget()
        self.table.setColumnCount(6)  # 减少一列，移除操作列
        self.table.setHorizontalHeaderLabels([
            "东方货号", "东方名称", "物料编号", "物料名称", "箱规", "拆分编码"
        ])
        
        # 设置表格列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        
        # 设置表格属性
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        
        # 添加双击事件
        self.table.doubleClicked.connect(self.on_table_double_clicked)
        
        layout.addWidget(self.table)
        
        # 分页控件 - 移动到表格底部
        page_frame = QFrame()
        page_layout = QHBoxLayout(page_frame)
        page_layout.setContentsMargins(10, 8, 10, 8)
        
        # 左侧：总条数显示
        page_layout.addWidget(QLabel("总记录数:"))
        self.total_label = QLabel("0")
        page_layout.addWidget(self.total_label)
        
        # 添加弹性占位符
        page_layout.addStretch()
        
        # 当前页
        page_layout.addWidget(QLabel("当前页:"))
        self.current_page_label = QLabel("1")
        page_layout.addWidget(self.current_page_label)
        
        # 每页记录数
        page_layout.addWidget(QLabel("每页记录:"))
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["20", "50", "100"])
        self.page_size_combo.currentTextChanged.connect(self.on_page_size_changed)
        page_layout.addWidget(self.page_size_combo)
        
        # 翻页按钮
        self.prev_btn = QPushButton("上一页")
        self.prev_btn.clicked.connect(self.prev_page)
        page_layout.addWidget(self.prev_btn)
        
        self.next_btn = QPushButton("下一页")
        self.next_btn.clicked.connect(self.next_page)
        page_layout.addWidget(self.next_btn)
        
        layout.addWidget(page_frame)
        
        # 初始化数据
        self.search_goods()
    
    def search_goods(self):
        """搜索东方货品"""
        try:
            params = {
                "cj_code": self.cj_code_edit.text().strip(),
                "cj_name": self.cj_name_edit.text().strip(),
                "material_id": self.material_id_edit.text().strip(),
                "page": self.current_page,
                "page_size": int(self.page_size_combo.currentText())
            }
            
            total, goods_list = self.cj_goods_db.get_cj_goods(params)
            self.total_count = total
            self.update_table(goods_list)
            self.update_pagination()
            
        except Exception as e:
            QMessageBox.warning(self, "警告", f"搜索失败：{str(e)}")
    
    def update_table(self, goods_list):
        """更新表格数据"""
        self.table.setRowCount(0)
        for row, goods in enumerate(goods_list):
            self.table.insertRow(row)
            
            # 添加数据
            self.table.setItem(row, 0, QTableWidgetItem(goods.get("cj_code", "")))
            self.table.setItem(row, 1, QTableWidgetItem(goods.get("cj_name", "")))
            self.table.setItem(row, 2, QTableWidgetItem(goods.get("material_id", "")))
            self.table.setItem(row, 3, QTableWidgetItem(goods.get("material_name", "")))
            self.table.setItem(row, 4, QTableWidgetItem(goods.get("box_spec", "")))
            self.table.setItem(row, 5, QTableWidgetItem(goods.get("split_code", "")))
    
    def add_goods(self):
        """添加东方货品"""
        dialog = CJGoodsDialog(self)
        if dialog.exec():
            goods_data = dialog.get_goods_data()
            success, message = self.cj_goods_db.add_cj_goods(goods_data)
            if success:
                QMessageBox.information(self, "提示", "添加成功")
                self.search_goods()
            else:
                QMessageBox.warning(self, "警告", f"添加失败：{message}")
    
    def edit_goods(self, cj_code):
        """编辑东方货品"""
        # 获取当前货品数据
        params = {"cj_code": cj_code, "page": 1, "page_size": 1}
        _, goods_list = self.cj_goods_db.get_cj_goods(params)
        if not goods_list:
            QMessageBox.warning(self, "警告", "货品不存在")
            return
            
        dialog = CJGoodsDialog(self, goods_list[0])
        if dialog.exec():
            goods_data = dialog.get_goods_data()
            success, message = self.cj_goods_db.update_cj_goods(cj_code, goods_data)
            if success:
                QMessageBox.information(self, "提示", "更新成功")
                self.search_goods()
            else:
                QMessageBox.warning(self, "警告", f"更新失败：{message}")
    
    def delete_goods(self, cj_code):
        """删除东方货品"""
        reply = QMessageBox.question(
            self, "确认", 
            f"确定要删除东方货号为 {cj_code} 的货品吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            success, message = self.cj_goods_db.delete_cj_goods(cj_code)
            if success:
                QMessageBox.information(self, "提示", "删除成功")
                self.search_goods()
            else:
                QMessageBox.warning(self, "警告", f"删除失败：{message}")

    def create_import_template(self):
        """创建导入模板"""
        try:
            # 创建Excel文件
            import pandas as pd
            
            # 模板数据
            template_data = {
                '东方货号': ['示例：CJ001'],
                '东方名称': ['示例：测试商品'],
                '物料编号': ['示例：M001'],
                '箱规': ['示例：12'],
                '拆分编码': ['示例：SP001'],
            }
            
            df = pd.DataFrame(template_data)
            
            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "保存导入模板",
                "东方货品导入模板.xlsx",
                "Excel Files (*.xlsx)"
            )
            
            if file_path:
                # 保存Excel文件
                df.to_excel(file_path, index=False)
                QMessageBox.information(self, "提示", "导入模板已创建")
                
                # 打开文件所在目录
                import os
                os.startfile(os.path.dirname(os.path.abspath(file_path)))
                
        except Exception as e:
            QMessageBox.warning(self, "警告", f"创建模板失败：{str(e)}")

    def import_excel(self):
        """导入Excel数据"""
        try:
            # 选择Excel文件
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择Excel文件",
                "",
                "Excel Files (*.xlsx *.xls)"
            )
            
            if not file_path:
                return
                
            # 读取Excel文件
            import pandas as pd
            df = pd.read_excel(file_path)
            
            # 验证必填字段
            required_fields = ['东方货号', '东方名称', '物料编号']
            for field in required_fields:
                if field not in df.columns:
                    QMessageBox.warning(self, "警告", f"Excel文件缺少必填字段：{field}")
                    return
            
            # 准备批量导入数据
            goods_list = []
            for index, row in df.iterrows():
                goods_data = {
                    'cj_code': str(row['东方货号']).strip(),
                    'cj_name': str(row['东方名称']).strip(),
                    'material_id': str(row['物料编号']).strip(),
                    'box_spec': str(row['箱规']).strip() if '箱规' in row else '',
                    'split_code': str(row['拆分编码']).strip() if '拆分编码' in row else '',
                }
                goods_list.append(goods_data)
            
            # 创建进度对话框
            progress = QProgressDialog(self)
            progress.setWindowTitle("导入数据")
            progress.setLabelText("正在验证和导入数据...")
            progress.setCancelButtonText("取消")
            progress.setMinimumDuration(0)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.setRange(0, 1)
            progress.setValue(0)
            
            # 执行批量导入
            result = self.cj_goods_db.batch_add_cj_goods_with_validation(goods_list)
            
            progress.setValue(1)
            
            # 处理导入结果
            if result['error_count'] > 0:
                # 导出错误信息
                self.export_batch_import_errors(result['errors'])
            
            # 显示导入结果
            result_message = result['message']
            if result['error_count'] > 0:
                result_message += "\n\n错误信息已导出到Excel文件"
            
            QMessageBox.information(self, "导入结果", result_message)
            
            # 刷新表格
            self.search_goods()
            
        except Exception as e:
            QMessageBox.warning(self, "警告", f"导入失败：{str(e)}")

    def export_batch_import_errors(self, errors):
        """导出批量导入错误信息"""
        try:
            # 创建导出数据
            export_data = []
            
            for error in errors:
                export_data.append({
                    '行号': error['index'],
                    '错误类型': error['type'],
                    '错误描述': error['message'],
                    '东方货号': error['data'].get('cj_code', ''),
                    '东方名称': error['data'].get('cj_name', ''),
                    '物料编号': error['data'].get('material_id', ''),
                    '建议操作': self.get_error_suggestion(error['type'])
                })
            
            df = pd.DataFrame(export_data)
            
            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出导入错误信息",
                "东方货品批量导入错误报告.xlsx",
                "Excel Files (*.xlsx)"
            )
            
            if file_path:
                # 保存Excel文件
                df.to_excel(file_path, index=False)
                QMessageBox.information(self, "提示", f"错误信息已导出到：{file_path}")
                
                # 打开文件所在目录
                import os
                os.startfile(os.path.dirname(os.path.abspath(file_path)))
                
        except Exception as e:
            QMessageBox.warning(self, "警告", f"导出错误信息失败：{str(e)}")

    def export_import_errors(self, invalid_materials, non_east_materials, error_records):
        """导出导入错误信息"""
        try:
            # 创建导出数据
            export_data = []
            
            # 添加不存在的物料编号
            for material_id in invalid_materials:
                export_data.append({
                    '物料编号': material_id,
                    '错误类型': '物料不存在',
                    '错误描述': '该物料编号在数据库中不存在',
                    '建议操作': '请检查物料编号是否正确，或先在物料管理中添加该物料'
                })
            
            # 添加非东方货物的物料编号
            for material_id in non_east_materials:
                export_data.append({
                    '物料编号': material_id,
                    '错误类型': '非东方货物',
                    '错误描述': '该物料不是东方货物（is_east_material != 1）',
                    '建议操作': '请在物料管理中将该物料标记为东方货物'
                })
            
            # 添加其他错误记录
            for error in error_records:
                # 从错误信息中提取物料编号（如果存在）
                material_id = "未知"
                if "物料编号" in error:
                    # 尝试从错误信息中提取物料编号
                    import re
                    match = re.search(r'物料编号\s+([^\s]+)', error)
                    if match:
                        material_id = match.group(1)
                
                export_data.append({
                    '物料编号': material_id,
                    '错误类型': '其他错误',
                    '错误描述': error,
                    '建议操作': '请检查数据格式和业务规则'
                })
            
            df = pd.DataFrame(export_data)
            
            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出导入错误信息",
                "东方货品导入错误报告.xlsx",
                "Excel Files (*.xlsx)"
            )
            
            if file_path:
                # 保存Excel文件
                df.to_excel(file_path, index=False)
                QMessageBox.information(self, "提示", f"错误信息已导出到：{file_path}")
                
                # 打开文件所在目录
                import os
                os.startfile(os.path.dirname(os.path.abspath(file_path)))
                
        except Exception as e:
            QMessageBox.warning(self, "警告", f"导出错误信息失败：{str(e)}")

    def export_invalid_materials(self, material_ids):
        """导出不存在的物料编号（保留原方法以兼容）"""
        try:
            # 创建导出数据
            export_data = {
                '物料编号': material_ids,
                '错误类型': ['物料不存在'] * len(material_ids),
                '错误描述': ['该物料编号在数据库中不存在'] * len(material_ids),
                '建议操作': ['请检查物料编号是否正确，或先在物料管理中添加该物料'] * len(material_ids)
            }
            
            df = pd.DataFrame(export_data)
            
            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出无效物料编号",
                "无效物料编号清单.xlsx",
                "Excel Files (*.xlsx)"
            )
            
            if file_path:
                # 保存Excel文件
                df.to_excel(file_path, index=False)
                QMessageBox.information(self, "提示", "无效物料编号已导出")
                
                # 打开文件所在目录
                import os
                os.startfile(os.path.dirname(os.path.abspath(file_path)))
                
        except Exception as e:
            QMessageBox.warning(self, "警告", f"导出失败：{str(e)}")

    def export_excel(self):
        """导出Excel数据"""
        try:
            # 获取所有数据（导出时不分页）
            params = {
                "cj_code": self.cj_code_edit.text().strip(),
                "cj_name": self.cj_name_edit.text().strip(),
                "material_id": self.material_id_edit.text().strip(),
                "page": 1,
                "page_size": self.total_count or 10000  # 使用总条数，确保导出全部数据
            }
            
            total, goods_list = self.cj_goods_db.get_cj_goods(params)
            
            if not goods_list:
                QMessageBox.warning(self, "警告", "没有数据可导出")
                return
            
            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出Excel",
                "东方货品数据.xlsx",
                "Excel Files (*.xlsx)"
            )
            
            if file_path:
                # 转换数据
                export_data = []
                for goods in goods_list:
                    export_data.append({
                        '东方货号': goods.get('cj_code', ''),
                        '东方名称': goods.get('cj_name', ''),
                        '物料编号': goods.get('material_id', ''),
                        '物料名称': goods.get('material_name', ''),
                        '箱规': goods.get('box_spec', ''),
                        '拆分编码': goods.get('split_code', ''),
                        '创建时间': goods.get('create_time', ''),
                        '创建人': goods.get('create_user', ''),
                        '更新时间': goods.get('update_time', ''),
                        '更新人': goods.get('update_user', '')
                    })
                
                # 创建DataFrame并导出
                import pandas as pd
                df = pd.DataFrame(export_data)
                df.to_excel(file_path, index=False)
                
                QMessageBox.information(self, "提示", f"成功导出 {len(goods_list)} 条数据")
                
                # 打开文件所在目录
                import os
                os.startfile(os.path.dirname(os.path.abspath(file_path)))
                
        except Exception as e:
            QMessageBox.warning(self, "警告", f"导出失败：{str(e)}")

    def batch_delete_goods(self):
        """批量删除东方货品"""
        # 获取选中的行
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())
        
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要删除的记录")
            return
        
        # 获取选中行的东方货号
        cj_codes = []
        for row in selected_rows:
            cj_code = self.table.item(row, 0).text()
            cj_codes.append(cj_code)
        
        # 确认删除
        reply = QMessageBox.question(
            self, "确认", 
            f"确定要删除选中的 {len(cj_codes)} 条记录吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 创建进度对话框
            progress = QProgressDialog(self)
            progress.setWindowTitle("删除数据")
            progress.setLabelText("正在删除...")
            progress.setCancelButtonText("取消")
            progress.setMinimumDuration(0)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.setRange(0, len(cj_codes))
            
            # 执行删除
            success_count = 0
            error_messages = []
            
            for i, cj_code in enumerate(cj_codes):
                if progress.wasCanceled():
                    break
                    
                success, message = self.cj_goods_db.delete_cj_goods(cj_code)
                if success:
                    success_count += 1
                else:
                    error_messages.append(f"东方货号 {cj_code}: {message}")
                
                progress.setValue(i + 1)
            
            # 显示删除结果
            result_message = f"删除完成！\n成功：{success_count}条\n失败：{len(error_messages)}条"
            if error_messages:
                result_message += "\n\n失败记录：\n" + "\n".join(error_messages)
            
            QMessageBox.information(self, "删除结果", result_message)
            
            # 刷新表格
            self.search_goods()

    def on_table_double_clicked(self, index):
        """处理表格双击事件"""
        row = index.row()
        cj_code = self.table.item(row, 0).text()  # 获取东方货号
        
        # 获取当前货品数据
        params = {"cj_code": cj_code, "page": 1, "page_size": 1}
        _, goods_list = self.cj_goods_db.get_cj_goods(params)
        if not goods_list:
            QMessageBox.warning(self, "警告", "货品不存在")
            return
            
        # 弹出编辑对话框
        dialog = CJGoodsDialog(self, goods_list[0])
        if dialog.exec():
            goods_data = dialog.get_goods_data()
            success, message = self.cj_goods_db.update_cj_goods(cj_code, goods_data)
            if success:
                QMessageBox.information(self, "提示", "更新成功")
                self.search_goods()
            else:
                QMessageBox.warning(self, "警告", f"更新失败：{message}")

    def update_pagination(self):
        """更新分页控件状态"""
        # 更新总条数
        self.total_label.setText(str(self.total_count))
        
        # 计算总页数
        page_size = int(self.page_size_combo.currentText())
        total_pages = max((self.total_count + page_size - 1) // page_size, 1)
        
        # 更新当前页显示
        self.current_page_label.setText(str(self.current_page))
        
        # 更新按钮状态
        self.prev_btn.setEnabled(self.current_page > 1)
        self.next_btn.setEnabled(self.current_page < total_pages)
    
    def on_page_size_changed(self, value):
        """每页记录数改变"""
        self.current_page = 1  # 重置到第一页
        self.search_goods()
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.search_goods()
    
    def next_page(self):
        """下一页"""
        page_size = int(self.page_size_combo.currentText())
        total_pages = (self.total_count + page_size - 1) // page_size
        if self.current_page < total_pages:
            self.current_page += 1
            self.search_goods()

    def get_error_suggestion(self, error_type):
        """获取错误建议"""
        suggestions = {
            'missing_fields': '请检查必填字段是否完整',
            'duplicate_code': '请检查东方货号是否重复',
            'invalid_material': '请确保物料编号存在且标记为东方货物',
            'database_error': '请检查数据格式和业务规则'
        }
        return suggestions.get(error_type, '请检查数据格式')