-- =====================================================
-- 需求计划表结构优化脚本（修复版）
-- 执行日期: 2025-01-21
-- 说明: 修复外键约束和索引问题
-- =====================================================

USE migosys;

-- 开始事务
START TRANSACTION;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 删除现有表（按依赖关系顺序）
-- =====================================================
DROP TABLE IF EXISTS `demand_executions`;
DROP TABLE IF EXISTS `demand_bom_materials`;
DROP TABLE IF EXISTS `demand_plan_details`;
DROP TABLE IF EXISTS `demand_plans`;

-- =====================================================
-- 2. 创建需求计划主表 (demand_plans)
-- =====================================================
CREATE TABLE `demand_plans` (
  `plan_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '需求计划主键',
  `plan_no` varchar(50) NOT NULL COMMENT '需求计划编号（自动生成，格式：XQJH-YYYYMMDD-序号）',
  `plan_date` date NOT NULL COMMENT '计划日期',
  `plan_name` varchar(200) NULL DEFAULT NULL COMMENT '计划名称',
  `department` varchar(100) NOT NULL COMMENT '申请部门',
  `applicant` varchar(50) NOT NULL COMMENT '申请人',
  `demand_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '需求类型：1-生产需求，2-销售需求，3-库存补充，4-其他需求',
  `priority_level` tinyint(4) NOT NULL DEFAULT 1 COMMENT '紧急程度：1-普通，2-紧急，3-特急',
  `plan_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '计划状态：0-草稿，1-已确认，2-执行中，3-已完成，9-已取消',
  `total_amount` decimal(18,2) NULL DEFAULT 0.00 COMMENT '计划总金额（预估）',
  `remark` text NULL COMMENT '备注说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(50) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `update_user` varchar(50) NULL DEFAULT NULL COMMENT '最后更新人',
  `is_locked` tinyint(4) NOT NULL DEFAULT 0 COMMENT '锁定状态：0-未锁定，1-已锁定（已生成执行计划）',
  `approval_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '审批状态：0-待审批，1-已审批，2-已驳回',
  `approver` varchar(50) NULL DEFAULT NULL COMMENT '审批人',
  `approval_time` datetime NULL DEFAULT NULL COMMENT '审批时间',
  `approval_remark` text NULL COMMENT '审批备注',
  
  PRIMARY KEY (`plan_id`),
  UNIQUE KEY `uk_plan_no` (`plan_no`),
  KEY `idx_plan_date` (`plan_date`),
  KEY `idx_department` (`department`),
  KEY `idx_plan_status` (`plan_status`),
  KEY `idx_demand_type` (`demand_type`),
  KEY `idx_priority_level` (`priority_level`),
  KEY `idx_approval_status` (`approval_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='需求计划主表';

-- =====================================================
-- 3. 创建需求计划明细表 (demand_plan_details)
-- =====================================================
CREATE TABLE `demand_plan_details` (
  `detail_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细主键',
  `plan_id` int(11) NOT NULL COMMENT '需求计划主键',
  `plan_no` varchar(50) NOT NULL COMMENT '需求计划编号（冗余字段，便于查询）',
  `sequence_no` int(11) NOT NULL COMMENT '序号',
  `material_id` varchar(50) NOT NULL COMMENT '成品物料编码',
  `material_name` varchar(200) NULL DEFAULT NULL COMMENT '物料名称（冗余字段）',
  `material_category_id` int(11) NULL DEFAULT NULL COMMENT '物料分类ID',
  `required_qty` decimal(18,4) NOT NULL COMMENT '需求数量',
  `required_unit` varchar(20) NOT NULL COMMENT '需求单位',
  `unit_price` decimal(10,4) NULL DEFAULT NULL COMMENT '预估单价',
  `total_amount` decimal(18,4) NULL DEFAULT NULL COMMENT '预估总金额',
  `required_date` date NOT NULL COMMENT '需求日期',
  `usage_purpose` varchar(200) NULL DEFAULT NULL COMMENT '用途说明',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  

  `execution_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '执行状态：0-未执行，1-部分执行，2-已完成',
  `executed_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '已执行数量',
  
 
  `production_plan_no` varchar(50) NULL DEFAULT NULL COMMENT '关联的生产计划编号',
  `production_plan_id` int(11) NULL DEFAULT NULL COMMENT '关联的生产计划ID',
  

  `purchase_request_no` varchar(50) NULL DEFAULT NULL COMMENT '关联的采购申请单号',
  `purchase_request_id` int(11) NULL DEFAULT NULL COMMENT '关联的采购申请单ID',
  

  `bom_id` int(11) NULL DEFAULT NULL COMMENT '使用的BOM主键',
  `bom_code` varchar(50) NULL DEFAULT NULL COMMENT '使用的BOM编码',
  `is_bom_expanded` tinyint(4) NOT NULL DEFAULT 0 COMMENT 'BOM是否已展开：0-未展开，1-已展开',
  `bom_expand_time` datetime NULL DEFAULT NULL COMMENT 'BOM展开时间',
  
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(50) NULL DEFAULT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) NULL DEFAULT NULL,
  
  PRIMARY KEY (`detail_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_plan_no` (`plan_no`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_material_category` (`material_category_id`),
  KEY `idx_required_date` (`required_date`),
  KEY `idx_execution_status` (`execution_status`),
  KEY `idx_production_plan` (`production_plan_id`),
  KEY `idx_purchase_request` (`purchase_request_id`),
  KEY `idx_bom_id` (`bom_id`),
  KEY `idx_bom_expanded` (`is_bom_expanded`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='需求计划明细表（成品物料需求）';

-- =====================================================
-- 4. 创建BOM展开需求表 (demand_bom_materials)
-- =====================================================
CREATE TABLE `demand_bom_materials` (
  `bom_material_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'BOM展开物料主键',
  `plan_id` int(11) NOT NULL COMMENT '需求计划主键',
  `detail_id` int(11) NOT NULL COMMENT '需求明细主键（成品物料）',
  `plan_no` varchar(50) NOT NULL COMMENT '需求计划编号',
  `parent_material_id` varchar(50) NOT NULL COMMENT '父级成品物料编码',
  `parent_required_qty` decimal(18,4) NOT NULL COMMENT '父级成品需求数量',
  

  `bom_id` int(11) NOT NULL COMMENT 'BOM主键',
  `bom_code` varchar(50) NOT NULL COMMENT 'BOM编码',
  

  `material_id` varchar(50) NOT NULL COMMENT '展开后的物料编码（原材料或包材）',
  `material_name` varchar(200) NULL DEFAULT NULL COMMENT '物料名称',
  `material_category_id` int(11) NULL DEFAULT NULL COMMENT '物料分类ID',
  `material_type` tinyint(4) NOT NULL COMMENT '物料类型：1-原材料，2-包材',
  

  `unit_required_qty` decimal(18,4) NOT NULL COMMENT '单位需求数量（BOM中的用量）',
  `required_unit` varchar(20) NOT NULL COMMENT '需求单位',
  `total_required_qty` decimal(18,4) NOT NULL COMMENT '总需求数量（单位用量×成品数量）',
  `scrap_rate` decimal(5,2) NULL DEFAULT 0.00 COMMENT '损耗率（%）',
  `actual_required_qty` decimal(18,4) NOT NULL COMMENT '实际需求数量（含损耗）',
  

  `unit_cost` decimal(10,4) NULL DEFAULT NULL COMMENT '单位成本',
  `total_cost` decimal(18,4) NULL DEFAULT NULL COMMENT '总成本',
  

  `supplier_id` varchar(50) NULL DEFAULT NULL COMMENT '建议供应商ID',
  

  `execution_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '执行状态：0-未执行，1-部分执行，2-已完成',
  `executed_qty` decimal(18,4) NULL DEFAULT 0.0000 COMMENT '已执行数量',
  

  `purchase_request_no` varchar(50) NULL DEFAULT NULL COMMENT '关联的采购申请单号',
  `purchase_request_id` int(11) NULL DEFAULT NULL COMMENT '关联的采购申请单ID',
  
  `required_date` date NOT NULL COMMENT '需求日期（继承自成品需求日期）',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(50) NULL DEFAULT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) NULL DEFAULT NULL,
  
  PRIMARY KEY (`bom_material_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_detail_id` (`detail_id`),
  KEY `idx_plan_no` (`plan_no`),
  KEY `idx_parent_material` (`parent_material_id`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_material_type` (`material_type`),
  KEY `idx_bom_id` (`bom_id`),
  KEY `idx_execution_status` (`execution_status`),
  KEY `idx_required_date` (`required_date`),
  KEY `idx_purchase_request` (`purchase_request_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='BOM展开需求表（原材料和包材需求）';

-- =====================================================
-- 5. 创建需求执行跟踪表 (demand_executions)
-- =====================================================
CREATE TABLE `demand_executions` (
  `execution_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '执行记录主键',
  `plan_id` int(11) NOT NULL COMMENT '需求计划主键',
  `detail_id` int(11) NULL DEFAULT NULL COMMENT '需求明细主键（成品物料）',
  `bom_material_id` int(11) NULL DEFAULT NULL COMMENT 'BOM展开物料主键（原材料/包材）',
  `plan_no` varchar(50) NOT NULL COMMENT '需求计划编号',
  `material_id` varchar(50) NOT NULL COMMENT '物料编码',
  `material_type` tinyint(4) NOT NULL COMMENT '物料类型：1-成品，2-原材料，3-包材',
  
  `execution_type` tinyint(4) NOT NULL COMMENT '执行类型：1-采购执行，2-生产执行，3-调拨执行',
  `execution_no` varchar(50) NULL DEFAULT NULL COMMENT '执行单号（采购单号/生产单号等）',
  `executed_qty` decimal(18,4) NOT NULL COMMENT '执行数量',
  `execution_date` date NOT NULL COMMENT '执行日期',
  `executor` varchar(50) NULL DEFAULT NULL COMMENT '执行人',
  `execution_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '执行状态：1-已执行，2-已完成，3-已取消',
  `remark` text NULL COMMENT '执行备注',
  
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(50) NULL DEFAULT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) NULL DEFAULT NULL,
  
  PRIMARY KEY (`execution_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_detail_id` (`detail_id`),
  KEY `idx_bom_material_id` (`bom_material_id`),
  KEY `idx_plan_no` (`plan_no`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_execution_type` (`execution_type`),
  KEY `idx_execution_date` (`execution_date`),
  KEY `idx_execution_status` (`execution_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='需求执行跟踪表';

-- =====================================================
-- 6. 添加外键约束（在所有表创建完成后）
-- =====================================================

-- 需求计划明细表外键
ALTER TABLE `demand_plan_details` 
ADD CONSTRAINT `fk_demand_detail_plan` FOREIGN KEY (`plan_id`) REFERENCES `demand_plans` (`plan_id`) ON DELETE CASCADE ON UPDATE RESTRICT;

-- BOM展开需求表外键
ALTER TABLE `demand_bom_materials` 
ADD CONSTRAINT `fk_bom_material_plan` FOREIGN KEY (`plan_id`) REFERENCES `demand_plans` (`plan_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
ADD CONSTRAINT `fk_bom_material_detail` FOREIGN KEY (`detail_id`) REFERENCES `demand_plan_details` (`detail_id`) ON DELETE CASCADE ON UPDATE RESTRICT;

-- 需求执行跟踪表外键
ALTER TABLE `demand_executions` 
ADD CONSTRAINT `fk_execution_plan` FOREIGN KEY (`plan_id`) REFERENCES `demand_plans` (`plan_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
ADD CONSTRAINT `fk_execution_detail` FOREIGN KEY (`detail_id`) REFERENCES `demand_plan_details` (`detail_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
ADD CONSTRAINT `fk_execution_bom_material` FOREIGN KEY (`bom_material_id`) REFERENCES `demand_bom_materials` (`bom_material_id`) ON DELETE CASCADE ON UPDATE RESTRICT;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 提交事务
COMMIT;

-- =====================================================
-- 执行完成提示
-- =====================================================
SELECT 
    '需求计划表结构优化完成！' as message,
    '修复了外键约束和索引问题' as description,
    NOW() as update_time;