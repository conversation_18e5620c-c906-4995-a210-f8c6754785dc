"""
期初库存录入对话框
用于录入期初库存数据
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QComboBox, QFormLayout, QDialogButtonBox, QDoubleSpinBox,
                           QDateEdit, QTextEdit, QMessageBox, QPushButton, QFrame,
                           QGroupBox, QGridLayout)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QIcon
from datetime import datetime, date

from core.logger import Logger
from core.config import get_current_user
from inventory.inventory_db import InventoryDB
from inventory.inventory_permission_service import InventoryPermissionService


class InventoryInitialDialog(QDialog):
    """期初库存录入对话框"""
    
    data_saved = pyqtSignal()  # 数据保存成功信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.inventory_db = InventoryDB()
        self.permission_service = InventoryPermissionService()
        self.current_user = get_current_user()
        
        self.setWindowTitle("期初库存录入")
        self.setModal(True)
        self.resize(600, 500)
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建物料信息区域
        self.create_material_section(main_layout)
        
        # 创建库存信息区域
        self.create_inventory_section(main_layout)
        
        # 创建其他信息区域
        self.create_other_section(main_layout)
        
        # 创建按钮区域
        self.create_button_section(main_layout)
    
    def create_material_section(self, parent_layout):
        """创建物料信息区域"""
        # 物料信息卡片
        material_card = QFrame()
        material_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        material_layout = QVBoxLayout(material_card)
        material_layout.setContentsMargins(20, 15, 20, 20)
        material_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📦 物料信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        material_layout.addWidget(title_label)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)
        
        # 第一行：物料编码、选择按钮
        form_layout.addWidget(QLabel("物料编码:"), 0, 0)
        self.material_id_edit = QLineEdit()
        self.material_id_edit.setReadOnly(True)
        self.material_id_edit.setPlaceholderText("请选择物料...")
        form_layout.addWidget(self.material_id_edit, 0, 1)
        
        self.select_material_btn = QPushButton("选择物料")
        self.select_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.select_material_btn.clicked.connect(self.select_material)
        form_layout.addWidget(self.select_material_btn, 0, 2)
        
        # 第二行：物料名称、物料分类
        form_layout.addWidget(QLabel("物料名称:"), 1, 0)
        self.material_name_edit = QLineEdit()
        self.material_name_edit.setReadOnly(True)
        form_layout.addWidget(self.material_name_edit, 1, 1, 1, 2)
        
        # 第三行：物料分类
        form_layout.addWidget(QLabel("物料分类:"), 2, 0)
        self.category_name_edit = QLineEdit()
        self.category_name_edit.setReadOnly(True)
        form_layout.addWidget(self.category_name_edit, 2, 1, 1, 2)
        
        material_layout.addWidget(form_widget)
        parent_layout.addWidget(material_card)
    
    def create_inventory_section(self, parent_layout):
        """创建库存信息区域"""
        # 库存信息卡片
        inventory_card = QFrame()
        inventory_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        inventory_layout = QVBoxLayout(inventory_card)
        inventory_layout.setContentsMargins(20, 15, 20, 20)
        inventory_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📊 库存信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        ""