"""
期初库存录入对话框
用于录入期初库存数据
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QComboBox, QFormLayout, QDialogButtonBox, QDoubleSpinBox,
                           QDateEdit, QTextEdit, QMessageBox, QPushButton, QFrame,
                           QGroupBox, QGridLayout, QWidget)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QIcon
from datetime import datetime, date

from core.logger import Logger
from core.config import get_current_user
from inventory.inventory_db import InventoryDB
from inventory.inventory_permission_service import InventoryPermissionService


class InventoryInitialDialog(QDialog):
    """期初库存录入对话框"""
    
    data_saved = pyqtSignal()  # 数据保存成功信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.inventory_db = InventoryDB()
        self.permission_service = InventoryPermissionService()
        self.current_user = get_current_user()
        self.selected_material = None  # 存储选中的物料信息

        self.setWindowTitle("期初库存录入")
        self.setModal(True)
        self.resize(600, 500)
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建物料信息区域
        self.create_material_section(main_layout)
        
        # 创建库存信息区域
        self.create_inventory_section(main_layout)
        
        # 创建其他信息区域
        self.create_other_section(main_layout)
        
        # 创建按钮区域
        self.create_button_section(main_layout)
    
    def create_material_section(self, parent_layout):
        """创建物料信息区域"""
        # 物料信息卡片
        material_card = QFrame()
        material_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        material_layout = QVBoxLayout(material_card)
        material_layout.setContentsMargins(20, 15, 20, 20)
        material_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📦 物料信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        material_layout.addWidget(title_label)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)
        
        # 第一行：物料编码、选择按钮
        form_layout.addWidget(QLabel("物料编码:"), 0, 0)
        self.material_id_edit = QLineEdit()
        self.material_id_edit.setReadOnly(True)
        self.material_id_edit.setPlaceholderText("请选择物料...")
        form_layout.addWidget(self.material_id_edit, 0, 1)
        
        self.select_material_btn = QPushButton("选择物料")
        self.select_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.select_material_btn.clicked.connect(self.select_material)
        form_layout.addWidget(self.select_material_btn, 0, 2)
        
        # 第二行：物料名称、物料分类
        form_layout.addWidget(QLabel("物料名称:"), 1, 0)
        self.material_name_edit = QLineEdit()
        self.material_name_edit.setReadOnly(True)
        form_layout.addWidget(self.material_name_edit, 1, 1, 1, 2)
        
        # 第三行：物料分类
        form_layout.addWidget(QLabel("物料分类:"), 2, 0)
        self.category_name_edit = QLineEdit()
        self.category_name_edit.setReadOnly(True)
        form_layout.addWidget(self.category_name_edit, 2, 1, 1, 2)
        
        material_layout.addWidget(form_widget)
        parent_layout.addWidget(material_card)
    
    def create_inventory_section(self, parent_layout):
        """创建库存信息区域"""
        # 库存信息卡片
        inventory_card = QFrame()
        inventory_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        inventory_layout = QVBoxLayout(inventory_card)
        inventory_layout.setContentsMargins(20, 15, 20, 20)
        inventory_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📊 库存信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        inventory_layout.addWidget(title_label)

        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)

        # 第一行：部门、仓库
        form_layout.addWidget(QLabel("归属部门:"), 0, 0)
        self.department_combo = QComboBox()
        self.load_department_options()
        form_layout.addWidget(self.department_combo, 0, 1)

        form_layout.addWidget(QLabel("仓库:"), 0, 2)
        self.warehouse_combo = QComboBox()
        self.load_warehouse_options()
        form_layout.addWidget(self.warehouse_combo, 0, 3)

        # 第二行：期初数量、单位
        form_layout.addWidget(QLabel("期初数量:"), 1, 0)
        self.initial_qty_spin = QDoubleSpinBox()
        self.initial_qty_spin.setRange(0, 999999.9999)
        self.initial_qty_spin.setDecimals(4)
        self.initial_qty_spin.setSuffix(" ")
        form_layout.addWidget(self.initial_qty_spin, 1, 1)

        form_layout.addWidget(QLabel("单位:"), 1, 2)
        self.unit_edit = QLineEdit()
        self.unit_edit.setReadOnly(True)
        form_layout.addWidget(self.unit_edit, 1, 3)

        # 第三行：单位成本、总成本
        form_layout.addWidget(QLabel("单位成本:"), 2, 0)
        self.unit_cost_spin = QDoubleSpinBox()
        self.unit_cost_spin.setRange(0, 99999.9999)
        self.unit_cost_spin.setDecimals(4)
        self.unit_cost_spin.setSuffix(" 元")
        self.unit_cost_spin.valueChanged.connect(self.calculate_total_cost)
        form_layout.addWidget(self.unit_cost_spin, 2, 1)

        form_layout.addWidget(QLabel("总成本:"), 2, 2)
        self.total_cost_edit = QLineEdit()
        self.total_cost_edit.setReadOnly(True)
        form_layout.addWidget(self.total_cost_edit, 2, 3)

        # 连接数量变化事件
        self.initial_qty_spin.valueChanged.connect(self.calculate_total_cost)

        inventory_layout.addWidget(form_widget)
        parent_layout.addWidget(inventory_card)

    def create_other_section(self, parent_layout):
        """创建其他信息区域"""
        # 其他信息卡片
        other_card = QFrame()
        other_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        other_layout = QVBoxLayout(other_card)
        other_layout.setContentsMargins(20, 15, 20, 20)
        other_layout.setSpacing(15)

        # 标题
        title_label = QLabel("📝 其他信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        other_layout.addWidget(title_label)

        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)

        # 第一行：批次号、生产日期
        form_layout.addWidget(QLabel("批次号:"), 0, 0)
        self.batch_no_edit = QLineEdit()
        self.batch_no_edit.setPlaceholderText("可选，用于批次管理...")
        form_layout.addWidget(self.batch_no_edit, 0, 1)

        form_layout.addWidget(QLabel("生产日期:"), 0, 2)
        self.production_date_edit = QDateEdit()
        self.production_date_edit.setDate(QDate.currentDate())
        self.production_date_edit.setCalendarPopup(True)
        form_layout.addWidget(self.production_date_edit, 0, 3)

        # 第二行：过期日期、状态
        form_layout.addWidget(QLabel("过期日期:"), 1, 0)
        self.expiry_date_edit = QDateEdit()
        self.expiry_date_edit.setDate(QDate.currentDate().addYears(1))
        self.expiry_date_edit.setCalendarPopup(True)
        form_layout.addWidget(self.expiry_date_edit, 1, 1)

        form_layout.addWidget(QLabel("状态:"), 1, 2)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["正常", "冻结", "报废"])
        form_layout.addWidget(self.status_combo, 1, 3)

        # 第三行：备注
        form_layout.addWidget(QLabel("备注:"), 2, 0)
        self.remark_edit = QTextEdit()
        self.remark_edit.setMaximumHeight(80)
        self.remark_edit.setPlaceholderText("可选，输入备注信息...")
        form_layout.addWidget(self.remark_edit, 2, 1, 1, 3)

        other_layout.addWidget(form_widget)
        parent_layout.addWidget(other_card)

    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                color: #666;
                border: 1px solid #d9d9d9;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 14px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #e6f7ff;
                border-color: #1890ff;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        # 保存按钮
        self.save_btn = QPushButton("保存")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.save_btn.clicked.connect(self.save_initial_inventory)
        button_layout.addWidget(self.save_btn)

        parent_layout.addLayout(button_layout)

    # ==================== 业务逻辑方法 ====================

    def select_material(self):
        """选择物料"""
        try:
            from material.material_search_dialog import MaterialSearchDialog
            dialog = MaterialSearchDialog(self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                selected_material = dialog.get_selected_material()
                if selected_material:
                    self.material_id_edit.setText(selected_material['material_id'])
                    self.material_name_edit.setText(selected_material['name'])
                    self.category_name_edit.setText(selected_material.get('category_name', ''))
                    self.unit_edit.setText(selected_material.get('base_unit', 'PCS'))

                    # 设置默认成本
                    if selected_material.get('cost'):
                        self.unit_cost_spin.setValue(float(selected_material['cost']))

                    # 存储完整的物料信息
                    self.selected_material = selected_material

        except ImportError:
            QMessageBox.warning(self, "提示", "物料搜索功能暂未实现，请手动输入物料信息")
        except Exception as e:
            Logger.log_error(f"选择物料失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"选择物料失败：{str(e)}")

    def calculate_total_cost(self):
        """计算总成本"""
        try:
            qty = self.initial_qty_spin.value()
            unit_cost = self.unit_cost_spin.value()
            total_cost = qty * unit_cost
            self.total_cost_edit.setText(f"{total_cost:.2f}")
        except Exception as e:
            Logger.log_error(f"计算总成本失败: {str(e)}")

    def load_department_options(self):
        """加载部门选项"""
        try:
            # 根据用户权限加载可访问的部门
            user_departments = self.current_user.get('departments', [])
            if self.permission_service.has_permission('system:admin'):
                # 管理员可以看到所有部门
                departments = ["通路一", "通路二", "生产部", "采购部"]
            else:
                departments = user_departments or [self.current_user.get('department', '')]

            for dept in departments:
                if dept:  # 确保部门不为空
                    self.department_combo.addItem(dept)

        except Exception as e:
            Logger.log_error(f"加载部门选项失败: {str(e)}")

    def load_warehouse_options(self):
        """加载仓库选项"""
        try:
            # TODO: 从数据库加载仓库列表
            warehouses = [
                ("WH001", "主仓库"),
                ("WH002", "原料仓"),
                ("WH003", "成品仓")
            ]

            for code, name in warehouses:
                self.warehouse_combo.addItem(f"{name}({code})", code)

        except Exception as e:
            Logger.log_error(f"加载仓库选项失败: {str(e)}")

    def validate_input(self):
        """验证输入数据"""
        if not self.material_id_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "请选择物料")
            return False

        if not self.department_combo.currentText():
            QMessageBox.warning(self, "验证失败", "请选择归属部门")
            return False

        if self.initial_qty_spin.value() <= 0:
            QMessageBox.warning(self, "验证失败", "期初数量必须大于0")
            return False

        # 检查部门权限
        department = self.department_combo.currentText()
        if not self.permission_service.check_department_permission(department):
            QMessageBox.warning(self, "权限不足", f"您没有操作部门 '{department}' 库存的权限")
            return False

        return True

    def save_initial_inventory(self):
        """保存期初库存"""
        try:
            # 验证输入
            if not self.validate_input():
                return

            # 构建保存数据
            inventory_data = {
                'material_id': self.material_id_edit.text().strip(),
                'material_name': self.material_name_edit.text().strip(),
                'material_category_id': getattr(self, 'selected_material', {}).get('category_id'),
                'department': self.department_combo.currentText(),
                'warehouse_code': self.warehouse_combo.currentData(),
                'warehouse_name': self.warehouse_combo.currentText().split('(')[0] if '(' in self.warehouse_combo.currentText() else self.warehouse_combo.currentText(),
                'initial_qty': self.initial_qty_spin.value(),
                'unit': self.unit_edit.text().strip() or 'PCS',
                'unit_cost': self.unit_cost_spin.value(),
                'total_cost': self.initial_qty_spin.value() * self.unit_cost_spin.value(),
                'batch_no': self.batch_no_edit.text().strip() or None,
                'production_date': self.production_date_edit.date().toPython() if self.production_date_edit.date().isValid() else None,
                'expiry_date': self.expiry_date_edit.date().toPython() if self.expiry_date_edit.date().isValid() else None,
                'status': self.status_combo.currentIndex() + 1,  # 1-正常，2-冻结，3-报废
                'remark': self.remark_edit.toPlainText().strip() or None
            }

            # 保存到数据库
            initial_id = self.inventory_db.create_initial_inventory(inventory_data)

            QMessageBox.information(self, "成功", f"期初库存录入成功！\n记录ID: {initial_id}")

            # 发送数据保存成功信号
            self.data_saved.emit()

            # 关闭对话框
            self.accept()

        except Exception as e:
            Logger.log_error(f"保存期初库存失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"保存失败：{str(e)}")

