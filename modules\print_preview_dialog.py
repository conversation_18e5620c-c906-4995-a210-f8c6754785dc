from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QPushButton, QLabel, 
                            QScrollArea, QWidget, QHBoxLayout, QMessageBox)
from PyQt6.QtCore import Qt, QRectF, QMarginsF, QPoint
from PyQt6.QtGui import QPainter, QFont, QPageSize, QPageLayout,QRegion, QPixmap
from PyQt6.QtPrintSupport import QPrinter, QPrintDialog,QPrinterInfo

class PrintPreviewDialog(QDialog):
    def __init__(self, handover_data, parent=None):
        
        super().__init__(parent)
        self.handover_data = handover_data
        self.is_ui_setup = False
        self.setup_ui()
        
        
    def setup_ui(self):
        """设置UI界面"""
        if self.is_ui_setup:
            return
        
        
        self.setWindowTitle("打印预览")
        self.setMinimumSize(1024, 768)
        
        layout = QVBoxLayout(self)
        
        # 预览区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        
        self.preview_widget = QWidget()
        self.preview_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: none;
                
            }
        """)
        
        preview_layout = QVBoxLayout(self.preview_widget)
        preview_layout.setSpacing(10)
        preview_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("纽仕兰退货交接单")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                font-family: SimHei;
                background-color: transparent;
                border: none;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        preview_layout.addWidget(title_label)
        
        # 基本信息区域
        info_widget = QWidget()
        info_widget.setStyleSheet("border:none;")
        info_layout = QVBoxLayout(info_widget)
        
        # 第一行
        row1_layout = QHBoxLayout()
        row1_layout.addWidget(self._create_info_pair("交接单号:", self.handover_data['main']['handover_no']))
        row1_layout.addWidget(self._create_info_pair("退货日期:", str(self.handover_data['main']['handover_date'])))
        info_layout.addLayout(row1_layout)
        # 添加行间距
        info_layout.addSpacing(20)  # 这里的数字20可以根据需要调整，单位是像素
        # 第二行
        row2_layout = QHBoxLayout()
        row2_layout.addWidget(self._create_info_pair("出库单号:", str(self.handover_data['main']['delivery_order_no'])))
        row2_layout.addWidget(self._create_info_pair("退货客户:", str(self.handover_data['main']['customer'])))
        info_layout.addLayout(row2_layout)
        # 添加行间距
        info_layout.addSpacing(20)  # 这里的数字20可以根据需要调整，单位是像素
        # 第三行
        row3_layout = QHBoxLayout()
        row3_layout.addWidget(self._create_info_pair("原始单号:", str(self.handover_data['main']['source_order_no'])))
        row3_layout.addWidget(self._create_info_pair("物流公司:", str(self.handover_data['main']['logistics_company'])))
        info_layout.addLayout(row3_layout)
        # 添加行间距
        info_layout.addSpacing(20)  # 这里的数字20可以根据需要调整，单位是像素
        # 第四行
        row4_layout = QHBoxLayout()
        row4_layout.addWidget(self._create_info_pair("退货类型:", "拒收退货" if self.handover_data['main']['return_type'] == 0 else "仓退"))
        row4_layout.addWidget(self._create_info_pair("部门:", str(self.handover_data['main']['department'])))
        info_layout.addLayout(row4_layout)
        # 添加行间距
        info_layout.addSpacing(20)  # 这里的数字20可以根据需要调整，单位是像素
        # 第五行
        row5_layout = QHBoxLayout()
        row5_layout.addWidget(self._create_info_pair("摘要:", str(self.handover_data['main']['summary'])))
        info_layout.addLayout(row5_layout)

        preview_layout.addWidget(info_widget)
        
        # 添加二维码显示
        qr_layout = QHBoxLayout()
        qr_layout.addStretch()
        
        try:
            from .return_db import ReturnDB
            db = ReturnDB()
            # 获取二维码图像
            qr_image = db.generate_handover_qrcode(
                self.handover_data['main']['handover_no']
            )
            
            if qr_image:
                qr_label = QLabel()
                qr_label.setPixmap(QPixmap.fromImage(qr_image).scaled(
                    100, 100, Qt.AspectRatioMode.KeepAspectRatio
                ))
                qr_layout.addWidget(qr_label)
        except Exception as e:
            print(f"显示二维码失败: {str(e)}")
        
        preview_layout.addLayout(qr_layout)
        
        # 明细表格
        table_widget = self._create_detail_table(self.handover_data['details'])
        preview_layout.addWidget(table_widget)
        
        # 签名区域
        signature_widget = QWidget()
        signature_layout = QHBoxLayout(signature_widget)
        signature_layout.addWidget(QLabel("物流交接人: _________________"))
        signature_layout.addStretch()
        signature_layout.addWidget(QLabel("仓库交接人: _________________")) 
        preview_layout.addWidget(signature_widget)
        
        scroll_area.setWidget(self.preview_widget)
        layout.addWidget(scroll_area)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        print_btn = QPushButton("打印")
        print_btn.clicked.connect(self.print_document)
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        
        button_layout.addStretch()
        button_layout.addWidget(print_btn)
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)
        
        self.is_ui_setup = True
        
    def _create_info_pair(self, label, value):
        """创建标签值对"""
        widget = QWidget()
        widget.setStyleSheet("border:none;")
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("font-weight: bold;")
        value_widget = QLabel(str(value))
        
        layout.addWidget(label_widget)
        layout.addWidget(value_widget)
        layout.addStretch()
        
        return widget
        
    def _create_detail_table(self, details):
        """创建明细表格"""
        from PyQt6.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
        
        table = QTableWidget()
        table.setColumnCount(8)
        table.setHorizontalHeaderLabels([
            "序号", "物料编码", "物料名称", 
            "批次", "仓库", "客户拒收原因", "退货数量", "收货数量"
        ])
        
        # 设置表格数据
        table.setRowCount(len(details))
        for row, detail in enumerate(details):
            table.setItem(row, 0, QTableWidgetItem(str(row + 1)))
            table.setItem(row, 1, QTableWidgetItem(detail['material_code']))
            table.setItem(row, 2, QTableWidgetItem(detail['material_name']))
            table.setItem(row, 3, QTableWidgetItem(str(detail['batch_no'])))
            table.setItem(row, 4, QTableWidgetItem(str(detail['warehouse'])))
            table.setItem(row, 5, QTableWidgetItem(str(detail['reject_reason'])))
            table.setItem(row, 6, QTableWidgetItem(str(detail['return_quantity'])))
            table.setItem(row, 7, QTableWidgetItem(str(detail['received_quantity'])))
        
        # 设置表格样式
        header = table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        
        # 修改表格样式，只保留底部横线
        table.setStyleSheet("""
            QTableWidget {
                background-color: transparent;
                border: none;
                gridline-color: transparent;
            }
            QHeaderView::section {
                background-color: transparent;
                padding: 4px;
                border: none;
                border-bottom: 1px solid black;  /* 标题行底部横线 */
                font-weight: bold;
            }
            QTableWidget::item {
                background-color: transparent;
                border: none;
                border-bottom: 1px solid black;  /* 数据行底部横线 */
                padding: 4px;
            }
        """)
        
        # 去掉表格的焦点边框
        table.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        
        # 隐藏垂直表头
        table.verticalHeader().setVisible(False)
        
        return table
        
    def _do_print(self, show_success_message=True):
        """内部共用的打印实现方法"""
        try:
            # 获取默认打印机
            printer_info = QPrinterInfo.defaultPrinter()
            if not printer_info:
                QMessageBox.warning(self, "警告", "未找到默认打印机")
                return False
            
            # 创建高分辨率打印机对象
            printer = QPrinter(printer_info, QPrinter.PrinterMode.HighResolution)
            
            # 设置打印机参数
            printer.setPageOrientation(QPageLayout.Orientation.Landscape)
            printer.setPageSize(QPageSize(QPageSize.PageSizeId.A4))
            
            # 设置页面边距
            margins = QMarginsF(0, 0, 0, 0)  # 统一使用无边距
            layout = QPageLayout(QPageSize(QPageSize.PageSizeId.A4),
                               QPageLayout.Orientation.Landscape,
                               margins,
                               QPageLayout.Unit.Millimeter)
            printer.setPageLayout(layout)
            
            printer.setFullPage(True)
            printer.setColorMode(QPrinter.ColorMode.GrayScale)
            
            # 直接开始打印
            painter = QPainter()
            if painter.begin(printer):
                # 获取页面大小
                page_rect = printer.pageRect(QPrinter.Unit.DevicePixel)
                
                # 调整缩放比例
                scale_factor = (page_rect.width() * 0.95) / self.preview_widget.width()
                painter.scale(scale_factor, scale_factor)
                
                # 移动绘制位置，确保内容居中
                x_offset = (page_rect.width() / scale_factor - self.preview_widget.width()) / 2
                y_offset = (page_rect.height() / scale_factor - self.preview_widget.height()) / 2
                painter.translate(x_offset, y_offset)
                
                # 打印预览窗口的内容
                self.preview_widget.render(painter)
                painter.end()
                
                # 更新打印状态
                handover_no = self.handover_data['main']['handover_no']
                from .return_db import ReturnDB
                db = ReturnDB()
                if db.update_print_status(handover_no):
                    if show_success_message:
                        QMessageBox.information(self, "成功", "打印成功！")
                    return True
            return False
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打印失败：{str(e)}")
            return False

    def print_document(self):
        """交互式打印方法"""
        try:
            print("\n=== 打印预览窗口打印 ===")
            print(f"窗口大小: {self.width()}x{self.height()}")
            print(f"预览组件大小: {self.preview_widget.width()}x{self.preview_widget.height()}")
            print(f"是否显示: {self.isVisible()}")
            print(f"是否已初始化: {self.is_ui_setup}")
            
            if self._do_print():
                self.accept()
            
        except Exception as e:
            print(f"打印预览窗口打印错误: {str(e)}")

    def direct_print(self, show_success_message=True):
        """直接打印方法"""
        try:
            print("\n=== 直接打印 ===")
            print(f"窗口大小: {self.width()}x{self.height()}")
            print(f"预览组件大小: {self.preview_widget.width()}x{self.preview_widget.height()}")
            print(f"是否显示: {self.isVisible()}")
            print(f"是否已初始化: {self.is_ui_setup}")
            
            if self._do_print(show_success_message):
                self.accept()
            
        except Exception as e:
            print(f"直接打印错误: {str(e)}")
