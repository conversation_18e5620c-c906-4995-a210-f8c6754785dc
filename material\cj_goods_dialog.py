from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QFormLayout, QDialogButtonBox, QMessageBox, QPushButton, QStyle)
from PyQt6.QtCore import Qt
from material.material_db import MaterialDB
from material.material_search_dialog import MaterialSearchDialog

class CJGoodsDialog(QDialog):
    """东方货品新增/编辑对话框"""
    
    def __init__(self, parent=None, goods=None):
        super().__init__(parent)
        self.goods = goods
        self.material_db = MaterialDB()
        
        self.setWindowTitle("东方货品信息" if not goods else f"编辑东方货品: {goods.get('cj_code', '')}")
        self.resize(400, 300)
        
        self.setup_ui()
        
        # 如果是编辑，填充数据
        if goods:
            self.fill_form_data()
    
    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        
        # 表单布局
        form_layout = QFormLayout()
        
        # 东方货号
        self.cj_code_edit = QLineEdit()
        form_layout.addRow("东方货号:", self.cj_code_edit)
        
        # 东方名称
        self.cj_name_edit = QLineEdit()
        form_layout.addRow("东方名称:", self.cj_name_edit)
        
        # 物料编号
        material_layout = QHBoxLayout()
        self.material_id_edit = QLineEdit()
        material_layout.addWidget(self.material_id_edit)
        
        # 添加搜索按钮
        self.search_btn = QPushButton("搜索")
        self.search_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogContentsView))
        self.search_btn.clicked.connect(self.search_material)
        material_layout.addWidget(self.search_btn)
        form_layout.addRow("物料编号:", material_layout)
        
        
        # 箱规
        self.box_spec_edit = QLineEdit()
        form_layout.addRow("箱规:", self.box_spec_edit)
        
        # 拆分编码
        self.split_code_edit = QLineEdit()
        form_layout.addRow("拆分编码:", self.split_code_edit)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.validate_and_accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def fill_form_data(self):
        """填充表单数据"""
        if not self.goods:
            return
            
        self.cj_code_edit.setText(self.goods.get("cj_code", ""))
        self.cj_code_edit.setReadOnly(True)  # 编辑时东方货号不可修改
        self.cj_name_edit.setText(self.goods.get("cj_name", ""))
        self.material_id_edit.setText(self.goods.get("material_id", ""))
        self.box_spec_edit.setText(self.goods.get("box_spec", ""))
        self.split_code_edit.setText(self.goods.get("split_code", ""))
        
      
    
    def get_goods_data(self):
        """获取表单数据"""
        return {
            "cj_code": self.cj_code_edit.text().strip(),
            "cj_name": self.cj_name_edit.text().strip(),
            "material_id": self.material_id_edit.text().strip(),
            "box_spec": self.box_spec_edit.text().strip(),
            "split_code": self.split_code_edit.text().strip()
        }
    
    def search_material(self):
        """打开物料搜索对话框"""
        dialog = MaterialSearchDialog(self)
        dialog.material_selected.connect(self.on_material_selected)
        dialog.exec()
    
    def on_material_selected(self, material):
        """物料选择处理"""
        if material:
            self.material_id_edit.setText(material.get("material_id", ""))
            
    
    def validate_and_accept(self):
        """验证并接受"""
        # 验证必填字段
        if not self.cj_code_edit.text().strip():
            QMessageBox.warning(self, "警告", "东方货号不能为空")
            return
        
        if not self.cj_name_edit.text().strip():
            QMessageBox.warning(self, "警告", "东方名称不能为空")
            return
        
        material_id = self.material_id_edit.text().strip()
        if not material_id:
            QMessageBox.warning(self, "警告", "物料编号不能为空")
            return
        
        # 验证物料是否存在且是东方货物
        material = self.material_db.get_material_by_id(material_id)
        if not material:
            QMessageBox.warning(self, "警告", "物料编号不存在或不是东方货物")
            return
        
   
        
        self.accept()