"""
自动编号模块 - 管理系统中的自动编号功能
提供自动编号的生成、管理和查询功能
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                           QTableWidgetItem, QPushButton, QHeaderView, QLabel, 
                           QLineEdit, QComboBox, QDialog, QFormLayout, QMessageBox,
                           QSpinBox, QCheckBox, QDateEdit, QDialogButtonBox, QTextEdit)
from PyQt6.QtCore import Qt, QDateTime, QDate
from PyQt6.QtGui import QIcon, QColor
from datetime import datetime, date
import re

class AutoNumberModule(QWidget):
    """自动编号管理模块"""
    
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 搜索区域
        search_layout = QHBoxLayout()
        
        self.rule_code_search = QLineEdit()
        self.rule_code_search.setPlaceholderText("规则编码")
        self.rule_code_search.setMaximumWidth(150)
        
        self.rule_name_search = QLineEdit()
        self.rule_name_search.setPlaceholderText("规则名称")
        self.rule_name_search.setMaximumWidth(150)
        
        self.status_filter = QComboBox()
        self.status_filter.addItem("全部状态", -1)
        self.status_filter.addItem("启用", 1)
        self.status_filter.addItem("禁用", 0)
        self.status_filter.setMaximumWidth(100)
        
        search_button = QPushButton("搜索")
        search_button.clicked.connect(self.search_rules)
        
        reset_button = QPushButton("重置")
        reset_button.clicked.connect(self.reset_search)
        
        search_layout.addWidget(QLabel("规则编码:"))
        search_layout.addWidget(self.rule_code_search)
        search_layout.addWidget(QLabel("规则名称:"))
        search_layout.addWidget(self.rule_name_search)
        search_layout.addWidget(QLabel("状态:"))
        search_layout.addWidget(self.status_filter)
        search_layout.addWidget(search_button)
        search_layout.addWidget(reset_button)
        search_layout.addStretch()
        
        # 操作按钮区域
        button_layout = QHBoxLayout()
        
        add_button = QPushButton("添加规则")
        add_button.setIcon(QIcon("resources/icons/add.png"))
        add_button.clicked.connect(self.add_rule)
        
        edit_button = QPushButton("编辑规则")
        edit_button.setIcon(QIcon("resources/icons/edit.png"))
        edit_button.clicked.connect(self.edit_rule)
        
        delete_button = QPushButton("删除规则")
        delete_button.setIcon(QIcon("resources/icons/delete.png"))
        delete_button.clicked.connect(self.delete_rule)
        
        test_button = QPushButton("测试编号")
        test_button.setIcon(QIcon("resources/icons/test.png"))
        test_button.clicked.connect(self.test_number)
        
        refresh_button = QPushButton("刷新")
        refresh_button.setIcon(QIcon("resources/icons/refresh.png"))
        refresh_button.clicked.connect(self.search_rules)
        
        button_layout.addWidget(add_button)
        button_layout.addWidget(edit_button)
        button_layout.addWidget(delete_button)
        button_layout.addWidget(test_button)
        button_layout.addStretch()
        button_layout.addWidget(refresh_button)
        
        # 规则表格
        self.rule_table = QTableWidget()
        self.rule_table.setColumnCount(9)
        self.rule_table.setHorizontalHeaderLabels([
            "ID", "规则编码", "规则名称", "前缀", "日期格式", "序号长度", "重置类型", "当前序号", "状态"
        ])
        
        # 设置表格样式
        self.rule_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.rule_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.rule_table.setAlternatingRowColors(True)
        self.rule_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.rule_table.cellDoubleClicked.connect(self.on_cell_double_clicked)
        
        # 自动调整表格列宽
        header = self.rule_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.ResizeToContents)
        
        # 布局
        layout.addLayout(search_layout)
        layout.addLayout(button_layout)
        layout.addWidget(self.rule_table)
        
        self.setLayout(layout)
        
        # 初始加载数据
        self.search_rules()
    
    def search_rules(self):
        """搜索编号规则"""
        try:
            rule_code = self.rule_code_search.text().strip()
            rule_name = self.rule_name_search.text().strip()
            status = self.status_filter.currentData()
            
            # 构建查询条件
            query = """
            SELECT 
                rule_id, rule_code, rule_name, prefix, date_format, 
                sequence_length, reset_type, current_sequence, status,
                description, current_business_date, create_time, update_time
            FROM auto_number_rule
            WHERE 1=1
            """
            params = []
            
            if rule_code:
                query += " AND rule_code LIKE %s"
                params.append(f"%{rule_code}%")
            
            if rule_name:
                query += " AND rule_name LIKE %s"
                params.append(f"%{rule_name}%")
            
            if status != -1:
                query += " AND status = %s"
                params.append(status)
            
            query += " ORDER BY rule_id ASC"
            
            # 执行查询
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            results = db_manager.execute_query(query, tuple(params) if params else None)
            
            # 清空表格
            self.rule_table.setRowCount(0)
            
            # 填充数据
            if results:
                self.rule_table.setRowCount(len(results))
                for row, rule in enumerate(results):
                    self.rule_table.setItem(row, 0, QTableWidgetItem(str(rule['rule_id'])))
                    self.rule_table.setItem(row, 1, QTableWidgetItem(rule['rule_code']))
                    self.rule_table.setItem(row, 2, QTableWidgetItem(rule['rule_name']))
                    self.rule_table.setItem(row, 3, QTableWidgetItem(rule['prefix']))
                    self.rule_table.setItem(row, 4, QTableWidgetItem(rule['date_format']))
                    self.rule_table.setItem(row, 5, QTableWidgetItem(str(rule['sequence_length'])))
                    
                    # 重置类型
                    reset_types = {1: "每日重置", 2: "每月重置", 3: "每年重置", 4: "永不重置"}
                    reset_type_text = reset_types.get(rule['reset_type'], "未知")
                    self.rule_table.setItem(row, 6, QTableWidgetItem(reset_type_text))
                    
                    self.rule_table.setItem(row, 7, QTableWidgetItem(str(rule['current_sequence'])))
                    
                    # 状态
                    status_item = QTableWidgetItem("启用" if rule['status'] == 1 else "禁用")
                    status_item.setForeground(QColor("#2ecc71" if rule['status'] == 1 else "#e74c3c"))
                    self.rule_table.setItem(row, 8, status_item)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询编号规则失败：{str(e)}")
    
    def reset_search(self):
        """重置搜索条件"""
        self.rule_code_search.clear()
        self.rule_name_search.clear()
        self.status_filter.setCurrentIndex(0)
        self.search_rules()
    
    def on_cell_double_clicked(self, row, column):
        """双击表格行编辑规则"""
        self.edit_rule()
    
    def add_rule(self):
        """添加编号规则"""
        dialog = AutoNumberRuleDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.search_rules()
    
    def edit_rule(self):
        """编辑编号规则"""
        current_row = self.rule_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要编辑的规则")
            return
        
        rule_id = int(self.rule_table.item(current_row, 0).text())
        
        # 获取规则详情
        try:
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            query = "SELECT * FROM auto_number_rule WHERE rule_id = %s"
            result = db_manager.execute_query(query, (rule_id,))
            
            if result:
                dialog = AutoNumberRuleDialog(self, rule=result[0])
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    self.search_rules()
            else:
                QMessageBox.warning(self, "警告", "规则不存在")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取规则详情失败：{str(e)}")
    
    def delete_rule(self):
        """删除编号规则"""
        current_row = self.rule_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要删除的规则")
            return
        
        rule_id = int(self.rule_table.item(current_row, 0).text())
        rule_name = self.rule_table.item(current_row, 2).text()
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除编号规则 '{rule_name}' 吗？\n删除后无法恢复！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                from modules.db_manager import DatabaseManager
                db_manager = DatabaseManager()
                
                # 检查是否有历史记录
                check_query = "SELECT COUNT(*) as count FROM auto_number_history WHERE rule_id = %s"
                result = db_manager.execute_query(check_query, (rule_id,))
                
                if result and result[0]['count'] > 0:
                    QMessageBox.warning(self, "警告", "该规则已有历史记录，无法删除")
                    return
                
                # 删除规则
                delete_query = "DELETE FROM auto_number_rule WHERE rule_id = %s"
                affected_rows = db_manager.execute_update(delete_query, (rule_id,))
                
                if affected_rows > 0:
                    QMessageBox.information(self, "成功", "编号规则删除成功")
                    self.search_rules()
                else:
                    QMessageBox.warning(self, "警告", "删除失败，规则可能不存在")
                    
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除编号规则失败：{str(e)}")
    
    def test_number(self):
        """测试编号生成"""
        current_row = self.rule_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要测试的规则")
            return
        
        rule_code = self.rule_table.item(current_row, 1).text()
        
        dialog = TestNumberDialog(self, rule_code)
        dialog.exec()


class AutoNumberRuleDialog(QDialog):
    """编号规则编辑对话框"""
    
    def __init__(self, parent=None, rule=None):
        super().__init__(parent)
        self.rule = rule
        self.init_ui()
        if rule:
            self.load_rule_data()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("编号规则" if not self.rule else "编辑编号规则")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout()
        
        # 表单布局
        form_layout = QFormLayout()
        
        self.rule_code_input = QLineEdit()
        self.rule_code_input.setPlaceholderText("如：CJ、TH、JJ等")
        self.rule_code_input.setMaxLength(20)
        
        self.rule_name_input = QLineEdit()
        self.rule_name_input.setPlaceholderText("如：出库单编号、退货单编号等")
        self.rule_name_input.setMaxLength(100)
        
        self.prefix_input = QLineEdit()
        self.prefix_input.setPlaceholderText("如：CJ-、TH-等")
        self.prefix_input.setMaxLength(20)
        
        self.date_format_input = QLineEdit()
        self.date_format_input.setPlaceholderText("如：YYYYMMDD")
        self.date_format_input.setText("YYYYMMDD")
        self.date_format_input.setMaxLength(20)
        
        self.sequence_length_input = QSpinBox()
        self.sequence_length_input.setRange(1, 10)
        self.sequence_length_input.setValue(4)
        
        self.reset_type_combo = QComboBox()
        self.reset_type_combo.addItem("每日重置", 1)
        self.reset_type_combo.addItem("每月重置", 2)
        self.reset_type_combo.addItem("每年重置", 3)
        self.reset_type_combo.addItem("永不重置", 4)
        
        self.status_checkbox = QCheckBox("启用")
        self.status_checkbox.setChecked(True)
        
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("规则描述...")
        
        # 添加到表单
        form_layout.addRow("规则编码*:", self.rule_code_input)
        form_layout.addRow("规则名称*:", self.rule_name_input)
        form_layout.addRow("编号前缀*:", self.prefix_input)
        form_layout.addRow("日期格式:", self.date_format_input)
        form_layout.addRow("序号长度:", self.sequence_length_input)
        form_layout.addRow("重置类型:", self.reset_type_combo)
        form_layout.addRow("状态:", self.status_checkbox)
        form_layout.addRow("描述:", self.description_input)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        layout.addLayout(form_layout)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def load_rule_data(self):
        """加载规则数据"""
        if not self.rule:
            return
        
        self.rule_code_input.setText(self.rule['rule_code'])
        self.rule_name_input.setText(self.rule['rule_name'])
        self.prefix_input.setText(self.rule['prefix'])
        self.date_format_input.setText(self.rule['date_format'])
        self.sequence_length_input.setValue(self.rule['sequence_length'])
        
        # 设置重置类型
        for i in range(self.reset_type_combo.count()):
            if self.reset_type_combo.itemData(i) == self.rule['reset_type']:
                self.reset_type_combo.setCurrentIndex(i)
                break
        
        self.status_checkbox.setChecked(self.rule['status'] == 1)
        self.description_input.setPlainText(self.rule['description'] or "")
        
        # 编辑时禁用规则编码修改
        self.rule_code_input.setEnabled(False)
    
    def accept(self):
        """确认保存"""
        if not self.validate():
            return
        
        try:
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            
            rule_data = self.get_rule_data()
            
            if self.rule:  # 更新
                query = """
                UPDATE auto_number_rule SET
                    rule_name = %s, prefix = %s, date_format = %s, sequence_length = %s,
                    reset_type = %s, status = %s, description = %s, update_time = NOW()
                WHERE rule_id = %s
                """
                params = (
                    rule_data['rule_name'], rule_data['prefix'], rule_data['date_format'],
                    rule_data['sequence_length'], rule_data['reset_type'], rule_data['status'],
                    rule_data['description'], self.rule['rule_id']
                )
            else:  # 新增
                query = """
                INSERT INTO auto_number_rule 
                (rule_code, rule_name, prefix, date_format, sequence_length, reset_type, status, description)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                params = (
                    rule_data['rule_code'], rule_data['rule_name'], rule_data['prefix'],
                    rule_data['date_format'], rule_data['sequence_length'], rule_data['reset_type'],
                    rule_data['status'], rule_data['description']
                )
            
            affected_rows = db_manager.execute_update(query, params)
            
            if affected_rows > 0:
                QMessageBox.information(self, "成功", "编号规则保存成功")
                super().accept()
            else:
                QMessageBox.warning(self, "警告", "保存失败")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存编号规则失败：{str(e)}")
    
    def validate(self):
        """验证输入"""
        if not self.rule_code_input.text().strip():
            QMessageBox.warning(self, "警告", "请输入规则编码")
            self.rule_code_input.setFocus()
            return False
        
        if not self.rule_name_input.text().strip():
            QMessageBox.warning(self, "警告", "请输入规则名称")
            self.rule_name_input.setFocus()
            return False
        
        if not self.prefix_input.text().strip():
            QMessageBox.warning(self, "警告", "请输入编号前缀")
            self.prefix_input.setFocus()
            return False
        
        # 验证规则编码格式
        rule_code = self.rule_code_input.text().strip()
        if not re.match(r'^[A-Z]{2,10}$', rule_code):
            QMessageBox.warning(self, "警告", "规则编码只能包含2-10个大写字母")
            self.rule_code_input.setFocus()
            return False
        
        # 检查规则编码是否重复（新增时）
        if not self.rule:
            try:
                from modules.db_manager import DatabaseManager
                db_manager = DatabaseManager()
                query = "SELECT COUNT(*) as count FROM auto_number_rule WHERE rule_code = %s"
                result = db_manager.execute_query(query, (rule_code,))
                
                if result and result[0]['count'] > 0:
                    QMessageBox.warning(self, "警告", "规则编码已存在")
                    self.rule_code_input.setFocus()
                    return False
            except Exception as e:
                QMessageBox.critical(self, "错误", f"验证规则编码失败：{str(e)}")
                return False
        
        return True
    
    def get_rule_data(self):
        """获取规则数据"""
        return {
            'rule_code': self.rule_code_input.text().strip(),
            'rule_name': self.rule_name_input.text().strip(),
            'prefix': self.prefix_input.text().strip(),
            'date_format': self.date_format_input.text().strip(),
            'sequence_length': self.sequence_length_input.value(),
            'reset_type': self.reset_type_combo.currentData(),
            'status': 1 if self.status_checkbox.isChecked() else 0,
            'description': self.description_input.toPlainText().strip()
        }


class TestNumberDialog(QDialog):
    """测试编号生成对话框"""
    
    def __init__(self, parent=None, rule_code=None):
        super().__init__(parent)
        self.rule_code = rule_code
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle(f"测试编号生成 - {self.rule_code}")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout()
        
        # 日期选择
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("业务日期:"))
        
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setCalendarPopup(True)
        date_layout.addWidget(self.date_input)
        date_layout.addStretch()
        
        # 生成按钮
        generate_button = QPushButton("生成编号")
        generate_button.clicked.connect(self.generate_number)
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setPlaceholderText("生成的编号将显示在这里...")
        
        layout.addLayout(date_layout)
        layout.addWidget(generate_button)
        layout.addWidget(QLabel("生成结果:"))
        layout.addWidget(self.result_text)
        
        self.setLayout(layout)
    
    def generate_number(self):
        """生成编号"""
        try:
            business_date = self.date_input.date().toString("yyyy-MM-dd")
            
            from modules.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            
            # 调用存储过程生成编号
            query = "CALL GetNextNumber(%s, %s, @generated_number)"
            db_manager.execute_update(query, (self.rule_code, business_date))
            
            # 获取生成的编号
            result_query = "SELECT @generated_number as number"
            result = db_manager.execute_query(result_query)
            
            if result and result[0]['number']:
                generated_number = result[0]['number']
                
                # 记录到历史表
                history_query = """
                INSERT INTO auto_number_history 
                (rule_id, generated_number, business_date, sequence_number, business_type, business_id)
                SELECT rule_id, %s, %s, 
                       CAST(RIGHT(%s, sequence_length) AS UNSIGNED),
                       'TEST', 'TEST'
                FROM auto_number_rule WHERE rule_code = %s
                """
                db_manager.execute_update(history_query, (generated_number, business_date, generated_number, self.rule_code))
                
                # 显示结果
                self.result_text.setPlainText(f"生成的编号: {generated_number}\n\n业务日期: {business_date}\n规则编码: {self.rule_code}")
                
                QMessageBox.information(self, "成功", f"编号生成成功：{generated_number}")
            else:
                QMessageBox.warning(self, "警告", "编号生成失败")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成编号失败：{str(e)}")


class AutoNumberService:
    """自动编号服务类 - 提供编号生成功能"""
    
    def __init__(self):
        from modules.db_manager import DatabaseManager
        self.db_manager = DatabaseManager()
    
    def generate_number(self, rule_code, business_date=None, business_type=None, business_id=None, user_id=None):
        """
        生成自动编号
        
        Args:
            rule_code: 规则编码
            business_date: 业务日期，默认为当前日期
            business_type: 业务类型
            business_id: 业务ID
            user_id: 用户ID
        
        Returns:
            str: 生成的编号
        """
        try:
            if not business_date:
                business_date = date.today().strftime('%Y-%m-%d')
            
            # 调用存储过程生成编号
            query = "CALL GetNextNumber(%s, %s, @generated_number)"
            self.db_manager.execute_update(query, (rule_code, business_date))
            
            # 获取生成的编号
            result_query = "SELECT @generated_number as number"
            result = self.db_manager.execute_query(result_query)
            
            if result and result[0]['number']:
                generated_number = result[0]['number']
                
                # 记录到历史表
                history_query = """
                INSERT INTO auto_number_history 
                (rule_id, generated_number, business_date, sequence_number, business_type, business_id, created_by)
                SELECT rule_id, %s, %s, 
                       CAST(RIGHT(%s, sequence_length) AS UNSIGNED),
                       %s, %s, %s
                FROM auto_number_rule WHERE rule_code = %s
                """
                self.db_manager.execute_update(history_query, (
                    generated_number, business_date, generated_number,
                    business_type, business_id, user_id, rule_code
                ))
                
                return generated_number
            else:
                raise Exception("编号生成失败")
                
        except Exception as e:
            raise Exception(f"生成编号失败：{str(e)}")
    
    def get_rule_info(self, rule_code):
        """获取规则信息"""
        try:
            query = "SELECT * FROM auto_number_rule WHERE rule_code = %s AND status = 1"
            result = self.db_manager.execute_query(query, (rule_code,))
            return result[0] if result else None
        except Exception as e:
            raise Exception(f"获取规则信息失败：{str(e)}")
    
    def get_number_history(self, rule_code=None, business_date=None, business_type=None):
        """获取编号历史"""
        try:
            query = """
            SELECT h.*, r.rule_name, r.prefix
            FROM auto_number_history h
            JOIN auto_number_rule r ON h.rule_id = r.rule_id
            WHERE 1=1
            """
            params = []
            
            if rule_code:
                query += " AND r.rule_code = %s"
                params.append(rule_code)
            
            if business_date:
                query += " AND h.business_date = %s"
                params.append(business_date)
            
            if business_type:
                query += " AND h.business_type = %s"
                params.append(business_type)
            
            query += " ORDER BY h.create_time DESC"
            
            return self.db_manager.execute_query(query, tuple(params) if params else None)
            
        except Exception as e:
            raise Exception(f"获取编号历史失败：{str(e)}") 
