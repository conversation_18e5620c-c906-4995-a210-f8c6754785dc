"""
BOM物料选择对话框
专门为BOM模块设计的物料选择功能
支持物料搜索、分类筛选、详细信息预览等
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QTableWidget, QPushButton, QTableWidgetItem, QMessageBox,
                           QHeaderView, QComboBox, QFrame, QGroupBox, QFormLayout,
                           QTextEdit, QSplitter, QTreeWidget, QTreeWidgetItem,
                           QCheckBox, QSpinBox, QDoubleSpinBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon
from material.material_db import MaterialDB

class BOMaterialSearchDialog(QDialog):
    """BOM物料选择对话框"""
    
    # 定义信号
    material_selected = pyqtSignal(dict)  # 选中物料信号
    
    def __init__(self, parent=None, exclude_material_id=None, selected_materials=None, material_type=None):
        super().__init__(parent)
        self.material_db = MaterialDB()
        self.exclude_material_id = exclude_material_id  # 排除的物料ID（通常是成品物料）
        self.selected_materials = selected_materials or []  # 已选择的物料列表
        self.material_type = material_type  # 物料类型：'product'=商品, 'package'=包材
        self.current_material = None  # 当前选中的物料
        
        self.setWindowTitle("BOM物料选择")
        self.setModal(True)
        self.resize(1200, 800)
        
        self.setup_ui()
        self.load_categories()
        self.search_materials()
    
    def setup_ui(self):
        """设置UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建搜索区域
        self.create_search_area(main_layout)
        
        # 创建主体区域（分割器）
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：分类树和搜索结果
        left_widget = self.create_left_panel()
        splitter.addWidget(left_widget)
        
        # 右侧：物料详情
        right_widget = self.create_right_panel()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([800, 400])
        main_layout.addWidget(splitter)
        
        # 创建按钮区域
        self.create_button_area(main_layout)
    
    def create_search_area(self, parent_layout):
        """创建搜索区域"""
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 10px;
            }
        """)
    
        
        search_layout = QVBoxLayout(search_frame)
        search_layout.setSpacing(10)
        
        # 第一行搜索条件
        first_row = QHBoxLayout()
        first_row.setSpacing(15)
        
        # 物料编码搜索
        first_row.addWidget(QLabel("物料编码:"))
        self.material_id_edit = QLineEdit()
        self.material_id_edit.setPlaceholderText("请输入物料编码")
        self.material_id_edit.setFixedWidth(150)
        self.material_id_edit.returnPressed.connect(self.search_materials)
        first_row.addWidget(self.material_id_edit)
        
        # 物料名称搜索
        first_row.addWidget(QLabel("物料名称:"))
        self.material_name_edit = QLineEdit()
        self.material_name_edit.setPlaceholderText("请输入物料名称")
        self.material_name_edit.setFixedWidth(200)
        self.material_name_edit.returnPressed.connect(self.search_materials)
        first_row.addWidget(self.material_name_edit)
        
        # 规格型号搜索
        first_row.addWidget(QLabel("规格型号:"))
        self.spec_edit = QLineEdit()
        self.spec_edit.setPlaceholderText("请输入规格型号")
        self.spec_edit.setFixedWidth(150)
        self.spec_edit.returnPressed.connect(self.search_materials)
        first_row.addWidget(self.spec_edit)
        
        first_row.addStretch()
        search_layout.addLayout(first_row)
        
        # 第二行搜索条件
        second_row = QHBoxLayout()
        second_row.setSpacing(15)
        
        # 物料分类
        second_row.addWidget(QLabel("物料分类:"))
        self.category_combo = QComboBox()
        self.category_combo.setFixedWidth(200)
        self.category_combo.currentTextChanged.connect(self.search_materials)
        second_row.addWidget(self.category_combo)
        
        # 只显示可用于BOM的物料
        self.bom_available_check = QCheckBox("仅显示可用于BOM的物料")
        self.bom_available_check.setChecked(True)
        self.bom_available_check.stateChanged.connect(self.search_materials)
        second_row.addWidget(self.bom_available_check)
        
        # 排除已选择的物料
        self.exclude_selected_check = QCheckBox("排除已选择的物料")
        self.exclude_selected_check.setChecked(True)
        self.exclude_selected_check.stateChanged.connect(self.search_materials)
        second_row.addWidget(self.exclude_selected_check)
        
        # 搜索按钮
        search_btn = QPushButton("🔍 搜索")
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        search_btn.clicked.connect(self.search_materials)
        second_row.addWidget(search_btn)
        
        # 重置按钮
        reset_btn = QPushButton("🔄 重置")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #d9d9d9;
                color: #333;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #bfbfbf;
            }
        """)
        reset_btn.clicked.connect(self.reset_search)
        second_row.addWidget(reset_btn)
        
        second_row.addStretch()
        search_layout.addLayout(second_row)
        
        parent_layout.addWidget(search_frame)

    def create_left_panel(self):
        """创建左侧面板"""
        left_widget = QFrame()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(10)
        
        #分类树（可选，暂时隐藏）
        # self.create_category_tree(left_layout)
        
        # 物料列表
        self.create_material_table(left_layout)
        
        return left_widget
    
    def create_material_table(self, parent_layout):
        """创建物料表格"""
        # 表格标题
        table_label = QLabel("物料列表")
        table_label.setFont(QFont("", 10, QFont.Weight.Bold))
        parent_layout.addWidget(table_label)
        
        # 创建表格
        self.material_table = QTableWidget()
        self.material_table.setColumnCount(6)
        
        headers = ["物料编码", "物料名称", "规格型号", "分类", "基本单位", "状态"]
        self.material_table.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.material_table.setAlternatingRowColors(True)
        self.material_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.material_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.material_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.material_table.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

        # 设置列宽
        header = self.material_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 物料名称
        
        self.material_table.setColumnWidth(0, 120)  # 物料编码
        self.material_table.setColumnWidth(2, 120)  # 规格型号
        self.material_table.setColumnWidth(3, 100)  # 分类
        self.material_table.setColumnWidth(4, 80)   # 基本单位
        self.material_table.setColumnWidth(5, 60)   # 状态
        
        # 连接信号
        self.material_table.itemSelectionChanged.connect(self.on_material_selection_changed)
        self.material_table.itemDoubleClicked.connect(self.on_material_double_clicked)
        
        parent_layout.addWidget(self.material_table)
        
        # 统计信息
        self.material_count_label = QLabel("共 0 条物料")
        self.material_count_label.setStyleSheet("color: #666; font-size: 12px;")
        parent_layout.addWidget(self.material_count_label)
    
    def create_right_panel(self):
        """创建右侧面板"""
        right_widget = QFrame()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(10)
        
        # 物料详情标题
        detail_label = QLabel("物料详情")
        detail_label.setFont(QFont("", 10, QFont.Weight.Bold))
        right_layout.addWidget(detail_label)
        
        # 物料详情组
        detail_group = QGroupBox()
        detail_form = QFormLayout(detail_group)
        detail_form.setSpacing(8)
        
        # 基本信息
        self.detail_material_id = QLabel("-")
        detail_form.addRow("物料编码:", self.detail_material_id)
        
        self.detail_material_name = QLabel("-")
        self.detail_material_name.setWordWrap(True)
        detail_form.addRow("物料名称:", self.detail_material_name)
        
        self.detail_spec_model = QLabel("-")
        self.detail_spec_model.setWordWrap(True)
        detail_form.addRow("规格型号:", self.detail_spec_model)
        
        self.detail_category = QLabel("-")
        detail_form.addRow("物料分类:", self.detail_category)
        
        self.detail_base_unit = QLabel("-")
        detail_form.addRow("基本单位:", self.detail_base_unit)
        
        self.detail_status = QLabel("-")
        detail_form.addRow("状态:", self.detail_status)
        
        # 库存信息
        detail_form.addRow("", QLabel(""))  # 分隔线
        
        self.detail_safety_stock = QLabel("-")
        detail_form.addRow("安全库存:", self.detail_safety_stock)
        
        self.detail_current_stock = QLabel("-")
        detail_form.addRow("当前库存:", self.detail_current_stock)
        
        # 成本信息
        detail_form.addRow("", QLabel(""))  # 分隔线
        
        self.detail_standard_cost = QLabel("-")
        detail_form.addRow("标准成本:", self.detail_standard_cost)
        
        self.detail_latest_price = QLabel("-")
        detail_form.addRow("最新价格:", self.detail_latest_price)
        
        right_layout.addWidget(detail_group)
        
        # 备注信息
        remark_label = QLabel("备注信息")
        remark_label.setFont(QFont("", 9, QFont.Weight.Bold))
        right_layout.addWidget(remark_label)
        
        self.detail_remark = QTextEdit()
        self.detail_remark.setReadOnly(True)
        self.detail_remark.setMaximumHeight(100)
        self.detail_remark.setPlaceholderText("暂无备注信息")
        right_layout.addWidget(self.detail_remark)
        
        right_layout.addStretch()
        
        return right_widget
    
    def create_button_area(self, parent_layout):
        """创建按钮区域"""
        button_frame = QFrame()
        button_frame.setStyleSheet("QFrame { background-color: #f5f5f5; }")
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(20, 10, 20, 10)
        
        # 选择数量标签
        self.selected_info_label = QLabel("请选择物料")
        self.selected_info_label.setStyleSheet("color: #666; font-weight: bold;")
        button_layout.addWidget(self.selected_info_label)
        
        button_layout.addStretch()
        
        # 确定选择按钮
        self.select_btn = QPushButton("✅ 确定选择")
       
        self.select_btn.setEnabled(False)
        self.select_btn.clicked.connect(self.confirm_selection)
        button_layout.addWidget(self.select_btn)
        
        # 取消按钮
        cancel_btn = QPushButton("❌ 取消")
      
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        parent_layout.addWidget(button_frame)
    
    def load_categories(self):
        """加载物料分类"""
        try:
            categories = self.material_db.get_categories()
            
            self.category_combo.clear()
            self.category_combo.addItem("全部分类", None)
            
            # 根据material_type筛选分类
            for category in categories:
                category_name = category.get('category_name', '')
                category_id = category.get('category_id')
                parent_id = category.get('parent_id')
                
                # 如果指定了material_type，只显示对应大类下的分类
                if self.material_type == 'product' and parent_id != 1:
                    continue
                elif self.material_type == 'package' and parent_id != 6:
                    continue
                
                self.category_combo.addItem(category_name, category_id)
                
        except Exception as e:
            QMessageBox.warning(self, "警告", f"加载物料分类失败：{str(e)}")
    
    def search_materials(self):
        """搜索物料"""
        try:
            # 构建搜索参数
            params = {
                "material_id": self.material_id_edit.text().strip() or None,
                "name": self.material_name_edit.text().strip() or None,
                "spec_model": self.spec_edit.text().strip() or None,
                "page": 1,
                "page_size": 1000  # 显示更多结果
            }
            
            # 分类筛选
            category_id = self.category_combo.currentData()
            if category_id:
                params["category_id"] = category_id
            
            # 根据物料类型获取数据
            if self.material_type in ['product', 'package']:
                # 使用修改后的get_materials_for_bom方法，传入material_type参数
                total, materials = self.material_db.get_materials_for_bom(params, self.material_type)
            elif self.bom_available_check.isChecked():
                # 只获取可用于BOM的物料（不限制大类）
                total, materials = self.material_db.get_materials_for_bom(params)
            else:
                # 获取所有物料
                total, materials = self.material_db.get_materials(params)
            
            # 过滤排除的物料
            filtered_materials = []
            for material in materials:
                material_id = material.get('material_id')
                
                # 排除成品物料
                if self.exclude_material_id and material_id == self.exclude_material_id:
                    continue
                
                # 排除已选择的物料
                if (self.exclude_selected_check.isChecked() and 
                    material_id in self.selected_materials):
                    continue
                
                filtered_materials.append(material)
            
            # 更新表格
            self.update_material_table(filtered_materials)
            
        except Exception as e:
            QMessageBox.warning(self, "警告", f"搜索物料失败：{str(e)}")
    
    def update_material_table(self, materials):
        """更新物料表格"""
        self.material_table.setRowCount(len(materials))
        
        for row, material in enumerate(materials):
            # 物料编码
            self.material_table.setItem(row, 0, QTableWidgetItem(material.get('material_id', '')))
            
            # 物料名称
            self.material_table.setItem(row, 1, QTableWidgetItem(material.get('name', '')))
            
            # 规格型号
            self.material_table.setItem(row, 2, QTableWidgetItem(material.get('spec_model', '')))
            
            # 分类
            self.material_table.setItem(row, 3, QTableWidgetItem(material.get('category_name', '')))
            
            # 基本单位
            self.material_table.setItem(row, 4, QTableWidgetItem(material.get('base_unit', '')))
            
            # 状态
            status = "正常" if material.get('status') == 1 else "停用"
            status_item = QTableWidgetItem(status)
            if material.get('status') == 1:
                status_item.setBackground(Qt.GlobalColor.green)
            else:
                status_item.setBackground(Qt.GlobalColor.red)
            self.material_table.setItem(row, 5, status_item)
            
            # 存储完整的物料数据
            self.material_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, material)
        
        # 更新统计信息
        self.material_count_label.setText(f"共 {len(materials)} 条物料")
    
    def on_material_selection_changed(self):
        """物料选择变化时的处理"""
        current_row = self.material_table.currentRow()
        
        if current_row >= 0:
            # 获取选中的物料数据
            material_item = self.material_table.item(current_row, 0)
            if material_item:
                material_data = material_item.data(Qt.ItemDataRole.UserRole)
                if material_data:
                    # 更新物料详情
                    self.update_material_detail(material_data)
                    
                    # 更新选择按钮状态
                    self.select_btn.setEnabled(True)
                    material_name = material_data.get('name', '未知物料')
                    self.selected_info_label.setText(f"已选择: {material_name}")
                else:
                    self.clear_material_detail()
                    self.select_btn.setEnabled(False)
                    self.selected_info_label.setText("请选择物料")
        else:
            self.clear_material_detail()
            self.select_btn.setEnabled(False)
            self.selected_info_label.setText("请选择物料")
    
    def on_material_double_clicked(self, item):
        """物料双击处理"""
        if self.current_material:
            self.confirm_selection()
    
    def update_material_detail(self, material):
        """更新物料详情"""
        if not material:
            self.clear_material_detail()
            return
        
        # 设置当前选中的物料
        self.current_material = material
        
        # 基本信息
        self.detail_material_id.setText(material.get('material_id', '-'))
        self.detail_material_name.setText(material.get('name', '-'))
        self.detail_spec_model.setText(material.get('spec_model', '-'))
        self.detail_category.setText(material.get('category_name', '-'))
        self.detail_base_unit.setText(material.get('base_unit', '-'))
        
        # 状态
        status = "正常" if material.get('status') == 1 else "停用"
        self.detail_status.setText(status)
        self.detail_status.setStyleSheet(
            "color: green; font-weight: bold;" if material.get('status') == 1 else "color: red; font-weight: bold;"
        )
        
        # 获取并显示库存信息
        material_id = material.get('material_id', '')
        if material_id:
            try:
                stock_quantity = self.material_db.get_material_stock(material_id)
                base_unit = material.get('base_unit', '')
                
                # 显示库存数量
                if stock_quantity > 0:
                    #显示整数
                    stock_text=f"{int(stock_quantity)} {base_unit}"
                    self.detail_current_stock.setText(stock_text)
                    self.detail_current_stock.setStyleSheet("color: green; font-weight: bold;")
                else:
                    self.detail_current_stock.setText("0")
                    self.detail_current_stock.setStyleSheet("color: red; font-weight: bold;")
                    
            except Exception as e:
                print(f"获取库存失败: {str(e)}")
                self.detail_current_stock.setText("获取失败")
                self.detail_current_stock.setStyleSheet("color: orange;")
        else:
            self.detail_current_stock.setText("-")
            self.detail_current_stock.setStyleSheet("")
        
        # 库存信息
        self.detail_safety_stock.setText(str(material.get('safety_stock', 0)))
        
        # 成本信息
        standard_cost = material.get('cost', 0)
        self.detail_standard_cost.setText(f"¥{standard_cost:.2f}" if standard_cost else "-")
        
        latest_price = material.get('latest_price', 0)
        self.detail_latest_price.setText(f"¥{latest_price:.2f}" if latest_price else "-")
        
        # 备注
        self.detail_remark.setPlainText(material.get('remark', ''))
    
    def clear_material_detail(self):
        """清空物料详情"""
        # 清空当前选中的物料
        self.current_material = None
        
        labels = [
            self.detail_material_id, self.detail_material_name, self.detail_spec_model,
            self.detail_category, self.detail_base_unit, self.detail_status,
            self.detail_current_stock, self.detail_safety_stock,
            self.detail_standard_cost, self.detail_latest_price
        ]
        
        for label in labels:
            label.setText("-")
        
        # 清除样式
        self.detail_status.setStyleSheet("")
        self.detail_current_stock.setStyleSheet("")
        self.detail_remark.clear()
    
    def reset_search(self):
        """重置搜索条件"""
        self.material_id_edit.clear()
        self.material_name_edit.clear()
        self.spec_edit.clear()
        self.category_combo.setCurrentIndex(0)
        self.bom_available_check.setChecked(True)
        self.exclude_selected_check.setChecked(True)
        self.search_materials()
    
    def confirm_selection(self):
        """确认选择"""
        if self.current_material:
            self.material_selected.emit(self.current_material)
            self.accept()
        else:
            QMessageBox.warning(self, "警告", "请先选择一个物料")
    
    def get_selected_material(self):
        """获取选中的物料"""
        return self.current_material










