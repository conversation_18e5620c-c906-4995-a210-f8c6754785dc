"""
BOM物料清单管理模块
包含BOM列表查询、新增、编辑等功能
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
                            QPushButton, QLineEdit, QLabel, QHeaderView, QComboBox, QMessageBox,
                            QFrame, QProgressDialog, QApplication, QCheckBox, QDateEdit, QMenu)
from PyQt6.QtCore import Qt, QTimer, QDate
from PyQt6.QtGui import QIcon, QCursor, QColor, QFont
from material.bom_db import BOMDB
from material.bom_dialog import BOMDialog
from datetime import datetime, date

class BOMManagementTab(QWidget):
    """BOM物料清单管理标签页"""
    
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.bom_db = BOMDB()
        self.current_page = 1
        self.page_size = 20
        self.total_records = 0
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """设置UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建搜索区域
        self.create_search_area(main_layout)
        
        # 创建工具栏
        self.create_toolbar(main_layout)
        
        # 创建表格
        self.create_table(main_layout)
        
        # 创建分页控件
        self.create_pagination(main_layout)
        
        # 创建统计信息
        self.create_stats(main_layout)
    
    def create_search_area(self, parent_layout):
        """创建搜索区域"""
        search_frame = QFrame()
        # search_frame.setStyleSheet("""
        #     QFrame {
        #         background-color: #f8f9fa;
        #         border: 1px solid #e9ecef;
        #         border-radius: 6px;
        #         padding: 10px;
        #     }
        # """)
        
        search_layout = QVBoxLayout(search_frame)
        search_layout.setSpacing(10)
        
        # 第一行搜索条件
        first_row = QHBoxLayout()
        first_row.setSpacing(15)
        
        # BOM编码搜索
        first_row.addWidget(QLabel("BOM编码:"))
        self.bom_code_edit = QLineEdit()
        self.bom_code_edit.setPlaceholderText("请输入BOM编码")
        self.bom_code_edit.setFixedWidth(200)
        self.bom_code_edit.returnPressed.connect(self.search_bom)
        first_row.addWidget(self.bom_code_edit)
        
        # BOM名称搜索
        first_row.addWidget(QLabel("BOM名称:"))
        self.bom_name_edit = QLineEdit()
        self.bom_name_edit.setPlaceholderText("请输入BOM名称")
        self.bom_name_edit.setFixedWidth(200)
        self.bom_name_edit.returnPressed.connect(self.search_bom)
        first_row.addWidget(self.bom_name_edit)
        
        # 成品物料搜索
        first_row.addWidget(QLabel("成品物料:"))
        self.parent_material_edit = QLineEdit()
        self.parent_material_edit.setPlaceholderText("请输入物料编码或名称")
        self.parent_material_edit.setFixedWidth(200)
        self.parent_material_edit.returnPressed.connect(self.search_bom)
        first_row.addWidget(self.parent_material_edit)
        
        first_row.addStretch()
        search_layout.addLayout(first_row)
        
        # 第二行搜索条件
        second_row = QHBoxLayout()
        second_row.setSpacing(15)
        
        # 状态筛选
        second_row.addWidget(QLabel("状态:"))
        self.status_combo = QComboBox()
        self.status_combo.addItems(["全部", "启用", "停用"])
        self.status_combo.setFixedWidth(100)
        self.status_combo.currentTextChanged.connect(self.search_bom)
        second_row.addWidget(self.status_combo)
        
        # 搜索按钮
        search_btn = QPushButton("🔍 搜索")
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        search_btn.clicked.connect(self.search_bom)
        second_row.addWidget(search_btn)
        
        # 重置按钮
        reset_btn = QPushButton("🔄 重置")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #d9d9d9;
                color: #333;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #bfbfbf;
            }
        """)
        reset_btn.clicked.connect(self.reset_search)
        second_row.addWidget(reset_btn)
        
        second_row.addStretch()
        search_layout.addLayout(second_row)
        
        parent_layout.addWidget(search_frame)
    
    def create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setSpacing(10)
        
        # 新增按钮
        self.add_btn = QPushButton("➕ 新增BOM")
        self.add_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
        """)
        self.add_btn.clicked.connect(self.add_bom)
        toolbar_layout.addWidget(self.add_btn)
        
        # 编辑按钮
        self.edit_btn = QPushButton("✏️ 编辑")
        self.edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.edit_btn.clicked.connect(self.edit_bom)
        toolbar_layout.addWidget(self.edit_btn)
        
        # 查看按钮
        self.view_btn = QPushButton("👁️ 查看")
        self.view_btn.setStyleSheet("""
            QPushButton {
                background-color: #722ed1;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #9254de;
            }
        """)
        self.view_btn.clicked.connect(self.view_bom)
        toolbar_layout.addWidget(self.view_btn)
        
        # 删除按钮
        self.delete_btn = QPushButton("🗑️ 删除")
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4d4f;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #ff7875;
            }
        """)
        self.delete_btn.clicked.connect(self.delete_bom)
        toolbar_layout.addWidget(self.delete_btn)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.VLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        toolbar_layout.addWidget(separator)
        
        # 复制按钮
        self.copy_btn = QPushButton("📋 复制BOM")
        self.copy_btn.setStyleSheet("""
            QPushButton {
                background-color: #fa8c16;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #ffa940;
            }
        """)
        self.copy_btn.clicked.connect(self.copy_bom)
        toolbar_layout.addWidget(self.copy_btn)
        
        # 导出按钮
        self.export_btn = QPushButton("📤 导出Excel")
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #13c2c2;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #36cfc9;
            }
        """)
        self.export_btn.clicked.connect(self.export_excel)
        toolbar_layout.addWidget(self.export_btn)
        
        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #595959;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #8c8c8c;
            }
        """)
        self.refresh_btn.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(self.refresh_btn)
        
        toolbar_layout.addStretch()
        parent_layout.addLayout(toolbar_layout)
    
    def create_table(self, parent_layout):
        """创建表格"""
        self.table = QTableWidget()
        self.table.setColumnCount(10)
        
        headers = [
            "BOM编码", "BOM名称", "成品物料编码", "成品物料名称", 
            "版本", "生产单位", "标准产出", "明细数量", "状态", "创建时间"
        ]
        self.table.setHorizontalHeaderLabels(headers)
        
        
        
        # 设置表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectItems)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 设置列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # BOM名称
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)  # 成品物料名称
        
        self.table.setColumnWidth(0, 150)  # BOM编码
        self.table.setColumnWidth(2, 120)  # 成品物料编码
        self.table.setColumnWidth(4, 80)   # 版本
        self.table.setColumnWidth(5, 80)   # 生产单位
        self.table.setColumnWidth(6, 100)  # 标准产出
        self.table.setColumnWidth(7, 80)   # 明细数量
        self.table.setColumnWidth(8, 80)   # 状态
        self.table.setColumnWidth(9, 150)  # 创建时间
        
        # 双击编辑
        self.table.doubleClicked.connect(self.view_bom)
        
        # 右键菜单
        self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)
        
        parent_layout.addWidget(self.table)
    
    def create_pagination(self, parent_layout):
        """创建分页控件"""
        pagination_layout = QHBoxLayout()
        
        # 页面信息
        self.page_info_label = QLabel("第 1 页，共 0 页")
        pagination_layout.addWidget(self.page_info_label)
        
        pagination_layout.addStretch()
        
        # 每页记录数
        pagination_layout.addWidget(QLabel("每页:"))
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["10", "20", "50", "100"])
        self.page_size_combo.setCurrentText(str(self.page_size))
        self.page_size_combo.currentTextChanged.connect(self.change_page_size)
        pagination_layout.addWidget(self.page_size_combo)
        
        # 分页按钮
        self.first_btn = QPushButton("首页")
        self.first_btn.clicked.connect(self.first_page)
        pagination_layout.addWidget(self.first_btn)
        
        self.prev_btn = QPushButton("上一页")
        self.prev_btn.clicked.connect(self.prev_page)
        pagination_layout.addWidget(self.prev_btn)
        
        self.next_btn = QPushButton("下一页")
        self.next_btn.clicked.connect(self.next_page)
        pagination_layout.addWidget(self.next_btn)
        
        self.last_btn = QPushButton("末页")
        self.last_btn.clicked.connect(self.last_page)
        pagination_layout.addWidget(self.last_btn)
        
        parent_layout.addLayout(pagination_layout)
    
    def create_stats(self, parent_layout):
        """创建统计信息"""
        stats_layout = QHBoxLayout()
        
        self.stats_label = QLabel("总计: 0 条记录")
        self.stats_label.setStyleSheet("color: #666; font-size: 12px;")
        stats_layout.addWidget(self.stats_label)
        
        stats_layout.addStretch()
        parent_layout.addLayout(stats_layout)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.table.itemAt(position) is None:
            return
        
        menu = QMenu(self)
        
        # 查看操作
        view_action = menu.addAction("👁️ 查看BOM")
        view_action.triggered.connect(self.view_bom)
        
        # 编辑操作
        edit_action = menu.addAction("✏️ 编辑BOM")
        edit_action.triggered.connect(self.edit_bom)
        
        menu.addSeparator()
        
        # 复制操作
        copy_action = menu.addAction("📋 复制BOM")
        copy_action.triggered.connect(self.copy_bom)
        
        menu.addSeparator()
        
        # 删除操作
        delete_action = menu.addAction("🗑️ 删除BOM")
        delete_action.triggered.connect(self.delete_bom)
        
        menu.exec(self.table.mapToGlobal(position))
    
    def get_search_params(self):
        """获取搜索参数"""
        params = {
            "bom_code": self.bom_code_edit.text().strip() or None,
            "bom_name": self.bom_name_edit.text().strip() or None,
            "parent_material_id": self.parent_material_edit.text().strip() or None,
            "page": self.current_page,
            "page_size": self.page_size
        }
        
        # 状态筛选
        status_text = self.status_combo.currentText()
        if status_text == "启用":
            params["status"] = 1
        elif status_text == "停用":
            params["status"] = 0
        
        return params
    
    def load_data(self):
        """加载BOM数据"""
        try:
            params = self.get_search_params()
            total, results = self.bom_db.get_bom_list(params)
            
            self.total_records = total
            self.update_table(results)
            self.update_pagination()
            self.update_stats()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载BOM数据失败：{str(e)}")
    
    def update_table(self, results):
        """更新表格数据"""
        self.table.setRowCount(len(results))
        
        for row, bom in enumerate(results):
            # BOM编码
            self.table.setItem(row, 0, QTableWidgetItem(str(bom.get('bom_code', ''))))
            
            # BOM名称
            self.table.setItem(row, 1, QTableWidgetItem(str(bom.get('bom_name', ''))))
            
            # 成品物料编码
            self.table.setItem(row, 2, QTableWidgetItem(str(bom.get('parent_material_id', ''))))
            
            # 成品物料名称
            self.table.setItem(row, 3, QTableWidgetItem(str(bom.get('parent_material_name', ''))))
            
            # 版本
            self.table.setItem(row, 4, QTableWidgetItem(str(bom.get('bom_version', ''))))
            
            # 生产单位
            self.table.setItem(row, 5, QTableWidgetItem(str(bom.get('production_unit', ''))))
            
            # 标准产出
            output_qty = bom.get('standard_output_qty', 0)
            self.table.setItem(row, 6, QTableWidgetItem(f"{output_qty}"))
            
            # 明细数量
            detail_count = bom.get('detail_count', 0)
            self.table.setItem(row, 7, QTableWidgetItem(str(detail_count)))
            
            # 状态
            status = "启用" if bom.get('status') == 1 else "停用"
            status_item = QTableWidgetItem(status)
            
            # 设置文本对齐方式为居中
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # 创建字体对象并设置为粗体
            font = QFont()
            font.setBold(True)
            status_item.setFont(font)
            
            # 根据状态设置不同的背景色和文字颜色
            if bom.get('status') == 1:  # 启用
                status_item.setBackground(QColor("#f6ffed"))  # 浅绿色背景
                status_item.setForeground(QColor("#52c41a"))  # 绿色文字
            else:  # 停用
                status_item.setBackground(QColor("#fff1f0"))  # 浅红色背景
                status_item.setForeground(QColor("#f5222d"))  # 红色文字
            
            # 设置工具提示
            status_item.setData(Qt.ItemDataRole.ToolTipRole, f"状态: {status}")
            
            self.table.setItem(row, 8, status_item)
            
            # 创建时间
            create_time = bom.get('create_time', '')
            if isinstance(create_time, datetime):
                create_time = create_time.strftime('%Y-%m-%d %H:%M')
            self.table.setItem(row, 9, QTableWidgetItem(str(create_time)))
            
            # 存储BOM ID到第一列的UserRole中
            bom_id = bom.get('bom_id')
            if bom_id:
                self.table.item(row, 0).setData(Qt.ItemDataRole.UserRole, bom_id)
    
    def update_pagination(self):
        """更新分页信息"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        total_pages = max(1, total_pages)
        
        self.page_info_label.setText(f"第 {self.current_page} 页，共 {total_pages} 页")
        
        # 更新按钮状态
        self.first_btn.setEnabled(self.current_page > 1)
        self.prev_btn.setEnabled(self.current_page > 1)
        self.next_btn.setEnabled(self.current_page < total_pages)
        self.last_btn.setEnabled(self.current_page < total_pages)
    
    def update_stats(self):
        """更新统计信息"""
        self.stats_label.setText(f"总计: {self.total_records} 条记录")
    
    def search_bom(self):
        """搜索BOM"""
        self.current_page = 1
        self.load_data()
    
    def reset_search(self):
        """重置搜索条件"""
        self.bom_code_edit.clear()
        self.bom_name_edit.clear()
        self.parent_material_edit.clear()
        self.status_combo.setCurrentIndex(0)
        self.current_page = 1
        self.load_data()
    
    def refresh_data(self):
        """刷新数据"""
        self.load_data()
    
    def change_page_size(self, size_text):
        """改变每页记录数"""
        self.page_size = int(size_text)
        self.current_page = 1
        self.load_data()
    
    def first_page(self):
        """首页"""
        self.current_page = 1
        self.load_data()
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_data()
    
    def next_page(self):
        """下一页"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        if self.current_page < total_pages:
            self.current_page += 1
            self.load_data()
    
    def last_page(self):
        """末页"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        self.current_page = max(1, total_pages)
        self.load_data()
    
    def get_selected_bom_id(self):
        """获取选中的BOM ID"""
        current_row = self.table.currentRow()
        if current_row < 0:
            return None
        
        item = self.table.item(current_row, 0)
        if item:
            return item.data(Qt.ItemDataRole.UserRole)
        return None
    
    def add_bom(self):
        """新增BOM"""
        try:
            dialog = BOMDialog(self, mode='add')
            dialog.data_saved.connect(self.refresh_data)  # 连接保存成功信号
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开新增BOM对话框失败：{str(e)}")
    
    def edit_bom(self):
        """编辑BOM"""
        bom_id = self.get_selected_bom_id()
        if not bom_id:
            QMessageBox.warning(self, "警告", "请选择要编辑的BOM记录")
            return
        
        try:
            dialog = BOMDialog(self, bom_id=bom_id, mode='edit')
            dialog.data_saved.connect(self.refresh_data)  # 连接保存成功信号
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开编辑BOM对话框失败：{str(e)}")
    
    def view_bom(self):
        """查看BOM"""
        bom_id = self.get_selected_bom_id()
        if not bom_id:
            QMessageBox.warning(self, "警告", "请选择要查看的BOM记录")
            return
        
        try:
            dialog = BOMDialog(self, bom_id=bom_id, mode='view')
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开查看BOM对话框失败：{str(e)}")
    
    def delete_bom(self):
        """删除BOM"""
        bom_id = self.get_selected_bom_id()
        if not bom_id:
            QMessageBox.warning(self, "警告", "请选择要删除的BOM记录")
            return
        
        # 获取BOM信息用于确认
        current_row = self.table.currentRow()
        bom_code = self.table.item(current_row, 0).text()
        bom_name = self.table.item(current_row, 1).text()
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除BOM [{bom_code}] {bom_name} 吗？\n\n注意：删除后将无法恢复！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.bom_db.delete_bom(bom_id)
                QMessageBox.information(self, "成功", "BOM删除成功！")
                self.refresh_data()
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除BOM失败：{str(e)}")
    
    def copy_bom(self):
        """复制BOM"""
        bom_id = self.get_selected_bom_id()
        if not bom_id:
            QMessageBox.warning(self, "警告", "请选择要复制的BOM记录")
            return
        
        try:
            # 获取BOM主表数据
            bom_data = self.bom_db.get_bom_by_id(bom_id)
            if not bom_data:
                QMessageBox.warning(self, "警告", "BOM数据不存在")
                return
            
            # 获取BOM明细数据（使用新的字典结构）
            details_data = self.bom_db.get_bom_details(bom_id)
            package_details = details_data.get('package_details', [])
            material_details = details_data.get('material_details', [])
            
            # 创建复制对话框（使用新增模式，但预填充数据）
            from material.bom_dialog import BOMDialog
            dialog = BOMDialog(self, mode='add')
            
            # 预填充基本数据
            dialog.bom_name_edit.setText(f"{bom_data.get('bom_name', '')} - 副本")
            dialog.parent_material_edit.setText(bom_data.get('parent_material_id', ''))
            dialog.parent_material_name_label.setText(bom_data.get('parent_material_name', ''))
            dialog.bom_version_edit.setText(bom_data.get('bom_version', '1.0'))
            
            # 设置生产单位
            production_unit = bom_data.get('production_unit', '个')
            index = dialog.production_unit_combo.findText(production_unit)
            if index >= 0:
                dialog.production_unit_combo.setCurrentIndex(index)
            else:
                dialog.production_unit_combo.setCurrentText(production_unit)
            
            # 设置其他字段
            dialog.standard_output_edit.setValue(bom_data.get('standard_output_qty', 1.0))
            if bom_data.get('package_qty_per_box'):
                dialog.package_qty_edit.setValue(bom_data.get('package_qty_per_box', 0))
            
            # 复制明细数据
            dialog.package_detail_data = package_details.copy() if package_details else []
            dialog.material_detail_data = material_details.copy() if material_details else []
            
            # 刷新表格显示
            dialog.refresh_package_table()
            dialog.refresh_material_table()
            
            dialog.data_saved.connect(self.refresh_data)
            dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"复制BOM失败：{str(e)}")
            print(f"复制BOM异常: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def export_excel(self):
        """导出Excel - 增强版本"""
        try:
            from PyQt6.QtWidgets import (QFileDialog, QMessageBox, QDialog, QVBoxLayout, 
                                       QHBoxLayout, QCheckBox, QPushButton, QLabel, 
                                       QButtonGroup, QProgressDialog, QApplication)
            from PyQt6.QtGui import QFont
            from PyQt6.QtCore import Qt
            import pandas as pd
            from datetime import datetime
            import os
            
            # 创建导出选项对话框
            export_dialog = self.create_export_options_dialog()
            if export_dialog.exec() != QDialog.DialogCode.Accepted:
                return
            
            # 获取导出选项
            export_options = export_dialog.get_export_options()
            
            # 获取当前搜索条件下的所有数据
            params = self.get_search_params()
            params['page'] = 1
            params['page_size'] = 10000  # 导出所有数据
            
            total, results = self.bom_db.get_bom_list(params)
            
            if not results:
                QMessageBox.information(self, "提示", "没有数据可以导出")
                return
            
            # 选择保存路径
            default_filename = f"BOM清单_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出Excel", default_filename, "Excel Files (*.xlsx)"
            )
            
            if not file_path:
                return
            
            # 创建进度对话框
            progress = QProgressDialog("正在导出数据...", "取消", 0, 100, self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()
            
            # 根据选项导出不同格式
            if export_options['export_type'] == 'summary':
                self.export_summary_excel(results, file_path, progress)
            elif export_options['export_type'] == 'detailed':
                self.export_detailed_excel(results, file_path, progress, export_options)
            elif export_options['export_type'] == 'combined':
                self.export_combined_excel(results, file_path, progress, export_options)
            
            progress.setValue(100)
            progress.close()
            
            # 询问是否打开文件
            reply = QMessageBox.question(
                self, "导出成功", 
                f"数据导出成功！\n文件保存至：{file_path}\n\n是否立即打开文件？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                import subprocess
                import platform
                
                # 跨平台打开文件
                if platform.system() == 'Windows':
                    os.startfile(file_path)
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.call(['open', file_path])
                else:  # Linux
                    subprocess.call(['xdg-open', file_path])
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出Excel失败：{str(e)}")
            print(f"导出Excel异常: {str(e)}")
            import traceback
            traceback.print_exc()

    def create_export_options_dialog(self):
        """创建导出选项对话框"""
        from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                                   QPushButton, QRadioButton, QCheckBox, QButtonGroup)
        from PyQt6.QtGui import QFont
        from PyQt6.QtCore import Qt
        
        dialog = QDialog(self)
        dialog.setWindowTitle("Excel导出选项")
        dialog.setModal(True)
        dialog.resize(400, 300)
        
        layout = QVBoxLayout(dialog)
        
        # 导出类型选择
        type_label = QLabel("导出类型：")
        type_label.setFont(QFont("", 10, QFont.Weight.Bold))
        layout.addWidget(type_label)
        
        # 使用QRadioButton替代QCheckBox，并用QButtonGroup管理
        type_group = QButtonGroup(dialog)
        
        summary_radio = QRadioButton("汇总导出（仅BOM主表信息）")
        summary_radio.setChecked(True)
        type_group.addButton(summary_radio, 1)
        layout.addWidget(summary_radio)
        
        detailed_radio = QRadioButton("明细导出（每个BOM一个工作表）")
        type_group.addButton(detailed_radio, 2)
        layout.addWidget(detailed_radio)
        
        combined_radio = QRadioButton("合并导出（主表+明细表在一个工作表）")
        type_group.addButton(combined_radio, 3)
        layout.addWidget(combined_radio)
        
        # 明细选项
        detail_label = QLabel("明细选项：")
        detail_label.setFont(QFont("", 10, QFont.Weight.Bold))
        layout.addWidget(detail_label)
        
        include_package = QCheckBox("包含包材明细")
        include_package.setChecked(True)
        layout.addWidget(include_package)
        
        include_material = QCheckBox("包含原料明细")
        include_material.setChecked(True)
        layout.addWidget(include_material)
        
        include_cost = QCheckBox("包含成本信息")
        include_cost.setChecked(False)
        layout.addWidget(include_cost)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        ok_button = QPushButton("确定")
        ok_button.clicked.connect(dialog.accept)
        button_layout.addWidget(ok_button)
        
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(dialog.reject)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
        
        # 添加获取选项的方法
        def get_export_options():
            export_type = 'summary'
            if detailed_radio.isChecked():
                export_type = 'detailed'
            elif combined_radio.isChecked():
                export_type = 'combined'
            
            return {
                'export_type': export_type,
                'include_package': include_package.isChecked(),
                'include_material': include_material.isChecked(),
                'include_cost': include_cost.isChecked()
            }
        
        dialog.get_export_options = get_export_options
        
        return dialog

    def export_summary_excel(self, results, file_path, progress):
        """导出汇总Excel（仅主表）"""
        import pandas as pd
        from PyQt6.QtWidgets import QApplication
        
        export_data = []
        
        for i, bom in enumerate(results):
            export_data.append({
                'BOM编码': bom.get('bom_code', ''),
                'BOM名称': bom.get('bom_name', ''),
                '成品物料编码': bom.get('parent_material_id', ''),
                '成品物料名称': bom.get('parent_material_name', ''),
                'BOM版本': bom.get('bom_version', ''),
                '生产单位': bom.get('production_unit', ''),
                '标准产出数量': bom.get('standard_output_qty', 0),
                '装箱数量': bom.get('package_qty_per_box', 0),
                '明细数量': bom.get('detail_count', 0),
                '状态': '启用' if bom.get('status') == 1 else '停用',
                '生效日期': bom.get('effective_date', ''),
                '失效日期': bom.get('expiry_date', ''),
                '备注': bom.get('remark', ''),
                '创建时间': bom.get('create_time', ''),
                '创建人': bom.get('create_user', ''),
                '更新时间': bom.get('update_time', ''),
                '更新人': bom.get('update_user', '')
            })
            
            # 更新进度
            progress.setValue(int((i + 1) / len(results) * 100))
            QApplication.processEvents()
            
            if progress.wasCanceled():
                return
        
        # 创建DataFrame并导出
        df = pd.DataFrame(export_data)
        df.to_excel(file_path, index=False, engine='openpyxl', sheet_name='BOM汇总')
        
        # 格式美化
        try:
            from material.excel_formatter import format_bom_excel_file
            format_bom_excel_file(file_path)
        except Exception as e:
            print(f"Excel格式美化失败: {e}")

    def export_detailed_excel(self, results, file_path, progress, options):
        """导出明细Excel（每个BOM一个工作表）"""
        import pandas as pd
        from PyQt6.QtWidgets import QApplication
        
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 创建汇总工作表
            summary_data = []
            for bom in results:
                summary_data.append({
                    'BOM编码': bom.get('bom_code', ''),
                    'BOM名称': bom.get('bom_name', ''),
                    '成品物料': bom.get('parent_material_name', ''),
                    '版本': bom.get('bom_version', ''),
                    '状态': '启用' if bom.get('status') == 1 else '停用'
                })
            
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='BOM汇总', index=False)
            
            # 为每个BOM创建详细工作表
            for i, bom in enumerate(results):
                bom_id = bom.get('bom_id')
                bom_code = bom.get('bom_code', f'BOM_{i+1}')
                
                # 获取BOM明细
                details_data = self.bom_db.get_bom_details(bom_id)
                
                # 创建BOM详细信息
                detail_data = []
                
                # 添加包材明细
                if options['include_package'] and details_data.get('package_details'):
                    for detail in details_data['package_details']:
                        detail_data.append({
                            '类型': '包材',
                            '序号': detail.get('sequence_no', ''),
                            '物料编码': detail.get('package_material_id', ''),
                            '物料名称': detail.get('material_name', ''),
                            '规格型号': detail.get('spec_model', ''),
                            '需求数量': detail.get('required_qty', 0),
                            '需求单位': detail.get('required_unit', ''),
                            '原箱数量': detail.get('original_box_qty', ''),
                            '原箱单位': detail.get('original_box_unit', ''),
                            '成本': detail.get('cost', 0) if options['include_cost'] else '',
                            '备注': detail.get('remark', '')
                        })
            
                # 添加原料明细
                if options['include_material'] and details_data.get('material_details'):
                    for detail in details_data['material_details']:
                        detail_data.append({
                            '类型': '原料',
                            '序号': detail.get('sequence_no', ''),
                            '物料编码': detail.get('raw_material_id', ''),
                            '物料名称': detail.get('material_name', ''),
                            '规格型号': detail.get('spec_model', ''),
                            '需求数量': detail.get('required_qty', 0),
                            '需求单位': detail.get('required_unit', ''),
                            '原箱数量': detail.get('original_box_qty', ''),
                            '原箱单位': detail.get('original_box_unit', ''),
                            '成本': detail.get('cost', 0) if options['include_cost'] else '',
                            '备注': detail.get('remark', '')
                        })
            
                if detail_data:
                    detail_df = pd.DataFrame(detail_data)
                    # 工作表名称限制在31个字符内
                    sheet_name = bom_code[:31] if len(bom_code) <= 31 else bom_code[:28] + '...'
                    detail_df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # 更新进度
                progress.setValue(int((i + 1) / len(results) * 100))
                QApplication.processEvents()
                
                if progress.wasCanceled():
                    return
        
        # 格式美化
        try:
            from material.excel_formatter import format_bom_excel_file
            format_bom_excel_file(file_path)
        except Exception as e:
            print(f"Excel格式美化失败: {e}")

    def export_combined_excel(self, results, file_path, progress, options):
        """导出合并Excel（主表+明细在一个工作表）"""
        import pandas as pd
        from PyQt6.QtWidgets import QApplication
        
        all_data = []
        
        for i, bom in enumerate(results):
            bom_id = bom.get('bom_id')
            
            # 获取BOM明细
            details_data = self.bom_db.get_bom_details(bom_id)
            
            # BOM基本信息
            base_info = {
                'BOM编码': bom.get('bom_code', ''),
                'BOM名称': bom.get('bom_name', ''),
                '成品物料编码': bom.get('parent_material_id', ''),
                '成品物料名称': bom.get('parent_material_name', ''),
                'BOM版本': bom.get('bom_version', ''),
                '生产单位': bom.get('production_unit', ''),
                '标准产出数量': bom.get('standard_output_qty', 0),
                '状态': '启用' if bom.get('status') == 1 else '停用'
            }
            
            # 如果没有明细，只添加主表信息
            has_details = False
            
            # 添加包材明细
            if options['include_package'] and details_data.get('package_details'):
                for detail in details_data['package_details']:
                    row_data = base_info.copy()
                    row_data.update({
                        '明细类型': '包材',
                        '明细序号': detail.get('sequence_no', ''),
                        '明细物料编码': detail.get('package_material_id', ''),
                        '明细物料名称': detail.get('material_name', ''),
                        '明细规格型号': detail.get('spec_model', ''),
                        '需求数量': detail.get('required_qty', 0),
                        '需求单位': detail.get('required_unit', ''),
                        '原箱数量': detail.get('original_box_qty', ''),
                        '原箱单位': detail.get('original_box_unit', ''),
                        '明细备注': detail.get('remark', '')
                    })
                    
                    if options['include_cost']:
                        row_data['明细成本'] = detail.get('cost', 0)
                    
                    all_data.append(row_data)
                    has_details = True
            
            # 添加原料明细
            if options['include_material'] and details_data.get('material_details'):
                for detail in details_data['material_details']:
                    row_data = base_info.copy()
                    row_data.update({
                        '明细类型': '原料',
                        '明细序号': detail.get('sequence_no', ''),
                        '明细物料编码': detail.get('raw_material_id', ''),
                        '明细物料名称': detail.get('material_name', ''),
                        '明细规格型号': detail.get('spec_model', ''),
                        '需求数量': detail.get('required_qty', 0),
                        '需求单位': detail.get('required_unit', ''),
                        '原箱数量': detail.get('original_box_qty', ''),
                        '原箱单位': detail.get('original_box_unit', ''),
                        '明细备注': detail.get('remark', '')
                    })
                    
                    if options['include_cost']:
                        row_data['明细成本'] = detail.get('cost', 0)
                    
                    all_data.append(row_data)
                    has_details = True
            
            # 如果没有明细，添加主表信息
            if not has_details:
                row_data = base_info.copy()
                row_data.update({
                    '明细类型': '无明细',
                    '明细序号': '',
                    '明细物料编码': '',
                    '明细物料名称': '',
                    '明细规格型号': '',
                    '需求数量': '',
                    '需求单位': '',
                    '原箱数量': '',
                    '原箱单位': '',
                    '明细备注': ''
                })
                
                if options['include_cost']:
                    row_data['明细成本'] = ''
                
                all_data.append(row_data)
            
            # 更新进度
            progress.setValue(int((i + 1) / len(results) * 100))
            QApplication.processEvents()
        
        # 创建DataFrame并导出
        df = pd.DataFrame(all_data)
        df.to_excel(file_path, index=False, engine='openpyxl', sheet_name='BOM明细清单')
        
        # 格式美化
        try:
            from material.excel_formatter import format_bom_excel_file
            format_bom_excel_file(file_path)
        except Exception as e:
            print(f"Excel格式美化失败: {e}")




