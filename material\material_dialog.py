"""
物料新增/编辑对话框
用于添加和编辑物料信息
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QComboBox, QFormLayout, QDialogButtonBox, QTabWidget,
                           QWidget, QSpinBox, QDoubleSpinBox, QTextEdit, QCheckBox)
from PyQt6.QtCore import Qt
from material.material_db import MaterialDB

class MaterialDialog(QDialog):
    """物料新增/编辑对话框"""
    
    def __init__(self, parent=None, material=None):
        super().__init__(parent)
        self.material = material
        self.material_db = MaterialDB()
        
        self.setWindowTitle("物料信息" if not material else f"编辑物料: {material.get('material_id', '')}")
        self.resize(600, 500)
        
        self.setup_ui()
        
        # 如果是编辑，填充数据
        if material:
            self.fill_form_data()
    
    def setup_ui(self):
        """设置UI界面"""
        main_layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 基本信息标签页
        basic_tab = QWidget()
        basic_layout = QFormLayout(basic_tab)
        
        # 物料编码
        self.material_id_edit = QLineEdit()
        basic_layout.addRow("物料编码:", self.material_id_edit)
        
        # 物料名称
        self.name_edit = QLineEdit()
        basic_layout.addRow("物料名称:", self.name_edit)
        
        # 规格型号
        self.spec_model_edit = QLineEdit()
        basic_layout.addRow("规格型号:", self.spec_model_edit)
        
        # 基本计量单位
        self.base_unit_edit = QLineEdit()
        basic_layout.addRow("基本计量单位:", self.base_unit_edit)
        
        # 原箱规格
        self.original_box_unit_edit = QSpinBox()
        self.original_box_unit_edit.setRange(0, 10000)
        basic_layout.addRow("原箱规格:", self.original_box_unit_edit)
        
        # 计量换算
        self.conversion_rate_edit = QSpinBox()
        self.conversion_rate_edit.setRange(0, 10000)
        basic_layout.addRow("计量换算:", self.conversion_rate_edit)
        
        # 成本
        self.cost_edit = QDoubleSpinBox()
        self.cost_edit.setRange(0, 1000000)
        self.cost_edit.setDecimals(2)
        self.cost_edit.setSuffix(" 元")
        basic_layout.addRow("成本:", self.cost_edit)
        
        # 条形码
        self.barcode_edit = QLineEdit()
        basic_layout.addRow("条形码:", self.barcode_edit)
        
        # 物料状态
        self.status_combo = QComboBox()
        self.status_combo.addItem("启用", 1)
        self.status_combo.addItem("停用", 0)
        basic_layout.addRow("物料状态:", self.status_combo)
        
        # 物料分类
        self.category_combo = QComboBox()
        self.load_categories()
        basic_layout.addRow("物料分类:", self.category_combo)
        # 是否东方货物
        self.is_east_material_checkbox = QCheckBox("是否东方货物")
        basic_layout.addRow("是否东方货物:", self.is_east_material_checkbox)

        # 添加基本信息标签页
        self.tab_widget.addTab(basic_tab, "基本信息")
        
        # 尺寸重量标签页
        size_tab = QWidget()
        size_layout = QFormLayout(size_tab)
        
        # 长度
        size_length_layout = QHBoxLayout()
        self.length_edit = QDoubleSpinBox()
        self.length_edit.setRange(0, 10000)
        self.length_edit.setDecimals(2)
        self.length_unit_combo = QComboBox()
        self.length_unit_combo.addItems(["mm", "cm", "m"])
        size_length_layout.addWidget(self.length_edit)
        size_length_layout.addWidget(self.length_unit_combo)
        size_layout.addRow("长度:", size_length_layout)
        
        # 宽度
        self.width_edit = QDoubleSpinBox()
        self.width_edit.setRange(0, 10000)
        self.width_edit.setDecimals(2)
        size_layout.addRow("宽度(cm):", self.width_edit)
        
        # 高度
        self.height_edit = QDoubleSpinBox()
        self.height_edit.setRange(0, 10000)
        self.height_edit.setDecimals(2)
        size_layout.addRow("高度(cm):", self.height_edit)
        
        # 毛重
        weight_layout = QHBoxLayout()
        self.gross_weight_edit = QDoubleSpinBox()
        self.gross_weight_edit.setRange(0, 10000)
        self.gross_weight_edit.setDecimals(3)
        self.weight_unit_combo = QComboBox()
        self.weight_unit_combo.addItems(["g", "kg", "t"])
        weight_layout.addWidget(self.gross_weight_edit)
        weight_layout.addWidget(self.weight_unit_combo)
        size_layout.addRow("毛重:", weight_layout)
        
        # 体积
        volume_layout = QHBoxLayout()
        self.volume_edit = QDoubleSpinBox()
        self.volume_edit.setRange(0, 10000)
        self.volume_edit.setDecimals(4)
        self.volume_unit_combo = QComboBox()
        self.volume_unit_combo.addItems(["mm³", "cm³", "m³"])
        volume_layout.addWidget(self.volume_edit)
        volume_layout.addWidget(self.volume_unit_combo)
        size_layout.addRow("体积:", volume_layout)
        
        # 添加尺寸重量标签页
        self.tab_widget.addTab(size_tab, "尺寸重量")
        
        # 备注标签页
        remark_tab = QWidget()
        remark_layout = QVBoxLayout(remark_tab)
        self.remark_edit = QTextEdit()
        remark_layout.addWidget(self.remark_edit)
        
        # 添加备注标签页
        self.tab_widget.addTab(remark_tab, "备注")
        
        # 添加标签页到主布局
        main_layout.addWidget(self.tab_widget)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
    
    def load_categories(self):
        """加载物料分类"""
        try:
            categories = self.material_db.get_categories()
            for category in categories:
                self.category_combo.addItem(category["category_name"], category["category_id"])
        except Exception as e:
            print(f"加载物料分类失败: {str(e)}")
    
    def fill_form_data(self):
        """填充表单数据"""
        if not self.material:
            return
        
        # 填充基本信息
        self.material_id_edit.setText(self.material.get("material_id", ""))
        self.material_id_edit.setReadOnly(True)  # 编辑时物料编码不可修改
        self.name_edit.setText(self.material.get("name", ""))
        self.spec_model_edit.setText(self.material.get("spec_model", ""))
        self.base_unit_edit.setText(self.material.get("base_unit", ""))
        self.original_box_unit_edit.setValue(self.material.get("original_box_unit", 0) or 0)
        self.conversion_rate_edit.setValue(self.material.get("conversion_rate", 0) or 0)
        self.barcode_edit.setText(self.material.get("barcode", ""))
        
        # 设置成本
        self.cost_edit.setValue(self.material.get("cost", 0) or 0)
        
        # 设置状态
        status_index = self.status_combo.findData(self.material.get("status", 1))
        if status_index >= 0:
            self.status_combo.setCurrentIndex(status_index)
        
        # 设置分类
        category_index = self.category_combo.findData(self.material.get("category_id"))
        if category_index >= 0:
            self.category_combo.setCurrentIndex(category_index)
        # 设置是否东方货物
        self.is_east_material_checkbox.setChecked(self.material.get("is_east_material", 0) or 0)
        # 填充尺寸重量
        self.length_edit.setValue(self.material.get("length", 0) or 0)
        length_unit_index = self.length_unit_combo.findText(self.material.get("length_unit", "mm"))
        if length_unit_index >= 0:
            self.length_unit_combo.setCurrentIndex(length_unit_index)
        
        self.width_edit.setValue(self.material.get("width", 0) or 0)
        
        self.height_edit.setValue(self.material.get("height", 0) or 0)
        
        self.gross_weight_edit.setValue(self.material.get("gross_weight", 0) or 0)
        weight_unit_index = self.weight_unit_combo.findText(self.material.get("weight_unit", "kg"))
        if weight_unit_index >= 0:
            self.weight_unit_combo.setCurrentIndex(weight_unit_index)
        
        self.volume_edit.setValue(self.material.get("volume", 0) or 0)
        volume_unit_index = self.volume_unit_combo.findText(self.material.get("volume_unit", "m³"))
        if volume_unit_index >= 0:
            self.volume_unit_combo.setCurrentIndex(volume_unit_index)
        
        # 填充备注
        self.remark_edit.setText(self.material.get("remark", ""))
    
    def get_material_data(self):
        """获取物料数据"""
        material_data = {
            "material_id": self.material_id_edit.text().strip(),
            "name": self.name_edit.text().strip(),
            "spec_model": self.spec_model_edit.text().strip(),
            "base_unit": self.base_unit_edit.text().strip(),
            "original_box_unit": self.original_box_unit_edit.value(),
            "conversion_rate": self.conversion_rate_edit.value(),
            "barcode": self.barcode_edit.text().strip(),
            "status": self.status_combo.currentData(),
            "category_id": self.category_combo.currentData(),
            "length_unit": self.length_unit_combo.currentText(),
            "length": self.length_edit.value(),
            "width": self.width_edit.value(),
            "height": self.height_edit.value(),
            "weight_unit": self.weight_unit_combo.currentText(),
            "gross_weight": self.gross_weight_edit.value(),
            "volume_unit": self.volume_unit_combo.currentText(),
            "volume": self.volume_edit.value(),
            "cost": self.cost_edit.value(),
            "remark": self.remark_edit.toPlainText().strip(),
            "is_east_material": self.is_east_material_checkbox.isChecked()
        }
        
        return material_data
    
    def accept(self):
        """确认按钮处理"""
        # 基本验证
        if not self.material_id_edit.text().strip():
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", "物料编码不能为空")
            return
        
        if not self.name_edit.text().strip():
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", "物料名称不能为空")
            return
        
        # 通过验证，接受对话框
        super().accept() 