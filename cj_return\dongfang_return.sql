CREATE TABLE return_application (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
    yewu_no VARCHAR(50) NOT NULL COMMENT '业务编号',
    return_date DATE NOT NULL COMMENT '退货日期',
    customer VARCHAR(100) NOT NULL COMMENT '送货客户',
    logistics VARCHAR(100) COMMENT '物流公司/方式',
    remark TEXT COMMENT '备注'
) COMMENT='退货申请主表';
CREATE TABLE return_application_detail (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
    return_id INT NOT NULL COMMENT '退货申请ID（外键）',
    waybill_no VARCHAR(50) NOT NULL COMMENT '运单号码',
    dongfang_code VARCHAR(50) NOT NULL COMMENT '东方货号',
    quantity INT NOT NULL COMMENT '数量',
    FOREIGN KEY (return_id) REFERENCES return_application(id)
) COMMENT='退货明细子表';
ALTER TABLE return_application
ADD COLUMN create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN create_user VARCHAR(50) COMMENT '创建人',
ADD COLUMN update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
ADD COLUMN update_user VARCHAR(50) COMMENT '最后更新人';

ALTER TABLE return_application_detail
ADD COLUMN create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN create_user VARCHAR(50) COMMENT '创建人',
ADD COLUMN update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
ADD COLUMN update_user VARCHAR(50) COMMENT '最后更新人';