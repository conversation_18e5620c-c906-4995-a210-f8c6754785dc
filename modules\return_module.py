from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QTabWidget, 
                           QTableWidget, QTableWidgetItem, QPushButton, 
                           QHBoxLayout, QComboBox, QDateEdit, QLineEdit, QCheckBox,
                           QLabel, QHeaderView, QMessageBox,QFileDialog,QStatusBar,QProgressDialog,QApplication)
from PyQt6.QtCore import Qt
from datetime import datetime, timedelta, date
import re
from .detail_dialog import DeliveryDetailDialog
from .handover_detail_dialog import HandoverDetailDialog
from openpyxl import Workbook
from openpyxl.styles import Font
from datetime import datetime
from .print_preview_dialog import PrintPreviewDialog
from PyQt6.QtGui import QColor
from PyQt6.QtCore import QTimer
class DeliveryOrderTab(QWidget):
    def __init__(self, return_db):
        super().__init__()
        self.return_db = return_db
        self.selected_rows = set()  # 存储所有被选中的行的单据编号
        self.init_ui()
        self.search_orders()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(10)  # 设置布局间距
        
        # 第一行搜索条件
        first_search_layout = QHBoxLayout()
        first_search_layout.setSpacing(10)  # 设置水平间距
        
        # 单号搜索
        first_search_layout.addWidget(QLabel("单据编号:"))
        self.order_input = QLineEdit()
        self.order_input.setPlaceholderText("请输入单据编号")
        self.order_input.returnPressed.connect(self.search_orders)  # 添加回车搜索
        first_search_layout.addWidget(self.order_input)
        
        # 源单据编号搜索
        first_search_layout.addWidget(QLabel("原始单号:"))
        self.source_order_input = QLineEdit()
        self.source_order_input.setPlaceholderText("请输入原始单号")
        self.source_order_input.returnPressed.connect(self.search_orders)  # 添加回车搜索
        first_search_layout.addWidget(self.source_order_input)
        
       
        # 搜索按钮
        search_btn = QPushButton("搜索")
        search_btn.clicked.connect(self.search_orders)
        first_search_layout.addWidget(search_btn)
        
        # 重置按钮
        reset_btn = QPushButton("重置")
        reset_btn.clicked.connect(self.reset_search)
        first_search_layout.addWidget(reset_btn)
        
        # 导入按钮
        import_btn = QPushButton("导入")
        import_btn.clicked.connect(self.show_import_dialog)
        first_search_layout.addWidget(import_btn)
        
        # 添加导出按钮
        export_btn = QPushButton("导出")
        export_btn.clicked.connect(self.export_data)
        first_search_layout.addWidget(export_btn)
        
        first_search_layout.addStretch()  # 添加弹性空间
        layout.addLayout(first_search_layout)
        
        # 第二行搜索条件
        second_search_layout = QHBoxLayout()
        second_search_layout.setSpacing(10)  # 设置水平间距
        
        # 日期选择
        second_search_layout.addWidget(QLabel("日期范围:"))
        self.start_date = QDateEdit()
        self.end_date = QDateEdit()
        
        # 移除日期控件的自定义样式
        for date_edit in [self.start_date, self.end_date]:
            date_edit.setDisplayFormat("yyyy-MM-dd")
            date_edit.setCalendarPopup(True)
            date_edit.setMinimumWidth(120)
            date_edit.setFixedWidth(150)
            # 移除样式表设置
            date_edit.setStyleSheet("")
        
        # 设置默认日期为当前日期往前推一个月
        today = date.today()
        one_month_ago = today - timedelta(days=31)  # 往前推30天
        
        self.start_date.setDate(one_month_ago)
        self.end_date.setDate(today)
        
        second_search_layout.addWidget(self.start_date)
        second_search_layout.addWidget(QLabel("至"))
        second_search_layout.addWidget(self.end_date)
        
        # 物流公司选择
        second_search_layout.addWidget(QLabel("物流公司:"))
        self.logistics_combo = QComboBox()
        self.logistics_combo.setStyleSheet("")  # 移除样式
        second_search_layout.addWidget(self.logistics_combo)
        
        # 状态下拉框
        second_search_layout.addWidget(QLabel("单据状态:"))
        self.status_combo = QComboBox()
        self.status_combo.setStyleSheet("")  # 移除样式
        self.status_combo.addItems(["全部", "开启", "关闭"])
        self.status_combo.setCurrentText("开启")  # 设置默认值为"开启"
        second_search_layout.addWidget(self.status_combo)
        
        second_search_layout.addStretch()  # 添加弹性空间
        layout.addLayout(second_search_layout)
        
        # 移除选择操作区域的复选框相关代码
        select_layout = QHBoxLayout()
        select_layout.setSpacing(10)
        
        # 添加选中统计标签
        self.select_count_label = QLabel("已选择: 0")
        select_layout.addWidget(self.select_count_label)
        
        # 添加批量生成交接单按钮
        self.batch_handover_btn = QPushButton("批量生成交接单")
        self.batch_handover_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                padding: 5px 15px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
            QPushButton:pressed {
                background-color: #389e0d;
            }
        """)
        self.batch_handover_btn.clicked.connect(self.batch_create_handover)
        select_layout.addWidget(self.batch_handover_btn)
        
        # 添加批量搜索按钮
        batch_search_btn = QPushButton("批量搜索")
        batch_search_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 5px 15px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
            QPushButton:pressed {
                background-color: #096dd9;
            }
        """)
        batch_search_btn.clicked.connect(self.show_batch_search_dialog)
        select_layout.addWidget(batch_search_btn)
        
        # 添加批量关闭按钮
        self.batch_close_btn = QPushButton("批量关闭")
        self.batch_close_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4d4f;
                color: white;
                border: none;
                padding: 5px 15px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #ff7875;
            }
            QPushButton:pressed {
                background-color: #cf1322;
            }
        """)
        self.batch_close_btn.clicked.connect(self.batch_close_orders)
        select_layout.addWidget(self.batch_close_btn)
        
        select_layout.addStretch()
        layout.addLayout(select_layout)
        
        # 修改表格
        self.table = QTableWidget()
        self.table.setColumnCount(8)  # 减少一列，移除选择列
        self.table.setHorizontalHeaderLabels([
            "单据编号", "收货日期", "来源单据编号", 
            "原始单号", "送货", "物流公司", 
            "剩余退货数量", "交接次数"
        ])
        
        # 启用排序
        self.table.setSortingEnabled(True)
        
        # 连接排序信号
        self.table.horizontalHeader().sectionClicked.connect(self.on_header_clicked)
        
        # 添加排序状态记录
        self.current_sort_column = -1  # 当前排序列
        self.current_sort_order = Qt.SortOrder.AscendingOrder  # 当前排序顺序
        
        # 设置表格属性
        self.table.setSelectionMode(QTableWidget.SelectionMode.ExtendedSelection)  # 允许多选
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 连接选择变化信号
        self.table.itemSelectionChanged.connect(self.update_selection_count)
        
        # 设置表格列宽自适应
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 单据编号
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 收货日期
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 来源单据编号
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 原始单号自适应
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 送货客户
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 物流公司
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # 剩余退货数量
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # 交接次数
        
        layout.addWidget(self.table)
        
        # 在表格下方添加分页控件
        pagination_layout = QHBoxLayout()
        pagination_layout.setSpacing(10)
        
        # 左侧显示总记录数
        self.total_label = QLabel("总记录数: 0")
        pagination_layout.addWidget(self.total_label)
        
        pagination_layout.addStretch()  # 添加弹性空间
        
        # 分页控件
        self.prev_btn = QPushButton("<")
        self.prev_btn.setFixedWidth(30)  # 设置按钮宽度
        self.prev_btn.clicked.connect(self.prev_page)
        
        self.page_label = QLabel("1")  # 当前页码
        
        self.next_btn = QPushButton(">")
        self.next_btn.setFixedWidth(30)  # 设置按钮宽度
        self.next_btn.clicked.connect(self.next_page)
        
        pagination_layout.addWidget(self.prev_btn)
        pagination_layout.addWidget(self.page_label)
        pagination_layout.addWidget(self.next_btn)
        
        layout.addLayout(pagination_layout)
        
        # 初始化分页相关变量
        self.page_size = 20  # 每页显示量
        self.current_page = 1  # 当前页码
        self.total_records = 0  # 总记录数
        
        # 添加双击事件处理
        self.table.cellDoubleClicked.connect(self.show_detail_dialog)

        # 为搜索相关的控件连接信号
        self.start_date.dateChanged.connect(self.search_orders)
        self.end_date.dateChanged.connect(self.search_orders)
        self.logistics_combo.currentIndexChanged.connect(self.search_orders)
        self.status_combo.currentIndexChanged.connect(self.search_orders)

    def reset_search(self):
        """重置搜索条件"""
        self.order_input.clear()
        self.source_order_input.clear()
        # 重置日期为当月
        today = date.today()
        one_month_ago = today - timedelta(days=31)  # 往前推30天
        self.start_date.setDate(one_month_ago)
        self.end_date.setDate(today)
        self.logistics_combo.setCurrentIndex(0)  # 重置物流公司为全部
        self.status_combo.setCurrentText("开启")  # 重置状态为开启
        
        # 重置分页
        self.current_page = 1
        
        # 清空表格选择
        self.table.clearSelection()
        
        # 更新选中计数显示
        self.update_selection_count()
        
        # 执行搜索
        self.search_orders()

    def search_orders(self):
        """搜索出库单"""
        try:
            # 如果是通过搜索按钮或搜索条件变化触发的搜索，重置页码到第一页
            # 通过判断调用者来确定是否需要重置页码
            caller = self.sender()
            if caller and caller not in [self.prev_btn, self.next_btn]:
                self.current_page = 1
            
            # 获取搜索参数
            filters = self.get_search_params()
            
            # 调用数据库查询
            total, results = self.return_db.get_delivery_orders(filters)
            
            # 更新表格数据
            self.update_table(total, results)
            
            # 更新分页状态
            self.total_records = total
            self.total_label.setText(f"总记录数: {total}")
            self.update_pagination_status()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"搜索失败：{str(e)}")

    def update_selection_count(self):
        """更新选中行数显示"""
        selected_rows = len(self.table.selectedItems()) // self.table.columnCount()
        self.select_count_label.setText(f"已选择: {selected_rows}")

    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.search_orders()

    def next_page(self):
        """下一页"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        if self.current_page < total_pages:
            self.current_page += 1
            self.search_orders()

    def load_logistics_companies(self):
        """加载物流公司列表"""
        companies = self.return_db.get_logistics_companies()
        self.logistics_combo.clear()
        self.logistics_combo.addItem("全部物流公司")
        for company in companies:
            if company:  # 确保公司名不为空
                self.logistics_combo.addItem(str(company))

    def show_import_dialog(self):
        from .import_dialog import ImportDialog
        dialog = ImportDialog(self)
        if dialog.exec() == ImportDialog.DialogCode.Accepted:
            try:
                # 获取导入数据
                import_data = dialog.import_data
                
                # 调用数据库导入方法
                result = self.return_db.import_delivery_orders(import_data)
                
                if result:
                    QMessageBox.information(
                        self, 
                        "导入完成", 
                        f"导入完成！\n"
                        f"成功导入: {len(import_data)} 条"
                    )
                    # 刷新表格数据
                    self.search_orders()
                else:
                    QMessageBox.critical(self, "错误", "导入失败：没有数据被导入")
                    
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导入失败：{str(e)}")

    

    def update_pagination_status(self):
        """更新分页状态"""
        # 计算总页数
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        
        # 更新页码显示
        self.page_label.setText(f"{self.current_page}/{total_pages}")
        
        # 更新按钮状态
        self.prev_btn.setEnabled(self.current_page > 1)
        self.next_btn.setEnabled(self.current_page < total_pages)

    @staticmethod
    def get_original_order_no(summary, source_order_no):
        """获取原始单号
        当源单据编号以"SO"开头时：
            如果摘要不为空且包含字母数字组合，则显示摘要内容
            否则显示源单据编号
        当源单据编号不以"SO"开头时，直接显示源单据编号
        """
        # 如果源单据编号为空，直接返回空符串
        if not source_order_no:
            return ''
        
        # 检查源单据编号是否以"SO"开头
        if source_order_no.startswith('SO'):
            # 如果摘要不为空，尝试提取字母数字组合
            if summary:
                import re
                matches = re.findall(r'[A-Za-z0-9]+', summary)
                if matches:
                    return matches[0]  # 返回第一个匹配的字母数字组合
            # 如果摘要为空或没有找到字母数字组合，返回源单据编号
            return source_order_no
        
        # 源单据编号不是以"SO"开头，直接返回源单据编号
        return source_order_no

    def show_detail_dialog(self, row, column):
        """显示明细对话框"""
        try:
            # 获取单据编号
            order_no = self.table.item(row, 0).text()  # 改为第一列，因为我们移除了选择列
            
            # 获取明细数据
            detail_data = self.return_db.get_delivery_order_details(order_no)
            
            if not detail_data:
                QMessageBox.warning(self, "警告", "未找到相关明细数据！")
                return
            
            # 创建并显示明细对话框
            dialog = DeliveryDetailDialog(self)
            dialog.return_db = self.return_db
            dialog.set_delivery_data(None, detail_data)
            
            # 如果对话框关闭后需要刷新数据
            if dialog.exec() == DeliveryDetailDialog.DialogCode.Accepted:
                self.search_orders()
                
        except Exception as e:
            QMessageBox.critical(
                self,
                "错误",
                str(e) if "已存在未审核的交接单" in str(e) else "生成交接单失败"
            )  # 添加右括号

    def batch_create_handover(self):
        """批量生成交接单"""
        try:
            # 获取选中的行
            selected_rows = set(item.row() for item in self.table.selectedItems())
            if not selected_rows:
                QMessageBox.warning(self, "提示", "请先选择要生成交接单的记录！")
                return
            
            # 收集选中行的单据编号
            order_nos = []
            for row in selected_rows:
                order_no = self.table.item(row, 0).text()  # 第一列是单据编号
                has_unaudited = self.table.item(row, 0).data(Qt.ItemDataRole.UserRole)
                if has_unaudited:
                    QMessageBox.warning(
                        self, 
                        "警告", 
                        f"单据 {order_no} 存在未审核的交接单，不能重复生成！"
                    )
                    return
                order_nos.append(order_no)
            
            # 确认是否生成交接单
            reply = QMessageBox.question(
                self, 
                '确认',
                f'是否确认为选中的 {len(order_nos)} 个出库单生成交接单？',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 创建进度对话框
                progress_dialog = QProgressDialog("正在生成交接单...", "取消", 0, len(order_nos), self)
                progress_dialog.setWindowTitle("生成进度")
                progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
                progress_dialog.setMinimumDuration(0)  # 立即显示
                progress_dialog.show()
                
                try:
                    def update_progress(value):
                        if progress_dialog.wasCanceled():
                            raise Exception("用户取消操作")
                        progress_dialog.setValue(value)
                        progress_dialog.setLabelText(f"正在处理 {value+1}/{len(order_nos)}")
                        QApplication.processEvents()  # 处理事件，保持UI响应
                    
                    # 调用数据库方法生成交接单
                    if self.return_db.batch_create_handover_orders(order_nos, update_progress):
                        QMessageBox.information(self, "成功", "批量生成交接单成功！")
                        self.search_orders()  # 刷新数据
                
                except Exception as e:
                    if str(e) != "用户取消操作":
                        QMessageBox.critical(self, "错误", f"生成交接单失败：{str(e)}")
                
                finally:
                    progress_dialog.close()
            else:
                return
        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成交接单失败：{str(e)}")

    def update_table(self, total, results):
        """更新表格数据"""
        # 临时关闭排序功能
        self.table.setSortingEnabled(False)
        
        self.table.setRowCount(0)
        if not results:
            # 重新启用排序功能
            self.table.setSortingEnabled(True)
            return
        
        self.table.setRowCount(len(results))
        for row, data in enumerate(results):
            # 设置单据编号，并存储未审核标志
            order_no_item = QTableWidgetItem(data['order_no'])
            order_no_item.setData(Qt.ItemDataRole.UserRole, data['has_unaudited_handover'])
            self.table.setItem(row, 0, order_no_item)
            
            # 设置其他列
            self.table.setItem(row, 1, QTableWidgetItem(str(data['confirm_receipt_date'])))
            self.table.setItem(row, 2, QTableWidgetItem(str(data['source_order_no'] or '')))
            
            # 直接使用数据库返回的原始单号
            self.table.setItem(row, 3, QTableWidgetItem(str(data['original_order_no'] or '')))
            
            # 设置其他列
            self.table.setItem(row, 4, QTableWidgetItem(str(data['customer'] or '')))
            self.table.setItem(row, 5, QTableWidgetItem(str(data['logistics_company'] or '')))
            self.table.setItem(row, 6, QTableWidgetItem(str(data['remaining_return_quantity'])))
            
            # 设置交接次数，并根据是否有未审核交接单设置样式
            count_item = QTableWidgetItem('1' if data.get('has_unaudited_handover') else '0')
            count_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # 如果有未审核的交接单，设置背景色为浅黄色
            if data.get('has_unaudited_handover'):
                count_item.setBackground(QColor("#FFFBE6"))
            
            self.table.setItem(row, 7, count_item)
            
            # 设置单元格对齐方式
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item:
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 重新启用排序功能
        self.table.setSortingEnabled(True)

    def get_search_params(self):
        """获取搜索参数"""
        filters = {
            'order_no': self.order_input.text().strip(),
            'source_order_no': self.source_order_input.text().strip(),
            'start_date': self.start_date.date().toPyDate(),
            'end_date': self.end_date.date().toPyDate(),
            'logistics_company': self.logistics_combo.currentText(),
            'page': self.current_page,
            'page_size': self.page_size
        }
        
        # 添加状态值
        status_text = self.status_combo.currentText()
        if status_text != "全部":
            filters['order_status'] = 0 if status_text == "开启" else 1
        
        return filters

    def on_header_clicked(self, logical_index):
        """处理表头点击排序"""
        # 更新排序状态
        if self.current_sort_column == logical_index:
            # 如果点击同一列,切换排序顺序
            self.current_sort_order = Qt.SortOrder.DescendingOrder if self.current_sort_order == Qt.SortOrder.AscendingOrder else Qt.SortOrder.AscendingOrder
        else:
            # 如果点击不同列,设置为升序
            self.current_sort_column = logical_index
            self.current_sort_order = Qt.SortOrder.AscendingOrder
        
        # 执行排序
        self.table.sortItems(logical_index, self.current_sort_order)

    def show_batch_search_dialog(self):
        """显示批量搜索对话框"""
        from .batch_search_dialog import BatchSearchDialog
        dialog = BatchSearchDialog(self)
        if dialog.exec() == BatchSearchDialog.DialogCode.Accepted:
            source_order_nos = dialog.get_source_order_nos()
            if source_order_nos:
                # 将单号列表转换为用逗号分隔的字符串
                self.source_order_input.setText(','.join(source_order_nos))
                
                # 清理输入的单号（去除空格并转换为大写）
                cleaned_source_order_nos = [no.strip().upper() for no in source_order_nos if no.strip()]
                
                # 获取所有匹配的单号
                found_order_nos = self.return_db.get_all_original_order_nos(cleaned_source_order_nos)
                
                # 找出未搜索到的单号
                not_found_order_nos = [no for no in cleaned_source_order_nos if no not in found_order_nos]
                
                # 打印调试信息
                print(f"输入的单号数量: {len(cleaned_source_order_nos)}")
                print(f"搜索到的单号数量: {len(found_order_nos)}")
                print(f"未找到的单号数量: {len(not_found_order_nos)}")
                
                # 执行正常的分页搜索以更新表格显示
                self.search_orders()
                
                if not_found_order_nos:
                    # 显示未找到的单号数量和具体单号
                    detail_message = f"有 {len(not_found_order_nos)} 个单号未搜索到：\n\n"
                    # 最多显示前5个未找到的单号
                    show_nos = not_found_order_nos[:5]
                    if len(not_found_order_nos) > 5:
                        detail_message += "\n".join(show_nos) + "\n..."
                    else:
                        detail_message += "\n".join(not_found_order_nos)
                    detail_message += "\n\n是否导出这些单号？"
                    
                    reply = QMessageBox.question(
                        self,
                        "提示",
                        detail_message,
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.No
                    )
                    
                    if reply == QMessageBox.StandardButton.Yes:
                        # 让用户选择保存位置
                        file_name, _ = QFileDialog.getSaveFileName(
                            self,
                            "导出未搜索到的单号",
                            f"未搜索到单号_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx",
                            "Excel Files (*.xlsx)"
                        )
                        
                        if file_name:
                            try:
                                # 创建工作簿
                                wb = Workbook()
                                ws = wb.active
                                ws.title = "未搜索到的单号"
                                
                                # 添加表头
                                ws.append(["序号", "原始单号"])
                                
                                # 添加数据
                                for i, order_no in enumerate(not_found_order_nos, 1):
                                    ws.append([i, order_no])
                                
                                # 调整列宽
                                for column in ws.columns:
                                    max_length = 0
                                    column = list(column)
                                    for cell in column:
                                        try:
                                            if len(str(cell.value)) > max_length:
                                                max_length = len(str(cell.value))
                                        except:
                                            pass
                                    adjusted_width = (max_length + 2)
                                    ws.column_dimensions[column[0].column_letter].width = adjusted_width
                                
                                # 保存文件
                                wb.save(file_name)
                                QMessageBox.information(self, "成功", "未搜索到的单号已成功导出！")
                            except Exception as e:
                                QMessageBox.critical(self, "错误", f"导出失败：{str(e)}")

    def export_data(self):
        """导出出库单数据"""
        try:
            # 获取筛选条件
            filters = {
                'order_no': self.order_input.text().strip(),
                'source_order_no': self.source_order_input.text().strip(),
                'start_date': self.start_date.date().toPyDate(),
                'end_date': self.end_date.date().toPyDate(),
                'logistics_company': self.logistics_combo.currentText() if self.logistics_combo.currentText() != "全部物流公司" else None,
                'order_status': None if self.status_combo.currentText() == "全部" else (0 if self.status_combo.currentText() == "开启" else 1)
            }
            
            # 获取数据
            results = self.return_db.export_delivery_orders(filters)
            
            if not results:
                QMessageBox.warning(self, "警告", "没有数据可供导出！")
                return
            
            # 让用户选择保存位置
            file_name, _ = QFileDialog.getSaveFileName(
                self,
                "导出数据",
                f"出库单_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )
            
            if not file_name:
                return

            # 创建进度对话框
            progress_dialog = QProgressDialog("正在导出数据...", "取消", 0, len(results), self)
            progress_dialog.setWindowTitle("导出进度")
            progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
            progress_dialog.setMinimumDuration(0)  # 立即显示
            progress_dialog.show()
                
            # 创建工作簿
            wb = Workbook()
            
            # 创建主表工作表
            ws = wb.active
            ws.title = "出库单"
            
            # 设置主表表头
            headers = [
                "单据编号", "收货日期", "来源单据编号", "原始单号", "客户", "部门", "物流公司",
                "明细号", "物料编码", "物料名称", "单位", "批号", "仓库", 
                "拒收原因", "发货数量", "确认数量", "退货数量", "剩余退货数量", "含税单价","单据状态"
            ]
            ws.append(headers)
            
            # 写入数据
            for i, row in enumerate(results):
                # 更新进度
                progress_dialog.setValue(i)
                progress_dialog.setLabelText(f"正在处理第 {i+1}/{len(results)} 条数据")
                
                # 检查是否取消
                if progress_dialog.wasCanceled():
                    wb.close()
                    return
                
                # 获取明细数据
                details = self.return_db.get_delivery_order_details(row['order_no'])
                if details and 'details' in details:
                    for detail in details['details']:
                        # 合并主表和明细数据
                        ws.append([
                            row['order_no'],                # 单据编号
                            str(row['confirm_receipt_date']), # 收货日期
                            row['source_order_no'],         # 来源单据编号
                            row['original_order_no'],       # 原始单号
                            row['customer'],                # 客户
                            details['header']['department'], # 部门
                            row['logistics_company'],       # 物流公司
                            detail['autoid'],               # 明细号
                            detail['material_code'],        # 物料编码
                            detail['material_name'],        # 物料名称
                            detail['unit'],                 # 单位
                            detail['batch_no'],             # 批号
                            detail['warehouse'],            # 仓库
                            detail['reject_reason'],        # 拒收原因
                            detail['delivery_quantity'],    # 发货数量
                            detail['confirmed_quantity'],   # 确认数量
                            detail['delivery_quantity'] - detail['confirmed_quantity'], # 退货数量
                            detail['remaining_return_quantity'], # 剩余退货数量
                            detail['unit_price_with_tax'],  # 添加含税单价
                            '开启' if detail['order_status'] == 0 else '关闭'  # 单据状态
                        ])

                QApplication.processEvents()  # 处理事件，保持UI响应

            # 调整列宽
            for column in ws.columns:
                max_length = 0
                column = list(column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                ws.column_dimensions[column[0].column_letter].width = adjusted_width

            # 冻结首行
            ws.freeze_panes = "A2"
            
            # 设置首行样式
            for cell in ws[1]:
                cell.font = Font(name='Calibri', size=9, bold=True)  # 设置标题字体为Calibri 9号粗体
            
            # 设置其他单元格字体
            for row in ws.iter_rows(min_row=2):  # 从2行开始
                for cell in row:
                    cell.font = Font(name='Calibri', size=9)  # 设置内容字体为Calibri 9号

            # 完成进度
            progress_dialog.setValue(len(results))
            
            # 保存文件
            wb.save(file_name)
            QMessageBox.information(self, "成功", "数据导出成功！")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败：{str(e)}")
        finally:
            if 'progress_dialog' in locals():
                progress_dialog.close()

    def batch_close_orders(self):
        """批量关闭选中的订单"""
        try:
            # 获取选中的行
            selected_rows = set(item.row() for item in self.table.selectedItems())
            if not selected_rows:
                QMessageBox.warning(self, "提示", "请先选择需要关闭的订单！")
                return
            
            # 调试信息
            print(f"选中的行数: {len(selected_rows)}")
            print(f"选中的行: {selected_rows}")
            
            # 检查是否启用了复选框列（根据表头判断）
            has_checkbox = False
            if self.table.columnCount() > 0 and self.table.horizontalHeaderItem(0):
                has_checkbox = "选择" in self.table.horizontalHeaderItem(0).text()
            
            order_no_col = 1 if has_checkbox else 0  # 如果有复选框列，单据编号在第2列；否则在第1列
            
            # 收集选中行的单据编号
            order_nos = []
            for row in selected_rows:
                # 确保行索引有效
                if row < 0 or row >= self.table.rowCount():
                    continue
                    
                # 获取单据编号
                order_no_item = self.table.item(row, order_no_col)
                if order_no_item and order_no_item.text().strip():
                    order_no = order_no_item.text().strip()
                    order_nos.append(order_no)
                    print(f"获取到单据编号: {order_no}")
            
            if not order_nos:
                QMessageBox.warning(self, "提示", "未能获取选中订单的编号！")
                return
            
            # 确认操作
            reply = QMessageBox.question(
                self, 
                "确认操作", 
                f"确定要关闭选中的 {len(order_nos)} 个订单吗？\n此操作不可逆！", 
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
                QMessageBox.StandardButton.No
            )
            
            if reply != QMessageBox.StandardButton.Yes:
                return
                
            try:
                # 显示进度对话框
                progress = QProgressDialog("正在批量关闭订单...", "取消", 0, 100, self)
                progress.setWindowModality(Qt.WindowModality.WindowModal)
                progress.setMinimumDuration(0)
                progress.setValue(0)
                progress.show()
                QApplication.processEvents()
                
                # 定义进度回调函数
                def update_progress(value):
                    if progress.wasCanceled():
                        return False
                    progress.setValue(value)
                    QApplication.processEvents()
                    return True
                
                # 调用数据库方法批量关闭订单
                print(f"传递给数据库的订单列表: {order_nos}")
                updated_count = self.return_db.batch_close_orders(order_nos, update_progress)
                    
                # 处理取消情况
                if progress.wasCanceled():
                    QMessageBox.information(self, "已取消", "批量关闭操作已取消")
                    return
                    
                # 显示结果
                if updated_count > 0:
                    QMessageBox.information(self, "成功", f"成功关闭 {updated_count} 个订单！")
                    # 刷新表格数据
                    self.search_orders()
                else:
                    QMessageBox.information(self, "提示", "没有订单需要关闭或所选订单已全部关闭！")
                    
            except Exception as e:
                QMessageBox.critical(self, "错误", f"批量关闭订单失败：{str(e)}")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"选择订单失败：{str(e)}")
            print(f"批量关闭订单异常: {str(e)}")  # 添加异常日志

class HandoverOrderTab(QWidget):
    def __init__(self, return_db):
        super().__init__()
        self.return_db = return_db
        self.current_page = 1
        self.page_size = 20
        self.total_records = 0
        self.init_ui()
        self.load_logistics_companies()
        self.search_orders()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        # 第一行搜索条件
        first_search_layout = QHBoxLayout()
        first_search_layout.setSpacing(10)
        
        # 搜索类型和输入框
        first_search_layout.addWidget(QLabel("搜索条件:"))
        self.search_type_combo = QComboBox()
        self.search_type_combo.setStyleSheet("")  # 移除样式
        self.search_type_combo.addItems(["原始单号", "交接单号", "出库单号"])
        self.search_type_combo.setFixedWidth(100)
        first_search_layout.addWidget(self.search_type_combo)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("请输入搜索内容")
        self.search_input.setMinimumWidth(200)
        self.search_input.returnPressed.connect(self.search_orders)
        first_search_layout.addWidget(self.search_input)
        
        
        # 日期选择
        first_search_layout.addWidget(QLabel("交接日期:"))
        self.start_date = QDateEdit()
        self.end_date = QDateEdit()
        
        for date_edit in [self.start_date, self.end_date]:
            date_edit.setDisplayFormat("yyyy-MM-dd")
            date_edit.setCalendarPopup(True)
            date_edit.setMinimumWidth(150)
            date_edit.setFixedWidth(150)
            date_edit.setStyleSheet("")  # 移除样式
        
        # 设置默认日期为当前日期往前推一个月
        today = date.today()
        one_month_ago = today - timedelta(days=31)  # 往前推30天
        
        self.start_date.setDate(one_month_ago)
        self.end_date.setDate(today)
        
        first_search_layout.addWidget(self.start_date)
        first_search_layout.addWidget(QLabel("至"))
        first_search_layout.addWidget(self.end_date)
        first_search_layout.addStretch()
        layout.addLayout(first_search_layout)
        
        # 第二行搜索条件
        second_search_layout = QHBoxLayout()
        second_search_layout.setSpacing(10)
        
        # 物流公司
        second_search_layout.addWidget(QLabel("物流公司:"))
        self.logistics_combo = QComboBox()
        self.logistics_combo.setStyleSheet("")  # 移除样式
        self.logistics_combo.setMinimumWidth(150)
        second_search_layout.addWidget(self.logistics_combo)
        
        
        # 退货类型
        second_search_layout.addWidget(QLabel("退货类型:"))
        self.return_type_combo = QComboBox()
        self.return_type_combo.setStyleSheet("")  # 移除样式
        self.return_type_combo.setMinimumWidth(150)
        self.return_type_combo.addItems(["全部", "拒收退货", "仓退", "代销退货"])
        second_search_layout.addWidget(self.return_type_combo)
        
        # 打印状态
        second_search_layout.addWidget(QLabel("打印状:"))
        self.print_status_combo = QComboBox()
        self.print_status_combo.setStyleSheet("")  # 移除样式
        self.print_status_combo.setMinimumWidth(150)
        self.print_status_combo.addItems(["全部", "未打印", "已打印"])
        second_search_layout.addWidget(self.print_status_combo)
        
        # 搜索和重置按钮
        self.search_btn = QPushButton("查询")
        self.search_btn.clicked.connect(self.search_orders)
        self.reset_btn = QPushButton("重置")
        self.reset_btn.clicked.connect(self.reset_search)
        
        second_search_layout.addWidget(self.search_btn)
        second_search_layout.addWidget(self.reset_btn)
        second_search_layout.addStretch()
        layout.addLayout(second_search_layout)
        
        # 添加操作栏
        operation_bar = QHBoxLayout()
        operation_bar.setSpacing(10)
        
        # 添加选中统计标签
        self.selection_label = QLabel("已选择: 0 ")
        operation_bar.addWidget(self.selection_label)
        
        # 添加批量打印按钮
        self.batch_print_btn = QPushButton("批量打印")
        self.batch_print_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 5px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
            QPushButton:pressed {
                background-color: #096dd9;
            }
        """)
        self.batch_print_btn.clicked.connect(self.batch_print_handovers)
        operation_bar.addWidget(self.batch_print_btn)
        
        # 添加批量删除按钮
        self.batch_delete_btn = QPushButton("批量删除")
        self.batch_delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4d4f;
                color: white;
                border: none;
                padding: 5px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ff7875;
            }
            QPushButton:pressed {
                background-color: #d9363e;
            }
        """)
        self.batch_delete_btn.clicked.connect(self.batch_delete_handovers)
        operation_bar.addWidget(self.batch_delete_btn)
        
        operation_bar.addStretch()
        layout.addLayout(operation_bar)
        
        # 表格
        self.table = QTableWidget()
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels([
            "交接单号", "交接日期", "出库单号", "原始单号",
            "客户", "物流公司", "打印状态"
        ])
        
        # 启用排序
        self.table.setSortingEnabled(False)
        
        # 连接排序信号
        self.table.horizontalHeader().sectionClicked.connect(self.on_header_clicked)
        
        # 添加排序状态记录
        self.current_sort_column = -1
        self.current_sort_order = Qt.SortOrder.AscendingOrder
        
        # 设置表格属性
        # 设置表格选择行为为整行选择
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        # 设置表格不可编辑
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        # 连接双击事件到显示详情对话框
        self.table.doubleClicked.connect(self.show_detail_dialog)
        # 连接选择变化事件到更新选择计数
        self.table.itemSelectionChanged.connect(self.update_selection_count)
        
        # 设置列宽
        # 获取表格的水平表头
        header = self.table.horizontalHeader()
        # 遍历所有列
        for i in range(self.table.columnCount()):
            # 设置每一列的大小调整模式为自适应内容
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
        
        # 将表格添加到布局中
        layout.addWidget(self.table)
        
        # 添加分页控件
        pagination_layout = QHBoxLayout()
        pagination_layout.setSpacing(10)
        
        # 左侧显示总记录数
        self.total_label = QLabel("总记录数: 0")
        pagination_layout.addWidget(self.total_label)
        
        pagination_layout.addStretch()
        
        # 分页控件
        self.prev_btn = QPushButton("<")
        self.prev_btn.setFixedWidth(30)
        self.prev_btn.clicked.connect(self.prev_page)
        
        self.page_label = QLabel("1")  # 当前页码
        
        self.next_btn = QPushButton(">")
        self.next_btn.setFixedWidth(30)
        self.next_btn.clicked.connect(self.next_page)


        
        pagination_layout.addWidget(self.prev_btn)
        pagination_layout.addWidget(self.page_label)
        pagination_layout.addWidget(self.next_btn)

        # 为搜索相关的控件连接信号
        self.search_type_combo.currentIndexChanged.connect(self.search_orders)
        self.start_date.dateChanged.connect(self.search_orders)
        self.end_date.dateChanged.connect(self.search_orders)
        self.logistics_combo.currentIndexChanged.connect(self.search_orders)
        self.return_type_combo.currentIndexChanged.connect(self.search_orders)
        self.print_status_combo.currentIndexChanged.connect(self.search_orders)
        layout.addLayout(pagination_layout)

    def load_logistics_companies(self):
        """加载物流公司列表"""
        try:
            companies = self.return_db.get_logistics_companies()
            self.logistics_combo.clear()
            self.logistics_combo.addItem("全部物流公司")
            for company in companies:
                if company:  # 确保公司名不为空
                    self.logistics_combo.addItem(str(company))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载物流公司失败：{str(e)}")

    def show_detail_dialog(self, index):
        """显示交接单明细对话框"""
        row = index.row()
        handover_no = self.table.item(row, 0).text()
        
        try:
            # 获取明细数据
            detail_data = self.return_db.get_handover_details(handover_no)
            
            if not detail_data:
                QMessageBox.warning(self, "警告", "未找到相关明细数据！")
                return
            
            # 创建并显示对话框
            dialog = HandoverDetailDialog(self)
            dialog.return_db = self.return_db
            dialog.set_handover_data(detail_data)
            dialog.exec()
            
            # 对话框关闭后刷新数据
            self.search_orders()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示明细失败：{str(e)}")

    def get_search_params(self):
        """获取搜索参数"""
        filters = {
                'search_type': self.search_type_combo.currentText(),
                'search_text': self.search_input.text().strip(),
                'start_date': self.start_date.date().toPyDate(),
                'end_date': self.end_date.date().toPyDate(),
                'logistics_company': self.logistics_combo.currentText() if self.logistics_combo.currentText() != "全部物流公司" else None,
                'return_type': self.get_return_type_value() if self.return_type_combo.currentText() != "全部" else None,
                'print_status': self.print_status_combo.currentText() if self.print_status_combo.currentText() != "全部" else None,
                'page': self.current_page,
                'page_size': self.page_size
        }
        return filters
    def search_orders(self):
        """查询交接单数据"""
        try:
            # 如果是通过搜索按钮或搜索条件变化触发的搜索，重置页码到第一页
            caller = self.sender()
            if caller and caller not in [self.prev_btn, self.next_btn]:
                self.current_page = 1
            
            # 构建过滤条件
            filters = {
                'search_type': self.search_type_combo.currentText(),
                'search_text': self.search_input.text().strip(),
                'start_date': self.start_date.date().toPyDate(),
                'end_date': self.end_date.date().toPyDate(),
                'logistics_company': self.logistics_combo.currentText(),
                'return_type': self.get_return_type_value(),
                'print_status': self.print_status_combo.currentText(),
                'page': self.current_page,
                'page_size': self.page_size
            }
            
            # 获取数据
            self.total_records, results = self.return_db.get_handover_orders(filters)
            
            # 更新总记录数和分页状态
            self.total_label.setText(f"总记录数: {self.total_records}")
            self.update_pagination_status()
            
            # 显示数据
            self.table.setRowCount(0)
            if not results:
                return
                
            self.table.setRowCount(len(results))
            for row, data in enumerate(results):
                try:
                    # 按照表格列顺序设置数据
                    self.table.setItem(row, 0, QTableWidgetItem(str(data['handover_no'])))
                    # 格式化日期显示
                    handover_date = data['handover_date']
                    if isinstance(handover_date, str):
                        # 如果是字符串，尝试转换为日期
                        try:
                            handover_date = datetime.strptime(handover_date, '%Y-%m-%d').date()
                        except ValueError:
                            handover_date = None
                    date_str = handover_date.strftime('%Y-%m-%d') if handover_date else ''
                    self.table.setItem(row, 1, QTableWidgetItem(date_str))
                    
                    self.table.setItem(row, 2, QTableWidgetItem(str(data['delivery_order_no'] or '')))
                    self.table.setItem(row, 3, QTableWidgetItem(str(data['original_order_no'] or '')))
                    self.table.setItem(row, 4, QTableWidgetItem(str(data['customer'] or '')))
                    self.table.setItem(row, 5, QTableWidgetItem(str(data['logistics_company'] or '')))
                    self.table.setItem(row, 6, QTableWidgetItem(str(data['print_status_text'])))
                    
                    # 设置单元格居中对齐
                    for col in range(self.table.columnCount()):
                        item = self.table.item(row, col)
                        if item:
                            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                            
                except Exception as e:
                    print(f"创建第 {row} 行时出错: {str(e)}")
              # 重置选择计数
            self.update_selection_count()         
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询失败：{str(e)}")

    def reset_search(self):
        """重置搜索条件"""
        self.search_type_combo.setCurrentText("原始单号")
        self.search_input.clear()
        self.logistics_combo.setCurrentText("全部物流公司")
        self.return_type_combo.setCurrentText("全部")
        self.print_status_combo.setCurrentText("全部")  # 重置打印状态
        
        # 重置日期为当月第一天和最后一天
        today = date.today()
        one_month_ago = today - timedelta(days=31)  # 往前推30天
        self.start_date.setDate(one_month_ago)
        self.end_date.setDate(today)
        
        # 重置分页
        self.current_page = 1
        

        # 执行搜索
        self.search_orders()

    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.search_orders()

    def next_page(self):
        """下一页"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        if self.current_page < total_pages:
            self.current_page += 1
            self.search_orders()

    def update_pagination_status(self):
        """更新分页状态"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        self.page_label.setText(f"{self.current_page}/{total_pages}")
        self.prev_btn.setEnabled(self.current_page > 1)
        self.next_btn.setEnabled(self.current_page < total_pages)

    def get_return_type_value(self):
        """获取退货类型值"""
        return_type_text = self.return_type_combo.currentText()
        if return_type_text == "全部":
            return None
        return {
            "拒收退货": 0,
            "仓退": 1,
            "代销退货": 2
        }.get(return_type_text)
    def update_selection_count(self):
        """更新选中行数显示"""
        selected_rows = len(set(item.row() for item in self.table.selectedItems()))
        self.selection_label.setText(f"已选择: {selected_rows} 行")

    def batch_print_handovers(self):
        """批量打印选中的交接单"""
        try:
            # 获取选中的行
            selected_rows = self.table.selectedItems()
            selected_handovers = []
            processed_rows = set()
            
            for item in selected_rows:
                row = item.row()
                if row not in processed_rows:
                    handover_no = self.table.item(row, 0).text()
                    selected_handovers.append(handover_no)
                    processed_rows.add(row)
            
            if not selected_handovers:
                QMessageBox.warning(self, "警告", "请先选择需要打印的交接单！")
                return
            
            reply = QMessageBox.question(
                self,
                '确认',
            f'是否确认打印选中的 {len(selected_handovers)} 个交接单？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 创建进度对话框
                progress_dialog = QProgressDialog("正在打印...", "取消", 0, len(selected_handovers), self)
                progress_dialog.setWindowTitle("打印进度")
                progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
                progress_dialog.setMinimumDuration(500)  # 立即显示
                
                success_count = 0
                failed_handovers = []
                
                for i, handover_no in enumerate(selected_handovers):
                    try:
                        # 更新进度
                        progress_dialog.setValue(i)
                        progress_dialog.setLabelText(f"正在打印 {i+1}/{len(selected_handovers)}\n交接单号: {handover_no}")
                    
                        # 检查是否取消
                        if progress_dialog.wasCanceled():
                            break
                    
                        # 获取数据并打印
                        detail_data = self.return_db.get_handover_details(handover_no)
                        if not detail_data:
                            failed_handovers.append(f"{handover_no}(未找到数据)")
                            continue
                        
                        preview_dialog = PrintPreviewDialog(detail_data, self)
                        preview_dialog.setFixedSize(1024, 768)
                        preview_dialog.setup_ui()
                        from PyQt6.QtCore import QCoreApplication
                        QCoreApplication.processEvents()
                        preview_dialog.preview_widget.setFixedSize(1000, 714)  # 与预览窗口打印时的尺寸一致
                        preview_dialog.direct_print(show_success_message=False)
                        success_count += 1
                    
                        # 短暂延时，让UI有机会更新
                        QApplication.processEvents()
                    
                    except Exception as e:
                            failed_handovers.append(f"{handover_no}({str(e)})")
            
                # 完成进度
                progress_dialog.setValue(len(selected_handovers))
            
                # 显示处理结果
                message = f"处理完成！\n成功打印: {success_count} 个"
                if failed_handovers:
                    message += f"\n失败: {len(failed_handovers)} 个"
                    message += "\n失败单号:\n" + "\n".join(failed_handovers)
            else:
                return
            QMessageBox.information(self, "处理结果", message)
            
            # 刷新数据
            self.search_orders()
       
        except Exception as e:
            QMessageBox.critical(self, "错误", f"批量打印失败：{str(e)}")

    def on_header_clicked(self, logical_index):
        """处理表头点击排序"""
        if self.current_sort_column == logical_index:
            self.current_sort_order = Qt.SortOrder.DescendingOrder if self.current_sort_order == Qt.SortOrder.AscendingOrder else Qt.SortOrder.AscendingOrder
        else:
            self.current_sort_column = logical_index
            self.current_sort_order = Qt.SortOrder.AscendingOrder
        
        self.table.sortItems(logical_index, self.current_sort_order)

    def batch_delete_handovers(self):
        """批量删除选中的交接单"""
        try:
            # 获取选中的行
            selected_rows = self.table.selectedItems()
            selected_handovers = []
            processed_rows = set()
            
            for item in selected_rows:
                row = item.row()
                if row not in processed_rows:
                    handover_no = self.table.item(row, 0).text()
                    selected_handovers.append(handover_no)
                    processed_rows.add(row)
            
            if not selected_handovers:
                QMessageBox.warning(self, "警告", "请先选择需要删除的交接单！")
                return
            
            reply = QMessageBox.question(
                self,
                '确认删除',
                f'是否确认删除选中的 {len(selected_handovers)} 个交接单？\n此操作不可恢复！',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 创建进度对话框
                progress_dialog = QProgressDialog("正在删除...", "取消", 0, len(selected_handovers), self)
                progress_dialog.setWindowTitle("删除进度")
                progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
                progress_dialog.setMinimumDuration(0)
                progress_dialog.show()
                
                success_count = 0
                failed_handovers = []
                
                for i, handover_no in enumerate(selected_handovers):
                    try:
                        # 更新进度
                        progress_dialog.setValue(i)
                        progress_dialog.setLabelText(f"正在删除 {i+1}/{len(selected_handovers)}\n交接单号: {handover_no}")
                        
                        # 检查是否取消
                        if progress_dialog.wasCanceled():
                            break
                        
                        # 调用数据库删除方法
                        if self.return_db.delete_handover_order(handover_no):
                            success_count += 1
                        else:
                            failed_handovers.append(f"{handover_no}(删除失败)")
                        
                    except Exception as e:
                        failed_handovers.append(f"{handover_no}({str(e)})")
                    
                    # 处理事件，保持UI响应
                    QApplication.processEvents()
                
                # 完成进度
                progress_dialog.setValue(len(selected_handovers))
                
                # 显示处理结果
                message = f"处理完成！\n成功删除: {success_count} 个"
                if failed_handovers:
                    message += f"\n失败: {len(failed_handovers)} 个"
                    message += "\n失败单号:\n" + "\n".join(failed_handovers)
                
                QMessageBox.information(self, "处理结果", message)
               
                # 刷新数据
                self.search_orders()
            else:
                    return    
        except Exception as e:
            QMessageBox.critical(self, "错误", f"批量删除失败：{str(e)}")


class ReturnWarehouseTab(QWidget):
    def __init__(self, return_db):
        super().__init__()
        self.return_db = return_db
        self.current_page = 1
        self.page_size = 20
        self.total_records = 0
        self.init_ui()
        self.load_logistics_companies()
        self.search_orders()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 第一行搜索条件
        first_search_layout = QHBoxLayout()
        first_search_layout.setSpacing(10)
        
        # 搜索类型和输入框
        first_search_layout.addWidget(QLabel("搜索条件:"))
        self.search_type_combo = QComboBox()
        self.search_type_combo.setStyleSheet("")  # 移除样式
        self.search_type_combo.addItems(["原始单号", "交接单号", "出库单号"])
        self.search_type_combo.setFixedWidth(100)
        first_search_layout.addWidget(self.search_type_combo)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("请输入搜索内容")
        self.search_input.setMinimumWidth(200)
        self.search_input.returnPressed.connect(self.search_orders)
        first_search_layout.addWidget(self.search_input)
        
        # 日期选择
        first_search_layout.addWidget(QLabel("审核日期:"))
        self.start_date = QDateEdit()
        self.end_date = QDateEdit()
        
        for date_edit in [self.start_date, self.end_date]:
            date_edit.setDisplayFormat("yyyy-MM-dd")
            date_edit.setCalendarPopup(True)
            date_edit.setMinimumWidth(150)
            date_edit.setFixedWidth(150)
            date_edit.setStyleSheet("")  # 移除样式
        
        # 设置默认日期为当前日期往前推一个月
        today = date.today()
        one_month_ago = today - timedelta(days=31)  # 往前推30天
        
        self.start_date.setDate(one_month_ago)
        self.end_date.setDate(today)
        
        first_search_layout.addWidget(self.start_date)
        first_search_layout.addWidget(QLabel("至"))
        first_search_layout.addWidget(self.end_date)
        first_search_layout.addStretch()
        layout.addLayout(first_search_layout)
        
        # 第二行搜索条件
        second_search_layout = QHBoxLayout()
        second_search_layout.setSpacing(10)
        
        
        # 物流公司
        second_search_layout.addWidget(QLabel("物流公司:"))
        self.logistics_combo = QComboBox()
        self.logistics_combo.setStyleSheet("")  # 移除样式
        self.logistics_combo.setMinimumWidth(150)
        second_search_layout.addWidget(self.logistics_combo)
        
        # 退货类型
        second_search_layout.addWidget(QLabel("退货类型:"))
        self.return_type_combo = QComboBox()
        self.return_type_combo.setStyleSheet("")  # 移除样式
        self.return_type_combo.setMinimumWidth(150)
        self.return_type_combo.addItems(["全部", "拒收退货", "仓退", "代销退货"])
        second_search_layout.addWidget(self.return_type_combo)
        
        #单据状态
        second_search_layout.addWidget(QLabel("单据状态:"))
        self.status_combo = QComboBox()
        self.status_combo.setStyleSheet("")  # 移除样式
        self.status_combo.setMinimumWidth(150)
        self.status_combo.addItems(["全部", "已审核", "已作废"])
        second_search_layout.addWidget(self.status_combo)
        
        # 查询和重置按钮
        self.search_btn = QPushButton("查询")
        self.search_btn.clicked.connect(self.search_orders)
        self.reset_btn = QPushButton("重置")
        self.reset_btn.clicked.connect(self.reset_search)

        # 导出按钮
        self.export_btn = QPushButton("导出")
        self.export_btn.clicked.connect(self.export_data)
        second_search_layout.addWidget(self.export_btn)
        
        second_search_layout.addWidget(self.search_btn)
        second_search_layout.addWidget(self.reset_btn)
        second_search_layout.addStretch()
        layout.addLayout(second_search_layout)
        
        # 表格
        self.table = QTableWidget()
        self.table.setColumnCount(9)  # 增加一列用于显示状态
        self.table.setHorizontalHeaderLabels([
            "交接单号", "审核日期", "出库单号", "原始单号", 
            "客户", "部门", "物流公司", "退货类型", "状态"  # 添加状态列
        ])
        
        # 启用排序
        self.table.setSortingEnabled(True)
        
        # 连接排序信号
        self.table.horizontalHeader().sectionClicked.connect(self.on_header_clicked)
        
        # 添加排序状态记录
        self.current_sort_column = -1
        self.current_sort_order = Qt.SortOrder.AscendingOrder
        
        # 设置表格属性
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.table.doubleClicked.connect(self.show_return_warehouse_dialog)
        
        # 设置列宽
        header = self.table.horizontalHeader()
        for i in range(self.table.columnCount()):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.table)
        
        # 添加分页控件
        pagination_layout = QHBoxLayout()
        pagination_layout.setSpacing(10)
        
        # 左侧显示总记录数
        self.total_label = QLabel("总记录数: 0")
        pagination_layout.addWidget(self.total_label)
        
        pagination_layout.addStretch()
        
        # 分页控件
        self.prev_btn = QPushButton("<")
        self.prev_btn.setFixedWidth(30)
        self.prev_btn.clicked.connect(self.prev_page)
        
        self.page_label = QLabel("1")  # 当前页码
        
        self.next_btn = QPushButton(">")
        self.next_btn.setFixedWidth(30)
        self.next_btn.clicked.connect(self.next_page)


        
        pagination_layout.addWidget(self.prev_btn)
        pagination_layout.addWidget(self.page_label)
        pagination_layout.addWidget(self.next_btn)

        # 为搜索相关的控件连接信号
        self.start_date.dateChanged.connect(self.search_orders)
        self.end_date.dateChanged.connect(self.search_orders)
        self.logistics_combo.currentIndexChanged.connect(self.search_orders)
        self.return_type_combo.currentIndexChanged.connect(self.search_orders)
        self.search_type_combo.currentIndexChanged.connect(self.search_orders)
        self.status_combo.currentIndexChanged.connect(self.search_orders)  # 添加这行，连接状态下拉框的信号
        layout.addLayout(pagination_layout)

    def load_logistics_companies(self):
        """加载物流公司列表"""
        try:
            companies = self.return_db.get_logistics_companies()
            self.logistics_combo.clear()
            self.logistics_combo.addItem("全部物流公司")
            for company in companies:
                if company:  # 确保公司名不为空
                    self.logistics_combo.addItem(str(company))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载物流公司失败：{str(e)}")
    def get_search_params(self):
        """获取搜索参数"""
        # 状态映射
        status_map = {
            "已审核": 1,
            "已作废": 2,
            "全部": None
        }
        
        filters = {
            'search_type': self.search_type_combo.currentText(),
            'search_text': self.search_input.text().strip(),
            'start_date': self.start_date.date().toPyDate(),
            'end_date': self.end_date.date().toPyDate(),
            'logistics_company': self.logistics_combo.currentText() if self.logistics_combo.currentText() != "全部物流公司" else None,  
            'return_type': self.get_return_type_value() if self.return_type_combo.currentText() != "全部" else None,
            'status': status_map.get(self.status_combo.currentText()),  # 转换状态文本为数值
            'page': self.current_page,
            'page_size': self.page_size
        }
        return filters
    def search_orders(self):
        """查询退货入库单数据"""
        try:
            # 获取筛选条件
            filters = self.get_search_params()
            
            # 执行查询
            total, results = self.return_db.get_return_warehouse_orders(filters)
            
            # 更新表格数据
            self.table.setRowCount(len(results))
            for row, data in enumerate(results):
                self.table.setItem(row, 0, QTableWidgetItem(str(data['handover_no'])))
                self.table.setItem(row, 1, QTableWidgetItem(str(data['audit_time'])))
                self.table.setItem(row, 2, QTableWidgetItem(str(data['delivery_order_no'])))
                self.table.setItem(row, 3, QTableWidgetItem(str(data['original_order_no'])))
                self.table.setItem(row, 4, QTableWidgetItem(str(data['customer'] or '')))
                self.table.setItem(row, 5, QTableWidgetItem(str(data['department'] or '')))
                self.table.setItem(row, 6, QTableWidgetItem(str(data['logistics_company'] or '')))
                self.table.setItem(row, 7, QTableWidgetItem(str(data['return_type_text'])))
                self.table.setItem(row, 8, QTableWidgetItem(str(data['status_text'])))  # 显示状态文本
                
                # 设置状态列的颜色
                status_item = self.table.item(row, 8)
                if data['handover_status'] == 2:  # 作废状态
                    status_item.setForeground(QColor("#ff4d4f"))  # 红色
                elif data['handover_status'] == 1:  # 已审核状态
                    status_item.setForeground(QColor("#52c41a"))  # 绿色
            
            # 更新总记录数和分页
            self.total_records = total
            self.total_label.setText(f"总记录数: {total}")
            self.update_pagination_status()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询失败：{str(e)}")

    def reset_search(self):
        """重置搜索条件"""
        self.search_type_combo.setCurrentText("原始单号")
        self.search_input.clear()
        self.logistics_combo.setCurrentText("全部物流公司")
        self.return_type_combo.setCurrentText("全部")
        self.status_combo.setCurrentText("全部")  # 添加这行，重置状态为全部
        
        # 重置日期为当月第一天和最后一天
        today = date.today()
        one_month_ago = today - timedelta(days=31)  # 往前推30天
        self.start_date.setDate(one_month_ago)
        self.end_date.setDate(today)
        
        # 重置分页
        self.current_page = 1
        

        # 执行搜索
        self.search_orders()

    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.search_orders()

    def next_page(self):
        """下一页"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        if self.current_page < total_pages:
            self.current_page += 1
            self.search_orders()

    def update_pagination_status(self):
        """更新分页状态"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        self.page_label.setText(f"{self.current_page}/{total_pages}")
        self.prev_btn.setEnabled(self.current_page > 1)
        self.next_btn.setEnabled(self.current_page < total_pages)

    def show_return_warehouse_dialog(self, index):
        """显示退货入库明细对话框"""
        try:
            row = index.row()
            handover_no = self.table.item(row, 0).text()
        
            # 获取明细数据
            detail_data = self.return_db.get_handover_details(handover_no)
        
            if not detail_data:
                QMessageBox.warning(self, "警告", "未找到相关明细数据！")
                return
        
            # 创建并显示明细对话框
            dialog = HandoverDetailDialog(self)
            dialog.return_db = self.return_db
            dialog.set_handover_data(detail_data)
            dialog.exec()
            self.search_orders()
            
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示明细失败：{str(e)}")
    def get_return_type_value(self):
        """获取当前选择的退货类型值"""
        return_type_text = self.return_type_combo.currentText()
        if return_type_text == "全部":
            return None
            
        return_type_map = {
            "拒收退货": 0,
            "仓退": 1,
            "代销退货": 2
        }
        return return_type_map.get(return_type_text)
            
    def export_data(self):
        """导出退货入库数据"""
        try:
            status_map = {
                "已审核": 1,
                "已作废": 2,
                "全部": None
            }
            # 获取当前筛选条件下的所有数据（不分页）
            filters = {
                'search_type': self.search_type_combo.currentText(),
                'search_text': self.search_input.text().strip(),
                'start_date': self.start_date.date().toPyDate(),
                'end_date': self.end_date.date().toPyDate(),
                'logistics_company': self.logistics_combo.currentText(),
                'return_type': self.get_return_type_value(),
                'status': status_map.get(self.status_combo.currentText()),
                'export_all': True  # 标记导出所有数据
            }
            
            # 获取数据
            _, results = self.return_db.get_return_warehouse_orders(filters)
            
            if not results:
                QMessageBox.warning(self, "警告", "没有数据可供导出！")
                return
            
            # 让用户选择保存位置
            file_name, _ = QFileDialog.getSaveFileName(
                self,
                "导出数据",
                f"退货入库单_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )
            
            if not file_name:
                return

            # 创建进度对话框
            progress_dialog = QProgressDialog("正在导出数据...", "取消", 0, len(results), self)
            progress_dialog.setWindowTitle("导出进度")
            progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
            progress_dialog.setMinimumDuration(0)  # 立即显示
            progress_dialog.show()
                
            # 创建工作簿
            wb = Workbook()
            
            # 创建主表工作表
            ws = wb.active
            ws.title = "退货入库单"
            
            # 设置主表表头
            headers = [
                "交接单号", "审核日期", "出库单号", "原始单号", "客户", "部门","物流公司", "退货类型",
                "明细号", "物料编码", "物料名称", "单位", "批号", "仓库", 
                "拒收原因", "退货数量", "收货数量", "退货入库单号", "库存状态"
            ]
            ws.append(headers)
            
            # 写入数据
            for i, row in enumerate(results):
                # 更新进度
                progress_dialog.setValue(i)
                progress_dialog.setLabelText(f"正在处理第 {i+1}/{len(results)} 条数据")
                
                # 检查是否取消
                if progress_dialog.wasCanceled():
                    wb.close()
                    return
                
                # 获取明细数据
                details = self.return_db.get_handover_details(row['handover_no'])
                if details and 'details' in details:
                    for detail in details['details']:
                        # 直接使用数据库返回的stock_status_text
                        stock_status = detail.get('stock_status_text', '')
                        
                        # 合并主表和明细数据
                        ws.append([
                            row['handover_no'],                # 交接单号
                            str(row['audit_time']),           # 审核日期
                            row['delivery_order_no'],         # 出库单号
                            row['source_order_no'],           # 原始单号
                            row['customer'],                  # 客户
                            row['department'],                # 部门
                            row['logistics_company'],         # 物流公司
                            row['return_type_text'],          # 退货类型
                            detail['delivery_autoid'],        # 明细号
                            detail['material_code'],          # 物料编码
                            detail['material_name'],          # 物料名称
                            detail['unit'],                   # 单位
                            detail['batch_no'],               # 批号
                            detail['warehouse'],              # 仓库
                            detail['reject_reason'],          # 拒收原因
                            detail['return_quantity'],        # 退货数量
                            detail['received_quantity'],      # 收货数量
                            detail['return_warehouse_no'],    # 退货入库单号
                            stock_status                      # 库存状态（使用数据库返回的中文状态）
                        ])

                QApplication.processEvents()  # 处理事件，保持UI响应

            # 调整列宽
            for column in ws.columns:
                max_length = 0
                column = list(column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                ws.column_dimensions[column[0].column_letter].width = adjusted_width

            # 冻结首行
            ws.freeze_panes = "A2"
            
            # 设置首行样式
            for cell in ws[1]:
                cell.font = Font(name='Calibri', size=9, bold=True)  # 设置标题字体为Calibri 9号粗体
            
            # 设置其他单元格字体
            for row in ws.iter_rows(min_row=2):  # 从2行开始
                for cell in row:
                    cell.font = Font(name='Calibri', size=9)  # 设置内容字体为Calibri 9号

            # 完成进度
            progress_dialog.setValue(len(results))
            
            # 保存文件
            wb.save(file_name)
            QMessageBox.information(self, "成功", "数据导出成功！")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败：{str(e)}")
        finally:
            if 'progress_dialog' in locals():
                progress_dialog.close()

    def on_header_clicked(self, logical_index):
        """处理表头点击排序"""
        # 更新排序状态
        if self.current_sort_column == logical_index:
            # 如果点击同一列,切换排序顺序
            self.current_sort_order = Qt.SortOrder.DescendingOrder if self.current_sort_order == Qt.SortOrder.AscendingOrder else Qt.SortOrder.AscendingOrder
        else:
            # 如果点击不同列,设置为升序
            self.current_sort_column = logical_index
            self.current_sort_order = Qt.SortOrder.AscendingOrder
        
        # 执行排序
        self.table.sortItems(logical_index, self.current_sort_order)



class LogTab(QWidget):
    def __init__(self, return_db):
        super().__init__()
        self.return_db = return_db
        self.current_page = 1
        self.page_size = 20
        self.total_records = 0
        self.init_ui()
        self.search_logs()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 搜索条件区域
        search_layout = QHBoxLayout()
        
        # 日期选择
        search_layout.addWidget(QLabel("操作日期:"))
        self.start_date = QDateEdit()
        self.end_date = QDateEdit()
        
        for date_edit in [self.start_date, self.end_date]:
            date_edit.setDisplayFormat("yyyy-MM-dd")
            date_edit.setCalendarPopup(True)
            date_edit.setMinimumWidth(120)
            date_edit.setFixedWidth(150)
            date_edit.setStyleSheet("")  # 移除样式
        
        # 设置默认日期为当前日期往前推一个月
        today = date.today()
        one_month_ago = today - timedelta(days=31)  # 往前推30天
        
        self.start_date.setDate(one_month_ago)
        self.end_date.setDate(today)
        
        search_layout.addWidget(self.start_date)
        search_layout.addWidget(QLabel("至"))
        search_layout.addWidget(self.end_date)
        
        # 操作类型
        search_layout.addWidget(QLabel("操作类型:"))
        self.operation_combo = QComboBox()
        self.operation_combo.addItems(["全部", "新增", "修改", "删除", "审核", "反审核", "打印","作废","拆分","删除明细"])
        search_layout.addWidget(self.operation_combo)
        
        # 搜索按钮
        self.search_btn = QPushButton("查询")
        self.search_btn.clicked.connect(self.search_logs)
        search_layout.addWidget(self.search_btn)
        
        # 重置按钮
        self.reset_btn = QPushButton("重置")
        self.reset_btn.clicked.connect(self.reset_search)
        search_layout.addWidget(self.reset_btn)
        
        # 添加清空日志按钮
        self.clear_btn = QPushButton("清空日志")
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4d4f;
                color: white;
                border: none;
                padding: 5px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ff7875;
            }
            QPushButton:pressed {
                background-color: #d9363e;
            }
        """)
        self.clear_btn.clicked.connect(self.clear_logs)
        search_layout.addWidget(self.clear_btn)
        
        search_layout.addStretch()
        # 为搜索相关的控件连接信号
        self.operation_combo.currentTextChanged.connect(self.search_logs)
        self.start_date.dateChanged.connect(self.search_logs)
        self.end_date.dateChanged.connect(self.search_logs)
        
        layout.addLayout(search_layout)
        
        # 日志表格
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "操作时间", "操作类型", "操作人", "单据编号", 
            "操作内容", "IP地址"
        ])
        
        # 设置表格属性
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 设置列宽
        header = self.table.horizontalHeader()
        for i in range(self.table.columnCount()):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.table)
        
        # 分页控件
        pagination_layout = QHBoxLayout()
        self.total_label = QLabel("总记录数: 0")
        self.prev_btn = QPushButton("<")
        self.next_btn = QPushButton(">")
        self.page_label = QLabel("1/1")
        
        self.prev_btn.clicked.connect(self.prev_page)
        self.next_btn.clicked.connect(self.next_page)
        
        pagination_layout.addWidget(self.total_label)
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.prev_btn)
        pagination_layout.addWidget(self.page_label)
        pagination_layout.addWidget(self.next_btn)
        self.layout().addLayout(pagination_layout)
        
    def get_search_params(self):
        """获取搜索参数"""
        return {
            'start_date': self.start_date.date().toPyDate(),
            'end_date': self.end_date.date().toPyDate(),
            'operation_type': self.operation_combo.currentText() if self.operation_combo.currentText() != "全部" else None,
            'page': self.current_page,
            'page_size': self.page_size
        }
        
    def search_logs(self):
        """查询日志数据"""
        try:
            # 如果是通过搜索按钮或搜索条件变化触发的搜索，重置页码到第一页
            caller = self.sender()
            if caller and caller not in [self.prev_btn, self.next_btn]:
                self.current_page = 1
            
            filters = {
                'start_date': self.start_date.date().toPyDate(),
                'end_date': self.end_date.date().toPyDate(),
                'operation_type': self.operation_combo.currentText(),
                'page': self.current_page,
                'page_size': self.page_size
            }
            
            self.total_records, results = self.return_db.get_operation_logs(filters)
            
            # 更新总记录数和分页状态
            self.total_label.setText(f"总记录数: {self.total_records}")
            self.update_pagination_status()
            
            # 显示数据
            self.table.setRowCount(0)
            if not results:
                return
                
            self.table.setRowCount(len(results))
            for row, data in enumerate(results):
                self.table.setItem(row, 0, QTableWidgetItem(str(data['operation_time'])))  # 添加右括号
                self.table.setItem(row, 1, QTableWidgetItem(str(data['operation_type'])))  # 添加右括号
                self.table.setItem(row, 2, QTableWidgetItem(str(data['operator'])))  # 添加右括号
                self.table.setItem(row, 3, QTableWidgetItem(str(data['document_no'])))  # 添加右括号
                self.table.setItem(row, 4, QTableWidgetItem(str(data['operation_content'])))  # 添加右括号
                self.table.setItem(row, 5, QTableWidgetItem(str(data['ip_address'])))  # 添加右括号
                
                # 设置单元格居中对齐
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    if item:
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询失败：{str(e)}")

    def reset_search(self):
        """重置搜索条件"""
        # 重置日期为当月
        today = date.today()
        one_month_ago = today - timedelta(days=31)  # 往前推30天
        self.start_date.setDate(one_month_ago)
        self.end_date.setDate(today)
        
        # 重置操作类型
        self.operation_combo.setCurrentText("全部")
        
        # 重置分页
        self.current_page = 1
        
        # 执行搜索
        self.search_logs()

    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.search_logs()

    def next_page(self):
        """下一页"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        if self.current_page < total_pages:
            self.current_page += 1
            self.search_logs()

    def update_pagination_status(self):
        """更新分页状态"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        self.page_label.setText(f"{self.current_page}/{total_pages}")
        self.prev_btn.setEnabled(self.current_page > 1)
        self.next_btn.setEnabled(self.current_page < total_pages)

    def clear_logs(self):
        """清空日志"""
        try:
            # 获取当前筛选条件
            filters = {
                'start_date': self.start_date.date().toPyDate(),
                'end_date': self.end_date.date().toPyDate(),
                'operation_type': self.operation_combo.currentText()
            }
            
            # 确认对话
            reply = QMessageBox.question(
                self,
                '确认清空',
                '是否确认清空符合当前筛选条件的日志记录？\n此操作不可恢复！',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 调用数据库清空方法
                count = self.return_db.clear_operation_logs(filters)
                
                if count > 0:
                    QMessageBox.information(
                        self, 
                        "成功", 
                        f"成功清空 {count} 条日志记录！"
                    )
                    # 刷新数据
                    self.search_logs()
                else:
                    QMessageBox.information(
                        self, 
                        "提示", 
                        "没有符合条件的日志记录需要清空！"
                    )
                    
        except Exception as e:
            QMessageBox.critical(self, "错误", f"清空日志失败：{str(e)}")

    def get_search_params(self):
        """获取搜索参数"""
        return {
            'start_date': self.start_date.date().toPyDate(),
            'end_date': self.end_date.date().toPyDate(),
            'operation_type': None if self.operation_combo.currentText() == "全部" else self.operation_combo.currentText(),
            'page': self.current_page,
            'page_size': self.page_size
        }
        
class ReturnModule(QWidget):
    def __init__(self, return_db):
        super().__init__()
        self.return_db = return_db
        self.init_ui()
        self.setup_style()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        self.tab_widget.setMovable(False)
        self.tab_widget.setDocumentMode(True)
        
        # 创建所有标签页实例
        self.delivery_tab = DeliveryOrderTab(self.return_db)
        self.handover_tab = HandoverOrderTab(self.return_db)
        self.return_warehouse_tab = ReturnWarehouseTab(self.return_db)
        self.log_tab = LogTab(self.return_db)
        
        # 只添加第一个标签页
        self.tab_widget.addTab(self.delivery_tab, "📦 出库单列表")
        
        # 延迟加载其他标签页
        self._tabs_loaded = {
            "delivery": True,  # 第一个标签页已加载
            "handover": False,
            "warehouse": False,
            "log": False
        }
        
        # 连接标签页切换信号
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
        layout.addWidget(self.tab_widget)
        
        # 只加载第一个标签页的数据
        self.delivery_tab.load_logistics_companies()
        self.delivery_tab.search_orders()
    
    def on_tab_changed(self, index):
        """签页切换时按需加载数据"""
        try:
            current_tab = self.tab_widget.widget(index)
            tab_name = self.tab_widget.tabText(index)
            
            # 检查标签页是否需要加载
            if tab_name == "📝 交接单列表" and not self._tabs_loaded["handover"]:
                self.tab_widget.addTab(self.handover_tab, "📝 交接单列表")
                self.handover_tab.load_logistics_companies()
                self._tabs_loaded["handover"] = True
                self.handover_tab.search_orders
                
            elif tab_name == "🔄 退货入库列表" and not self._tabs_loaded["warehouse"]:
                self.tab_widget.addTab(self.return_warehouse_tab, "🔄 退货入库列表")
                self.return_warehouse_tab.load_logistics_companies()
                self._tabs_loaded["warehouse"] = True
                self.return_warehouse_tab.search_orders
                
            elif tab_name == "📜 操作日志" and not self._tabs_loaded["log"]:
                self.tab_widget.addTab(self.log_tab, "📜 操作日志")
                self._tabs_loaded["log"] = True
                self.log_tab.search_logs
                
            
            # 刷新当前标签页数据
            if hasattr(current_tab, 'search_orders'):
                current_tab.search_orders()
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"刷新数据失败：{str(e)}")
    def setup_style(self):
        """设置样式表"""
          
        pass
