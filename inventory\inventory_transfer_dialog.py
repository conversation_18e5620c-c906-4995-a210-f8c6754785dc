"""
库存调拨对话框
用于在不同部门之间调拨库存
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                           QComboBox, QFormLayout, QDialogButtonBox, QDoubleSpinBox,
                           QDateEdit, QTextEdit, QMessageBox, QPushButton, QFrame,
                           QGroupBox, QGridLayout, QWidget, QTableWidget, QTableWidgetItem,
                           QHeaderView)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QIcon, QColor
from datetime import datetime, date

from core.logger import Logger
from core.config import get_current_user
from inventory.inventory_db import InventoryDB
from inventory.inventory_permission_service import InventoryPermissionService


class InventoryTransferDialog(QDialog):
    """库存调拨对话框"""
    
    data_saved = pyqtSignal()  # 数据保存成功信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.inventory_db = InventoryDB()
        self.permission_service = InventoryPermissionService()
        self.current_user = get_current_user()
        self.selected_material = None  # 存储选中的物料信息
        self.available_inventory = []  # 可用库存列表
        
        self.setWindowTitle("库存调拨")
        self.setModal(True)
        self.resize(800, 600)
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建物料选择区域
        self.create_material_section(main_layout)
        
        # 创建调拨信息区域
        self.create_transfer_section(main_layout)
        
        # 创建可用库存显示区域
        self.create_inventory_section(main_layout)
        
        # 创建按钮区域
        self.create_button_section(main_layout)
    
    def create_material_section(self, parent_layout):
        """创建物料选择区域"""
        # 物料信息卡片
        material_card = QFrame()
        material_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        material_layout = QVBoxLayout(material_card)
        material_layout.setContentsMargins(20, 15, 20, 20)
        material_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📦 物料信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        material_layout.addWidget(title_label)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)
        
        # 第一行：物料编码、选择按钮
        form_layout.addWidget(QLabel("物料编码:"), 0, 0)
        self.material_id_edit = QLineEdit()
        self.material_id_edit.setReadOnly(True)
        self.material_id_edit.setPlaceholderText("请选择物料...")
        form_layout.addWidget(self.material_id_edit, 0, 1)
        
        self.select_material_btn = QPushButton("选择物料")
        self.select_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        self.select_material_btn.clicked.connect(self.select_material)
        form_layout.addWidget(self.select_material_btn, 0, 2)
        
        # 第二行：物料名称
        form_layout.addWidget(QLabel("物料名称:"), 1, 0)
        self.material_name_edit = QLineEdit()
        self.material_name_edit.setReadOnly(True)
        form_layout.addWidget(self.material_name_edit, 1, 1, 1, 2)
        
        material_layout.addWidget(form_widget)
        parent_layout.addWidget(material_card)
    
    def create_transfer_section(self, parent_layout):
        """创建调拨信息区域"""
        # 调拨信息卡片
        transfer_card = QFrame()
        transfer_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        transfer_layout = QVBoxLayout(transfer_card)
        transfer_layout.setContentsMargins(20, 15, 20, 20)
        transfer_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("🔄 调拨信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        transfer_layout.addWidget(title_label)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QGridLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)
        
        # 第一行：源部门、目标部门
        form_layout.addWidget(QLabel("源部门:"), 0, 0)
        self.from_department_combo = QComboBox()
        self.from_department_combo.currentTextChanged.connect(self.on_from_department_changed)
        self.load_department_options(self.from_department_combo)
        form_layout.addWidget(self.from_department_combo, 0, 1)
        
        form_layout.addWidget(QLabel("目标部门:"), 0, 2)
        self.to_department_combo = QComboBox()
        self.load_department_options(self.to_department_combo)
        form_layout.addWidget(self.to_department_combo, 0, 3)
        
        # 第二行：调拨数量、单位
        form_layout.addWidget(QLabel("调拨数量:"), 1, 0)
        self.transfer_qty_spin = QDoubleSpinBox()
        self.transfer_qty_spin.setRange(0, 999999.9999)
        self.transfer_qty_spin.setDecimals(4)
        self.transfer_qty_spin.setSuffix(" ")
        form_layout.addWidget(self.transfer_qty_spin, 1, 1)
        
        form_layout.addWidget(QLabel("单位:"), 1, 2)
        self.unit_edit = QLineEdit()
        self.unit_edit.setReadOnly(True)
        form_layout.addWidget(self.unit_edit, 1, 3)
        
        # 第三行：备注
        form_layout.addWidget(QLabel("备注:"), 2, 0)
        self.remark_edit = QTextEdit()
        self.remark_edit.setMaximumHeight(60)
        self.remark_edit.setPlaceholderText("可选，输入调拨原因...")
        form_layout.addWidget(self.remark_edit, 2, 1, 1, 3)
        
        transfer_layout.addWidget(form_widget)
        parent_layout.addWidget(transfer_card)
    
    def create_inventory_section(self, parent_layout):
        """创建可用库存显示区域"""
        # 库存信息卡片
        inventory_card = QFrame()
        inventory_card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
            }
        """)
        inventory_layout = QVBoxLayout(inventory_card)
        inventory_layout.setContentsMargins(20, 15, 20, 20)
        inventory_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📊 可用库存")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #262626;
                border: none;
                padding: 0px;
            }
        """)
        inventory_layout.addWidget(title_label)
        
        # 库存表格
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(5)
        self.inventory_table.setHorizontalHeaderLabels([
            "部门", "仓库", "总数量", "可用数量", "预留数量"
        ])
        
        # 设置表格样式
        self.inventory_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #f0f0f0;
                background-color: white;
                alternate-background-color: #fafafa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QHeaderView::section {
                background-color: #fafafa;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #f0f0f0;
                font-weight: bold;
            }
        """)
        
        self.inventory_table.setAlternatingRowColors(True)
        self.inventory_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.inventory_table.setMaximumHeight(200)
        
        # 设置列宽
        header = self.inventory_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.resizeSection(2, 100)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        header.resizeSection(3, 100)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        header.resizeSection(4, 100)
        
        inventory_layout.addWidget(self.inventory_table)
        parent_layout.addWidget(inventory_card)
    
    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                color: #666;
                border: 1px solid #d9d9d9;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 14px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #e6f7ff;
                border-color: #1890ff;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        # 调拨按钮
        self.transfer_btn = QPushButton("确认调拨")
        self.transfer_btn.setStyleSheet("""
            QPushButton {
                background-color: #fa8c16;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ffa940;
            }
        """)
        self.transfer_btn.clicked.connect(self.transfer_inventory)
        button_layout.addWidget(self.transfer_btn)
        
        parent_layout.addLayout(button_layout)

    # ==================== 业务逻辑方法 ====================

    def select_material(self):
        """选择物料"""
        try:
            from material.material_search_dialog import MaterialSearchDialog
            dialog = MaterialSearchDialog(self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                selected_material = dialog.get_selected_material()
                if selected_material:
                    self.material_id_edit.setText(selected_material['material_id'])
                    self.material_name_edit.setText(selected_material['name'])
                    self.unit_edit.setText(selected_material.get('base_unit', 'PCS'))

                    # 存储完整的物料信息
                    self.selected_material = selected_material

                    # 加载该物料的可用库存
                    self.load_material_inventory()

        except ImportError:
            QMessageBox.warning(self, "提示", "物料搜索功能暂未实现，请手动输入物料信息")
        except Exception as e:
            Logger.log_error(f"选择物料失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"选择物料失败：{str(e)}")

    def load_department_options(self, combo_box):
        """加载部门选项"""
        try:
            # 根据用户权限加载可访问的部门
            user_departments = self.current_user.get('departments', [])
            if self.permission_service.has_permission('system:admin'):
                # 管理员可以看到所有部门
                departments = ["通路一", "通路二", "生产部", "采购部"]
            else:
                departments = user_departments or [self.current_user.get('department', '')]

            for dept in departments:
                if dept:  # 确保部门不为空
                    combo_box.addItem(dept)

        except Exception as e:
            Logger.log_error(f"加载部门选项失败: {str(e)}")

    def on_from_department_changed(self):
        """源部门变化时的处理"""
        if self.selected_material:
            self.load_material_inventory()

    def load_material_inventory(self):
        """加载物料的可用库存"""
        try:
            if not self.selected_material:
                return

            material_id = self.selected_material['material_id']

            # 查询该物料在各部门的库存情况
            filters = {'material_id': material_id}
            total, inventory_list = self.inventory_db.get_department_inventory_list(
                filters=filters, page=1, page_size=100
            )

            self.available_inventory = inventory_list
            self.update_inventory_table()

        except Exception as e:
            Logger.log_error(f"加载物料库存失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载库存信息失败：{str(e)}")

    def update_inventory_table(self):
        """更新库存表格"""
        try:
            self.inventory_table.setRowCount(len(self.available_inventory))

            for row, inventory in enumerate(self.available_inventory):
                # 部门
                self.inventory_table.setItem(row, 0, QTableWidgetItem(str(inventory.get('department', ''))))

                # 仓库
                warehouse_name = inventory.get('warehouse_name') or inventory.get('warehouse_code', '')
                self.inventory_table.setItem(row, 1, QTableWidgetItem(str(warehouse_name)))

                # 总数量
                self.inventory_table.setItem(row, 2, QTableWidgetItem(f"{inventory.get('total_qty', 0):.2f}"))

                # 可用数量
                available_qty = inventory.get('available_qty', 0)
                available_item = QTableWidgetItem(f"{available_qty:.2f}")
                if available_qty <= 0:
                    available_item.setBackground(QColor('#ffebee'))
                    available_item.setForeground(QColor('#f44336'))
                self.inventory_table.setItem(row, 3, available_item)

                # 预留数量
                self.inventory_table.setItem(row, 4, QTableWidgetItem(f"{inventory.get('reserved_qty', 0):.2f}"))

        except Exception as e:
            Logger.log_error(f"更新库存表格失败: {str(e)}")

    def validate_input(self):
        """验证输入数据"""
        if not self.material_id_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "请选择物料")
            return False

        from_dept = self.from_department_combo.currentText()
        to_dept = self.to_department_combo.currentText()

        if not from_dept:
            QMessageBox.warning(self, "验证失败", "请选择源部门")
            return False

        if not to_dept:
            QMessageBox.warning(self, "验证失败", "请选择目标部门")
            return False

        if from_dept == to_dept:
            QMessageBox.warning(self, "验证失败", "源部门和目标部门不能相同")
            return False

        if self.transfer_qty_spin.value() <= 0:
            QMessageBox.warning(self, "验证失败", "调拨数量必须大于0")
            return False

        # 检查部门权限
        if not self.permission_service.check_department_permission(from_dept):
            QMessageBox.warning(self, "权限不足", f"您没有操作源部门 '{from_dept}' 库存的权限")
            return False

        if not self.permission_service.check_department_permission(to_dept):
            QMessageBox.warning(self, "权限不足", f"您没有操作目标部门 '{to_dept}' 库存的权限")
            return False

        # 检查源部门库存是否充足
        material_id = self.selected_material['material_id']
        available_qty = 0
        for inventory in self.available_inventory:
            if inventory['department'] == from_dept:
                available_qty += inventory.get('available_qty', 0)

        if available_qty < self.transfer_qty_spin.value():
            QMessageBox.warning(self, "库存不足",
                              f"源部门 '{from_dept}' 可用库存不足\n"
                              f"可用数量: {available_qty:.4f}\n"
                              f"需要数量: {self.transfer_qty_spin.value():.4f}")
            return False

        return True

    def transfer_inventory(self):
        """执行库存调拨"""
        try:
            # 验证输入
            if not self.validate_input():
                return

            # 确认调拨
            from_dept = self.from_department_combo.currentText()
            to_dept = self.to_department_combo.currentText()
            transfer_qty = self.transfer_qty_spin.value()
            material_name = self.material_name_edit.text()

            reply = QMessageBox.question(
                self, "确认调拨",
                f"确认要执行以下调拨操作吗？\n\n"
                f"物料: {material_name}\n"
                f"从: {from_dept}\n"
                f"到: {to_dept}\n"
                f"数量: {transfer_qty:.4f} {self.unit_edit.text()}\n\n"
                f"此操作不可撤销！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 构建调拨数据
            transfer_data = {
                'material_id': self.selected_material['material_id'],
                'from_department': from_dept,
                'to_department': to_dept,
                'transfer_qty': transfer_qty,
                'remark': self.remark_edit.toPlainText().strip() or None
            }

            # 执行调拨
            result = self.inventory_db.transfer_inventory(transfer_data)

            QMessageBox.information(self, "成功",
                                  f"库存调拨成功！\n"
                                  f"调拨单号: {result['transfer_no']}\n"
                                  f"{result['message']}")

            # 发送数据保存成功信号
            self.data_saved.emit()

            # 关闭对话框
            self.accept()

        except Exception as e:
            Logger.log_error(f"库存调拨失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"调拨失败：{str(e)}")
