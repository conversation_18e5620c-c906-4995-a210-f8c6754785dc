# -*- mode: python ; coding: utf-8 -*-
#####################################
# 退货管理系统 - 版本更新记录
#####################################
# Version 4.0.0 (2024-12-27)
# 更新内容：
# 1. 完成东方退货系统完整模块
# 2. 新增退货申请和入库收货单业务流程
# 3. 完善状态流转控制和业务规则验证
# 4. 优化用户界面和交互体验
# Version 3.8.9
# 更新内容：
# 优化拆分 更新 审核逻辑
# Version 3.1.5 (2024-12-26)
# 更新内容：
# 1. 优化作废查询及功能
# Version 3.1.4 (2024-12-26)
# 更新内容：
# 1. 增加作废功能
# 2. 增加拆分功能
# 3. 增加删除明细功能
# Version 3.1.3 (2024-12-23)
# 更新内容：
# 1. 修复已知审核及反审核问题
# 2. 明细拆分修改自动排序
# Version 3.1.2 (2024-12-20)
# 更新内容：
# 1. 数据库查询优化
# 2. 支持模糊搜索及精确搜索切换
# 3. 修复已知bug
# Version 3.1.0 (2024-01-15)
# 更新内容：
# 1. 新增操作日志功能
# 2. 优化数据库连接
# 3. 修复已知bug
# 4. 优化代码结构
# 5. 增加版本更新记录
# Version 3.0.0 (2024-01-01)
# 更新内容：
# 1. 增加批量交接单功能
# 2. 增加批量打印功能
# 3. 增加批量导出功能
# 4. 增加批量删除功能
# 5. 增加批量导入功能
#####################################

import os
import mysql.connector

mysql_path = os.path.dirname(mysql.connector.__file__)
icon_path = os.path.join('resources', 'icons', 'app.ico')

# 确保所有图标文件都被包含
icons_path = os.path.join('resources', 'icons')

block_cipher = None

a = Analysis(
    ['return_window.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('modules', 'modules'),
        ('core', 'core'),
        ('material', 'material'),
        ('report', 'report'),
        ('system', 'system'),
        ('plan', 'plan'),  # 新增需求计划模块
        ('demand', 'demand'),  # 新增需求计划模块
        ('cj_return', 'cj_return'),  # 新增东方退货模块
        ('resources', 'resources'),  # 包含整个resources目录
        ('resources/icons', 'resources/icons'),  # 明确包含图标目录
        ('resources/icons/home.ico', 'resources/icons'),  # 明确包含home.ico文件
        ('home_module.py', '.'),
        ('login_window.py', '.'),
        (os.path.join(mysql_path, 'locales'), 'mysql/connector/locales'),
        ('styles.qss', '.')
    ],
    hiddenimports=[
        'PyQt6.sip',
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'PyQt6.QtPrintSupport',  # 新增打印支持
        'pandas',
        'numpy',
        'openpyxl',
        'openpyxl.cell._writer',
        'et_xmlfile',
        'mysql.connector',
        'mysql.connector.locales',
        'mysql.connector.locales.eng',
        'mysql.connector.plugins',
        'mysql.connector.plugins.caching_sha2_password',
        'mysql.connector.plugins.mysql_native_password',
        'tkinter',
        'PIL',  
        'qrcode',
        'datetime',
        'random',
        'socket',
        'xlrd',
        'xlwt',
        # 新增东方退货模块相关导入
        'cj_return.dongfang_return_module',
        'cj_return.dongfang_return_dialog',
        'cj_return.dongfang_return_db',
        'cj_return.dongfang_detail_dialog',
        'cj_return.warehouse_receipt_module',
        'cj_return.warehouse_receipt_dialog',
        'cj_return.warehouse_receipt_print_dialog'
        # 新增需求计划模块相关导入
        'demand.demand_plan_module',
        'demand.demand_plan_dialog',
        'demand.demand_plan_db',
        'demand.demand_detail_dialog',
        'demand.demand_plan_print_dialog'
        #新增BOM模块相关导入
        'material.bom_module',
        'material.bom_dialog',
        'material.bom_db',
        'material.bom_detail_dialog',
        'material.bom_material_search_dialog',
        'material.bom_package_detail_dialog',
        'material.bom_material_detail_dialog',
        'material.bom_print_dialog',
        'material.bom_import_dialog',
        'material.bom_copy_dialog'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib', 
        'scipy', 
        'PyQt6.QtWebEngineCore'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='运营管理系统v4.1.0',  # 更新版本号
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path
) 
